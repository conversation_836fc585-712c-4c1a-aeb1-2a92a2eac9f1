# HMDM测试数据说明

## 测试数据目录结构

```
test_data/
├── README.md                    # 本文件
├── scenarios/                   # 场景测试数据
│   ├── situation_analysis.json  # 态势分析场景
│   ├── threat_calculation.json  # 威胁计算场景
│   └── decision_support.json    # 辅助决策场景
├── alternatives/                # 备选方案数据
│   ├── small_dataset.xlsx       # 小规模数据集（3-5个方案）
│   ├── medium_dataset.xlsx      # 中等规模数据集（10-20个方案）
│   ├── large_dataset.xlsx       # 大规模数据集（50+个方案）
│   └── invalid_data.xlsx        # 异常数据测试
├── evaluation_schemes/          # 评估方案数据
│   ├── default_scheme.json      # 默认评估方案
│   ├── custom_scheme_1.json     # 自定义方案1（重视效率）
│   ├── custom_scheme_2.json     # 自定义方案2（重视成本）
│   └── weight_variations.json   # 权重变化测试数据
├── performance/                 # 性能测试数据
│   ├── stress_test_data.xlsx    # 压力测试数据
│   └── concurrent_test.json     # 并发测试配置
├── boundary/                    # 边界条件测试数据
│   ├── empty_data.xlsx          # 空数据测试
│   ├── minimal_data.xlsx        # 最小数据集
│   └── extreme_values.xlsx      # 极值测试数据
└── integration/                 # 集成测试数据
    ├── complete_workflow.json   # 完整工作流程数据
    └── api_test_cases.json      # API测试用例数据
```

## 数据文件说明

### 1. 场景测试数据

#### situation_analysis.json
- **用途**：态势分析场景的任务分解测试
- **内容**：包含态势分析任务的完整分解结构
- **特点**：涵盖数据收集、分析、展示等典型子任务

#### threat_calculation.json
- **用途**：威胁计算场景的任务分解测试
- **内容**：威胁识别、评估、预警的任务结构
- **特点**：强调实时性和准确性要求

#### decision_support.json
- **用途**：辅助决策场景的任务分解测试
- **内容**：方案生成、评估、推荐的任务流程
- **特点**：注重决策质量和效率平衡

### 2. 备选方案数据

#### small_dataset.xlsx
- **方案数量**：3-5个
- **指标数量**：5-8个
- **用途**：基本功能测试和演示
- **特点**：数据简单清晰，便于验证算法正确性

#### medium_dataset.xlsx
- **方案数量**：10-20个
- **指标数量**：10-15个
- **用途**：常规业务场景测试
- **特点**：数据规模适中，接近实际应用

#### large_dataset.xlsx
- **方案数量**：50+个
- **指标数量**：20+个
- **用途**：性能测试和压力测试
- **特点**：大规模数据，测试系统处理能力

#### invalid_data.xlsx
- **用途**：异常数据处理测试
- **内容**：包含缺失值、异常值、格式错误等
- **特点**：测试系统的鲁棒性和错误处理能力

### 3. 评估方案数据

#### default_scheme.json
- **用途**：默认评估方案测试
- **内容**：7大类标准评估指标
- **权重分配**：均衡分配，适用于一般场景

#### custom_scheme_1.json
- **用途**：效率优先的评估方案
- **特点**：效率类指标权重较高
- **适用场景**：时间敏感的任务场景

#### custom_scheme_2.json
- **用途**：成本优先的评估方案
- **特点**：成本类指标权重较高
- **适用场景**：资源受限的任务场景

#### weight_variations.json
- **用途**：权重敏感性分析测试
- **内容**：多组不同的权重配置
- **特点**：用于测试权重变化对结果的影响

### 4. 性能测试数据

#### stress_test_data.xlsx
- **用途**：系统压力测试
- **规模**：100+方案，50+指标
- **特点**：测试系统在极限条件下的表现

#### concurrent_test.json
- **用途**：并发测试配置
- **内容**：多用户并发操作的测试场景
- **特点**：模拟真实的多用户环境

### 5. 边界条件测试数据

#### empty_data.xlsx
- **用途**：空数据处理测试
- **内容**：空的Excel文件或只有表头的文件
- **特点**：测试系统对空输入的处理

#### minimal_data.xlsx
- **用途**：最小数据集测试
- **内容**：1个方案，1个指标的最小配置
- **特点**：测试系统的最小运行要求

#### extreme_values.xlsx
- **用途**：极值测试
- **内容**：包含极大值、极小值、零值等
- **特点**：测试数值计算的稳定性

### 6. 集成测试数据

#### complete_workflow.json
- **用途**：完整工作流程测试
- **内容**：从任务创建到方案推荐的完整数据
- **特点**：验证端到端的业务流程

#### api_test_cases.json
- **用途**：API接口测试
- **内容**：各种API调用的测试用例
- **特点**：覆盖所有API端点和参数组合

## 数据格式规范

### Excel文件格式
```
| name | description | indicator1 | indicator2 | ... |
|------|-------------|------------|------------|-----|
| 方案A | 描述信息     | 0.8        | 0.6        | ... |
| 方案B | 描述信息     | 0.7        | 0.9        | ... |
```

### JSON文件格式
```json
{
  "metadata": {
    "name": "数据集名称",
    "description": "数据集描述",
    "version": "1.0",
    "created_date": "2024-01-01"
  },
  "data": {
    // 具体数据内容
  }
}
```

## 数据使用指南

### 1. 基本功能测试
- 使用small_dataset.xlsx和default_scheme.json
- 验证基本的决策算法功能
- 确保结果的正确性

### 2. 性能测试
- 使用large_dataset.xlsx和stress_test_data.xlsx
- 监控响应时间和资源使用
- 验证系统的处理能力

### 3. 鲁棒性测试
- 使用invalid_data.xlsx和边界条件数据
- 测试异常情况的处理
- 验证错误信息的准确性

### 4. 场景测试
- 使用scenarios目录下的场景数据
- 测试不同业务场景的适应性
- 验证任务分解的合理性

### 5. 敏感性分析
- 使用weight_variations.json
- 测试权重变化对结果的影响
- 验证决策结果的稳定性

## 数据维护说明

### 1. 数据更新
- 定期检查数据的有效性
- 根据系统功能变化更新测试数据
- 保持数据的代表性和完整性

### 2. 版本控制
- 对测试数据进行版本管理
- 记录数据变更历史
- 确保测试的可重现性

### 3. 数据安全
- 不包含敏感信息
- 使用模拟数据进行测试
- 保护测试数据的完整性

## 注意事项

1. **数据一致性**：确保测试数据与系统期望的格式一致
2. **数据完整性**：测试数据应覆盖各种可能的输入情况
3. **数据真实性**：虽然是测试数据，但应反映真实的业务场景
4. **数据可维护性**：测试数据应易于理解和维护
5. **数据可扩展性**：测试数据结构应支持功能扩展

## 联系信息

如有测试数据相关问题，请联系：
- 测试团队：<EMAIL>
- 技术支持：<EMAIL>
