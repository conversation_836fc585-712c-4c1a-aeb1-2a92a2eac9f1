"""
数据模型模块

定义系统中使用的核心数据结构：
- 任务分解相关的数据模型
- 评估指标体系数据模型
- 决策矩阵数据模型
"""

from .task_models import (
    TaskType, OperationType, ExecutorType,
    TaskAttribute, IOPattern, Task, MetaOperation, TaskHierarchy
)

from .evaluation_models import (
    IndicatorType, IndicatorCategory, AggregationMethod,
    IndicatorDefinition, IndicatorWeight, IndicatorValue,
    EvaluationScheme, EvaluationResult
)

from .decision_models import (
    DecisionMethod, FuzzySetType,
    FuzzyNumber, Alternative, DecisionMatrix, DecisionResult
)

__all__ = [
    # 任务模型
    "TaskType", "OperationType", "ExecutorType",
    "TaskAttribute", "IOPattern", "Task", "MetaOperation", "TaskHierarchy",

    # 评估模型
    "IndicatorType", "IndicatorCategory", "AggregationMethod",
    "IndicatorDefinition", "IndicatorWeight", "IndicatorValue",
    "EvaluationScheme", "EvaluationResult",

    # 决策模型
    "DecisionMethod", "FuzzySetType",
    "FuzzyNumber", "Alternative", "DecisionMatrix", "DecisionResult"
]
