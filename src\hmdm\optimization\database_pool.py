"""
数据库连接池优化系统

提供高性能的数据库连接管理：
- 连接池管理
- 连接复用和回收
- 连接健康检查
- 性能监控
- 自动扩缩容
"""

import time
import threading
import logging
import sqlite3
from typing import Optional, Dict, Any, List, Callable, ContextManager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from queue import Queue, Empty, Full
from contextlib import contextmanager
from enum import Enum

from ..monitoring.performance_monitor import PerformanceMonitor


class ConnectionState(Enum):
    """连接状态"""
    IDLE = "空闲"
    ACTIVE = "活跃"
    CLOSED = "已关闭"
    ERROR = "错误"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    connection: Any
    created_time: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    use_count: int = 0
    state: ConnectionState = ConnectionState.IDLE
    error_count: int = 0
    
    def is_expired(self, max_lifetime: int) -> bool:
        """检查连接是否过期"""
        return (datetime.now() - self.created_time).total_seconds() > max_lifetime
    
    def is_idle_timeout(self, idle_timeout: int) -> bool:
        """检查连接是否空闲超时"""
        return (datetime.now() - self.last_used).total_seconds() > idle_timeout
    
    def mark_used(self):
        """标记连接被使用"""
        self.last_used = datetime.now()
        self.use_count += 1
        self.state = ConnectionState.ACTIVE
    
    def mark_idle(self):
        """标记连接为空闲"""
        self.state = ConnectionState.IDLE
    
    def mark_error(self):
        """标记连接错误"""
        self.error_count += 1
        self.state = ConnectionState.ERROR


class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 performance_monitor: Optional[PerformanceMonitor] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        self.performance_monitor = performance_monitor
        
        # 连接池配置
        self.database_url = self.config.get('database_url', ':memory:')
        self.min_connections = self.config.get('min_connections', 2)
        self.max_connections = self.config.get('max_connections', 10)
        self.max_lifetime = self.config.get('max_lifetime', 3600)  # 1小时
        self.idle_timeout = self.config.get('idle_timeout', 300)   # 5分钟
        self.connection_timeout = self.config.get('connection_timeout', 30)  # 30秒
        self.health_check_interval = self.config.get('health_check_interval', 60)  # 1分钟
        
        # 连接池状态
        self.pool: Queue = Queue(maxsize=self.max_connections)
        self.active_connections: Dict[str, ConnectionInfo] = {}
        self.connection_counter = 0
        self.pool_lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_created': 0,
            'total_closed': 0,
            'total_errors': 0,
            'current_active': 0,
            'current_idle': 0,
            'peak_active': 0,
            'total_requests': 0,
            'total_wait_time': 0
        }
        
        # 健康检查线程
        self.health_check_thread: Optional[threading.Thread] = None
        self.stop_health_check = threading.Event()
        
        # 初始化连接池
        self._initialize_pool()
        
        # 启动健康检查
        self._start_health_check()
        
        self.logger.info(f"数据库连接池初始化完成，最小连接数: {self.min_connections}, 最大连接数: {self.max_connections}")
    
    def get_connection(self, timeout: Optional[float] = None) -> ContextManager[Any]:
        """获取数据库连接（上下文管理器）"""
        return self._connection_context(timeout or self.connection_timeout)
    
    @contextmanager
    def _connection_context(self, timeout: float):
        """连接上下文管理器"""
        connection_info = None
        start_time = time.time()
        
        try:
            # 获取连接
            connection_info = self._acquire_connection(timeout)
            
            if self.performance_monitor:
                wait_time = time.time() - start_time
                self.performance_monitor.record_metric(
                    "db_connection_wait_time",
                    wait_time,
                    description="数据库连接等待时间"
                )
            
            yield connection_info.connection
            
        except Exception as e:
            if connection_info:
                connection_info.mark_error()
            
            if self.performance_monitor:
                self.performance_monitor.record_error("db_connection_error")
            
            self.logger.error(f"数据库连接使用异常: {e}")
            raise
            
        finally:
            # 释放连接
            if connection_info:
                self._release_connection(connection_info)
    
    def _acquire_connection(self, timeout: float) -> ConnectionInfo:
        """获取连接"""
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            # 尝试从池中获取空闲连接
            try:
                connection_info = self.pool.get(timeout=timeout)
                
                # 检查连接健康状态
                if self._is_connection_healthy(connection_info):
                    connection_info.mark_used()
                    
                    with self.pool_lock:
                        self.active_connections[connection_info.connection_id] = connection_info
                        self.stats['current_active'] += 1
                        self.stats['current_idle'] -= 1
                        self.stats['peak_active'] = max(self.stats['peak_active'], self.stats['current_active'])
                    
                    return connection_info
                else:
                    # 连接不健康，关闭并创建新连接
                    self._close_connection(connection_info)
                    return self._create_new_connection()
                    
            except Empty:
                # 池中没有可用连接，尝试创建新连接
                if len(self.active_connections) < self.max_connections:
                    return self._create_new_connection()
                else:
                    raise Exception("连接池已满，无法获取新连接")
                    
        except Exception as e:
            wait_time = time.time() - start_time
            self.stats['total_wait_time'] += wait_time
            self.logger.error(f"获取数据库连接失败: {e}")
            raise
    
    def _release_connection(self, connection_info: ConnectionInfo):
        """释放连接"""
        try:
            with self.pool_lock:
                if connection_info.connection_id in self.active_connections:
                    del self.active_connections[connection_info.connection_id]
                    self.stats['current_active'] -= 1
            
            # 检查连接是否应该被关闭
            if (connection_info.state == ConnectionState.ERROR or
                connection_info.is_expired(self.max_lifetime) or
                connection_info.error_count > 3):
                
                self._close_connection(connection_info)
                return
            
            # 将连接返回到池中
            connection_info.mark_idle()
            
            try:
                self.pool.put_nowait(connection_info)
                with self.pool_lock:
                    self.stats['current_idle'] += 1
                    
            except Full:
                # 池已满，关闭连接
                self._close_connection(connection_info)
                
        except Exception as e:
            self.logger.error(f"释放数据库连接失败: {e}")
            self._close_connection(connection_info)
    
    def _create_new_connection(self) -> ConnectionInfo:
        """创建新连接"""
        try:
            with self.pool_lock:
                self.connection_counter += 1
                connection_id = f"conn_{self.connection_counter}"
            
            # 创建数据库连接
            connection = self._create_database_connection()
            
            connection_info = ConnectionInfo(
                connection_id=connection_id,
                connection=connection
            )
            
            connection_info.mark_used()
            
            with self.pool_lock:
                self.active_connections[connection_id] = connection_info
                self.stats['total_created'] += 1
                self.stats['current_active'] += 1
                self.stats['peak_active'] = max(self.stats['peak_active'], self.stats['current_active'])
            
            self.logger.debug(f"创建新数据库连接: {connection_id}")
            return connection_info
            
        except Exception as e:
            self.stats['total_errors'] += 1
            self.logger.error(f"创建数据库连接失败: {e}")
            raise
    
    def _create_database_connection(self) -> Any:
        """创建数据库连接"""
        # 这里使用SQLite作为示例，实际应用中可以支持多种数据库
        if self.database_url == ':memory:' or self.database_url.endswith('.db'):
            return sqlite3.connect(self.database_url, check_same_thread=False)
        else:
            # 可以扩展支持其他数据库
            raise ValueError(f"不支持的数据库URL: {self.database_url}")
    
    def _close_connection(self, connection_info: ConnectionInfo):
        """关闭连接"""
        try:
            if connection_info.connection:
                connection_info.connection.close()
            
            connection_info.state = ConnectionState.CLOSED
            
            with self.pool_lock:
                self.stats['total_closed'] += 1
                if connection_info.connection_id in self.active_connections:
                    del self.active_connections[connection_info.connection_id]
                    self.stats['current_active'] -= 1
            
            self.logger.debug(f"关闭数据库连接: {connection_info.connection_id}")
            
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")
    
    def _is_connection_healthy(self, connection_info: ConnectionInfo) -> bool:
        """检查连接健康状态"""
        try:
            if connection_info.state == ConnectionState.ERROR:
                return False
            
            if connection_info.is_expired(self.max_lifetime):
                return False
            
            # 执行简单的健康检查查询
            cursor = connection_info.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            
            return True
            
        except Exception:
            return False
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            for _ in range(self.min_connections):
                connection_info = self._create_new_connection()
                connection_info.mark_idle()
                self.pool.put_nowait(connection_info)
                
                with self.pool_lock:
                    if connection_info.connection_id in self.active_connections:
                        del self.active_connections[connection_info.connection_id]
                        self.stats['current_active'] -= 1
                    self.stats['current_idle'] += 1
                    
        except Exception as e:
            self.logger.error(f"初始化连接池失败: {e}")
    
    def _start_health_check(self):
        """启动健康检查线程"""
        if self.health_check_thread and self.health_check_thread.is_alive():
            return
        
        self.stop_health_check.clear()
        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.health_check_thread.start()
    
    def _health_check_loop(self):
        """健康检查循环"""
        while not self.stop_health_check.is_set():
            try:
                self._perform_health_check()
                self.stop_health_check.wait(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"健康检查异常: {e}")
                time.sleep(self.health_check_interval)
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 检查空闲连接
            idle_connections = []
            
            # 从池中取出所有连接进行检查
            while True:
                try:
                    connection_info = self.pool.get_nowait()
                    idle_connections.append(connection_info)
                except Empty:
                    break
            
            # 检查每个连接的健康状态
            healthy_connections = []
            for connection_info in idle_connections:
                if (connection_info.is_idle_timeout(self.idle_timeout) or
                    not self._is_connection_healthy(connection_info)):
                    
                    self._close_connection(connection_info)
                    with self.pool_lock:
                        self.stats['current_idle'] -= 1
                else:
                    healthy_connections.append(connection_info)
            
            # 将健康的连接放回池中
            for connection_info in healthy_connections:
                try:
                    self.pool.put_nowait(connection_info)
                except Full:
                    self._close_connection(connection_info)
                    with self.pool_lock:
                        self.stats['current_idle'] -= 1
            
            # 确保最小连接数
            current_total = self.stats['current_active'] + self.stats['current_idle']
            if current_total < self.min_connections:
                needed = self.min_connections - current_total
                for _ in range(needed):
                    try:
                        connection_info = self._create_new_connection()
                        connection_info.mark_idle()
                        self.pool.put_nowait(connection_info)
                        
                        with self.pool_lock:
                            if connection_info.connection_id in self.active_connections:
                                del self.active_connections[connection_info.connection_id]
                                self.stats['current_active'] -= 1
                            self.stats['current_idle'] += 1
                            
                    except Exception as e:
                        self.logger.error(f"补充连接失败: {e}")
                        break
            
        except Exception as e:
            self.logger.error(f"健康检查执行失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self.pool_lock:
            avg_wait_time = (self.stats['total_wait_time'] / self.stats['total_requests'] 
                           if self.stats['total_requests'] > 0 else 0)
            
            return {
                'pool_config': {
                    'min_connections': self.min_connections,
                    'max_connections': self.max_connections,
                    'max_lifetime': self.max_lifetime,
                    'idle_timeout': self.idle_timeout
                },
                'current_state': {
                    'active_connections': self.stats['current_active'],
                    'idle_connections': self.stats['current_idle'],
                    'total_connections': self.stats['current_active'] + self.stats['current_idle']
                },
                'lifetime_stats': {
                    'total_created': self.stats['total_created'],
                    'total_closed': self.stats['total_closed'],
                    'total_errors': self.stats['total_errors'],
                    'peak_active': self.stats['peak_active'],
                    'total_requests': self.stats['total_requests'],
                    'avg_wait_time_ms': avg_wait_time * 1000
                }
            }
    
    def close_pool(self):
        """关闭连接池"""
        try:
            # 停止健康检查
            self.stop_health_check.set()
            if self.health_check_thread:
                self.health_check_thread.join(timeout=5)
            
            # 关闭所有活跃连接
            with self.pool_lock:
                for connection_info in list(self.active_connections.values()):
                    self._close_connection(connection_info)
            
            # 关闭所有空闲连接
            while True:
                try:
                    connection_info = self.pool.get_nowait()
                    self._close_connection(connection_info)
                except Empty:
                    break
            
            self.logger.info("数据库连接池已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭连接池失败: {e}")
