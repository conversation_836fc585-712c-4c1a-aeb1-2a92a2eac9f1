"""
HMDM增强安全管理系统

提供完善的权限控制、数据加密、审计日志和安全认证功能。
符合军用级安全标准和要求。
"""

import hashlib
import hmac
import secrets
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import base64
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import jwt

from .military_security import SecurityLevel


class AuthenticationMethod(Enum):
    """认证方式"""
    PASSWORD = "密码认证"
    TOKEN = "令牌认证"
    CERTIFICATE = "证书认证"
    BIOMETRIC = "生物特征认证"
    MULTI_FACTOR = "多因素认证"


class PermissionType(Enum):
    """权限类型"""
    READ = "读取"
    WRITE = "写入"
    EXECUTE = "执行"
    DELETE = "删除"
    ADMIN = "管理"
    AUDIT = "审计"


class AuditEventType(Enum):
    """审计事件类型"""
    LOGIN = "登录"
    LOGOUT = "登出"
    ACCESS = "访问"
    MODIFY = "修改"
    DELETE = "删除"
    ADMIN_ACTION = "管理操作"
    SECURITY_VIOLATION = "安全违规"
    SYSTEM_EVENT = "系统事件"


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    password_hash: str
    salt: str
    security_level: SecurityLevel
    permissions: List[PermissionType] = field(default_factory=list)
    roles: List[str] = field(default_factory=list)
    created_time: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    login_attempts: int = 0
    is_locked: bool = False
    is_active: bool = True
    session_token: Optional[str] = None
    token_expires: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'security_level': self.security_level.value,
            'permissions': [p.value for p in self.permissions],
            'roles': self.roles,
            'created_time': self.created_time.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_attempts': self.login_attempts,
            'is_locked': self.is_locked,
            'is_active': self.is_active
        }


@dataclass
class AuditEvent:
    """审计事件"""
    event_id: str
    event_type: AuditEventType
    user_id: str
    username: str
    timestamp: datetime
    resource: str
    action: str
    result: str
    ip_address: str
    user_agent: str
    details: Dict[str, Any] = field(default_factory=dict)
    security_level: SecurityLevel = SecurityLevel.INTERNAL
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'user_id': self.user_id,
            'username': self.username,
            'timestamp': self.timestamp.isoformat(),
            'resource': self.resource,
            'action': self.action,
            'result': self.result,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'details': self.details,
            'security_level': self.security_level.value
        }


class EnhancedSecurityManager:
    """增强安全管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.config = config or {}
        self.session_timeout = self.config.get('session_timeout', 3600)  # 1小时
        self.max_login_attempts = self.config.get('max_login_attempts', 3)
        self.password_min_length = self.config.get('password_min_length', 8)
        self.enable_encryption = self.config.get('enable_encryption', True)
        
        # 用户管理
        self.users: Dict[str, User] = {}
        self.active_sessions: Dict[str, str] = {}  # token -> user_id
        
        # 审计日志
        self.audit_events: List[AuditEvent] = []
        self.audit_file = self.config.get('audit_file', 'logs/audit.log')
        
        # 加密管理
        self.encryption_key = None
        self.cipher_suite = None
        if self.enable_encryption:
            self._init_encryption()
        
        # JWT密钥
        self.jwt_secret = self.config.get('jwt_secret', secrets.token_urlsafe(32))
        
        # 初始化默认管理员用户
        self._init_default_admin()
        
        self.logger.info("增强安全管理器初始化完成")
    
    def _init_encryption(self):
        """初始化加密系统"""
        try:
            # 生成或加载加密密钥
            key_file = self.config.get('encryption_key_file', 'config/encryption.key')
            
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                self.encryption_key = Fernet.generate_key()
                os.makedirs(os.path.dirname(key_file), exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
            
            self.cipher_suite = Fernet(self.encryption_key)
            self.logger.info("加密系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"加密系统初始化失败: {e}")
            self.enable_encryption = False
    
    def _init_default_admin(self):
        """初始化默认管理员用户"""
        admin_username = self.config.get('default_admin_username', 'admin')
        admin_password = self.config.get('default_admin_password', 'admin123')
        
        if admin_username not in [user.username for user in self.users.values()]:
            admin_user = self.create_user(
                username=admin_username,
                password=admin_password,
                security_level=SecurityLevel.TOP_SECRET,
                permissions=[PermissionType.ADMIN, PermissionType.AUDIT],
                roles=['administrator', 'security_officer']
            )
            self.logger.info(f"默认管理员用户已创建: {admin_username}")
    
    def create_user(self, username: str, password: str, 
                   security_level: SecurityLevel = SecurityLevel.INTERNAL,
                   permissions: List[PermissionType] = None,
                   roles: List[str] = None) -> User:
        """创建用户"""
        try:
            # 验证用户名唯一性
            if any(user.username == username for user in self.users.values()):
                raise ValueError(f"用户名 {username} 已存在")
            
            # 验证密码强度
            if not self._validate_password(password):
                raise ValueError("密码不符合安全要求")
            
            # 生成用户ID和密码哈希
            user_id = secrets.token_urlsafe(16)
            salt = secrets.token_urlsafe(16)
            password_hash = self._hash_password(password, salt)
            
            # 创建用户对象
            user = User(
                user_id=user_id,
                username=username,
                password_hash=password_hash,
                salt=salt,
                security_level=security_level,
                permissions=permissions or [],
                roles=roles or []
            )
            
            self.users[user_id] = user
            
            # 记录审计事件
            self._log_audit_event(
                event_type=AuditEventType.ADMIN_ACTION,
                user_id='system',
                username='system',
                resource='user_management',
                action='create_user',
                result='success',
                details={'created_user': username, 'security_level': security_level.value}
            )
            
            self.logger.info(f"用户创建成功: {username}")
            return user
            
        except Exception as e:
            self.logger.error(f"创建用户失败: {e}")
            raise
    
    def authenticate_user(self, username: str, password: str, 
                         ip_address: str = '', user_agent: str = '') -> Optional[str]:
        """用户认证"""
        try:
            # 查找用户
            user = None
            for u in self.users.values():
                if u.username == username:
                    user = u
                    break
            
            if not user:
                self._log_audit_event(
                    event_type=AuditEventType.LOGIN,
                    user_id='unknown',
                    username=username,
                    resource='authentication',
                    action='login',
                    result='failure',
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={'reason': 'user_not_found'}
                )
                return None
            
            # 检查用户状态
            if not user.is_active or user.is_locked:
                self._log_audit_event(
                    event_type=AuditEventType.LOGIN,
                    user_id=user.user_id,
                    username=username,
                    resource='authentication',
                    action='login',
                    result='failure',
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={'reason': 'account_locked' if user.is_locked else 'account_inactive'}
                )
                return None
            
            # 验证密码
            if not self._verify_password(password, user.password_hash, user.salt):
                user.login_attempts += 1
                if user.login_attempts >= self.max_login_attempts:
                    user.is_locked = True
                    self.logger.warning(f"用户 {username} 因多次登录失败被锁定")
                
                self._log_audit_event(
                    event_type=AuditEventType.LOGIN,
                    user_id=user.user_id,
                    username=username,
                    resource='authentication',
                    action='login',
                    result='failure',
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={'reason': 'invalid_password', 'attempts': user.login_attempts}
                )
                return None
            
            # 认证成功，生成会话令牌
            token = self._generate_session_token(user)
            user.last_login = datetime.now()
            user.login_attempts = 0
            user.session_token = token
            user.token_expires = datetime.now() + timedelta(seconds=self.session_timeout)
            
            self.active_sessions[token] = user.user_id
            
            self._log_audit_event(
                event_type=AuditEventType.LOGIN,
                user_id=user.user_id,
                username=username,
                resource='authentication',
                action='login',
                result='success',
                ip_address=ip_address,
                user_agent=user_agent,
                details={'security_level': user.security_level.value}
            )
            
            self.logger.info(f"用户认证成功: {username}")
            return token
            
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return None
    
    def validate_session(self, token: str) -> Optional[User]:
        """验证会话令牌"""
        try:
            if token not in self.active_sessions:
                return None
            
            user_id = self.active_sessions[token]
            user = self.users.get(user_id)
            
            if not user or not user.is_active:
                self._invalidate_session(token)
                return None
            
            # 检查令牌是否过期
            if user.token_expires and datetime.now() > user.token_expires:
                self._invalidate_session(token)
                return None
            
            return user
            
        except Exception as e:
            self.logger.error(f"会话验证失败: {e}")
            return None
    
    def check_permission(self, user: User, resource: str, 
                        permission: PermissionType, 
                        required_security_level: SecurityLevel = SecurityLevel.INTERNAL) -> bool:
        """检查用户权限"""
        try:
            # 检查用户状态
            if not user.is_active or user.is_locked:
                return False
            
            # 检查安全等级
            if user.security_level.value < required_security_level.value:
                self._log_audit_event(
                    event_type=AuditEventType.SECURITY_VIOLATION,
                    user_id=user.user_id,
                    username=user.username,
                    resource=resource,
                    action='access_denied',
                    result='security_level_insufficient',
                    details={
                        'user_level': user.security_level.value,
                        'required_level': required_security_level.value
                    }
                )
                return False
            
            # 检查权限
            if permission not in user.permissions and PermissionType.ADMIN not in user.permissions:
                self._log_audit_event(
                    event_type=AuditEventType.SECURITY_VIOLATION,
                    user_id=user.user_id,
                    username=user.username,
                    resource=resource,
                    action='access_denied',
                    result='permission_insufficient',
                    details={
                        'required_permission': permission.value,
                        'user_permissions': [p.value for p in user.permissions]
                    }
                )
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"权限检查失败: {e}")
            return False

    def encrypt_data(self, data: str) -> str:
        """加密数据"""
        if not self.enable_encryption or not self.cipher_suite:
            return data

        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            self.logger.error(f"数据加密失败: {e}")
            return data

    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        if not self.enable_encryption or not self.cipher_suite:
            return encrypted_data

        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            self.logger.error(f"数据解密失败: {e}")
            return encrypted_data

    def logout_user(self, token: str, ip_address: str = '', user_agent: str = '') -> bool:
        """用户登出"""
        try:
            user = self.validate_session(token)
            if user:
                self._log_audit_event(
                    event_type=AuditEventType.LOGOUT,
                    user_id=user.user_id,
                    username=user.username,
                    resource='authentication',
                    action='logout',
                    result='success',
                    ip_address=ip_address,
                    user_agent=user_agent
                )

            self._invalidate_session(token)
            return True

        except Exception as e:
            self.logger.error(f"用户登出失败: {e}")
            return False

    def change_password(self, user_id: str, old_password: str, new_password: str) -> bool:
        """修改密码"""
        try:
            user = self.users.get(user_id)
            if not user:
                return False

            # 验证旧密码
            if not self._verify_password(old_password, user.password_hash, user.salt):
                self._log_audit_event(
                    event_type=AuditEventType.SECURITY_VIOLATION,
                    user_id=user.user_id,
                    username=user.username,
                    resource='password_change',
                    action='change_password',
                    result='failure',
                    details={'reason': 'invalid_old_password'}
                )
                return False

            # 验证新密码强度
            if not self._validate_password(new_password):
                return False

            # 更新密码
            new_salt = secrets.token_urlsafe(16)
            new_password_hash = self._hash_password(new_password, new_salt)

            user.password_hash = new_password_hash
            user.salt = new_salt

            self._log_audit_event(
                event_type=AuditEventType.ADMIN_ACTION,
                user_id=user.user_id,
                username=user.username,
                resource='password_change',
                action='change_password',
                result='success'
            )

            self.logger.info(f"用户 {user.username} 密码修改成功")
            return True

        except Exception as e:
            self.logger.error(f"密码修改失败: {e}")
            return False

    def get_audit_events(self, start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None,
                        event_type: Optional[AuditEventType] = None,
                        user_id: Optional[str] = None,
                        limit: int = 100) -> List[AuditEvent]:
        """获取审计事件"""
        try:
            events = self.audit_events.copy()

            # 时间过滤
            if start_time:
                events = [e for e in events if e.timestamp >= start_time]
            if end_time:
                events = [e for e in events if e.timestamp <= end_time]

            # 事件类型过滤
            if event_type:
                events = [e for e in events if e.event_type == event_type]

            # 用户过滤
            if user_id:
                events = [e for e in events if e.user_id == user_id]

            # 按时间倒序排列并限制数量
            events.sort(key=lambda x: x.timestamp, reverse=True)
            return events[:limit]

        except Exception as e:
            self.logger.error(f"获取审计事件失败: {e}")
            return []

    def get_security_statistics(self) -> Dict[str, Any]:
        """获取安全统计信息"""
        try:
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)

            # 基础统计
            total_users = len(self.users)
            active_users = len([u for u in self.users.values() if u.is_active])
            locked_users = len([u for u in self.users.values() if u.is_locked])
            active_sessions = len(self.active_sessions)

            # 审计事件统计
            total_events = len(self.audit_events)
            events_24h = len([e for e in self.audit_events if e.timestamp >= last_24h])
            events_7d = len([e for e in self.audit_events if e.timestamp >= last_7d])

            # 安全事件统计
            security_violations = len([e for e in self.audit_events
                                     if e.event_type == AuditEventType.SECURITY_VIOLATION])
            failed_logins = len([e for e in self.audit_events
                               if e.event_type == AuditEventType.LOGIN and e.result == 'failure'])

            # 按事件类型统计
            event_type_stats = {}
            for event in self.audit_events:
                event_type = event.event_type.value
                event_type_stats[event_type] = event_type_stats.get(event_type, 0) + 1

            return {
                'users': {
                    'total': total_users,
                    'active': active_users,
                    'locked': locked_users,
                    'active_sessions': active_sessions
                },
                'audit_events': {
                    'total': total_events,
                    'last_24h': events_24h,
                    'last_7d': events_7d,
                    'by_type': event_type_stats
                },
                'security': {
                    'violations': security_violations,
                    'failed_logins': failed_logins,
                    'encryption_enabled': self.enable_encryption
                },
                'timestamp': now.isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取安全统计失败: {e}")
            return {}

    def _generate_session_token(self, user: User) -> str:
        """生成会话令牌"""
        payload = {
            'user_id': user.user_id,
            'username': user.username,
            'security_level': user.security_level.value,
            'permissions': [p.value for p in user.permissions],
            'iat': datetime.now().timestamp(),
            'exp': (datetime.now() + timedelta(seconds=self.session_timeout)).timestamp()
        }

        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')

    def _invalidate_session(self, token: str):
        """使会话令牌失效"""
        if token in self.active_sessions:
            user_id = self.active_sessions[token]
            user = self.users.get(user_id)
            if user:
                user.session_token = None
                user.token_expires = None
            del self.active_sessions[token]

    def _hash_password(self, password: str, salt: str) -> str:
        """密码哈希"""
        return hashlib.pbkdf2_hmac('sha256',
                                  password.encode('utf-8'),
                                  salt.encode('utf-8'),
                                  100000).hex()

    def _verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        return hmac.compare_digest(
            self._hash_password(password, salt),
            password_hash
        )

    def _validate_password(self, password: str) -> bool:
        """验证密码强度"""
        if len(password) < self.password_min_length:
            return False

        # 检查密码复杂性
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)

        return sum([has_upper, has_lower, has_digit, has_special]) >= 3

    def _log_audit_event(self, event_type: AuditEventType, user_id: str, username: str,
                        resource: str, action: str, result: str,
                        ip_address: str = '', user_agent: str = '',
                        details: Dict[str, Any] = None,
                        security_level: SecurityLevel = SecurityLevel.INTERNAL):
        """记录审计事件"""
        try:
            event = AuditEvent(
                event_id=secrets.token_urlsafe(16),
                event_type=event_type,
                user_id=user_id,
                username=username,
                timestamp=datetime.now(),
                resource=resource,
                action=action,
                result=result,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details or {},
                security_level=security_level
            )

            self.audit_events.append(event)

            # 写入审计日志文件
            self._write_audit_log(event)

        except Exception as e:
            self.logger.error(f"记录审计事件失败: {e}")

    def _write_audit_log(self, event: AuditEvent):
        """写入审计日志文件"""
        try:
            os.makedirs(os.path.dirname(self.audit_file), exist_ok=True)

            with open(self.audit_file, 'a', encoding='utf-8') as f:
                log_entry = json.dumps(event.to_dict(), ensure_ascii=False)
                f.write(log_entry + '\n')

        except Exception as e:
            self.logger.error(f"写入审计日志失败: {e}")
