<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人机功能分配模型系统 (HMDM)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cpu"></i>
                人机功能分配模型系统 (HMDM)
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#task-section">任务分解</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#evaluation-section">方案评估</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#results-section">结果分析</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 状态面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i>
                            系统状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="status-panel">
                            <div class="col-md-3">
                                <div class="status-item">
                                    <span class="status-label">任务层次结构:</span>
                                    <span class="status-value" id="task-status">未创建</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <span class="status-label">评估方案:</span>
                                    <span class="status-value" id="scheme-status">未创建</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <span class="status-label">备选方案:</span>
                                    <span class="status-value" id="alternatives-status">0个</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <span class="status-label">评估结果:</span>
                                    <span class="status-value" id="results-status">无</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务分解部分 -->
        <div class="row mb-4" id="task-section">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-diagram-3"></i>
                            任务分解
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="scenario-select" class="form-label">选择任务场景</label>
                                    <select class="form-select" id="scenario-select">
                                        <option value="态势分析">态势分析</option>
                                        <option value="威胁计算">威胁计算</option>
                                        <option value="辅助决策">辅助决策</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" id="create-task-btn">
                                    <i class="bi bi-plus-circle"></i>
                                    创建任务结构
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div id="task-hierarchy-display" class="border rounded p-3" style="min-height: 200px;">
                                    <p class="text-muted text-center">任务层次结构将在这里显示</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案评估部分 -->
        <div class="row mb-4" id="evaluation-section">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clipboard-check"></i>
                            方案评估
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 评估方案设置 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>评估方案设置</h6>
                                <div class="mb-3">
                                    <label for="scheme-type" class="form-label">方案类型</label>
                                    <select class="form-select" id="scheme-type">
                                        <option value="default">默认方案</option>
                                        <option value="custom">自定义方案</option>
                                    </select>
                                </div>
                                <button class="btn btn-success" id="create-scheme-btn">
                                    <i class="bi bi-gear"></i>
                                    创建评估方案
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>备选方案管理</h6>
                                <div class="mb-3">
                                    <button class="btn btn-info" id="add-alternatives-btn">
                                        <i class="bi bi-plus"></i>
                                        添加备选方案
                                    </button>
                                    <button class="btn btn-secondary" id="import-data-btn">
                                        <i class="bi bi-upload"></i>
                                        导入数据
                                    </button>
                                </div>
                                <div id="alternatives-list" class="border rounded p-3" style="min-height: 100px;">
                                    <p class="text-muted text-center">备选方案列表</p>
                                </div>
                            </div>
                        </div>

                        <!-- 评估执行 -->
                        <div class="row">
                            <div class="col-12">
                                <h6>执行评估</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="decision-method" class="form-label">决策方法</label>
                                            <select class="form-select" id="decision-method">
                                                <option value="WRDM">加权相对偏差距离最小法</option>
                                                <option value="TOPSIS">TOPSIS法</option>
                                                <option value="FUZZY_AHP">模糊层次分析法</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button class="btn btn-warning me-2" id="run-evaluation-btn">
                                                    <i class="bi bi-play-circle"></i>
                                                    执行评估
                                                </button>
                                                <button class="btn btn-info" id="run-comparison-btn">
                                                    <i class="bi bi-bar-chart"></i>
                                                    方案比较
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果分析部分 -->
        <div class="row mb-4" id="results-section">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up"></i>
                            结果分析
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="results-display">
                            <p class="text-muted text-center">评估结果将在这里显示</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success" id="export-results-btn" disabled>
                                <i class="bi bi-download"></i>
                                导出结果
                            </button>
                            <button class="btn btn-danger" id="clear-session-btn">
                                <i class="bi bi-trash"></i>
                                清空数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <!-- 添加备选方案模态框 -->
    <div class="modal fade" id="addAlternativesModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加备选方案</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="alternatives-form">
                        <div class="alternative-item mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="方案名称" name="name">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="方案描述" name="description">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-4">
                                    <input type="number" class="form-control" placeholder="工作负荷 (0-1)" name="workload" step="0.01" min="0" max="1">
                                </div>
                                <div class="col-md-4">
                                    <input type="number" class="form-control" placeholder="效率 (0-1)" name="efficiency" step="0.01" min="0" max="1">
                                </div>
                                <div class="col-md-4">
                                    <input type="number" class="form-control" placeholder="成本" name="cost" step="1000">
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" id="add-alternative-item-btn">
                        <i class="bi bi-plus"></i>
                        添加更多方案
                    </button>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-alternatives-btn">保存方案</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传模态框 -->
    <div class="modal fade" id="importDataModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import-file" class="form-label">选择文件</label>
                        <input type="file" class="form-control" id="import-file" accept=".json,.xlsx,.xls">
                    </div>
                    <div class="alert alert-info">
                        <small>支持JSON格式的配置文件或Excel格式的备选方案文件</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="upload-file-btn">上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">处理中，请稍候...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
