# 创建new_docs文档目录结构

# 主要文档分类目录
New-Item -ItemType Directory -Path "new_docs/01_project_overview" -Force
New-Item -ItemType Directory -Path "new_docs/02_requirements" -Force
New-Item -ItemType Directory -Path "new_docs/03_architecture" -Force
New-Item -ItemType Directory -Path "new_docs/04_design" -Force
New-Item -ItemType Directory -Path "new_docs/05_implementation" -Force
New-Item -ItemType Directory -Path "new_docs/06_testing" -Force
New-Item -ItemType Directory -Path "new_docs/07_deployment" -Force
New-Item -ItemType Directory -Path "new_docs/08_user_guides" -Force
New-Item -ItemType Directory -Path "new_docs/09_api_reference" -Force
New-Item -ItemType Directory -Path "new_docs/10_quality_assurance" -Force
New-Item -ItemType Directory -Path "new_docs/11_project_management" -Force

# 资源目录
New-Item -ItemType Directory -Path "new_docs/assets" -Force
New-Item -ItemType Directory -Path "new_docs/assets/images" -Force
New-Item -ItemType Directory -Path "new_docs/assets/diagrams" -Force
New-Item -ItemType Directory -Path "new_docs/assets/screenshots" -Force

# 模板目录
New-Item -ItemType Directory -Path "new_docs/templates" -Force

Write-Host "文档目录结构创建完成！"
