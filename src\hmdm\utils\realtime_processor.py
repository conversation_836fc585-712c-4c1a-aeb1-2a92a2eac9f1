"""
实时数据处理器

支持军事环境下的流式数据处理、实时分析和快速响应。
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from queue import Queue, Empty
import threading
from concurrent.futures import ThreadPoolExecutor
import numpy as np


class DataType(Enum):
    """数据类型"""
    SENSOR = "传感器数据"
    INTELLIGENCE = "情报数据"
    COMMAND = "指令数据"
    STATUS = "状态数据"
    ALERT = "告警数据"
    DECISION = "决策数据"


class ProcessingPriority(Enum):
    """处理优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class RealTimeData:
    """实时数据"""
    id: str
    data_type: DataType
    content: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = ""
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    security_level: str = "SECRET"
    processed: bool = False
    processing_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "data_type": self.data_type.value,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "priority": self.priority.value,
            "security_level": self.security_level,
            "processed": self.processed,
            "processing_time": self.processing_time
        }


@dataclass
class ProcessingRule:
    """处理规则"""
    id: str
    name: str
    data_type: DataType
    condition: Callable[[RealTimeData], bool]
    action: Callable[[RealTimeData], Any]
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    enabled: bool = True
    execution_count: int = 0
    last_execution: Optional[datetime] = None
    average_execution_time: float = 0.0


class RealTimeProcessor:
    """实时数据处理器"""
    
    def __init__(self, max_workers: int = 4, buffer_size: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.max_workers = max_workers
        self.buffer_size = buffer_size
        
        # 数据缓冲区和队列
        self.data_buffer: Queue = Queue(maxsize=buffer_size)
        self.high_priority_queue: Queue = Queue()
        self.normal_priority_queue: Queue = Queue()
        
        # 处理规则
        self.processing_rules: Dict[str, ProcessingRule] = {}
        
        # 统计信息
        self.stats = {
            "total_received": 0,
            "total_processed": 0,
            "total_dropped": 0,
            "average_processing_time": 0.0,
            "throughput": 0.0,  # 每秒处理数据量
            "buffer_utilization": 0.0
        }
        
        # 实时监控数据
        self.recent_data: List[RealTimeData] = []
        self.alert_thresholds = {
            "buffer_full": 0.9,
            "processing_delay": 5.0,  # 秒
            "error_rate": 0.1
        }
        
        # 线程池和控制
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.processing_active = True
        self.processor_threads = []
        
        # 启动处理线程
        self._start_processing_threads()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_performance, daemon=True)
        self.monitor_thread.start()
    
    def add_processing_rule(self, rule: ProcessingRule) -> bool:
        """添加处理规则"""
        if rule.id in self.processing_rules:
            self.logger.warning(f"处理规则 {rule.id} 已存在")
            return False
        
        self.processing_rules[rule.id] = rule
        self.logger.info(f"添加处理规则: {rule.name}")
        return True
    
    def remove_processing_rule(self, rule_id: str) -> bool:
        """移除处理规则"""
        if rule_id in self.processing_rules:
            del self.processing_rules[rule_id]
            self.logger.info(f"移除处理规则: {rule_id}")
            return True
        return False
    
    def ingest_data(self, data: RealTimeData) -> bool:
        """接收数据"""
        try:
            # 检查缓冲区是否已满
            if self.data_buffer.full():
                self.stats["total_dropped"] += 1
                self.logger.warning("数据缓冲区已满，丢弃数据")
                return False
            
            # 根据优先级分配到不同队列
            if data.priority in [ProcessingPriority.URGENT, ProcessingPriority.CRITICAL]:
                self.high_priority_queue.put(data)
            else:
                self.normal_priority_queue.put(data)
            
            self.stats["total_received"] += 1
            
            # 保存最近数据用于监控
            self.recent_data.append(data)
            if len(self.recent_data) > 100:  # 只保留最近100条
                self.recent_data.pop(0)
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据接收失败: {e}")
            return False
    
    def process_data_sync(self, data: RealTimeData) -> Dict[str, Any]:
        """同步处理数据"""
        start_time = time.time()
        results = {}
        
        try:
            # 应用处理规则
            applicable_rules = [
                rule for rule in self.processing_rules.values()
                if rule.enabled and 
                (rule.data_type == data.data_type or rule.data_type == DataType.SENSOR) and
                rule.condition(data)
            ]
            
            # 按优先级排序
            applicable_rules.sort(key=lambda r: r.priority.value, reverse=True)
            
            for rule in applicable_rules:
                try:
                    rule_start = time.time()
                    result = rule.action(data)
                    rule_end = time.time()
                    
                    # 更新规则统计
                    rule.execution_count += 1
                    rule.last_execution = datetime.now()
                    execution_time = rule_end - rule_start
                    rule.average_execution_time = (
                        (rule.average_execution_time * (rule.execution_count - 1) + execution_time) /
                        rule.execution_count
                    )
                    
                    results[rule.id] = result
                    
                except Exception as e:
                    self.logger.error(f"规则 {rule.id} 执行失败: {e}")
                    results[rule.id] = {"error": str(e)}
            
            # 标记为已处理
            data.processed = True
            processing_time = time.time() - start_time
            data.processing_time = processing_time
            
            # 更新统计信息
            self.stats["total_processed"] += 1
            current_avg = self.stats["average_processing_time"]
            processed_count = self.stats["total_processed"]
            self.stats["average_processing_time"] = (
                (current_avg * (processed_count - 1) + processing_time) / processed_count
            )
            
            return {
                "data_id": data.id,
                "processing_time": processing_time,
                "rules_applied": len(applicable_rules),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            return {
                "data_id": data.id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def process_data_async(self, data: RealTimeData) -> Dict[str, Any]:
        """异步处理数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.process_data_sync, data)
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        # 更新缓冲区利用率
        self.stats["buffer_utilization"] = (
            self.high_priority_queue.qsize() + self.normal_priority_queue.qsize()
        ) / self.buffer_size
        
        # 计算吞吐量（最近1分钟）
        recent_time = datetime.now() - timedelta(minutes=1)
        recent_processed = len([d for d in self.recent_data if d.timestamp > recent_time and d.processed])
        self.stats["throughput"] = recent_processed / 60.0  # 每秒处理量
        
        return self.stats.copy()
    
    def get_rule_stats(self) -> List[Dict[str, Any]]:
        """获取规则统计信息"""
        rule_stats = []
        for rule in self.processing_rules.values():
            rule_stats.append({
                "id": rule.id,
                "name": rule.name,
                "data_type": rule.data_type.value,
                "priority": rule.priority.value,
                "enabled": rule.enabled,
                "execution_count": rule.execution_count,
                "average_execution_time": rule.average_execution_time,
                "last_execution": rule.last_execution.isoformat() if rule.last_execution else None
            })
        return rule_stats
    
    def get_recent_data(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的数据"""
        recent = self.recent_data[-limit:] if len(self.recent_data) > limit else self.recent_data
        return [data.to_dict() for data in recent]
    
    def create_alert_rule(self, name: str, data_type: DataType, 
                         threshold_field: str, threshold_value: float,
                         comparison: str = "greater") -> str:
        """创建告警规则"""
        rule_id = f"alert_{name}_{int(time.time())}"
        
        def alert_condition(data: RealTimeData) -> bool:
            if threshold_field not in data.content:
                return False
            
            value = data.content[threshold_field]
            if comparison == "greater":
                return value > threshold_value
            elif comparison == "less":
                return value < threshold_value
            elif comparison == "equal":
                return value == threshold_value
            return False
        
        def alert_action(data: RealTimeData) -> Dict[str, Any]:
            alert_info = {
                "alert_type": "threshold_exceeded",
                "rule_name": name,
                "threshold_field": threshold_field,
                "threshold_value": threshold_value,
                "actual_value": data.content.get(threshold_field),
                "data_source": data.source,
                "timestamp": data.timestamp.isoformat()
            }
            
            self.logger.warning(f"告警触发: {name} - {alert_info}")
            return alert_info
        
        rule = ProcessingRule(
            id=rule_id,
            name=f"告警规则: {name}",
            data_type=data_type,
            condition=alert_condition,
            action=alert_action,
            priority=ProcessingPriority.HIGH
        )
        
        self.add_processing_rule(rule)
        return rule_id
    
    def create_aggregation_rule(self, name: str, data_type: DataType,
                              field_name: str, window_size: int = 60,
                              aggregation_type: str = "average") -> str:
        """创建聚合规则"""
        rule_id = f"agg_{name}_{int(time.time())}"
        data_window = []
        
        def aggregation_condition(data: RealTimeData) -> bool:
            return field_name in data.content
        
        def aggregation_action(data: RealTimeData) -> Dict[str, Any]:
            nonlocal data_window
            
            # 添加新数据点
            data_window.append({
                "value": data.content[field_name],
                "timestamp": data.timestamp
            })
            
            # 清理过期数据
            cutoff_time = datetime.now() - timedelta(seconds=window_size)
            data_window = [d for d in data_window if d["timestamp"] > cutoff_time]
            
            if not data_window:
                return {"error": "no_data_in_window"}
            
            values = [d["value"] for d in data_window]
            
            if aggregation_type == "average":
                result = np.mean(values)
            elif aggregation_type == "sum":
                result = np.sum(values)
            elif aggregation_type == "max":
                result = np.max(values)
            elif aggregation_type == "min":
                result = np.min(values)
            elif aggregation_type == "count":
                result = len(values)
            else:
                result = np.mean(values)  # 默认平均值
            
            return {
                "aggregation_type": aggregation_type,
                "field_name": field_name,
                "window_size": window_size,
                "data_points": len(values),
                "result": float(result),
                "timestamp": datetime.now().isoformat()
            }
        
        rule = ProcessingRule(
            id=rule_id,
            name=f"聚合规则: {name}",
            data_type=data_type,
            condition=aggregation_condition,
            action=aggregation_action,
            priority=ProcessingPriority.NORMAL
        )
        
        self.add_processing_rule(rule)
        return rule_id
    
    def _start_processing_threads(self) -> None:
        """启动处理线程"""
        # 高优先级处理线程
        high_priority_thread = threading.Thread(
            target=self._process_queue,
            args=(self.high_priority_queue, "high_priority"),
            daemon=True
        )
        high_priority_thread.start()
        self.processor_threads.append(high_priority_thread)
        
        # 普通优先级处理线程
        for i in range(self.max_workers - 1):
            normal_thread = threading.Thread(
                target=self._process_queue,
                args=(self.normal_priority_queue, f"normal_{i}"),
                daemon=True
            )
            normal_thread.start()
            self.processor_threads.append(normal_thread)
    
    def _process_queue(self, queue: Queue, thread_name: str) -> None:
        """处理队列中的数据"""
        self.logger.info(f"启动处理线程: {thread_name}")
        
        while self.processing_active:
            try:
                # 获取数据，超时1秒
                data = queue.get(timeout=1)
                
                # 处理数据
                self.process_data_sync(data)
                
                queue.task_done()
                
            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理线程 {thread_name} 异常: {e}")
                time.sleep(1)
    
    def _monitor_performance(self) -> None:
        """监控性能"""
        while self.processing_active:
            try:
                stats = self.get_processing_stats()
                
                # 检查告警条件
                if stats["buffer_utilization"] > self.alert_thresholds["buffer_full"]:
                    self.logger.warning(f"缓冲区利用率过高: {stats['buffer_utilization']:.2%}")
                
                if stats["average_processing_time"] > self.alert_thresholds["processing_delay"]:
                    self.logger.warning(f"处理延迟过高: {stats['average_processing_time']:.2f}s")
                
                # 计算错误率
                if stats["total_received"] > 0:
                    error_rate = stats["total_dropped"] / stats["total_received"]
                    if error_rate > self.alert_thresholds["error_rate"]:
                        self.logger.warning(f"错误率过高: {error_rate:.2%}")
                
                time.sleep(10)  # 每10秒监控一次
                
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
                time.sleep(5)
    
    def shutdown(self) -> None:
        """关闭处理器"""
        self.processing_active = False
        
        # 等待队列处理完成
        self.high_priority_queue.join()
        self.normal_priority_queue.join()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 等待监控线程结束
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("实时数据处理器已关闭")
