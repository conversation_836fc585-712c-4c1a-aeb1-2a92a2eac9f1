{% extends "base.html" %}

{% block title %}人机功能分配 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-users-cog"></i>
                    人机功能分配决策系统
                </h1>
                <p class="page-subtitle">智能化的人机功能分配决策支持</p>
            </div>
        </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper bg-primary text-white rounded-circle me-3">
                            <i class="fas fa-power-off"></i>
                        </div>
                        <div>
                            <h6 class="card-title mb-0">系统状态</h6>
                            <span class="badge {% if allocation_system %}bg-success{% else %}bg-danger{% endif %}">
                                {% if allocation_system %}运行中{% else %}未启动{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper bg-info text-white rounded-circle me-3">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div>
                            <h6 class="card-title mb-0">分配模式</h6>
                            <small class="text-muted">
                                {% if allocation_config %}
                                    {{ allocation_config.allocation_mode }}
                                {% else %}
                                    未配置
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper bg-warning text-white rounded-circle me-3">
                            <i class="fas fa-target"></i>
                        </div>
                        <div>
                            <h6 class="card-title mb-0">优化目标</h6>
                            <small class="text-muted">
                                {% if allocation_config %}
                                    {{ allocation_config.optimization_objective }}
                                {% else %}
                                    未配置
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper bg-success text-white rounded-circle me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h6 class="card-title mb-0">方案数量</h6>
                            <small class="text-muted">
                                {% if allocation_config %}
                                    {{ allocation_config.default_scheme_count }}
                                {% else %}
                                    5
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能导航卡片 -->
    <div class="row">
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 feature-card" onclick="location.href='/allocation/task-input'">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-tasks fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">任务输入与分解</h5>
                    <p class="card-text">输入任务层次结构，进行智能化任务分解和分析</p>
                    <div class="mt-auto">
                        <a href="/allocation/task-input" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 开始输入
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 feature-card" onclick="location.href='/allocation/scheme-comparison'">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-balance-scale fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">方案生成与比较</h5>
                    <p class="card-text">生成多个分配方案，进行综合比较和分析</p>
                    <div class="mt-auto">
                        <a href="/allocation/scheme-comparison" class="btn btn-info">
                            <i class="fas fa-arrow-right"></i> 方案比较
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 feature-card" onclick="location.href='/allocation/effectiveness-evaluation'">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-chart-bar fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">效能评估</h5>
                    <p class="card-text">多维度评估分配方案的协同效能和风险</p>
                    <div class="mt-auto">
                        <a href="/allocation/effectiveness-evaluation" class="btn btn-warning">
                            <i class="fas fa-arrow-right"></i> 效能评估
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 feature-card" onclick="location.href='/allocation/decision-result'">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-trophy fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">决策结果</h5>
                    <p class="card-text">查看最优分配方案和实施指导建议</p>
                    <div class="mt-auto">
                        <a href="/allocation/decision-result" class="btn btn-success">
                            <i class="fas fa-arrow-right"></i> 查看结果
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置管理区域 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i>
                        系统配置管理
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>当前配置概览</h6>
                            {% if allocation_config %}
                            <ul class="list-unstyled">
                                <li><strong>分配模式:</strong> {{ allocation_config.allocation_mode }}</li>
                                <li><strong>优化目标:</strong> {{ allocation_config.optimization_objective }}</li>
                                <li><strong>决策阈值:</strong> {{ allocation_config.decision_threshold }}</li>
                                <li><strong>置信度阈值:</strong> {{ allocation_config.confidence_threshold }}</li>
                                <li><strong>默认方案数:</strong> {{ allocation_config.default_scheme_count }}</li>
                            </ul>
                            {% else %}
                            <p class="text-muted">配置信息不可用</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>快速操作</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="loadConfig()">
                                    <i class="fas fa-download"></i> 重新加载配置
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="exportConfig()">
                                    <i class="fas fa-file-export"></i> 导出配置
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="showConfigModal()">
                                    <i class="fas fa-edit"></i> 编辑配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置编辑模态框 -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑分配配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">分配模式</label>
                                <select class="form-select" id="allocationMode">
                                    <option value="automatic">自动分配</option>
                                    <option value="semi_automatic">半自动分配</option>
                                    <option value="manual">手动分配</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">优化目标</label>
                                <select class="form-select" id="optimizationObjective">
                                    <option value="efficiency">效率优先</option>
                                    <option value="reliability">可靠性优先</option>
                                    <option value="cost_effectiveness">成本效益优先</option>
                                    <option value="balanced">平衡优化</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">默认方案数</label>
                                <input type="number" class="form-control" id="defaultSchemeCount" min="2" max="10" value="5">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">决策阈值</label>
                                <input type="number" class="form-control" id="decisionThreshold" min="0" max="1" step="0.01" value="0.1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">置信度阈值</label>
                                <input type="number" class="form-control" id="confidenceThreshold" min="0" max="1" step="0.01" value="0.7">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">保存配置</button>
            </div>
        </div>
    </div>
</div>

<style>
.feature-card {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.icon-wrapper {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon {
    opacity: 0.8;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<script>
// 加载配置
function loadConfig() {
    fetch('/api/allocation/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('加载配置失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('请求失败: ' + error.message);
        });
}

// 导出配置
function exportConfig() {
    fetch('/api/allocation/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'allocation_config.json';
                a.click();
                URL.revokeObjectURL(url);
            } else {
                alert('导出配置失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('请求失败: ' + error.message);
        });
}

// 显示配置模态框
function showConfigModal() {
    // 加载当前配置到表单
    {% if allocation_config %}
    document.getElementById('allocationMode').value = '{{ allocation_config.allocation_mode }}';
    document.getElementById('optimizationObjective').value = '{{ allocation_config.optimization_objective }}';
    document.getElementById('defaultSchemeCount').value = {{ allocation_config.default_scheme_count }};
    document.getElementById('decisionThreshold').value = {{ allocation_config.decision_threshold }};
    document.getElementById('confidenceThreshold').value = {{ allocation_config.confidence_threshold }};
    {% endif %}
    
    new bootstrap.Modal(document.getElementById('configModal')).show();
}

// 保存配置
function saveConfig() {
    const config = {
        allocation_mode: document.getElementById('allocationMode').value,
        optimization_objective: document.getElementById('optimizationObjective').value,
        default_scheme_count: parseInt(document.getElementById('defaultSchemeCount').value),
        decision_threshold: parseFloat(document.getElementById('decisionThreshold').value),
        confidence_threshold: parseFloat(document.getElementById('confidenceThreshold').value)
    };
    
    fetch('/api/allocation/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('配置保存成功');
            bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
            location.reload();
        } else {
            alert('配置保存失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}
</script>
{% endblock %}
