"""
多目标模糊决策模块

实现加权相对偏差距离最小法的多目标模糊决策模型：
1. 建立决策论域
2. 确定因素指标集合
3. 确定因素指标值矩阵
4. 计算模糊关系矩阵
5. 确定各因素指标的权值向量
6. 根据目标函数决策结果
"""

from .fuzzy_decision_engine import FuzzyDecisionEngine
from .decision_utils import (
    calculate_consistency_ratio,
    normalize_fuzzy_weights,
    calculate_rank_correlation,
    calculate_decision_stability,
    detect_rank_reversal,
    calculate_decision_confidence,
    generate_decision_report,
    visualize_decision_results,
    export_decision_results
)
from .rapid_decision_engine import RapidDecisionEngine
from .military_decision_support import (
    MilitaryDecisionSupport,
    MilitaryResource,
    MilitaryObjective,
    MilitaryOption,
    DecisionType,
    OperationalPhase,
    ResourceType
)

__all__ = [
    "FuzzyDecisionEngine",
    "RapidDecisionEngine",
    "MilitaryDecisionSupport",
    "MilitaryResource",
    "MilitaryObjective",
    "MilitaryOption",
    "DecisionType",
    "OperationalPhase",
    "ResourceType",
    "calculate_consistency_ratio",
    "normalize_fuzzy_weights",
    "calculate_rank_correlation",
    "calculate_decision_stability",
    "detect_rank_reversal",
    "calculate_decision_confidence",
    "generate_decision_report",
    "visualize_decision_results",
    "export_decision_results"
]
