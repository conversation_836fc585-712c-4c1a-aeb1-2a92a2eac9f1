{% extends "base.html" %}

{% block title %}决策结果 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-trophy"></i>
                    决策结果
                </h1>
                <p class="page-subtitle">最优人机功能分配方案和实施指导</p>
            </div>
        </div>
    </div>

    <!-- 决策概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="alert-heading mb-1">决策完成</h5>
                        <p class="mb-0">基于多维度分析，系统已为您推荐最优的人机功能分配方案</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最优方案展示 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star"></i>
                        推荐方案详情
                    </h5>
                </div>
                <div class="card-body">
                    <div id="optimalSchemeDetail">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>正在加载方案详情...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i>
                        方案评分
                    </h5>
                </div>
                <div class="card-body">
                    <div id="scoreBreakdown">
                        <div class="text-center">
                            <canvas id="scoreChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb"></i>
                        实施建议
                    </h5>
                </div>
                <div class="card-body">
                    <div id="implementationAdvice">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                优先部署高置信度任务分配
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                建立人机协同训练机制
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                设置实时监控和调整机制
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                制定应急预案和备选方案
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务分配详情 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks"></i>
                        详细任务分配
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="allocationTable">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>任务类型</th>
                                    <th>分配给</th>
                                    <th>置信度</th>
                                    <th>复杂度</th>
                                    <th>重要性</th>
                                    <th>建议</th>
                                </tr>
                            </thead>
                            <tbody id="allocationTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能预测 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i>
                        性能预测
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performancePredictionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        风险分析
                    </h5>
                </div>
                <div class="card-body">
                    <div id="riskAnalysis">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-success mb-1" id="lowRiskTasks">0</h4>
                                    <small class="text-muted">低风险任务</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-warning mb-1" id="mediumRiskTasks">0</h4>
                                    <small class="text-muted">中风险任务</small>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-danger mb-1" id="highRiskTasks">0</h4>
                                    <small class="text-muted">高风险任务</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-info mb-1" id="overallRisk">0.0</h4>
                                    <small class="text-muted">整体风险</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">决策操作</h6>
                            <small class="text-muted">保存决策结果或开始新的分配流程</small>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-info" onclick="exportDecision()">
                                <i class="fas fa-file-export"></i> 导出决策
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="saveDecision()">
                                <i class="fas fa-save"></i> 保存方案
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="implementScheme()">
                                <i class="fas fa-play"></i> 实施方案
                            </button>
                            <button type="button" class="btn btn-primary" onclick="startNewAllocation()">
                                <i class="fas fa-plus"></i> 新建分配
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.allocation-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.confidence-bar {
    height: 20px;
    border-radius: 10px;
}

.task-recommendation {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let evaluationResults = null;
let selectedScheme = null;
let taskHierarchy = null;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 加载数据
    loadData();
    
    if (selectedScheme) {
        displayOptimalScheme();
        displayAllocationTable();
        displayRiskAnalysis();
        drawCharts();
    } else {
        showToast('未找到决策数据，请重新进行分配流程', 'warning');
        setTimeout(() => {
            window.location.href = '/allocation';
        }, 2000);
    }
});

// 加载数据
function loadData() {
    // 从sessionStorage加载数据
    const evaluationData = sessionStorage.getItem('evaluationResults');
    const schemeData = sessionStorage.getItem('selectedScheme');
    const taskData = sessionStorage.getItem('taskHierarchy');
    
    if (evaluationData) {
        evaluationResults = JSON.parse(evaluationData);
    }
    
    if (schemeData) {
        selectedScheme = JSON.parse(schemeData);
    } else if (evaluationResults && evaluationResults.length > 0) {
        // 如果没有明确选择的方案，使用评估结果中的第一个（最优）方案
        selectedScheme = evaluationResults[0];
    }
    
    if (taskData) {
        taskHierarchy = JSON.parse(taskData);
    }
}

// 显示最优方案
function displayOptimalScheme() {
    if (!selectedScheme) return;
    
    const container = document.getElementById('optimalSchemeDetail');
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h5 class="text-primary mb-3">${selectedScheme.name}</h5>
                <p class="text-muted">${selectedScheme.description}</p>
                
                <h6>核心指标</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <h4 class="text-success mb-1">${(selectedScheme.overall_effectiveness * 100).toFixed(1)}%</h4>
                            <small class="text-muted">综合效能</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <h4 class="text-info mb-1">${(selectedScheme.effectiveness_score * 100).toFixed(1)}%</h4>
                            <small class="text-muted">效能评分</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h6>详细评分</h6>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <small>任务完成率</small>
                        <small>${(selectedScheme.metrics.task_completion * 100).toFixed(1)}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: ${selectedScheme.metrics.task_completion * 100}%"></div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <small>时间效率</small>
                        <small>${(selectedScheme.metrics.time_efficiency * 100).toFixed(1)}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-info" style="width: ${selectedScheme.metrics.time_efficiency * 100}%"></div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <small>资源利用率</small>
                        <small>${(selectedScheme.metrics.resource_utilization * 100).toFixed(1)}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: ${selectedScheme.metrics.resource_utilization * 100}%"></div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <small>错误率</small>
                        <small>${(selectedScheme.metrics.error_rate * 100).toFixed(1)}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-danger" style="width: ${selectedScheme.metrics.error_rate * 100}%"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 显示分配表格
function displayAllocationTable() {
    if (!selectedScheme || !taskHierarchy) return;
    
    const tbody = document.getElementById('allocationTableBody');
    tbody.innerHTML = '';
    
    Object.entries(selectedScheme.allocations).forEach(([taskId, allocation]) => {
        const task = taskHierarchy.tasks[taskId];
        if (!task) return;
        
        const row = tbody.insertRow();
        
        // 生成建议
        let recommendation = '';
        if (allocation.confidence < 0.7) {
            recommendation = '需要额外验证';
        } else if (task.attributes.real_time_requirement && allocation.executor === 'human') {
            recommendation = '考虑机器辅助';
        } else if (task.attributes.complexity > 0.8 && allocation.executor === 'machine') {
            recommendation = '建议人机协同';
        } else {
            recommendation = '分配合理';
        }
        
        row.innerHTML = `
            <td>
                <strong>${task.name}</strong>
                <br><small class="text-muted">${task.description || '无描述'}</small>
            </td>
            <td>
                <span class="badge bg-secondary">${task.task_type}</span>
            </td>
            <td>
                <span class="badge allocation-badge ${
                    allocation.executor === 'human' ? 'bg-info' :
                    allocation.executor === 'machine' ? 'bg-warning' : 'bg-success'
                }">
                    ${allocation.executor === 'human' ? '人类' :
                      allocation.executor === 'machine' ? '机器' : '人机协同'}
                </span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress confidence-bar flex-grow-1 me-2">
                        <div class="progress-bar ${
                            allocation.confidence >= 0.8 ? 'bg-success' :
                            allocation.confidence >= 0.6 ? 'bg-warning' : 'bg-danger'
                        }" style="width: ${allocation.confidence * 100}%"></div>
                    </div>
                    <small>${(allocation.confidence * 100).toFixed(1)}%</small>
                </div>
            </td>
            <td>
                <span class="badge ${
                    task.attributes.complexity >= 0.7 ? 'bg-danger' :
                    task.attributes.complexity >= 0.4 ? 'bg-warning' : 'bg-success'
                }">${task.attributes.complexity.toFixed(2)}</span>
            </td>
            <td>
                <span class="badge ${
                    task.attributes.importance >= 0.7 ? 'bg-danger' :
                    task.attributes.importance >= 0.4 ? 'bg-warning' : 'bg-success'
                }">${task.attributes.importance.toFixed(2)}</span>
            </td>
            <td>
                <div class="task-recommendation">
                    ${recommendation}
                </div>
            </td>
        `;
    });
}

// 显示风险分析
function displayRiskAnalysis() {
    if (!selectedScheme || !taskHierarchy) return;
    
    let lowRisk = 0, mediumRisk = 0, highRisk = 0;
    let totalRisk = 0;
    let taskCount = 0;
    
    Object.entries(selectedScheme.allocations).forEach(([taskId, allocation]) => {
        const task = taskHierarchy.tasks[taskId];
        if (!task) return;
        
        // 计算任务风险
        let risk = 0;
        risk += (1 - allocation.confidence) * 0.4; // 置信度风险
        risk += task.attributes.complexity * 0.3; // 复杂度风险
        risk += task.attributes.importance * 0.2; // 重要性风险
        risk += (task.attributes.real_time_requirement ? 0.1 : 0); // 实时性风险
        
        totalRisk += risk;
        taskCount++;
        
        if (risk < 0.3) lowRisk++;
        else if (risk < 0.6) mediumRisk++;
        else highRisk++;
    });
    
    const overallRisk = taskCount > 0 ? totalRisk / taskCount : 0;
    
    document.getElementById('lowRiskTasks').textContent = lowRisk;
    document.getElementById('mediumRiskTasks').textContent = mediumRisk;
    document.getElementById('highRiskTasks').textContent = highRisk;
    document.getElementById('overallRisk').textContent = (overallRisk * 100).toFixed(1) + '%';
}

// 绘制图表
function drawCharts() {
    drawScoreChart();
    drawPerformancePredictionChart();
}

// 绘制评分图表
function drawScoreChart() {
    const ctx = document.getElementById('scoreChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['任务完成', '时间效率', '资源利用', '可靠性', '协同效率'],
            datasets: [{
                data: [
                    selectedScheme.metrics.task_completion * 100,
                    selectedScheme.metrics.time_efficiency * 100,
                    selectedScheme.metrics.resource_utilization * 100,
                    (1 - selectedScheme.metrics.error_rate) * 100,
                    (1 - selectedScheme.metrics.coordination_overhead) * 100
                ],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#fd7e14',
                    '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 绘制性能预测图表
function drawPerformancePredictionChart() {
    const ctx = document.getElementById('performancePredictionChart').getContext('2d');
    
    // 模拟性能预测数据
    const timePoints = ['当前', '1个月', '3个月', '6个月', '1年'];
    const performanceData = [
        selectedScheme.overall_effectiveness * 100,
        selectedScheme.overall_effectiveness * 100 * 1.05,
        selectedScheme.overall_effectiveness * 100 * 1.12,
        selectedScheme.overall_effectiveness * 100 * 1.18,
        selectedScheme.overall_effectiveness * 100 * 1.25
    ];
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: timePoints,
            datasets: [{
                label: '预期性能',
                data: performanceData,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '方案性能预测趋势'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 导出决策
function exportDecision() {
    if (!selectedScheme) {
        showToast('没有决策结果可以导出', 'warning');
        return;
    }
    
    const decision = {
        decision_date: new Date().toISOString(),
        selected_scheme: selectedScheme,
        task_hierarchy: taskHierarchy,
        implementation_plan: {
            phases: [
                {
                    name: '准备阶段',
                    duration: '1-2周',
                    activities: ['人员培训', '系统配置', '测试验证']
                },
                {
                    name: '试运行阶段',
                    duration: '2-4周',
                    activities: ['小规模部署', '性能监控', '问题调优']
                },
                {
                    name: '全面部署阶段',
                    duration: '4-8周',
                    activities: ['全面推广', '持续监控', '效果评估']
                }
            ]
        },
        risk_mitigation: {
            high_risk_tasks: [],
            contingency_plans: [],
            monitoring_points: []
        }
    };
    
    const blob = new Blob([JSON.stringify(decision, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'allocation_decision.json';
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('决策结果导出成功', 'success');
}

// 保存方案
function saveDecision() {
    showToast('方案保存成功', 'success');
}

// 实施方案
function implementScheme() {
    if (confirm('确定要开始实施此分配方案吗？')) {
        showToast('方案实施已启动，请关注系统监控', 'success');
    }
}

// 开始新的分配
function startNewAllocation() {
    if (confirm('确定要开始新的分配流程吗？当前结果将被清除。')) {
        // 清除sessionStorage
        sessionStorage.removeItem('taskHierarchy');
        sessionStorage.removeItem('comparisonResults');
        sessionStorage.removeItem('evaluationResults');
        sessionStorage.removeItem('selectedScheme');
        
        // 跳转到任务输入页面
        window.location.href = '/allocation/task-input';
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 简单的提示实现
    alert(message);
}
</script>
{% endblock %}
