"""
数据加载器

提供各种格式数据的加载和转换功能
"""

from typing import Dict, List, Any, Optional, Union
import json
import yaml
import pandas as pd
import numpy as np
from pathlib import Path
import logging

from ..models.task_models import Task, TaskHierarchy, TaskType, ExecutorType
from ..models.evaluation_models import EvaluationScheme, IndicatorDefinition, IndicatorType
from ..models.decision_models import Alternative, FuzzyNumber


class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def load_json(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载JSON文件失败 {file_path}: {e}")
            return {}
    
    def load_yaml(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载YAML文件失败 {file_path}: {e}")
            return {}
    
    def load_excel(self, file_path: Union[str, Path], sheet_name: Optional[str] = None) -> pd.DataFrame:
        """加载Excel文件"""
        try:
            return pd.read_excel(file_path, sheet_name=sheet_name)
        except Exception as e:
            self.logger.error(f"加载Excel文件失败 {file_path}: {e}")
            return pd.DataFrame()
    
    def load_csv(self, file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """加载CSV文件"""
        try:
            return pd.read_csv(file_path, **kwargs)
        except Exception as e:
            self.logger.error(f"加载CSV文件失败 {file_path}: {e}")
            return pd.DataFrame()
    
    def load_task_hierarchy_from_json(self, file_path: Union[str, Path]) -> Optional[TaskHierarchy]:
        """从JSON文件加载任务层次结构"""
        data = self.load_json(file_path)
        if not data:
            return None
        
        try:
            hierarchy = TaskHierarchy(root_task_id=data["root_task_id"])
            
            for task_id, task_data in data["tasks"].items():
                # 处理任务类型的映射
                task_type_str = task_data["task_type"]
                task_type_mapping = {
                    "MISSION_TASK": TaskType.MISSION_TASK,
                    "ZZ_TASK": TaskType.ZZ_TASK,
                    "TYPICAL_FUNCTION": TaskType.TYPICAL_FUNCTION,
                    "INTERACTION_PROCESS": TaskType.INTERACTION_PROCESS,
                    "OPERATION_SEQUENCE": TaskType.OPERATION_SEQUENCE,
                    "META_OPERATION": TaskType.META_OPERATION
                }
                task_type = task_type_mapping.get(task_type_str, TaskType.MISSION_TASK)

                # 处理执行者类型的映射
                executor_type_str = task_data.get("executor_type", "HUMAN_MACHINE")
                executor_type_mapping = {
                    "HUMAN": ExecutorType.HUMAN,
                    "MACHINE": ExecutorType.MACHINE,
                    "HUMAN_MACHINE": ExecutorType.HUMAN_MACHINE
                }
                executor_type = executor_type_mapping.get(executor_type_str, ExecutorType.HUMAN_MACHINE)

                task = Task(
                    id=task_data["id"],
                    name=task_data["name"],
                    description=task_data["description"],
                    task_type=task_type,
                    parent_id=task_data.get("parent_id"),
                    children_ids=task_data.get("children_ids", []),
                    level=task_data.get("level", 0),
                    executor_type=executor_type
                )
                hierarchy.tasks[task_id] = task
            
            return hierarchy
        except Exception as e:
            self.logger.error(f"解析任务层次结构失败: {e}")
            return None
    
    def load_evaluation_scheme_from_json(self, file_path: Union[str, Path]) -> Optional[EvaluationScheme]:
        """从JSON文件加载评估方案"""
        data = self.load_json(file_path)
        if not data:
            return None
        
        try:
            scheme = EvaluationScheme(
                id=data.get("id", ""),
                name=data.get("name", ""),
                description=data.get("description", "")
            )
            
            # 加载指标
            for indicator_id, indicator_data in data.get("indicators", {}).items():
                # 处理指标类型的映射
                indicator_type_str = indicator_data["indicator_type"]
                indicator_type_mapping = {
                    "WORKLOAD": IndicatorType.WORKLOAD,
                    "EFFICIENCY": IndicatorType.EFFICIENCY,
                    "RELIABILITY": IndicatorType.RELIABILITY,
                    "USABILITY": IndicatorType.USABILITY,
                    "SAFETY": IndicatorType.SAFETY,
                    "COST": IndicatorType.COST,
                    "PERFORMANCE": IndicatorType.PERFORMANCE
                }
                indicator_type = indicator_type_mapping.get(indicator_type_str, IndicatorType.WORKLOAD)

                indicator = IndicatorDefinition(
                    id=indicator_data["id"],
                    name=indicator_data["name"],
                    description=indicator_data["description"],
                    indicator_type=indicator_type,
                    min_value=indicator_data.get("min_value", 0.0),
                    max_value=indicator_data.get("max_value", 1.0),
                    is_benefit=indicator_data.get("is_benefit", True),
                    unit=indicator_data.get("unit", "")
                )
                scheme.indicators[indicator_id] = indicator
            
            # 加载权重
            for weight_id, weight_data in data.get("weights", {}).items():
                scheme.set_weight(
                    weight_data["indicator_id"],
                    weight_data["weight"],
                    weight_data.get("confidence", 1.0),
                    weight_data.get("source", "数据文件")
                )
            
            return scheme
        except Exception as e:
            self.logger.error(f"解析评估方案失败: {e}")
            return None
    
    def load_alternatives_from_excel(self, file_path: Union[str, Path]) -> List[Alternative]:
        """从Excel文件加载备选方案"""
        result = self.load_excel(file_path)

        # 如果返回的是字典（多个工作表），取第一个工作表
        if isinstance(result, dict):
            df = list(result.values())[0]
        else:
            df = result

        if df.empty:
            return []
        
        alternatives = []
        
        try:
            for _, row in df.iterrows():
                alternative = Alternative(
                    name=str(row.get("name", "")),
                    description=str(row.get("description", ""))
                )
                
                # 加载属性
                for col in df.columns:
                    if col not in ["name", "description", "id"]:
                        try:
                            value = float(row[col])
                            alternative.attributes[col] = value
                        except (ValueError, TypeError):
                            alternative.attributes[col] = str(row[col])
                
                alternatives.append(alternative)
        
        except Exception as e:
            self.logger.error(f"解析备选方案失败: {e}")
        
        return alternatives
    
    def load_expert_judgments_from_excel(self, file_path: Union[str, Path]) -> Dict[str, Dict[str, Any]]:
        """从Excel文件加载专家判断数据"""
        try:
            # 读取多个工作表
            excel_file = pd.ExcelFile(file_path)
            expert_data = {}
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                expert_data[sheet_name] = df.to_dict('records')
            
            return expert_data
        
        except Exception as e:
            self.logger.error(f"加载专家判断数据失败: {e}")
            return {}
    
    def create_fuzzy_numbers_from_expert_data(self, expert_data: Dict[str, List[float]]) -> Dict[str, FuzzyNumber]:
        """从专家数据创建模糊数"""
        fuzzy_numbers = {}
        
        for indicator_id, values in expert_data.items():
            if len(values) >= 3:
                # 使用最小值、平均值、最大值创建三角模糊数
                min_val = min(values)
                max_val = max(values)
                avg_val = np.mean(values)
                
                fuzzy_numbers[indicator_id] = FuzzyNumber(parameters=[min_val, avg_val, max_val])
            elif len(values) == 2:
                # 创建区间模糊数
                fuzzy_numbers[indicator_id] = FuzzyNumber(
                    fuzzy_type="INTERVAL",
                    parameters=[min(values), max(values)]
                )
            elif len(values) == 1:
                # 创建确定值（退化的三角模糊数）
                val = values[0]
                fuzzy_numbers[indicator_id] = FuzzyNumber(parameters=[val, val, val])
        
        return fuzzy_numbers
    
    def save_json(self, data: Dict[str, Any], file_path: Union[str, Path]) -> bool:
        """保存数据到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"保存JSON文件失败 {file_path}: {e}")
            return False
    
    def save_yaml(self, data: Dict[str, Any], file_path: Union[str, Path]) -> bool:
        """保存数据到YAML文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            return True
        except Exception as e:
            self.logger.error(f"保存YAML文件失败 {file_path}: {e}")
            return False
    
    def validate_data_format(self, data: Dict[str, Any], schema: Dict[str, Any]) -> List[str]:
        """验证数据格式"""
        errors = []
        
        def validate_field(field_path: str, value: Any, expected_type: type, required: bool = True):
            if required and value is None:
                errors.append(f"必填字段 {field_path} 缺失")
            elif value is not None and not isinstance(value, expected_type):
                errors.append(f"字段 {field_path} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}")
        
        # 基础字段验证
        for field, config in schema.items():
            field_value = data.get(field)
            validate_field(field, field_value, config["type"], config.get("required", True))
        
        return errors
    
    def create_sample_data(self) -> Dict[str, Any]:
        """创建示例数据"""
        return {
            "task_hierarchy": {
                "root_task_id": "mission_001",
                "tasks": {
                    "mission_001": {
                        "id": "mission_001",
                        "name": "态势分析任务",
                        "description": "分析当前态势并生成报告",
                        "task_type": "MISSION_TASK",
                        "parent_id": None,
                        "children_ids": ["zz_001", "zz_002"],
                        "level": 0,
                        "executor_type": "HUMAN_MACHINE"
                    },
                    "zz_001": {
                        "id": "zz_001",
                        "name": "数据收集",
                        "description": "收集相关数据",
                        "task_type": "ZZ_TASK",
                        "parent_id": "mission_001",
                        "children_ids": [],
                        "level": 1,
                        "executor_type": "MACHINE"
                    },
                    "zz_002": {
                        "id": "zz_002",
                        "name": "数据分析",
                        "description": "分析收集的数据",
                        "task_type": "ZZ_TASK",
                        "parent_id": "mission_001",
                        "children_ids": [],
                        "level": 1,
                        "executor_type": "HUMAN"
                    }
                }
            },
            "evaluation_scheme": {
                "id": "scheme_001",
                "name": "默认评估方案",
                "description": "基础评估方案",
                "indicators": {
                    "workload": {
                        "id": "workload",
                        "name": "工作负荷",
                        "description": "操作者工作负荷",
                        "indicator_type": "WORKLOAD",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "is_benefit": False,
                        "unit": "标准化值"
                    },
                    "efficiency": {
                        "id": "efficiency",
                        "name": "效率",
                        "description": "任务执行效率",
                        "indicator_type": "EFFICIENCY",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "is_benefit": True,
                        "unit": "标准化值"
                    }
                },
                "weights": {
                    "workload": {
                        "indicator_id": "workload",
                        "weight": 0.4,
                        "confidence": 1.0,
                        "source": "专家评分"
                    },
                    "efficiency": {
                        "indicator_id": "efficiency",
                        "weight": 0.6,
                        "confidence": 1.0,
                        "source": "专家评分"
                    }
                }
            },
            "alternatives": [
                {
                    "name": "方案A",
                    "description": "人工主导方案",
                    "attributes": {
                        "workload": 0.7,
                        "efficiency": 0.6,
                        "cost": 100000
                    }
                },
                {
                    "name": "方案B",
                    "description": "机器主导方案",
                    "attributes": {
                        "workload": 0.3,
                        "efficiency": 0.8,
                        "cost": 200000
                    }
                }
            ]
        }
