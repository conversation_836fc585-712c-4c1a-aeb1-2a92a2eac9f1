{"meta": {"format": 3, "version": "7.10.2", "timestamp": "2025-09-08T07:20:29.473644", "branch_coverage": false, "show_contexts": false}, "files": {"src\\__init__.py": {"executed_lines": [4, 5, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 5, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 5, 6], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\__init__.py": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 12, 13, 14, 15, 17, 18], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\analysis\\__init__.py": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\analysis\\situation_awareness.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 69, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 93, 110, 111, 113, 141, 169, 196, 268, 315, 334, 356, 368, 385, 398, 440, 456, 460, 485, 505, 528, 542, 559, 574, 598], "summary": {"covered_lines": 79, "num_statements": 330, "percent_covered": 23.939393939393938, "percent_covered_display": "24", "missing_lines": 251, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 67, 71, 95, 114, 117, 118, 121, 130, 138, 139, 143, 144, 145, 148, 151, 153, 154, 156, 159, 160, 162, 163, 165, 166, 167, 171, 172, 173, 175, 178, 180, 181, 183, 186, 187, 189, 190, 192, 193, 194, 198, 200, 202, 203, 204, 206, 207, 208, 209, 210, 211, 213, 216, 217, 220, 221, 222, 223, 225, 228, 233, 242, 245, 248, 251, 254, 257, 258, 260, 261, 263, 264, 265, 266, 270, 277, 278, 279, 280, 283, 284, 285, 286, 287, 293, 300, 303, 306, 308, 310, 311, 312, 313, 317, 318, 320, 322, 336, 337, 338, 339, 340, 341, 344, 345, 346, 349, 350, 353, 354, 358, 365, 366, 371, 372, 373, 376, 377, 378, 379, 380, 381, 383, 387, 395, 396, 401, 402, 404, 407, 408, 409, 410, 413, 414, 415, 416, 417, 419, 420, 423, 424, 425, 426, 429, 430, 431, 432, 433, 434, 435, 436, 438, 445, 446, 448, 449, 450, 451, 452, 454, 458, 462, 464, 465, 466, 467, 468, 469, 470, 471, 472, 475, 476, 478, 479, 480, 481, 483, 487, 488, 490, 491, 494, 495, 496, 497, 498, 499, 501, 503, 507, 509, 510, 511, 512, 513, 516, 518, 519, 526, 531, 533, 534, 535, 536, 537, 538, 540, 544, 546, 547, 548, 549, 550, 557, 561, 562, 565, 566, 567, 568, 569, 570, 572, 576, 583, 585, 586, 589, 590, 593, 594, 596, 600, 601, 603, 604, 607, 609, 610, 611, 612, 613, 614, 616, 618, 620], "excluded_lines": [], "functions": {"SituationEntity.distance_to": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 67], "excluded_lines": []}, "SituationEntity.predict_position": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [71], "excluded_lines": []}, "SituationAssessment.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [95], "excluded_lines": []}, "SituationAwarenessEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [114, 117, 118, 121, 130, 138, 139], "excluded_lines": []}, "SituationAwarenessEngine.ingest_sensor_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [143, 144, 145, 148, 151, 153, 154, 156, 159, 160, 162, 163, 165, 166, 167], "excluded_lines": []}, "SituationAwarenessEngine.ingest_intelligence_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [171, 172, 173, 175, 178, 180, 181, 183, 186, 187, 189, 190, 192, 193, 194], "excluded_lines": []}, "SituationAwarenessEngine.perform_situation_assessment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [198, 200, 202, 203, 204, 206, 207, 208, 209, 210, 211, 213, 216, 217, 220, 221, 222, 223, 225, 228, 233, 242, 245, 248, 251, 254, 257, 258, 260, 261, 263, 264, 265, 266], "excluded_lines": []}, "SituationAwarenessEngine.predict_situation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [270, 277, 278, 279, 280, 283, 284, 285, 286, 287, 293, 300, 303, 306, 308, 310, 311, 312, 313], "excluded_lines": []}, "SituationAwarenessEngine.get_situation_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [317, 318, 320, 322], "excluded_lines": []}, "SituationAwarenessEngine._update_entity_from_sensor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [336, 337, 338, 339, 340, 341, 344, 345, 346, 349, 350, 353, 354], "excluded_lines": []}, "SituationAwarenessEngine._create_entity_from_sensor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [358, 365, 366], "excluded_lines": []}, "SituationAwarenessEngine._enhance_entity_with_intelligence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [371, 372, 373, 376, 377, 378, 379, 380, 381, 383], "excluded_lines": []}, "SituationAwarenessEngine._create_entity_from_intelligence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [387, 395, 396], "excluded_lines": []}, "SituationAwarenessEngine._assess_threat_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [401, 402, 404, 407, 408, 409, 410, 413, 414, 415, 416, 417, 419, 420, 423, 424, 425, 426, 429, 430, 431, 432, 433, 434, 435, 436, 438], "excluded_lines": []}, "SituationAwarenessEngine._generate_situation_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [445, 446, 448, 449, 450, 451, 452, 454], "excluded_lines": []}, "SituationAwarenessEngine._generate_predictions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [458], "excluded_lines": []}, "SituationAwarenessEngine._generate_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [462, 464, 465, 466, 467, 468, 469, 470, 471, 472, 475, 476, 478, 479, 480, 481, 483], "excluded_lines": []}, "SituationAwarenessEngine._calculate_assessment_confidence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [487, 488, 490, 491, 494, 495, 496, 497, 498, 499, 501, 503], "excluded_lines": []}, "SituationAwarenessEngine._detect_potential_conflicts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [507, 509, 510, 511, 512, 513, 516, 518, 519, 526], "excluded_lines": []}, "SituationAwarenessEngine._predict_threat_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [531, 533, 534, 535, 536, 537, 538, 540], "excluded_lines": []}, "SituationAwarenessEngine._identify_key_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [544, 546, 547, 548, 549, 550, 557], "excluded_lines": []}, "SituationAwarenessEngine._calculate_prediction_confidence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [561, 562, 565, 566, 567, 568, 569, 570, 572], "excluded_lines": []}, "SituationAwarenessEngine._get_entity_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [576, 583, 585, 586, 589, 590, 593, 594, 596], "excluded_lines": []}, "SituationAwarenessEngine._analyze_threat_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [600, 601, 603, 604, 607, 609, 610, 611, 612, 613, 614, 616, 618, 620], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 69, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 93, 110, 111, 113, 141, 169, 196, 268, 315, 334, 356, 368, 385, 398, 440, 456, 460, 485, 505, 528, 542, 559, 574, 598], "summary": {"covered_lines": 79, "num_statements": 79, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ThreatLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SituationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfidenceLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SituationEntity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 67, 71], "excluded_lines": []}, "SituationAssessment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [95], "excluded_lines": []}, "SituationAwarenessEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 245, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 245, "excluded_lines": 0}, "missing_lines": [114, 117, 118, 121, 130, 138, 139, 143, 144, 145, 148, 151, 153, 154, 156, 159, 160, 162, 163, 165, 166, 167, 171, 172, 173, 175, 178, 180, 181, 183, 186, 187, 189, 190, 192, 193, 194, 198, 200, 202, 203, 204, 206, 207, 208, 209, 210, 211, 213, 216, 217, 220, 221, 222, 223, 225, 228, 233, 242, 245, 248, 251, 254, 257, 258, 260, 261, 263, 264, 265, 266, 270, 277, 278, 279, 280, 283, 284, 285, 286, 287, 293, 300, 303, 306, 308, 310, 311, 312, 313, 317, 318, 320, 322, 336, 337, 338, 339, 340, 341, 344, 345, 346, 349, 350, 353, 354, 358, 365, 366, 371, 372, 373, 376, 377, 378, 379, 380, 381, 383, 387, 395, 396, 401, 402, 404, 407, 408, 409, 410, 413, 414, 415, 416, 417, 419, 420, 423, 424, 425, 426, 429, 430, 431, 432, 433, 434, 435, 436, 438, 445, 446, 448, 449, 450, 451, 452, 454, 458, 462, 464, 465, 466, 467, 468, 469, 470, 471, 472, 475, 476, 478, 479, 480, 481, 483, 487, 488, 490, 491, 494, 495, 496, 497, 498, 499, 501, 503, 507, 509, 510, 511, 512, 513, 516, 518, 519, 526, 531, 533, 534, 535, 536, 537, 538, 540, 544, 546, 547, 548, 549, 550, 557, 561, 562, 565, 566, 567, 568, 569, 570, 572, 576, 583, 585, 586, 589, 590, 593, 594, 596, 600, 601, 603, 604, 607, 609, 610, 611, 612, 613, 614, 616, 618, 620], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 69, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 93, 110, 111, 113, 141, 169, 196, 268, 315, 334, 356, 368, 385, 398, 440, 456, 460, 485, 505, 528, 542, 559, 574, 598], "summary": {"covered_lines": 79, "num_statements": 79, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\communication\\__init__.py": {"executed_lines": [1, 12, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 12, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 12, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\communication\\military_comms.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 81, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 130, 131, 133, 161, 178, 228, 273, 303, 328, 350, 368, 376, 398, 418, 425, 435, 449, 473, 484, 494, 505], "summary": {"covered_lines": 91, "num_statements": 270, "percent_covered": 33.7037037037037, "percent_covered_display": "34", "missing_lines": 179, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 83, 123, 124, 127, 134, 135, 138, 141, 142, 145, 154, 157, 158, 163, 164, 165, 166, 168, 169, 171, 172, 174, 175, 176, 180, 182, 183, 186, 187, 190, 191, 192, 194, 195, 196, 199, 200, 201, 202, 203, 206, 209, 212, 213, 216, 219, 221, 222, 224, 225, 226, 230, 232, 233, 234, 235, 237, 238, 239, 242, 245, 246, 248, 249, 250, 253, 254, 255, 258, 259, 260, 262, 265, 267, 269, 270, 271, 275, 276, 277, 279, 282, 283, 286, 287, 288, 291, 292, 293, 294, 296, 297, 299, 300, 301, 306, 308, 310, 324, 326, 332, 353, 370, 371, 373, 374, 378, 379, 381, 382, 384, 400, 403, 404, 405, 406, 408, 409, 412, 413, 414, 416, 420, 421, 423, 427, 429, 430, 431, 432, 433, 437, 439, 440, 443, 444, 445, 447, 451, 452, 454, 455, 456, 458, 459, 460, 463, 464, 465, 466, 468, 470, 471, 475, 476, 477, 478, 480, 481, 482, 486, 488, 489, 491, 492, 496, 497, 498, 499, 501, 502, 503, 507, 510, 511, 513], "excluded_lines": [], "functions": {"MilitaryMessage.is_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [77, 78, 79], "excluded_lines": []}, "MilitaryMessage.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [83], "excluded_lines": []}, "MilitaryUnit.is_online": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [123, 124, 127], "excluded_lines": []}, "MilitaryCommunicationSystem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [134, 135, 138, 141, 142, 145, 154, 157, 158], "excluded_lines": []}, "MilitaryCommunicationSystem.register_unit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 168, 169, 171, 172, 174, 175, 176], "excluded_lines": []}, "MilitaryCommunicationSystem.send_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [180, 182, 183, 186, 187, 190, 191, 192, 194, 195, 196, 199, 200, 201, 202, 203, 206, 209, 212, 213, 216, 219, 221, 222, 224, 225, 226], "excluded_lines": []}, "MilitaryCommunicationSystem.receive_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [230, 232, 233, 234, 235, 237, 238, 239, 242, 245, 246, 248, 249, 250, 253, 254, 255, 258, 259, 260, 262, 265, 267, 269, 270, 271], "excluded_lines": []}, "MilitaryCommunicationSystem.acknowledge_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [275, 276, 277, 279, 282, 283, 286, 287, 288, 291, 292, 293, 294, 296, 297, 299, 300, 301], "excluded_lines": []}, "MilitaryCommunicationSystem.broadcast_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [306, 308, 310, 324, 326], "excluded_lines": []}, "MilitaryCommunicationSystem.create_command_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [332], "excluded_lines": []}, "MilitaryCommunicationSystem.create_status_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [353], "excluded_lines": []}, "MilitaryCommunicationSystem.register_message_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [370, 371, 373, 374], "excluded_lines": []}, "MilitaryCommunicationSystem.get_unit_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [378, 379, 381, 382, 384], "excluded_lines": []}, "MilitaryCommunicationSystem.get_communication_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [400, 403, 404, 405, 406, 408, 409, 412, 413, 414, 416], "excluded_lines": []}, "MilitaryCommunicationSystem._check_security_clearance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [420, 421, 423], "excluded_lines": []}, "MilitaryCommunicationSystem._trigger_message_handlers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [427, 429, 430, 431, 432, 433], "excluded_lines": []}, "MilitaryCommunicationSystem.start_background_tasks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [437, 439, 440, 443, 444, 445, 447], "excluded_lines": []}, "MilitaryCommunicationSystem.cleanup_expired_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [451, 452, 454, 455, 456, 458, 459, 460, 463, 464, 465, 466, 468, 470, 471], "excluded_lines": []}, "MilitaryCommunicationSystem._cleanup_expired_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [475, 476, 477, 478, 480, 481, 482], "excluded_lines": []}, "MilitaryCommunicationSystem.update_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [486, 488, 489, 491, 492], "excluded_lines": []}, "MilitaryCommunicationSystem._update_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [496, 497, 498, 499, 501, 502, 503], "excluded_lines": []}, "MilitaryCommunicationSystem.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [507, 510, 511, 513], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 81, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 130, 131, 133, 161, 178, 228, 273, 303, 328, 350, 368, 376, 398, 418, 425, 435, 449, 473, 484, 494, 505], "summary": {"covered_lines": 91, "num_statements": 91, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MessageType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MessagePriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UnitType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MilitaryMessage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 83], "excluded_lines": []}, "MilitaryUnit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [123, 124, 127], "excluded_lines": []}, "MilitaryCommunicationSystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 172, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 172, "excluded_lines": 0}, "missing_lines": [134, 135, 138, 141, 142, 145, 154, 157, 158, 163, 164, 165, 166, 168, 169, 171, 172, 174, 175, 176, 180, 182, 183, 186, 187, 190, 191, 192, 194, 195, 196, 199, 200, 201, 202, 203, 206, 209, 212, 213, 216, 219, 221, 222, 224, 225, 226, 230, 232, 233, 234, 235, 237, 238, 239, 242, 245, 246, 248, 249, 250, 253, 254, 255, 258, 259, 260, 262, 265, 267, 269, 270, 271, 275, 276, 277, 279, 282, 283, 286, 287, 288, 291, 292, 293, 294, 296, 297, 299, 300, 301, 306, 308, 310, 324, 326, 332, 353, 370, 371, 373, 374, 378, 379, 381, 382, 384, 400, 403, 404, 405, 406, 408, 409, 412, 413, 414, 416, 420, 421, 423, 427, 429, 430, 431, 432, 433, 437, 439, 440, 443, 444, 445, 447, 451, 452, 454, 455, 456, 458, 459, 460, 463, 464, 465, 466, 468, 470, 471, 475, 476, 477, 478, 480, 481, 482, 486, 488, 489, 491, 492, 496, 497, 498, 499, 501, 502, 503, 507, 510, 511, 513], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 81, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 130, 131, 133, 161, 178, 228, 273, 303, 328, 350, 368, 376, 398, 418, 425, 435, 449, 473, 484, 494, 505], "summary": {"covered_lines": 91, "num_statements": 91, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\core\\__init__.py": {"executed_lines": [1, 12, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 12, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 12, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\core\\system_manager.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 52, 53, 54, 57, 58, 59, 60, 63, 64, 65, 68, 69, 70, 73, 84, 85, 86, 88, 100, 101, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 142, 143, 145, 178, 216, 241, 269, 277, 300, 307, 317, 342, 354, 363, 380, 416, 443, 482, 504, 529, 580, 588, 599, 603, 616, 640], "summary": {"covered_lines": 87, "num_statements": 361, "percent_covered": 24.099722991689752, "percent_covered_display": "24", "missing_lines": 274, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 93, 94, 95, 97, 98, 104, 105, 107, 120, 121, 122, 125, 127, 146, 149, 150, 153, 154, 157, 158, 161, 162, 165, 168, 176, 180, 181, 182, 185, 188, 190, 191, 192, 193, 194, 195, 196, 199, 202, 204, 205, 206, 208, 209, 211, 212, 213, 214, 218, 219, 220, 223, 225, 226, 227, 230, 232, 233, 234, 236, 237, 238, 239, 243, 244, 245, 247, 271, 272, 273, 274, 275, 279, 280, 283, 286, 288, 289, 290, 292, 294, 296, 297, 298, 302, 303, 304, 305, 309, 310, 311, 312, 313, 314, 315, 319, 321, 322, 325, 326, 327, 330, 333, 335, 336, 338, 339, 340, 344, 345, 346, 347, 348, 349, 350, 352, 356, 357, 358, 359, 360, 361, 366, 367, 368, 371, 383, 414, 418, 419, 420, 422, 423, 424, 426, 427, 429, 430, 431, 433, 434, 435, 437, 438, 439, 441, 445, 446, 447, 448, 450, 451, 454, 455, 456, 457, 458, 461, 462, 463, 464, 465, 467, 468, 469, 470, 472, 473, 475, 476, 477, 478, 479, 480, 484, 485, 486, 488, 490, 491, 493, 494, 495, 497, 498, 500, 501, 502, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 522, 523, 525, 526, 527, 531, 533, 537, 538, 540, 542, 549, 553, 554, 556, 557, 564, 565, 566, 567, 568, 569, 575, 577, 578, 583, 584, 586, 590, 591, 594, 595, 596, 597, 601, 605, 612, 613, 614, 618, 620, 623, 624, 627, 628, 629, 630, 631, 632, 633, 635, 637, 638, 642, 647, 648, 649, 650, 651, 654, 655, 656, 657, 658, 660, 662, 663, 664], "excluded_lines": [], "functions": {"SystemConfig.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 93, 94, 95, 97, 98], "excluded_lines": []}, "SystemConfig.from_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [104, 105, 107, 120, 121, 122, 125, 127], "excluded_lines": []}, "HMDMSystemManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [146, 149, 150, 153, 154, 157, 158, 161, 162, 165, 168, 176], "excluded_lines": []}, "HMDMSystemManager.start_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [180, 181, 182, 185, 188, 190, 191, 192, 193, 194, 195, 196, 199, 202, 204, 205, 206, 208, 209, 211, 212, 213, 214], "excluded_lines": []}, "HMDMSystemManager.stop_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [218, 219, 220, 223, 225, 226, 227, 230, 232, 233, 234, 236, 237, 238, 239], "excluded_lines": []}, "HMDMSystemManager.get_system_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [243, 244, 245, 247], "excluded_lines": []}, "HMDMSystemManager.get_module": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [271, 272, 273, 274, 275], "excluded_lines": []}, "HMDMSystemManager.restart_module": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [279, 280, 283, 286, 288, 289, 290, 292, 294, 296, 297, 298], "excluded_lines": []}, "HMDMSystemManager.register_event_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [302, 303, 304, 305], "excluded_lines": []}, "HMDMSystemManager.emit_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [309, 310, 311, 312, 313, 314, 315], "excluded_lines": []}, "HMDMSystemManager.update_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [319, 321, 322, 325, 326, 327, 330, 333, 335, 336, 338, 339, 340], "excluded_lines": []}, "HMDMSystemManager._load_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [344, 345, 346, 347, 348, 349, 350, 352], "excluded_lines": []}, "HMDMSystemManager._save_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [356, 357, 358, 359, 360, 361], "excluded_lines": []}, "HMDMSystemManager._setup_logging": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [366, 367, 368, 371], "excluded_lines": []}, "HMDMSystemManager._init_module_registry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [383, 414], "excluded_lines": []}, "HMDMSystemManager._calculate_startup_order": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [418, 419, 420, 422, 437, 438, 439, 441], "excluded_lines": []}, "HMDMSystemManager._calculate_startup_order.visit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [423, 424, 426, 427, 429, 430, 431, 433, 434, 435], "excluded_lines": []}, "HMDMSystemManager._start_module": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [445, 446, 447, 448, 450, 451, 454, 455, 456, 457, 458, 461, 462, 463, 464, 465, 467, 468, 469, 470, 472, 473, 475, 476, 477, 478, 479, 480], "excluded_lines": []}, "HMDMSystemManager._stop_module": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [484, 485, 486, 488, 490, 491, 493, 494, 495, 497, 498, 500, 501, 502], "excluded_lines": []}, "HMDMSystemManager._create_module_instance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 522, 523, 525, 526, 527], "excluded_lines": []}, "HMDMSystemManager._setup_data_channels": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [531, 533, 537, 538, 540, 542, 549, 553, 554, 556, 557, 564, 565, 566, 567, 568, 569, 575, 577, 578], "excluded_lines": []}, "HMDMSystemManager._start_event_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [583, 584, 586], "excluded_lines": []}, "HMDMSystemManager._handle_module_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [590, 591, 594, 595, 596, 597], "excluded_lines": []}, "HMDMSystemManager._handle_system_warning": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [601], "excluded_lines": []}, "HMDMSystemManager._create_directories": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [605, 612, 613, 614], "excluded_lines": []}, "HMDMSystemManager._cleanup_resources": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [618, 620, 623, 624, 627, 628, 629, 630, 631, 632, 633, 635, 637, 638], "excluded_lines": []}, "HMDMSystemManager._validate_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [642, 647, 648, 649, 650, 651, 654, 655, 656, 657, 658, 660, 662, 663, 664], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 52, 53, 54, 57, 58, 59, 60, 63, 64, 65, 68, 69, 70, 73, 84, 85, 86, 88, 100, 101, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 142, 143, 145, 178, 216, 241, 269, 277, 300, 307, 317, 342, 354, 363, 380, 416, 443, 482, 504, 529, 580, 588, 599, 603, 616, 640], "summary": {"covered_lines": 87, "num_statements": 87, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SystemStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModuleStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SystemConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 93, 94, 95, 97, 98, 104, 105, 107, 120, 121, 122, 125, 127], "excluded_lines": []}, "ModuleInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "HMDMSystemManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 258, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 258, "excluded_lines": 0}, "missing_lines": [146, 149, 150, 153, 154, 157, 158, 161, 162, 165, 168, 176, 180, 181, 182, 185, 188, 190, 191, 192, 193, 194, 195, 196, 199, 202, 204, 205, 206, 208, 209, 211, 212, 213, 214, 218, 219, 220, 223, 225, 226, 227, 230, 232, 233, 234, 236, 237, 238, 239, 243, 244, 245, 247, 271, 272, 273, 274, 275, 279, 280, 283, 286, 288, 289, 290, 292, 294, 296, 297, 298, 302, 303, 304, 305, 309, 310, 311, 312, 313, 314, 315, 319, 321, 322, 325, 326, 327, 330, 333, 335, 336, 338, 339, 340, 344, 345, 346, 347, 348, 349, 350, 352, 356, 357, 358, 359, 360, 361, 366, 367, 368, 371, 383, 414, 418, 419, 420, 422, 423, 424, 426, 427, 429, 430, 431, 433, 434, 435, 437, 438, 439, 441, 445, 446, 447, 448, 450, 451, 454, 455, 456, 457, 458, 461, 462, 463, 464, 465, 467, 468, 469, 470, 472, 473, 475, 476, 477, 478, 479, 480, 484, 485, 486, 488, 490, 491, 493, 494, 495, 497, 498, 500, 501, 502, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 522, 523, 525, 526, 527, 531, 533, 537, 538, 540, 542, 549, 553, 554, 556, 557, 564, 565, 566, 567, 568, 569, 575, 577, 578, 583, 584, 586, 590, 591, 594, 595, 596, 597, 601, 605, 612, 613, 614, 618, 620, 623, 624, 627, 628, 629, 630, 631, 632, 633, 635, 637, 638, 642, 647, 648, 649, 650, 651, 654, 655, 656, 657, 658, 660, 662, 663, 664], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 52, 53, 54, 57, 58, 59, 60, 63, 64, 65, 68, 69, 70, 73, 84, 85, 86, 88, 100, 101, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 142, 143, 145, 178, 216, 241, 269, 277, 300, 307, 317, 342, 354, 363, 380, 416, 443, 482, 504, 529, 580, 588, 599, 603, 616, 640], "summary": {"covered_lines": 87, "num_statements": 87, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\decision\\__init__.py": {"executed_lines": [1, 13, 14, 25, 26, 36], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 13, 14, 25, 26, 36], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 13, 14, 25, 26, 36], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\decision\\decision_utils.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 15, 18, 40, 69, 98, 124, 158, 185, 231, 335], "summary": {"covered_lines": 17, "num_statements": 190, "percent_covered": 8.947368421052632, "percent_covered_display": "9", "missing_lines": 173, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 25, 26, 29, 32, 33, 36, 37, 42, 43, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 59, 61, 66, 71, 72, 75, 78, 79, 82, 83, 86, 87, 89, 90, 91, 94, 95, 100, 101, 104, 105, 107, 108, 109, 110, 113, 114, 116, 127, 129, 130, 133, 134, 137, 139, 140, 141, 142, 143, 145, 146, 155, 161, 162, 165, 167, 168, 171, 174, 177, 178, 180, 182, 189, 212, 213, 228, 235, 236, 239, 240, 242, 243, 246, 247, 248, 250, 252, 253, 254, 255, 256, 258, 260, 261, 262, 265, 266, 267, 268, 271, 272, 273, 274, 275, 278, 279, 280, 281, 283, 284, 285, 286, 287, 288, 290, 291, 292, 293, 294, 295, 298, 299, 300, 304, 305, 306, 307, 308, 309, 310, 312, 313, 314, 316, 317, 318, 319, 322, 323, 324, 327, 329, 330, 332, 340, 341, 343, 344, 354, 355, 357, 358, 360, 361, 362, 363, 364, 371, 372, 375, 376, 377, 378, 380, 381, 382, 383, 385, 387, 388], "excluded_lines": [], "functions": {"calculate_consistency_ratio": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 25, 26, 29, 32, 33, 36, 37], "excluded_lines": []}, "normalize_fuzzy_weights": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [42, 43, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 59, 61, 66], "excluded_lines": []}, "calculate_rank_correlation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [71, 72, 75, 78, 79, 82, 83, 86, 87, 89, 90, 91, 94, 95], "excluded_lines": []}, "calculate_decision_stability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [100, 101, 104, 105, 107, 108, 109, 110, 113, 114, 116], "excluded_lines": []}, "detect_rank_reversal": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [127, 129, 130, 133, 134, 137, 139, 140, 141, 142, 143, 145, 146, 155], "excluded_lines": []}, "calculate_decision_confidence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [161, 162, 165, 167, 168, 171, 174, 177, 178, 180, 182], "excluded_lines": []}, "generate_decision_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [189, 212, 213, 228], "excluded_lines": []}, "visualize_decision_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 68, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [235, 236, 239, 240, 242, 243, 246, 247, 248, 250, 252, 253, 254, 255, 256, 258, 260, 261, 262, 265, 266, 267, 268, 271, 272, 273, 274, 275, 278, 279, 280, 281, 283, 284, 285, 286, 287, 288, 290, 291, 292, 293, 294, 295, 298, 299, 300, 304, 305, 306, 307, 308, 309, 310, 312, 313, 314, 316, 317, 318, 319, 322, 323, 324, 327, 329, 330, 332], "excluded_lines": []}, "export_decision_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [340, 341, 343, 344, 354, 355, 357, 358, 360, 361, 362, 363, 364, 371, 372, 375, 376, 377, 378, 380, 381, 382, 383, 385, 387, 388], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 15, 18, 40, 69, 98, 124, 158, 185, 231, 335], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 15, 18, 40, 69, 98, 124, 158, 185, 231, 335], "summary": {"covered_lines": 17, "num_statements": 190, "percent_covered": 8.947368421052632, "percent_covered_display": "9", "missing_lines": 173, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 25, 26, 29, 32, 33, 36, 37, 42, 43, 46, 47, 48, 49, 52, 53, 54, 55, 56, 57, 59, 61, 66, 71, 72, 75, 78, 79, 82, 83, 86, 87, 89, 90, 91, 94, 95, 100, 101, 104, 105, 107, 108, 109, 110, 113, 114, 116, 127, 129, 130, 133, 134, 137, 139, 140, 141, 142, 143, 145, 146, 155, 161, 162, 165, 167, 168, 171, 174, 177, 178, 180, 182, 189, 212, 213, 228, 235, 236, 239, 240, 242, 243, 246, 247, 248, 250, 252, 253, 254, 255, 256, 258, 260, 261, 262, 265, 266, 267, 268, 271, 272, 273, 274, 275, 278, 279, 280, 281, 283, 284, 285, 286, 287, 288, 290, 291, 292, 293, 294, 295, 298, 299, 300, 304, 305, 306, 307, 308, 309, 310, 312, 313, 314, 316, 317, 318, 319, 322, 323, 324, 327, 329, 330, 332, 340, 341, 343, 344, 354, 355, 357, 358, 360, 361, 362, 363, 364, 371, 372, 375, 376, 377, 378, 380, 381, 382, 383, 385, 387, 388], "excluded_lines": []}}}, "src\\hmdm\\decision\\fuzzy_decision_engine.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 19, 20, 22, 25, 90, 106, 119, 163, 208, 254, 285, 320, 371], "summary": {"covered_lines": 19, "num_statements": 189, "percent_covered": 10.052910052910052, "percent_covered_display": "10", "missing_lines": 170, "excluded_lines": 0}, "missing_lines": [23, 37, 38, 43, 44, 45, 48, 51, 54, 57, 60, 65, 71, 72, 75, 76, 77, 78, 79, 81, 82, 88, 92, 93, 95, 96, 97, 98, 101, 102, 104, 109, 110, 111, 113, 114, 116, 117, 125, 126, 128, 130, 131, 133, 134, 135, 138, 139, 141, 143, 144, 146, 148, 149, 152, 153, 156, 157, 159, 161, 165, 166, 169, 170, 171, 174, 177, 178, 179, 181, 182, 183, 186, 189, 194, 195, 198, 199, 200, 204, 206, 211, 212, 215, 218, 221, 224, 225, 226, 227, 228, 231, 234, 236, 239, 244, 245, 247, 252, 256, 257, 258, 260, 262, 263, 264, 265, 267, 270, 271, 274, 276, 279, 281, 283, 287, 288, 289, 291, 293, 295, 297, 298, 299, 300, 303, 309, 312, 313, 314, 315, 316, 318, 322, 323, 325, 326, 329, 330, 333, 334, 335, 338, 340, 341, 344, 347, 348, 349, 351, 354, 357, 358, 360, 362, 369, 373, 375, 376, 377, 378, 380, 381, 382, 383, 385, 386, 387, 388, 390], "excluded_lines": [], "functions": {"FuzzyDecisionEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [23], "excluded_lines": []}, "FuzzyDecisionEngine.weighted_relative_deviation_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [37, 38, 43, 44, 45, 48, 51, 54, 57, 60, 65, 71, 72, 75, 76, 77, 78, 79, 81, 82, 88], "excluded_lines": []}, "FuzzyDecisionEngine._calculate_fuzzy_relation_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [92, 93, 95, 96, 97, 98, 101, 102, 104], "excluded_lines": []}, "FuzzyDecisionEngine._calculate_similarity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 113, 114, 116, 117], "excluded_lines": []}, "FuzzyDecisionEngine._calculate_relative_deviation_distances": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [125, 126, 128, 130, 131, 133, 134, 135, 138, 139, 141, 143, 144, 146, 148, 149, 152, 153, 156, 157, 159, 161], "excluded_lines": []}, "FuzzyDecisionEngine.topsis_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [165, 166, 169, 170, 171, 174, 177, 178, 179, 181, 182, 183, 186, 189, 194, 195, 198, 199, 200, 204, 206], "excluded_lines": []}, "FuzzyDecisionEngine.fuzzy_ahp_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [211, 212, 215, 218, 221, 224, 225, 226, 227, 228, 231, 234, 236, 239, 244, 245, 247, 252], "excluded_lines": []}, "FuzzyDecisionEngine._build_fuzzy_judgment_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [256, 257, 258, 260, 262, 263, 264, 265, 267, 270, 271, 274, 276, 279, 281, 283], "excluded_lines": []}, "FuzzyDecisionEngine._calculate_fuzzy_weights": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [287, 288, 289, 291, 293, 295, 297, 298, 299, 300, 303, 309, 312, 313, 314, 315, 316, 318], "excluded_lines": []}, "FuzzyDecisionEngine.sensitivity_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [322, 323, 325, 326, 329, 330, 333, 334, 335, 338, 340, 341, 344, 347, 348, 349, 351, 354, 357, 358, 360, 362, 369], "excluded_lines": []}, "FuzzyDecisionEngine.compare_methods": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [373, 375, 376, 377, 378, 380, 381, 382, 383, 385, 386, 387, 388, 390], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 19, 20, 22, 25, 90, 106, 119, 163, 208, 254, 285, 320, 371], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"FuzzyDecisionEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 170, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 170, "excluded_lines": 0}, "missing_lines": [23, 37, 38, 43, 44, 45, 48, 51, 54, 57, 60, 65, 71, 72, 75, 76, 77, 78, 79, 81, 82, 88, 92, 93, 95, 96, 97, 98, 101, 102, 104, 109, 110, 111, 113, 114, 116, 117, 125, 126, 128, 130, 131, 133, 134, 135, 138, 139, 141, 143, 144, 146, 148, 149, 152, 153, 156, 157, 159, 161, 165, 166, 169, 170, 171, 174, 177, 178, 179, 181, 182, 183, 186, 189, 194, 195, 198, 199, 200, 204, 206, 211, 212, 215, 218, 221, 224, 225, 226, 227, 228, 231, 234, 236, 239, 244, 245, 247, 252, 256, 257, 258, 260, 262, 263, 264, 265, 267, 270, 271, 274, 276, 279, 281, 283, 287, 288, 289, 291, 293, 295, 297, 298, 299, 300, 303, 309, 312, 313, 314, 315, 316, 318, 322, 323, 325, 326, 329, 330, 333, 334, 335, 338, 340, 341, 344, 347, 348, 349, 351, 354, 357, 358, 360, 362, 369, 373, 375, 376, 377, 378, 380, 381, 382, 383, 385, 386, 387, 388, 390], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 19, 20, 22, 25, 90, 106, 119, 163, 208, 254, 285, 320, 371], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\decision\\military_decision_support.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 86, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 115, 133, 134, 136, 160, 192, 218, 295, 319, 376, 408, 439, 470, 485, 517, 533, 558, 570, 585, 603, 622, 630, 638, 646], "summary": {"covered_lines": 97, "num_statements": 251, "percent_covered": 38.645418326693225, "percent_covered_display": "39", "missing_lines": 154, "excluded_lines": 0}, "missing_lines": [70, 88, 89, 90, 92, 93, 94, 96, 117, 118, 120, 121, 123, 124, 126, 127, 128, 130, 137, 140, 143, 151, 166, 167, 170, 171, 172, 173, 174, 175, 176, 177, 180, 181, 182, 183, 185, 186, 188, 189, 190, 198, 199, 207, 209, 210, 211, 214, 216, 225, 227, 229, 230, 237, 240, 241, 242, 243, 244, 246, 248, 249, 252, 255, 283, 285, 287, 288, 289, 300, 302, 304, 312, 313, 314, 323, 326, 328, 339, 350, 352, 363, 374, 380, 383, 384, 395, 406, 412, 415, 426, 437, 442, 445, 457, 468, 475, 476, 477, 479, 480, 481, 483, 490, 493, 494, 497, 498, 501, 502, 503, 506, 507, 508, 511, 512, 513, 515, 519, 522, 530, 531, 538, 539, 541, 542, 543, 545, 546, 547, 549, 551, 562, 563, 564, 565, 566, 568, 573, 580, 581, 583, 588, 594, 595, 601, 607, 616, 619, 620, 624, 632, 640, 648], "excluded_lines": [], "functions": {"MilitaryResource.effective_quantity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [70], "excluded_lines": []}, "MilitaryObjective.is_achievable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [88, 89, 90, 92, 93, 94, 96], "excluded_lines": []}, "MilitaryOption.calculate_effectiveness": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [117, 118, 120, 121, 123, 124, 126, 127, 128, 130], "excluded_lines": []}, "MilitaryDecisionSupport.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [137, 140, 143, 151], "excluded_lines": []}, "MilitaryDecisionSupport.generate_decision_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [166, 167, 170, 171, 172, 173, 174, 175, 176, 177, 180, 181, 182, 183, 185, 186, 188, 189, 190], "excluded_lines": []}, "MilitaryDecisionSupport.evaluate_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [198, 199, 207, 209, 210, 211, 214, 216], "excluded_lines": []}, "MilitaryDecisionSupport.recommend_decision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [225, 227, 229, 230, 237, 240, 241, 242, 243, 244, 246, 248, 249, 252, 255, 283, 285, 287, 288, 289], "excluded_lines": []}, "MilitaryDecisionSupport.optimize_resource_allocation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [300, 302, 304, 312, 313, 314], "excluded_lines": []}, "MilitaryDecisionSupport._generate_tactical_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [323, 326, 328, 339, 350, 352, 363, 374], "excluded_lines": []}, "MilitaryDecisionSupport._generate_operational_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [380, 383, 384, 395, 406], "excluded_lines": []}, "MilitaryDecisionSupport._generate_emergency_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [412, 415, 426, 437], "excluded_lines": []}, "MilitaryDecisionSupport._generate_resource_allocation_options": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [442, 445, 457, 468], "excluded_lines": []}, "MilitaryDecisionSupport._evaluate_option_feasibility": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [475, 476, 477, 479, 480, 481, 483], "excluded_lines": []}, "MilitaryDecisionSupport._calculate_option_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [490, 493, 494, 497, 498, 501, 502, 503, 506, 507, 508, 511, 512, 513, 515], "excluded_lines": []}, "MilitaryDecisionSupport._calculate_option_risk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [519, 522, 530, 531], "excluded_lines": []}, "MilitaryDecisionSupport._solve_resource_allocation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [538, 539, 541, 542, 543, 545, 546, 547, 549, 551], "excluded_lines": []}, "MilitaryDecisionSupport._generate_decision_rationale": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [562, 563, 564, 565, 566, 568], "excluded_lines": []}, "MilitaryDecisionSupport._generate_implementation_guidance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [573, 580, 581, 583], "excluded_lines": []}, "MilitaryDecisionSupport._generate_risk_mitigation_measures": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [588, 594, 595, 601], "excluded_lines": []}, "MilitaryDecisionSupport._record_decision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [607, 616, 619, 620], "excluded_lines": []}, "MilitaryDecisionSupport._tactical_decision_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [624], "excluded_lines": []}, "MilitaryDecisionSupport._operational_decision_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [632], "excluded_lines": []}, "MilitaryDecisionSupport._strategic_decision_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [640], "excluded_lines": []}, "MilitaryDecisionSupport._emergency_decision_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [648], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 86, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 115, 133, 134, 136, 160, 192, 218, 295, 319, 376, 408, 439, 470, 485, 517, 533, 558, 570, 585, 603, 622, 630, 638, 646], "summary": {"covered_lines": 97, "num_statements": 97, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DecisionType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OperationalPhase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResourceType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MilitaryResource": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [70], "excluded_lines": []}, "MilitaryObjective": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [88, 89, 90, 92, 93, 94, 96], "excluded_lines": []}, "MilitaryOption": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [117, 118, 120, 121, 123, 124, 126, 127, 128, 130], "excluded_lines": []}, "MilitaryDecisionSupport": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 136, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 136, "excluded_lines": 0}, "missing_lines": [137, 140, 143, 151, 166, 167, 170, 171, 172, 173, 174, 175, 176, 177, 180, 181, 182, 183, 185, 186, 188, 189, 190, 198, 199, 207, 209, 210, 211, 214, 216, 225, 227, 229, 230, 237, 240, 241, 242, 243, 244, 246, 248, 249, 252, 255, 283, 285, 287, 288, 289, 300, 302, 304, 312, 313, 314, 323, 326, 328, 339, 350, 352, 363, 374, 380, 383, 384, 395, 406, 412, 415, 426, 437, 442, 445, 457, 468, 475, 476, 477, 479, 480, 481, 483, 490, 493, 494, 497, 498, 501, 502, 503, 506, 507, 508, 511, 512, 513, 515, 519, 522, 530, 531, 538, 539, 541, 542, 543, 545, 546, 547, 549, 551, 562, 563, 564, 565, 566, 568, 573, 580, 581, 583, 588, 594, 595, 601, 607, 616, 619, 620, 624, 632, 640, 648], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 86, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 115, 133, 134, 136, 160, 192, 218, 295, 319, 376, 408, 439, 470, 485, 517, 533, 558, 570, 585, 603, 622, 630, 638, 646], "summary": {"covered_lines": 97, "num_statements": 97, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\decision\\rapid_decision_engine.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 20, 21, 23, 29, 86, 126, 150, 165, 172, 195, 201, 230, 266, 273, 286, 292], "summary": {"covered_lines": 22, "num_statements": 131, "percent_covered": 16.793893129770993, "percent_covered_display": "17", "missing_lines": 109, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27, 40, 42, 43, 46, 47, 48, 49, 50, 52, 54, 57, 58, 61, 64, 65, 68, 71, 74, 77, 78, 80, 82, 83, 84, 99, 100, 103, 105, 107, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 130, 132, 133, 135, 138, 141, 142, 148, 152, 153, 155, 157, 158, 160, 161, 163, 168, 169, 170, 174, 175, 177, 178, 179, 180, 182, 183, 185, 187, 188, 189, 191, 193, 198, 199, 207, 208, 211, 225, 226, 228, 232, 233, 236, 239, 240, 241, 242, 243, 244, 245, 247, 261, 262, 264, 269, 270, 271, 277, 278, 279, 281, 288, 289, 290, 294], "excluded_lines": [], "functions": {"RapidDecisionEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27], "excluded_lines": []}, "RapidDecisionEngine.rapid_wrdm_decision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [40, 42, 43, 46, 47, 48, 49, 50, 52, 54, 57, 58, 61, 64, 65, 68, 71, 74, 77, 78, 80, 82, 83, 84], "excluded_lines": []}, "RapidDecisionEngine.parallel_rapid_decision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [99, 100, 103, 105, 107, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124], "excluded_lines": []}, "RapidDecisionEngine.precompute_decision_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [130, 132, 133, 135, 138, 141, 142, 148], "excluded_lines": []}, "RapidDecisionEngine._rapid_build_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [152, 153, 155, 157, 158, 160, 161, 163], "excluded_lines": []}, "RapidDecisionEngine._rapid_normalize": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [168, 169, 170], "excluded_lines": []}, "RapidDecisionEngine._rapid_calculate_weights": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [174, 175, 177, 178, 179, 180, 182, 183, 185, 187, 188, 189, 191, 193], "excluded_lines": []}, "RapidDecisionEngine._rapid_calculate_scores": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [198, 199], "excluded_lines": []}, "RapidDecisionEngine._generate_rapid_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [207, 208, 211, 225, 226, 228], "excluded_lines": []}, "RapidDecisionEngine._emergency_decision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [232, 233, 236, 239, 240, 241, 242, 243, 244, 245, 247, 261, 262, 264], "excluded_lines": []}, "RapidDecisionEngine._generate_cache_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [269, 270, 271], "excluded_lines": []}, "RapidDecisionEngine._create_template_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [277, 278, 279, 281], "excluded_lines": []}, "RapidDecisionEngine.clear_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [288, 289, 290], "excluded_lines": []}, "RapidDecisionEngine.get_cache_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [294], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 20, 21, 23, 29, 86, 126, 150, 165, 172, 195, 201, 230, 266, 273, 286, 292], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RapidDecisionEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 109, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 109, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27, 40, 42, 43, 46, 47, 48, 49, 50, 52, 54, 57, 58, 61, 64, 65, 68, 71, 74, 77, 78, 80, 82, 83, 84, 99, 100, 103, 105, 107, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 130, 132, 133, 135, 138, 141, 142, 148, 152, 153, 155, 157, 158, 160, 161, 163, 168, 169, 170, 174, 175, 177, 178, 179, 180, 182, 183, 185, 187, 188, 189, 191, 193, 198, 199, 207, 208, 211, 225, 226, 228, 232, 233, 236, 239, 240, 241, 242, 243, 244, 245, 247, 261, 262, 264, 269, 270, 271, 277, 278, 279, 281, 288, 289, 290, 294], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 20, 21, 23, 29, 86, 126, 150, 165, 172, 195, 201, 230, 266, 273, 286, 292], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\evaluation\\__init__.py": {"executed_lines": [1, 10, 11, 21], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 10, 11, 21], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 10, 11, 21], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\evaluation\\evaluation_utils.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 19, 55, 84, 156, 171, 186, 295], "summary": {"covered_lines": 14, "num_statements": 185, "percent_covered": 7.5675675675675675, "percent_covered_display": "8", "missing_lines": 171, "excluded_lines": 0}, "missing_lines": [22, 23, 25, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 42, 43, 44, 45, 48, 49, 50, 52, 58, 59, 61, 64, 65, 66, 69, 70, 72, 73, 81, 87, 88, 90, 98, 99, 110, 111, 112, 113, 114, 115, 117, 118, 126, 129, 130, 143, 144, 153, 158, 159, 161, 162, 164, 165, 167, 168, 173, 174, 176, 177, 179, 180, 182, 183, 190, 191, 194, 195, 197, 198, 201, 202, 203, 204, 206, 207, 208, 209, 210, 211, 214, 215, 216, 220, 223, 224, 225, 226, 228, 229, 230, 232, 233, 235, 236, 237, 240, 241, 242, 244, 245, 246, 247, 248, 249, 252, 253, 254, 255, 256, 257, 258, 261, 264, 265, 267, 268, 269, 270, 271, 273, 275, 276, 277, 279, 280, 282, 285, 287, 289, 290, 292, 300, 301, 303, 304, 305, 314, 315, 316, 317, 320, 321, 322, 328, 329, 330, 331, 332, 334, 335, 337, 339, 340, 343, 344, 345, 350, 351, 353, 355, 356, 357, 359, 360, 362, 369, 370], "excluded_lines": [], "functions": {"calculate_indicator_importance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [22, 23, 25, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 42, 43, 44, 45, 48, 49, 50, 52], "excluded_lines": []}, "detect_evaluation_outliers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [58, 59, 61, 64, 65, 66, 69, 70, 72, 73, 81], "excluded_lines": []}, "generate_evaluation_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [87, 88, 90, 98, 99, 110, 111, 112, 113, 114, 115, 117, 118, 126, 129, 130, 143, 144, 153], "excluded_lines": []}, "calculate_skewness": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [158, 159, 161, 162, 164, 165, 167, 168], "excluded_lines": []}, "calculate_kurtosis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [173, 174, 176, 177, 179, 180, 182, 183], "excluded_lines": []}, "visualize_evaluation_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 68, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [190, 191, 194, 195, 197, 198, 201, 202, 203, 204, 206, 207, 208, 209, 210, 211, 214, 215, 216, 220, 223, 224, 225, 226, 228, 229, 230, 232, 233, 235, 236, 237, 240, 241, 242, 244, 245, 246, 247, 248, 249, 252, 253, 254, 255, 256, 257, 258, 261, 264, 265, 267, 268, 269, 270, 271, 273, 275, 276, 277, 279, 280, 282, 285, 287, 289, 290, 292], "excluded_lines": []}, "export_evaluation_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [300, 301, 303, 304, 305, 314, 315, 316, 317, 320, 321, 322, 328, 329, 330, 331, 332, 334, 335, 337, 339, 340, 343, 344, 345, 350, 351, 353, 355, 356, 357, 359, 360, 362, 369, 370], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 19, 55, 84, 156, 171, 186, 295], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 19, 55, 84, 156, 171, 186, 295], "summary": {"covered_lines": 14, "num_statements": 185, "percent_covered": 7.5675675675675675, "percent_covered_display": "8", "missing_lines": 171, "excluded_lines": 0}, "missing_lines": [22, 23, 25, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 42, 43, 44, 45, 48, 49, 50, 52, 58, 59, 61, 64, 65, 66, 69, 70, 72, 73, 81, 87, 88, 90, 98, 99, 110, 111, 112, 113, 114, 115, 117, 118, 126, 129, 130, 143, 144, 153, 158, 159, 161, 162, 164, 165, 167, 168, 173, 174, 176, 177, 179, 180, 182, 183, 190, 191, 194, 195, 197, 198, 201, 202, 203, 204, 206, 207, 208, 209, 210, 211, 214, 215, 216, 220, 223, 224, 225, 226, 228, 229, 230, 232, 233, 235, 236, 237, 240, 241, 242, 244, 245, 246, 247, 248, 249, 252, 253, 254, 255, 256, 257, 258, 261, 264, 265, 267, 268, 269, 270, 271, 273, 275, 276, 277, 279, 280, 282, 285, 287, 289, 290, 292, 300, 301, 303, 304, 305, 314, 315, 316, 317, 320, 321, 322, 328, 329, 330, 331, 332, 334, 335, 337, 339, 340, 343, 344, 345, 350, 351, 353, 355, 356, 357, 359, 360, 362, 369, 370], "excluded_lines": []}}}, "src\\hmdm\\evaluation\\military_indicators.py": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 23, 24, 93, 94, 142, 143, 220, 221, 298, 299, 340, 341, 400, 401], "summary": {"covered_lines": 19, "num_statements": 44, "percent_covered": 43.18181818181818, "percent_covered_display": "43", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [18, 26, 29, 39, 47, 50, 60, 68, 71, 81, 89, 91, 96, 99, 109, 117, 120, 130, 138, 140, 145, 223, 301, 343, 403], "excluded_lines": [], "functions": {"MilitaryIndicatorSystem.get_military_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [18], "excluded_lines": []}, "MilitaryIndicatorSystem._get_combat_effectiveness_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [26, 29, 39, 47, 50, 60, 68, 71, 81, 89, 91], "excluded_lines": []}, "MilitaryIndicatorSystem._get_command_control_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [96, 99, 109, 117, 120, 130, 138, 140], "excluded_lines": []}, "MilitaryIndicatorSystem._get_support_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [145], "excluded_lines": []}, "MilitaryIndicatorSystem._get_information_system_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [223], "excluded_lines": []}, "MilitaryIndicatorSystem._get_personnel_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [301], "excluded_lines": []}, "MilitaryIndicatorSystem._get_equipment_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "MilitaryIndicatorSystem._get_environment_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [403], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 23, 24, 93, 94, 142, 143, 220, 221, 298, 299, 340, 341, 400, 401], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MilitaryIndicatorSystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [18, 26, 29, 39, 47, 50, 60, 68, 71, 81, 89, 91, 96, 99, 109, 117, 120, 130, 138, 140, 145, 223, 301, 343, 403], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 23, 24, 93, 94, 142, 143, 220, 221, 298, 299, 340, 341, 400, 401], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\evaluation\\scheme_evaluator.py": {"executed_lines": [1, 7, 8, 9, 10, 12, 15, 16, 17, 20, 21, 23, 27, 77, 96, 119, 138, 157, 172, 188, 205, 221, 242, 261, 341, 417, 544, 599, 625, 629], "summary": {"covered_lines": 28, "num_statements": 262, "percent_covered": 10.687022900763358, "percent_covered_display": "11", "missing_lines": 234, "excluded_lines": 0}, "missing_lines": [24, 25, 32, 39, 40, 42, 47, 50, 57, 59, 60, 62, 69, 72, 73, 75, 82, 84, 85, 86, 87, 90, 91, 92, 94, 102, 103, 105, 106, 109, 110, 113, 114, 117, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 141, 144, 145, 147, 148, 149, 151, 152, 153, 155, 159, 161, 162, 164, 165, 167, 168, 170, 174, 176, 177, 178, 180, 181, 183, 184, 186, 190, 192, 193, 195, 196, 197, 199, 200, 201, 203, 207, 209, 210, 211, 213, 214, 216, 217, 219, 223, 225, 226, 228, 230, 231, 233, 235, 236, 238, 240, 244, 246, 247, 249, 251, 252, 254, 256, 257, 259, 268, 272, 273, 276, 282, 284, 285, 286, 287, 288, 290, 295, 297, 300, 301, 302, 303, 304, 305, 307, 310, 311, 312, 314, 315, 316, 317, 319, 320, 321, 322, 324, 325, 328, 339, 347, 351, 352, 355, 368, 369, 370, 376, 377, 389, 390, 391, 392, 393, 399, 400, 401, 403, 406, 407, 415, 419, 421, 428, 525, 535, 536, 537, 540, 542, 548, 554, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 569, 579, 580, 581, 583, 584, 585, 586, 588, 589, 590, 591, 592, 595, 597, 604, 605, 608, 611, 612, 613, 618, 621, 623, 627, 635, 638, 645, 646, 648, 649, 650, 651, 652, 654, 655, 662, 663, 670, 672], "excluded_lines": [], "functions": {"SchemeEvaluator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [24, 25], "excluded_lines": []}, "SchemeEvaluator.evaluate_single_scheme": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [32, 39, 40, 42, 47, 50, 57, 59, 60, 62, 69, 72, 73, 75], "excluded_lines": []}, "SchemeEvaluator.evaluate_multiple_schemes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [82, 84, 85, 86, 87, 90, 91, 92, 94], "excluded_lines": []}, "SchemeEvaluator._extract_indicator_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [102, 103, 105, 106, 109, 110, 113, 114, 117], "excluded_lines": []}, "SchemeEvaluator._calculate_default_indicator_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136], "excluded_lines": []}, "SchemeEvaluator._calculate_workload_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [141, 144, 145, 147, 148, 149, 151, 152, 153, 155], "excluded_lines": []}, "SchemeEvaluator._calculate_efficiency_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [159, 161, 162, 164, 165, 167, 168, 170], "excluded_lines": []}, "SchemeEvaluator._calculate_reliability_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [174, 176, 177, 178, 180, 181, 183, 184, 186], "excluded_lines": []}, "SchemeEvaluator._calculate_usability_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [190, 192, 193, 195, 196, 197, 199, 200, 201, 203], "excluded_lines": []}, "SchemeEvaluator._calculate_safety_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [207, 209, 210, 211, 213, 214, 216, 217, 219], "excluded_lines": []}, "SchemeEvaluator._calculate_cost_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [223, 225, 226, 228, 230, 231, 233, 235, 236, 238, 240], "excluded_lines": []}, "SchemeEvaluator._calculate_performance_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [244, 246, 247, 249, 251, 252, 254, 256, 257, 259], "excluded_lines": []}, "SchemeEvaluator.recommend_best_scheme": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [268, 272, 273, 276, 282, 284, 285, 286, 287, 288, 290, 295, 297, 300, 301, 302, 303, 304, 305, 307, 310, 311, 312, 314, 315, 316, 317, 319, 320, 321, 322, 324, 325, 328, 339], "excluded_lines": []}, "SchemeEvaluator.compare_schemes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [347, 351, 352, 355, 368, 369, 370, 376, 377, 389, 390, 391, 392, 393, 399, 400, 401, 403, 406, 407, 415], "excluded_lines": []}, "SchemeEvaluator.create_default_evaluation_scheme": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [419, 421, 428, 525, 535, 536, 537, 540, 542], "excluded_lines": []}, "SchemeEvaluator.create_military_evaluation_scheme": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [548, 554, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 569, 579, 580, 581, 583, 584, 585, 586, 588, 589, 590, 591, 592, 595, 597], "excluded_lines": []}, "SchemeEvaluator.evaluate_with_military_indicators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [604, 605, 608, 611, 612, 613, 618, 621, 623], "excluded_lines": []}, "SchemeEvaluator.get_military_indicator_categories": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [627], "excluded_lines": []}, "SchemeEvaluator.analyze_military_performance_gap": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [635, 638, 645, 646, 648, 649, 650, 651, 652, 654, 655, 662, 663, 670, 672], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 15, 16, 17, 20, 21, 23, 27, 77, 96, 119, 138, 157, 172, 188, 205, 221, 242, 261, 341, 417, 544, 599, 625, 629], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SchemeEvaluator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 234, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 234, "excluded_lines": 0}, "missing_lines": [24, 25, 32, 39, 40, 42, 47, 50, 57, 59, 60, 62, 69, 72, 73, 75, 82, 84, 85, 86, 87, 90, 91, 92, 94, 102, 103, 105, 106, 109, 110, 113, 114, 117, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 141, 144, 145, 147, 148, 149, 151, 152, 153, 155, 159, 161, 162, 164, 165, 167, 168, 170, 174, 176, 177, 178, 180, 181, 183, 184, 186, 190, 192, 193, 195, 196, 197, 199, 200, 201, 203, 207, 209, 210, 211, 213, 214, 216, 217, 219, 223, 225, 226, 228, 230, 231, 233, 235, 236, 238, 240, 244, 246, 247, 249, 251, 252, 254, 256, 257, 259, 268, 272, 273, 276, 282, 284, 285, 286, 287, 288, 290, 295, 297, 300, 301, 302, 303, 304, 305, 307, 310, 311, 312, 314, 315, 316, 317, 319, 320, 321, 322, 324, 325, 328, 339, 347, 351, 352, 355, 368, 369, 370, 376, 377, 389, 390, 391, 392, 393, 399, 400, 401, 403, 406, 407, 415, 419, 421, 428, 525, 535, 536, 537, 540, 542, 548, 554, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 569, 579, 580, 581, 583, 584, 585, 586, 588, 589, 590, 591, 592, 595, 597, 604, 605, 608, 611, 612, 613, 618, 621, 623, 627, 635, 638, 645, 646, 648, 649, 650, 651, 652, 654, 655, 662, 663, 670, 672], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 15, 16, 17, 20, 21, 23, 27, 77, 96, 119, 138, 157, 172, 188, 205, 221, 242, 261, 341, 417, 544, 599, 625, 629], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\knowledge\\__init__.py": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\knowledge\\military_knowledge_base.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 124, 125, 127, 145, 161, 172, 204, 229, 234, 240, 275, 313, 341, 391, 398, 404, 424, 451], "summary": {"covered_lines": 81, "num_statements": 251, "percent_covered": 32.27091633466136, "percent_covered_display": "32", "missing_lines": 170, "excluded_lines": 0}, "missing_lines": [60, 61, 64, 65, 68, 69, 72, 73, 74, 76, 93, 94, 97, 98, 99, 100, 103, 104, 106, 128, 131, 132, 135, 136, 139, 140, 143, 147, 148, 151, 152, 154, 155, 157, 158, 159, 163, 164, 165, 166, 168, 169, 170, 178, 179, 182, 183, 184, 185, 187, 190, 191, 192, 193, 196, 198, 200, 201, 202, 206, 207, 209, 210, 213, 214, 215, 218, 219, 220, 221, 222, 225, 227, 231, 232, 236, 237, 238, 242, 243, 244, 246, 247, 250, 252, 253, 255, 256, 257, 258, 259, 260, 261, 263, 266, 267, 269, 271, 272, 273, 279, 280, 283, 285, 286, 289, 290, 291, 292, 293, 296, 297, 298, 300, 301, 304, 305, 307, 309, 310, 311, 315, 326, 327, 328, 331, 332, 335, 336, 337, 339, 344, 354, 364, 365, 368, 378, 388, 389, 393, 394, 395, 396, 400, 401, 402, 409, 410, 418, 420, 421, 422, 429, 430, 431, 433, 434, 435, 437, 445, 447, 448, 449, 453, 454, 455, 456, 457, 458, 459], "excluded_lines": [], "functions": {"KnowledgeItem.matches_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [60, 61, 64, 65, 68, 69, 72, 73, 74, 76], "excluded_lines": []}, "Rule.evaluate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [93, 94, 97, 98, 99, 100, 103, 104, 106], "excluded_lines": []}, "MilitaryKnowledgeBase.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [128, 131, 132, 135, 136, 139, 140, 143], "excluded_lines": []}, "MilitaryKnowledgeBase.add_knowledge_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [147, 148, 151, 152, 154, 155, 157, 158, 159], "excluded_lines": []}, "MilitaryKnowledgeBase.add_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 168, 169, 170], "excluded_lines": []}, "MilitaryKnowledgeBase.search_knowledge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [178, 179, 182, 183, 184, 185, 187, 190, 191, 192, 193, 196, 198, 200, 201, 202], "excluded_lines": []}, "MilitaryKnowledgeBase.get_related_knowledge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [206, 207, 209, 210, 213, 214, 215, 218, 219, 220, 221, 222, 225, 227], "excluded_lines": []}, "MilitaryKnowledgeBase.add_fact": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [231, 232], "excluded_lines": []}, "MilitaryKnowledgeBase.remove_fact": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [236, 237, 238], "excluded_lines": []}, "MilitaryKnowledgeBase.infer_knowledge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [242, 243, 244, 246, 247, 250, 252, 253, 255, 256, 257, 258, 259, 260, 261, 263, 266, 267, 269, 271, 272, 273], "excluded_lines": []}, "MilitaryKnowledgeBase.get_expert_advice": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [279, 280, 283, 285, 286, 289, 290, 291, 292, 293, 296, 297, 298, 300, 301, 304, 305, 307, 309, 310, 311], "excluded_lines": []}, "MilitaryKnowledgeBase.get_knowledge_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [315, 326, 327, 328, 331, 332, 335, 336, 337, 339], "excluded_lines": []}, "MilitaryKnowledgeBase._initialize_default_knowledge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [344, 354, 364, 365, 368, 378, 388, 389], "excluded_lines": []}, "MilitaryKnowledgeBase._update_keyword_index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [393, 394, 395, 396], "excluded_lines": []}, "MilitaryKnowledgeBase._update_type_index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [400, 401, 402], "excluded_lines": []}, "MilitaryKnowledgeBase._generate_advice_from_knowledge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [409, 410, 418, 420, 421, 422], "excluded_lines": []}, "MilitaryKnowledgeBase._generate_advice_from_inference": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [429, 430, 431, 433, 434, 435, 437, 445, 447, 448, 449], "excluded_lines": []}, "MilitaryKnowledgeBase._add_context_facts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [453, 454, 455, 456, 457, 458, 459], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 124, 125, 127, 145, 161, 172, 204, 229, 234, 240, 275, 313, 341, 391, 398, 404, 424, 451], "summary": {"covered_lines": 81, "num_statements": 81, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"KnowledgeType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfidenceLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "KnowledgeItem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [60, 61, 64, 65, 68, 69, 72, 73, 74, 76], "excluded_lines": []}, "Rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [93, 94, 97, 98, 99, 100, 103, 104, 106], "excluded_lines": []}, "ExpertAdvice": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MilitaryKnowledgeBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 151, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 151, "excluded_lines": 0}, "missing_lines": [128, 131, 132, 135, 136, 139, 140, 143, 147, 148, 151, 152, 154, 155, 157, 158, 159, 163, 164, 165, 166, 168, 169, 170, 178, 179, 182, 183, 184, 185, 187, 190, 191, 192, 193, 196, 198, 200, 201, 202, 206, 207, 209, 210, 213, 214, 215, 218, 219, 220, 221, 222, 225, 227, 231, 232, 236, 237, 238, 242, 243, 244, 246, 247, 250, 252, 253, 255, 256, 257, 258, 259, 260, 261, 263, 266, 267, 269, 271, 272, 273, 279, 280, 283, 285, 286, 289, 290, 291, 292, 293, 296, 297, 298, 300, 301, 304, 305, 307, 309, 310, 311, 315, 326, 327, 328, 331, 332, 335, 336, 337, 339, 344, 354, 364, 365, 368, 378, 388, 389, 393, 394, 395, 396, 400, 401, 402, 409, 410, 418, 420, 421, 422, 429, 430, 431, 433, 434, 435, 437, 445, 447, 448, 449, 453, 454, 455, 456, 457, 458, 459], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 124, 125, 127, 145, 161, 172, 204, 229, 234, 240, 275, 313, 341, 391, 398, 404, 424, 451], "summary": {"covered_lines": 81, "num_statements": 81, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\ml\\__init__.py": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 4, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [26, 34], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 4, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [26, 34], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 4, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [26, 34], "excluded_lines": []}}}, "src\\hmdm\\ml\\intelligent_prediction_engine.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 34, 35, 36, 37, 41, 44, 45, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 104, 118, 119, 121, 143, 151, 204, 282, 354, 362, 402, 424, 477, 503, 526, 563, 625, 642], "summary": {"covered_lines": 77, "num_statements": 379, "percent_covered": 20.316622691292874, "percent_covered_display": "20", "missing_lines": 302, "excluded_lines": 0}, "missing_lines": [30, 31, 38, 39, 78, 106, 122, 123, 124, 127, 128, 129, 130, 133, 136, 139, 141, 145, 146, 148, 149, 153, 154, 155, 157, 160, 161, 162, 163, 164, 166, 167, 168, 169, 170, 172, 173, 174, 175, 176, 178, 179, 180, 181, 184, 186, 187, 190, 191, 194, 195, 197, 198, 200, 201, 202, 206, 207, 208, 210, 211, 214, 217, 218, 220, 225, 227, 228, 229, 231, 240, 241, 245, 248, 250, 251, 252, 253, 254, 256, 257, 258, 259, 260, 262, 263, 264, 265, 267, 268, 269, 270, 273, 275, 276, 278, 279, 280, 284, 285, 286, 288, 289, 292, 295, 296, 297, 298, 299, 302, 305, 306, 307, 310, 311, 316, 318, 319, 322, 323, 324, 330, 345, 347, 348, 350, 351, 352, 356, 357, 358, 359, 360, 364, 365, 366, 368, 371, 376, 377, 380, 381, 382, 383, 385, 396, 398, 399, 400, 406, 407, 410, 411, 413, 414, 417, 418, 420, 421, 422, 426, 427, 428, 431, 432, 433, 434, 437, 440, 445, 448, 449, 450, 453, 454, 455, 456, 459, 460, 461, 463, 464, 467, 468, 469, 471, 473, 474, 475, 479, 481, 482, 485, 488, 489, 492, 493, 494, 495, 497, 499, 500, 501, 505, 507, 508, 511, 514, 517, 518, 520, 522, 523, 524, 528, 529, 530, 532, 533, 536, 537, 538, 541, 542, 543, 545, 546, 547, 550, 551, 552, 554, 555, 556, 558, 560, 561, 565, 566, 567, 569, 570, 571, 573, 574, 576, 577, 579, 581, 582, 585, 586, 587, 589, 590, 593, 594, 595, 596, 597, 598, 600, 601, 602, 603, 604, 607, 608, 609, 610, 612, 613, 614, 615, 617, 619, 620, 622, 623, 627, 628, 629, 638, 640, 644, 646, 647, 648, 649, 650, 651, 652, 653, 656, 657, 658, 659, 661, 662, 664, 665, 666], "excluded_lines": [], "functions": {"ModelConfig.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [78], "excluded_lines": []}, "PredictionResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [106], "excluded_lines": []}, "IntelligentPredictionEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [122, 123, 124, 127, 128, 129, 130, 133, 136, 139, 141], "excluded_lines": []}, "IntelligentPredictionEngine._check_dependencies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [145, 146, 148, 149], "excluded_lines": []}, "IntelligentPredictionEngine.create_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [153, 154, 155, 157, 160, 161, 162, 163, 164, 166, 167, 168, 169, 170, 172, 173, 174, 175, 176, 178, 179, 180, 181, 184, 186, 187, 190, 191, 194, 195, 197, 198, 200, 201, 202], "excluded_lines": []}, "IntelligentPredictionEngine.train_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 42, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [206, 207, 208, 210, 211, 214, 217, 218, 220, 225, 227, 228, 229, 231, 240, 241, 245, 248, 250, 251, 252, 253, 254, 256, 257, 258, 259, 260, 262, 263, 264, 265, 267, 268, 269, 270, 273, 275, 276, 278, 279, 280], "excluded_lines": []}, "IntelligentPredictionEngine.predict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [284, 285, 286, 288, 289, 292, 295, 296, 297, 298, 299, 302, 305, 306, 307, 310, 311, 316, 318, 319, 322, 323, 324, 330, 345, 347, 348, 350, 351, 352], "excluded_lines": []}, "IntelligentPredictionEngine.batch_predict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [356, 357, 358, 359, 360], "excluded_lines": []}, "IntelligentPredictionEngine.get_model_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [364, 365, 366, 368, 371, 376, 377, 380, 381, 382, 383, 385, 396, 398, 399, 400], "excluded_lines": []}, "IntelligentPredictionEngine.get_prediction_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [406, 407, 410, 411, 413, 414, 417, 418, 420, 421, 422], "excluded_lines": []}, "IntelligentPredictionEngine._create_deep_learning_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [426, 427, 428, 431, 432, 433, 434, 437, 440, 445, 448, 449, 450, 453, 454, 455, 456, 459, 460, 461, 463, 464, 467, 468, 469, 471, 473, 474, 475], "excluded_lines": []}, "IntelligentPredictionEngine._preprocess_training_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [479, 481, 482, 485, 488, 489, 492, 493, 494, 495, 497, 499, 500, 501], "excluded_lines": []}, "IntelligentPredictionEngine._preprocess_prediction_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [505, 507, 508, 511, 514, 517, 518, 520, 522, 523, 524], "excluded_lines": []}, "IntelligentPredictionEngine._save_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [528, 529, 530, 532, 533, 536, 537, 538, 541, 542, 543, 545, 546, 547, 550, 551, 552, 554, 555, 556, 558, 560, 561], "excluded_lines": []}, "IntelligentPredictionEngine._load_saved_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 42, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [565, 566, 567, 569, 570, 571, 573, 574, 576, 577, 579, 581, 582, 585, 586, 587, 589, 590, 593, 594, 595, 596, 597, 598, 600, 601, 602, 603, 604, 607, 608, 609, 610, 612, 613, 614, 615, 617, 619, 620, 622, 623], "excluded_lines": []}, "IntelligentPredictionEngine.list_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [627, 628, 629, 638, 640], "excluded_lines": []}, "IntelligentPredictionEngine.delete_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [644, 646, 647, 648, 649, 650, 651, 652, 653, 656, 657, 658, 659, 661, 662, 664, 665, 666], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 34, 35, 36, 37, 41, 44, 45, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 104, 118, 119, 121, 143, 151, 204, 282, 354, 362, 402, 424, 477, 503, 526, 563, 625, 642], "summary": {"covered_lines": 77, "num_statements": 81, "percent_covered": 95.06172839506173, "percent_covered_display": "95", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [30, 31, 38, 39], "excluded_lines": []}}, "classes": {"ModelType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PredictionTask": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [78], "excluded_lines": []}, "PredictionResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [106], "excluded_lines": []}, "IntelligentPredictionEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 296, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 296, "excluded_lines": 0}, "missing_lines": [122, 123, 124, 127, 128, 129, 130, 133, 136, 139, 141, 145, 146, 148, 149, 153, 154, 155, 157, 160, 161, 162, 163, 164, 166, 167, 168, 169, 170, 172, 173, 174, 175, 176, 178, 179, 180, 181, 184, 186, 187, 190, 191, 194, 195, 197, 198, 200, 201, 202, 206, 207, 208, 210, 211, 214, 217, 218, 220, 225, 227, 228, 229, 231, 240, 241, 245, 248, 250, 251, 252, 253, 254, 256, 257, 258, 259, 260, 262, 263, 264, 265, 267, 268, 269, 270, 273, 275, 276, 278, 279, 280, 284, 285, 286, 288, 289, 292, 295, 296, 297, 298, 299, 302, 305, 306, 307, 310, 311, 316, 318, 319, 322, 323, 324, 330, 345, 347, 348, 350, 351, 352, 356, 357, 358, 359, 360, 364, 365, 366, 368, 371, 376, 377, 380, 381, 382, 383, 385, 396, 398, 399, 400, 406, 407, 410, 411, 413, 414, 417, 418, 420, 421, 422, 426, 427, 428, 431, 432, 433, 434, 437, 440, 445, 448, 449, 450, 453, 454, 455, 456, 459, 460, 461, 463, 464, 467, 468, 469, 471, 473, 474, 475, 479, 481, 482, 485, 488, 489, 492, 493, 494, 495, 497, 499, 500, 501, 505, 507, 508, 511, 514, 517, 518, 520, 522, 523, 524, 528, 529, 530, 532, 533, 536, 537, 538, 541, 542, 543, 545, 546, 547, 550, 551, 552, 554, 555, 556, 558, 560, 561, 565, 566, 567, 569, 570, 571, 573, 574, 576, 577, 579, 581, 582, 585, 586, 587, 589, 590, 593, 594, 595, 596, 597, 598, 600, 601, 602, 603, 604, 607, 608, 609, 610, 612, 613, 614, 615, 617, 619, 620, 622, 623, 627, 628, 629, 638, 640, 644, 646, 647, 648, 649, 650, 651, 652, 653, 656, 657, 658, 659, 661, 662, 664, 665, 666], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 34, 35, 36, 37, 41, 44, 45, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 104, 118, 119, 121, 143, 151, 204, 282, 354, 362, 402, 424, 477, 503, 526, 563, 625, 642], "summary": {"covered_lines": 77, "num_statements": 81, "percent_covered": 95.06172839506173, "percent_covered_display": "95", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [30, 31, 38, 39], "excluded_lines": []}}}, "src\\hmdm\\ml\\nlp_processor.py": {"executed_lines": [1, 8, 9], "summary": {"covered_lines": 2, "num_statements": 279, "percent_covered": 0.7168458781362007, "percent_covered_display": "1", "missing_lines": 277, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 38, 40, 41, 42, 43, 44, 47, 49, 50, 51, 52, 53, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 73, 88, 91, 92, 95, 98, 99, 100, 103, 106, 107, 113, 114, 115, 116, 117, 118, 121, 123, 125, 127, 129, 132, 133, 136, 139, 142, 145, 148, 151, 154, 173, 175, 176, 178, 179, 181, 188, 190, 191, 192, 194, 195, 196, 197, 204, 206, 208, 209, 210, 212, 214, 215, 216, 217, 220, 223, 226, 227, 229, 232, 233, 235, 237, 238, 239, 241, 243, 244, 245, 248, 251, 254, 255, 258, 261, 262, 263, 264, 266, 276, 277, 278, 280, 283, 286, 288, 290, 293, 295, 297, 299, 301, 302, 303, 305, 306, 307, 308, 309, 310, 312, 314, 316, 318, 319, 321, 322, 324, 325, 326, 327, 330, 331, 333, 334, 335, 336, 338, 339, 340, 341, 342, 343, 345, 347, 348, 349, 351, 353, 355, 358, 364, 367, 369, 370, 371, 373, 375, 376, 379, 380, 381, 389, 390, 391, 399, 400, 401, 409, 410, 411, 418, 420, 421, 422, 424, 426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 443, 445, 446, 447, 449, 451, 452, 453, 455, 457, 458, 461, 462, 463, 465, 466, 468, 469, 471, 472, 475, 476, 479, 481, 482, 483, 485, 487, 489, 491, 493, 495, 497, 498, 499, 501, 502, 503, 505, 506, 507, 509, 510, 511, 513, 515, 516, 517, 519, 522, 523, 525, 527, 540, 542, 550, 552, 564, 566, 574, 581], "excluded_lines": [], "functions": {"TextAnalysisResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [73], "excluded_lines": []}, "MilitaryNLPProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [92, 95, 98, 99, 100, 103, 106, 107, 113, 114, 115, 116, 117, 118, 121, 123], "excluded_lines": []}, "MilitaryNLPProcessor.analyze_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [127, 129, 132, 133, 136, 139, 142, 145, 148, 151, 154, 173, 175, 176, 178, 179, 181], "excluded_lines": []}, "MilitaryNLPProcessor.extract_commands": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [190, 191, 192, 194, 195, 196, 197, 204, 206, 208, 209, 210], "excluded_lines": []}, "MilitaryNLPProcessor.similarity_search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [214, 215, 216, 217, 220, 223, 226, 227, 229, 232, 233, 235, 237, 238, 239], "excluded_lines": []}, "MilitaryNLPProcessor.get_analysis_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [243, 244, 245, 248, 251, 254, 255, 258, 261, 262, 263, 264, 266, 276, 277, 278], "excluded_lines": []}, "MilitaryNLPProcessor._preprocess_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [283, 286, 288], "excluded_lines": []}, "MilitaryNLPProcessor._detect_text_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [293, 295, 297, 299, 301, 302, 303, 305, 306, 307, 308, 309, 310, 312], "excluded_lines": []}, "MilitaryNLPProcessor._analyze_sentiment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [316, 318, 319, 321, 322, 324, 325, 326, 327, 330, 331, 333, 334, 335, 336, 338, 339, 340, 341, 342, 343, 345, 347, 348, 349], "excluded_lines": []}, "MilitaryNLPProcessor._extract_keywords": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [353, 355, 358, 364, 367, 369, 370, 371], "excluded_lines": []}, "MilitaryNLPProcessor._extract_entities": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [375, 376, 379, 380, 381, 389, 390, 391, 399, 400, 401, 409, 410, 411, 418, 420, 421, 422], "excluded_lines": []}, "MilitaryNLPProcessor._generate_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 443, 445, 446, 447, 449, 451, 452, 453], "excluded_lines": []}, "MilitaryNLPProcessor._assess_urgency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [457, 458, 461, 462, 463, 465, 466, 468, 469, 471, 472, 475, 476, 479, 481, 482, 483], "excluded_lines": []}, "MilitaryNLPProcessor._classify_security_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [487, 489, 491, 493, 495, 497, 498, 499, 501, 502, 503, 505, 506, 507, 509, 510, 511, 513, 515, 516, 517], "excluded_lines": []}, "MilitaryNLPProcessor._split_sentences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [522, 523], "excluded_lines": []}, "MilitaryNLPProcessor._load_military_terms": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [527], "excluded_lines": []}, "MilitaryNLPProcessor._load_command_patterns": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [542], "excluded_lines": []}, "MilitaryNLPProcessor._load_urgency_keywords": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [552], "excluded_lines": []}, "MilitaryNLPProcessor._load_stop_words": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [566, 574, 581], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9], "summary": {"covered_lines": 2, "num_statements": 69, "percent_covered": 2.898550724637681, "percent_covered_display": "3", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 38, 40, 41, 42, 43, 44, 47, 49, 50, 51, 52, 53, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 88, 91, 125, 188, 212, 241, 280, 290, 314, 351, 373, 424, 455, 485, 519, 525, 540, 550, 564], "excluded_lines": []}}, "classes": {"TextType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SentimentType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TextAnalysisResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [73], "excluded_lines": []}, "MilitaryNLPProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 209, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 209, "excluded_lines": 0}, "missing_lines": [92, 95, 98, 99, 100, 103, 106, 107, 113, 114, 115, 116, 117, 118, 121, 123, 127, 129, 132, 133, 136, 139, 142, 145, 148, 151, 154, 173, 175, 176, 178, 179, 181, 190, 191, 192, 194, 195, 196, 197, 204, 206, 208, 209, 210, 214, 215, 216, 217, 220, 223, 226, 227, 229, 232, 233, 235, 237, 238, 239, 243, 244, 245, 248, 251, 254, 255, 258, 261, 262, 263, 264, 266, 276, 277, 278, 283, 286, 288, 293, 295, 297, 299, 301, 302, 303, 305, 306, 307, 308, 309, 310, 312, 316, 318, 319, 321, 322, 324, 325, 326, 327, 330, 331, 333, 334, 335, 336, 338, 339, 340, 341, 342, 343, 345, 347, 348, 349, 353, 355, 358, 364, 367, 369, 370, 371, 375, 376, 379, 380, 381, 389, 390, 391, 399, 400, 401, 409, 410, 411, 418, 420, 421, 422, 426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 443, 445, 446, 447, 449, 451, 452, 453, 457, 458, 461, 462, 463, 465, 466, 468, 469, 471, 472, 475, 476, 479, 481, 482, 483, 487, 489, 491, 493, 495, 497, 498, 499, 501, 502, 503, 505, 506, 507, 509, 510, 511, 513, 515, 516, 517, 522, 523, 527, 542, 552, 566, 574, 581], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9], "summary": {"covered_lines": 2, "num_statements": 69, "percent_covered": 2.898550724637681, "percent_covered_display": "3", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 38, 40, 41, 42, 43, 44, 47, 49, 50, 51, 52, 53, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 88, 91, 125, 188, 212, 241, 280, 290, 314, 351, 373, 424, 455, 485, 519, 525, 540, 550, 564], "excluded_lines": []}}}, "src\\hmdm\\ml\\recommendation_engine.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 314, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 314, "excluded_lines": 0}, "missing_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 31, 34, 36, 37, 38, 39, 40, 43, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 64, 75, 76, 78, 79, 80, 81, 82, 83, 85, 87, 97, 100, 101, 104, 107, 110, 113, 114, 115, 118, 119, 122, 124, 126, 128, 129, 132, 134, 135, 137, 138, 139, 141, 144, 145, 152, 154, 155, 157, 158, 159, 161, 164, 165, 168, 170, 171, 174, 177, 179, 182, 185, 187, 188, 190, 191, 192, 194, 196, 197, 198, 200, 201, 204, 205, 206, 208, 209, 212, 215, 217, 218, 219, 220, 221, 223, 225, 226, 227, 229, 231, 232, 233, 234, 235, 238, 239, 240, 243, 244, 245, 247, 257, 258, 259, 261, 263, 271, 273, 274, 275, 276, 278, 280, 283, 284, 285, 288, 289, 290, 291, 294, 297, 298, 299, 301, 302, 303, 304, 305, 306, 307, 310, 312, 313, 314, 321, 323, 325, 326, 327, 329, 332, 333, 336, 339, 340, 341, 342, 345, 347, 348, 355, 357, 359, 360, 361, 363, 365, 366, 367, 369, 370, 372, 374, 375, 376, 378, 381, 382, 384, 385, 386, 389, 390, 392, 393, 394, 396, 398, 399, 400, 402, 403, 404, 405, 408, 409, 410, 411, 412, 413, 414, 417, 418, 421, 424, 425, 426, 427, 428, 430, 432, 433, 434, 436, 440, 441, 444, 445, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 459, 462, 463, 466, 468, 469, 470, 472, 474, 475, 476, 478, 481, 482, 483, 486, 487, 488, 491, 492, 493, 495, 497, 499, 500, 501, 503, 505, 506, 507, 510, 511, 512, 513, 516, 519, 520, 521, 522, 525, 526, 528, 530, 531, 533, 536, 537, 544, 547, 548, 550, 551, 553, 555, 557, 585, 613, 641, 642, 643, 646, 648, 650, 651], "excluded_lines": [], "functions": {"RecommendationItem.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "RecommendationResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [87], "excluded_lines": []}, "IntelligentRecommendationEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [101, 104, 107, 110, 113, 114, 115, 118, 119, 122, 124], "excluded_lines": []}, "IntelligentRecommendationEngine.add_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [128, 129, 132, 134, 135, 137, 138, 139], "excluded_lines": []}, "IntelligentRecommendationEngine.record_interaction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [144, 145, 152, 154, 155, 157, 158, 159], "excluded_lines": []}, "IntelligentRecommendationEngine.get_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [164, 165, 168, 170, 171, 174, 177, 179, 182, 185, 187, 188, 190, 191, 192], "excluded_lines": []}, "IntelligentRecommendationEngine.get_similar_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [196, 197, 198, 200, 201, 204, 205, 206, 208, 209, 212, 215, 217, 218, 219, 220, 221, 223, 225, 226, 227], "excluded_lines": []}, "IntelligentRecommendationEngine.get_recommendation_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [231, 232, 233, 234, 235, 238, 239, 240, 243, 244, 245, 247, 257, 258, 259], "excluded_lines": []}, "IntelligentRecommendationEngine._filter_items_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [263, 271, 273, 274, 275, 276, 278], "excluded_lines": []}, "IntelligentRecommendationEngine._collaborative_filtering": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [283, 284, 285, 288, 289, 290, 291, 294, 297, 298, 299, 301, 302, 303, 304, 305, 306, 307, 310, 312, 313, 314, 321, 323, 325, 326, 327], "excluded_lines": []}, "IntelligentRecommendationEngine._content_based_recommendation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [332, 333, 336, 339, 340, 341, 342, 345, 347, 348, 355, 357, 359, 360, 361], "excluded_lines": []}, "IntelligentRecommendationEngine._find_similar_users": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [365, 366, 367, 369, 370, 372, 374, 375, 376, 378, 381, 382, 384, 385, 386, 389, 390, 392, 393, 394], "excluded_lines": []}, "IntelligentRecommendationEngine._get_user_preferences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [398, 399, 400, 402, 403, 404, 405, 408, 409, 410, 411, 412, 413, 414, 417, 418, 421, 424, 425, 426, 427, 428, 430, 432, 433, 434], "excluded_lines": []}, "IntelligentRecommendationEngine._calculate_content_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [440, 441, 444, 445, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 459, 462, 463, 466, 468, 469, 470], "excluded_lines": []}, "IntelligentRecommendationEngine._calculate_context_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [474, 475, 476, 478, 481, 482, 483, 486, 487, 488, 491, 492, 493, 495, 497, 499, 500, 501], "excluded_lines": []}, "IntelligentRecommendationEngine._update_feature_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [505, 506, 507, 510, 511, 512, 513, 516, 519, 520, 521, 522, 525, 526, 528, 530, 531], "excluded_lines": []}, "IntelligentRecommendationEngine._record_recommendation_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [536, 537, 544, 547, 548, 550, 551], "excluded_lines": []}, "IntelligentRecommendationEngine._initialize_recommendation_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [555, 557, 585, 613, 641, 642, 643, 646, 648, 650, 651], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 65, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 31, 34, 36, 37, 38, 39, 40, 43, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 75, 76, 78, 79, 80, 81, 82, 83, 85, 97, 100, 126, 141, 161, 194, 229, 261, 280, 329, 363, 396, 436, 472, 503, 533, 553], "excluded_lines": []}}, "classes": {"RecommendationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RecommendationMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RecommendationItem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "RecommendationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [87], "excluded_lines": []}, "IntelligentRecommendationEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 247, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 247, "excluded_lines": 0}, "missing_lines": [101, 104, 107, 110, 113, 114, 115, 118, 119, 122, 124, 128, 129, 132, 134, 135, 137, 138, 139, 144, 145, 152, 154, 155, 157, 158, 159, 164, 165, 168, 170, 171, 174, 177, 179, 182, 185, 187, 188, 190, 191, 192, 196, 197, 198, 200, 201, 204, 205, 206, 208, 209, 212, 215, 217, 218, 219, 220, 221, 223, 225, 226, 227, 231, 232, 233, 234, 235, 238, 239, 240, 243, 244, 245, 247, 257, 258, 259, 263, 271, 273, 274, 275, 276, 278, 283, 284, 285, 288, 289, 290, 291, 294, 297, 298, 299, 301, 302, 303, 304, 305, 306, 307, 310, 312, 313, 314, 321, 323, 325, 326, 327, 332, 333, 336, 339, 340, 341, 342, 345, 347, 348, 355, 357, 359, 360, 361, 365, 366, 367, 369, 370, 372, 374, 375, 376, 378, 381, 382, 384, 385, 386, 389, 390, 392, 393, 394, 398, 399, 400, 402, 403, 404, 405, 408, 409, 410, 411, 412, 413, 414, 417, 418, 421, 424, 425, 426, 427, 428, 430, 432, 433, 434, 440, 441, 444, 445, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 459, 462, 463, 466, 468, 469, 470, 474, 475, 476, 478, 481, 482, 483, 486, 487, 488, 491, 492, 493, 495, 497, 499, 500, 501, 505, 506, 507, 510, 511, 512, 513, 516, 519, 520, 521, 522, 525, 526, 528, 530, 531, 536, 537, 544, 547, 548, 550, 551, 555, 557, 585, 613, 641, 642, 643, 646, 648, 650, 651], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 65, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 31, 34, 36, 37, 38, 39, 40, 43, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 75, 76, 78, 79, 80, 81, 82, 83, 85, 97, 100, 126, 141, 161, 194, 229, 261, 280, 329, 363, 396, 436, 472, 503, 533, 553], "excluded_lines": []}}}, "src\\hmdm\\models\\__init__.py": {"executed_lines": [1, 10, 15, 21, 26], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 10, 15, 21, 26], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 10, 15, 21, 26], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\models\\decision_models.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 43, 53, 54, 64, 76, 86, 87, 88, 89, 90, 91, 94, 97, 100, 103, 104, 105, 107, 111, 116, 131, 132, 133, 134, 135, 136, 139, 142, 145, 146, 147, 150, 151, 154, 155, 158, 159, 161, 166, 195, 224, 243, 271, 291, 292, 293, 294, 295, 296, 299, 302, 303, 306, 307, 310, 313, 314, 315, 317, 326, 330], "summary": {"covered_lines": 84, "num_statements": 184, "percent_covered": 45.65217391304348, "percent_covered_display": "46", "missing_lines": 100, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49, 50, 51, 56, 57, 58, 59, 60, 61, 62, 66, 67, 68, 69, 70, 71, 72, 73, 74, 78, 109, 113, 118, 163, 164, 168, 169, 172, 173, 176, 177, 179, 181, 182, 183, 184, 186, 189, 191, 192, 193, 197, 198, 200, 202, 203, 204, 206, 208, 209, 210, 211, 212, 214, 216, 217, 218, 219, 221, 222, 226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 241, 245, 246, 248, 249, 251, 252, 254, 255, 256, 258, 259, 260, 262, 263, 265, 266, 267, 269, 273, 319, 320, 321, 324, 328, 332], "excluded_lines": [], "functions": {"FuzzyNumber.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49, 50, 51], "excluded_lines": []}, "FuzzyNumber.center": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 59, 60, 61, 62], "excluded_lines": []}, "FuzzyNumber.defuzzify": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [66, 67, 68, 69, 70, 71, 72, 73, 74], "excluded_lines": []}, "FuzzyNumber.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [78], "excluded_lines": []}, "Alternative.add_fuzzy_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [109], "excluded_lines": []}, "Alternative.get_crisp_values": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [113], "excluded_lines": []}, "Alternative.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [118], "excluded_lines": []}, "DecisionMatrix.add_alternative": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [163, 164], "excluded_lines": []}, "DecisionMatrix.build_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [168, 169, 172, 173, 176, 177, 179, 181, 182, 183, 184, 186, 189, 191, 192, 193], "excluded_lines": []}, "DecisionMatrix.normalize_matrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [197, 198, 200, 202, 203, 204, 206, 208, 209, 210, 211, 212, 214, 216, 217, 218, 219, 221, 222], "excluded_lines": []}, "DecisionMatrix.apply_weights": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 241], "excluded_lines": []}, "DecisionMatrix.calculate_ideal_solutions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [245, 246, 248, 249, 251, 252, 254, 255, 256, 258, 259, 260, 262, 263, 265, 266, 267, 269], "excluded_lines": []}, "DecisionMatrix.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [273], "excluded_lines": []}, "DecisionResult.set_rankings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [319, 320, 321, 324], "excluded_lines": []}, "DecisionResult.get_top_n": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [328], "excluded_lines": []}, "DecisionResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [332], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 43, 53, 54, 64, 76, 86, 87, 88, 89, 90, 91, 94, 97, 100, 103, 104, 105, 107, 111, 116, 131, 132, 133, 134, 135, 136, 139, 142, 145, 146, 147, 150, 151, 154, 155, 158, 159, 161, 166, 195, 224, 243, 271, 291, 292, 293, 294, 295, 296, 299, 302, 303, 306, 307, 310, 313, 314, 315, 317, 326, 330], "summary": {"covered_lines": 84, "num_statements": 84, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DecisionMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FuzzySetType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FuzzyNumber": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49, 50, 51, 56, 57, 58, 59, 60, 61, 62, 66, 67, 68, 69, 70, 71, 72, 73, 74, 78], "excluded_lines": []}, "Alternative": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [109, 113, 118], "excluded_lines": []}, "DecisionMatrix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [163, 164, 168, 169, 172, 173, 176, 177, 179, 181, 182, 183, 184, 186, 189, 191, 192, 193, 197, 198, 200, 202, 203, 204, 206, 208, 209, 210, 211, 212, 214, 216, 217, 218, 219, 221, 222, 226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 241, 245, 246, 248, 249, 251, 252, 254, 255, 256, 258, 259, 260, 262, 263, 265, 266, 267, 269, 273], "excluded_lines": []}, "DecisionResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [319, 320, 321, 324, 328, 332], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 43, 53, 54, 64, 76, 86, 87, 88, 89, 90, 91, 94, 97, 100, 103, 104, 105, 107, 111, 116, 131, 132, 133, 134, 135, 136, 139, 142, 145, 146, 147, 150, 151, 154, 155, 158, 159, 161, 166, 195, 224, 243, 271, 291, 292, 293, 294, 295, 296, 299, 302, 303, 306, 307, 310, 313, 314, 315, 317, 326, 330], "summary": {"covered_lines": 84, "num_statements": 84, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\models\\evaluation_models.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 59, 62, 63, 66, 67, 68, 71, 73, 81, 101, 102, 103, 104, 105, 106, 107, 109, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 141, 142, 143, 144, 145, 146, 149, 150, 153, 156, 159, 160, 161, 163, 172, 183, 191, 196, 212, 213, 214, 215, 216, 217, 218, 221, 224, 225, 228, 231, 232, 233, 234, 236, 240, 264, 289], "summary": {"covered_lines": 96, "num_statements": 153, "percent_covered": 62.745098039215684, "percent_covered_display": "63", "missing_lines": 57, "excluded_lines": 0}, "missing_lines": [75, 76, 78, 79, 83, 111, 131, 165, 166, 170, 174, 175, 181, 185, 186, 187, 188, 189, 193, 198, 238, 242, 243, 244, 245, 246, 247, 248, 250, 251, 252, 253, 254, 255, 256, 257, 258, 261, 262, 266, 268, 269, 270, 271, 273, 274, 276, 277, 278, 279, 280, 281, 283, 284, 286, 287, 291], "excluded_lines": [], "functions": {"IndicatorDefinition.normalize_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [75, 76, 78, 79], "excluded_lines": []}, "IndicatorDefinition.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [83], "excluded_lines": []}, "IndicatorWeight.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [111], "excluded_lines": []}, "IndicatorValue.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [131], "excluded_lines": []}, "EvaluationScheme.add_indicator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [165, 166, 170], "excluded_lines": []}, "EvaluationScheme.set_weight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [174, 175, 181], "excluded_lines": []}, "EvaluationScheme.normalize_weights": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [185, 186, 187, 188, 189], "excluded_lines": []}, "EvaluationScheme.get_indicator_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [193], "excluded_lines": []}, "EvaluationScheme.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [198], "excluded_lines": []}, "EvaluationResult.add_indicator_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [238], "excluded_lines": []}, "EvaluationResult.calculate_total_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [242, 243, 244, 245, 246, 247, 248, 250, 251, 252, 253, 254, 255, 256, 257, 258, 261, 262], "excluded_lines": []}, "EvaluationResult.calculate_type_scores": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [266, 268, 269, 270, 271, 273, 274, 276, 277, 278, 279, 280, 281, 283, 284, 286, 287], "excluded_lines": []}, "EvaluationResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [291], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 59, 62, 63, 66, 67, 68, 71, 73, 81, 101, 102, 103, 104, 105, 106, 107, 109, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 141, 142, 143, 144, 145, 146, 149, 150, 153, 156, 159, 160, 161, 163, 172, 183, 191, 196, 212, 213, 214, 215, 216, 217, 218, 221, 224, 225, 228, 231, 232, 233, 234, 236, 240, 264, 289], "summary": {"covered_lines": 96, "num_statements": 96, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"IndicatorType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IndicatorCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AggregationMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IndicatorDefinition": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [75, 76, 78, 79, 83], "excluded_lines": []}, "IndicatorWeight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [111], "excluded_lines": []}, "IndicatorValue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [131], "excluded_lines": []}, "EvaluationScheme": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [165, 166, 170, 174, 175, 181, 185, 186, 187, 188, 189, 193, 198], "excluded_lines": []}, "EvaluationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [238, 242, 243, 244, 245, 246, 247, 248, 250, 251, 252, 253, 254, 255, 256, 257, 258, 261, 262, 266, 268, 269, 270, 271, 273, 274, 276, 277, 278, 279, 280, 281, 283, 284, 286, 287, 291], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 59, 62, 63, 66, 67, 68, 71, 73, 81, 101, 102, 103, 104, 105, 106, 107, 109, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 141, 142, 143, 144, 145, 146, 149, 150, 153, 156, 159, 160, 161, 163, 172, 183, 191, 196, 212, 213, 214, 215, 216, 217, 218, 221, 224, 225, 228, 231, 232, 233, 234, 236, 240, 264, 289], "summary": {"covered_lines": 96, "num_statements": 96, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\models\\task_models.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 57, 73, 77, 82, 83, 84, 85, 86, 87, 88, 89, 91, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 114, 117, 120, 121, 122, 125, 126, 127, 128, 130, 136, 142, 164, 165, 166, 167, 168, 169, 170, 171, 173, 183, 196, 197, 198, 199, 200, 202, 213, 217, 225, 232, 246, 257], "summary": {"covered_lines": 86, "num_statements": 131, "percent_covered": 65.64885496183206, "percent_covered_display": "66", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [59, 75, 79, 93, 132, 133, 134, 138, 139, 140, 144, 175, 177, 185, 186, 193, 204, 207, 208, 209, 211, 215, 219, 220, 221, 222, 227, 228, 229, 230, 234, 235, 237, 238, 239, 240, 242, 244, 248, 249, 251, 252, 253, 255, 259], "excluded_lines": [], "functions": {"TaskAttribute.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [59], "excluded_lines": []}, "TaskAttribute.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [75], "excluded_lines": []}, "TaskAttribute.__contains__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [79], "excluded_lines": []}, "IOPattern.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [93], "excluded_lines": []}, "Task.add_child": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [132, 133, 134], "excluded_lines": []}, "Task.remove_child": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [138, 139, 140], "excluded_lines": []}, "Task.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [144], "excluded_lines": []}, "MetaOperation.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [175, 177], "excluded_lines": []}, "MetaOperation.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [185, 186, 193], "excluded_lines": []}, "TaskHierarchy.add_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [204, 207, 208, 209, 211], "excluded_lines": []}, "TaskHierarchy.get_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [215], "excluded_lines": []}, "TaskHierarchy.get_children": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 222], "excluded_lines": []}, "TaskHierarchy.get_parent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [227, 228, 229, 230], "excluded_lines": []}, "TaskHierarchy.get_path_to_root": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [234, 235, 237, 238, 239, 240, 242, 244], "excluded_lines": []}, "TaskHierarchy.get_all_descendants": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [248, 249, 251, 252, 253, 255], "excluded_lines": []}, "TaskHierarchy.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [259], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 57, 73, 77, 82, 83, 84, 85, 86, 87, 88, 89, 91, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 114, 117, 120, 121, 122, 125, 126, 127, 128, 130, 136, 142, 164, 165, 166, 167, 168, 169, 170, 171, 173, 183, 196, 197, 198, 199, 200, 202, 213, 217, 225, 232, 246, 257], "summary": {"covered_lines": 86, "num_statements": 86, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TaskType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OperationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ExecutorType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskAttribute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [59, 75, 79], "excluded_lines": []}, "IOPattern": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [93], "excluded_lines": []}, "Task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [132, 133, 134, 138, 139, 140, 144], "excluded_lines": []}, "MetaOperation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [175, 177, 185, 186, 193], "excluded_lines": []}, "TaskHierarchy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [204, 207, 208, 209, 211, 215, 219, 220, 221, 222, 227, 228, 229, 230, 234, 235, 237, 238, 239, 240, 242, 244, 248, 249, 251, 252, 253, 255, 259], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 52, 53, 54, 55, 57, 73, 77, 82, 83, 84, 85, 86, 87, 88, 89, 91, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 114, 117, 120, 121, 122, 125, 126, 127, 128, 130, 136, 142, 164, 165, 166, 167, 168, 169, 170, 171, 173, 183, 196, 197, 198, 199, 200, 202, 213, 217, 225, 232, 246, 257], "summary": {"covered_lines": 86, "num_statements": 86, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\monitoring\\__init__.py": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\monitoring\\performance_monitor.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 92, 93, 95, 129, 139, 146, 170, 187, 200, 213, 226, 230, 253, 309, 320, 329, 343, 360, 395, 413, 456, 477, 501, 528], "summary": {"covered_lines": 66, "num_statements": 261, "percent_covered": 25.28735632183908, "percent_covered_display": "25", "missing_lines": 195, "excluded_lines": 0}, "missing_lines": [56, 80, 96, 97, 100, 101, 102, 103, 106, 107, 108, 109, 112, 113, 114, 117, 118, 121, 124, 125, 127, 131, 132, 134, 135, 136, 137, 141, 142, 143, 144, 149, 150, 159, 162, 165, 167, 168, 172, 179, 189, 192, 202, 203, 205, 215, 216, 218, 228, 234, 235, 238, 239, 242, 243, 244, 245, 247, 249, 250, 251, 255, 256, 257, 260, 261, 262, 265, 268, 269, 270, 273, 276, 277, 278, 280, 305, 306, 307, 313, 322, 324, 325, 327, 331, 332, 333, 334, 335, 336, 337, 339, 340, 341, 345, 346, 348, 351, 354, 356, 357, 358, 362, 364, 365, 369, 370, 372, 376, 377, 381, 382, 384, 388, 389, 392, 393, 397, 398, 401, 405, 410, 411, 415, 416, 417, 419, 420, 421, 422, 423, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 438, 440, 441, 450, 451, 453, 454, 459, 460, 463, 464, 467, 468, 471, 472, 475, 479, 480, 488, 489, 490, 492, 494, 495, 497, 498, 499, 504, 505, 506, 508, 510, 511, 513, 515, 523, 524, 525, 530, 531, 532, 533, 534, 535, 536, 543, 544, 545, 546, 547, 554, 555, 556], "excluded_lines": [], "functions": {"PerformanceMetric.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [56], "excluded_lines": []}, "Alert.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [80], "excluded_lines": []}, "PerformanceMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 101, 102, 103, 106, 107, 108, 109, 112, 113, 114, 117, 118, 121, 124, 125, 127], "excluded_lines": []}, "PerformanceMonitor.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [131, 132, 134, 135, 136, 137], "excluded_lines": []}, "PerformanceMonitor.stop_monitoring_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [141, 142, 143, 144], "excluded_lines": []}, "PerformanceMonitor.record_metric": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [149, 150, 159, 162, 165, 167, 168], "excluded_lines": []}, "PerformanceMonitor.record_request_time": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [172, 179], "excluded_lines": []}, "PerformanceMonitor.record_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [189, 192], "excluded_lines": []}, "PerformanceMonitor.record_cache_hit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [202, 203, 205], "excluded_lines": []}, "PerformanceMonitor.record_cache_miss": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [215, 216, 218], "excluded_lines": []}, "PerformanceMonitor.get_current_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [228], "excluded_lines": []}, "PerformanceMonitor.get_metrics_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [234, 235, 238, 239, 242, 243, 244, 245, 247, 249, 250, 251], "excluded_lines": []}, "PerformanceMonitor.get_performance_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [255, 256, 257, 260, 261, 262, 265, 268, 269, 270, 273, 276, 277, 278, 280, 305, 306, 307], "excluded_lines": []}, "PerformanceMonitor.add_alert_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [313], "excluded_lines": []}, "PerformanceMonitor.get_alerts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [322, 324, 325, 327], "excluded_lines": []}, "PerformanceMonitor.resolve_alert": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [331, 332, 333, 334, 335, 336, 337, 339, 340, 341], "excluded_lines": []}, "PerformanceMonitor._monitoring_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [345, 346, 348, 351, 354, 356, 357, 358], "excluded_lines": []}, "PerformanceMonitor._collect_system_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [362, 364, 365, 369, 370, 372, 376, 377, 381, 382, 384, 388, 389, 392, 393], "excluded_lines": []}, "PerformanceMonitor._cleanup_old_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [397, 398, 401, 405, 410, 411], "excluded_lines": []}, "PerformanceMonitor._check_alert_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [415, 416, 417, 419, 420, 421, 422, 423, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 438, 440, 441, 450, 451, 453, 454], "excluded_lines": []}, "PerformanceMonitor._init_alert_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [459, 460, 463, 464, 467, 468, 471, 472, 475], "excluded_lines": []}, "PerformanceMonitor.export_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [479, 480, 488, 489, 490, 492, 494, 495, 497, 498, 499], "excluded_lines": []}, "PerformanceMonitor.get_metric_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [504, 505, 506, 508, 510, 511, 513, 515, 523, 524, 525], "excluded_lines": []}, "performance_timer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [530, 556], "excluded_lines": []}, "performance_timer.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [531, 555], "excluded_lines": []}, "performance_timer.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [532, 533, 534, 535, 536, 543, 544, 545, 546, 547, 554], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 92, 93, 95, 129, 139, 146, 170, 187, 200, 213, 226, 230, 253, 309, 320, 329, 343, 360, 395, 413, 456, 477, 501, 528], "summary": {"covered_lines": 66, "num_statements": 66, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MetricType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AlertLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceMetric": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [56], "excluded_lines": []}, "Alert": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [80], "excluded_lines": []}, "PerformanceMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 178, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 178, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 101, 102, 103, 106, 107, 108, 109, 112, 113, 114, 117, 118, 121, 124, 125, 127, 131, 132, 134, 135, 136, 137, 141, 142, 143, 144, 149, 150, 159, 162, 165, 167, 168, 172, 179, 189, 192, 202, 203, 205, 215, 216, 218, 228, 234, 235, 238, 239, 242, 243, 244, 245, 247, 249, 250, 251, 255, 256, 257, 260, 261, 262, 265, 268, 269, 270, 273, 276, 277, 278, 280, 305, 306, 307, 313, 322, 324, 325, 327, 331, 332, 333, 334, 335, 336, 337, 339, 340, 341, 345, 346, 348, 351, 354, 356, 357, 358, 362, 364, 365, 369, 370, 372, 376, 377, 381, 382, 384, 388, 389, 392, 393, 397, 398, 401, 405, 410, 411, 415, 416, 417, 419, 420, 421, 422, 423, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 438, 440, 441, 450, 451, 453, 454, 459, 460, 463, 464, 467, 468, 471, 472, 475, 479, 480, 488, 489, 490, 492, 494, 495, 497, 498, 499, 504, 505, 506, 508, 510, 511, 513, 515, 523, 524, 525], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 92, 93, 95, 129, 139, 146, 170, 187, 200, 213, 226, 230, 253, 309, 320, 329, 343, 360, 395, 413, 456, 477, 501, 528], "summary": {"covered_lines": 66, "num_statements": 81, "percent_covered": 81.48148148148148, "percent_covered_display": "81", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [530, 531, 532, 533, 534, 535, 536, 543, 544, 545, 546, 547, 554, 555, 556], "excluded_lines": []}}}, "src\\hmdm\\optimization\\__init__.py": {"executed_lines": [1, 11, 21, 27], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 21, 27], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 21, 27], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\optimization\\cache_manager.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 60, 66, 67, 69, 74, 89, 118, 126, 131, 135, 147, 148, 150, 157, 173, 203, 211, 218, 222, 238, 244, 252, 253, 255, 288, 321, 342, 361, 382, 410, 414, 430, 439, 444, 469, 470, 472, 479, 502, 522, 535, 547, 568], "summary": {"covered_lines": 71, "num_statements": 344, "percent_covered": 20.63953488372093, "percent_covered_display": "21", "missing_lines": 273, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 62, 63, 70, 71, 72, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 91, 92, 94, 96, 98, 99, 100, 101, 102, 103, 106, 108, 110, 111, 113, 115, 116, 120, 121, 122, 123, 124, 128, 129, 133, 137, 138, 139, 151, 152, 153, 154, 155, 159, 160, 161, 163, 164, 165, 166, 169, 170, 171, 175, 176, 177, 179, 181, 182, 183, 184, 185, 186, 189, 190, 192, 193, 194, 195, 196, 198, 200, 201, 205, 206, 207, 208, 209, 213, 214, 215, 216, 220, 224, 225, 226, 229, 230, 231, 232, 235, 236, 240, 241, 242, 246, 247, 248, 249, 257, 258, 259, 262, 263, 264, 265, 266, 269, 270, 271, 272, 275, 284, 286, 290, 292, 293, 294, 295, 296, 297, 300, 301, 302, 304, 305, 306, 307, 308, 311, 312, 313, 315, 317, 318, 319, 323, 324, 327, 330, 331, 333, 334, 336, 338, 339, 340, 344, 345, 346, 348, 349, 351, 352, 353, 355, 357, 358, 359, 363, 364, 365, 366, 369, 377, 379, 380, 384, 385, 386, 387, 388, 390, 391, 393, 406, 407, 408, 412, 416, 417, 419, 420, 421, 422, 423, 425, 427, 428, 432, 433, 434, 435, 437, 442, 446, 447, 449, 450, 452, 455, 456, 457, 460, 461, 463, 465, 466, 473, 474, 477, 481, 482, 483, 484, 485, 487, 488, 491, 492, 493, 494, 495, 497, 499, 500, 504, 505, 506, 508, 514, 515, 517, 519, 520, 524, 525, 526, 527, 528, 529, 530, 532, 533, 537, 538, 539, 540, 541, 542, 544, 545, 549, 550, 551, 553, 554, 555, 556, 557, 559, 565, 566, 571, 572], "excluded_lines": [], "functions": {"CacheItem.is_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [56, 57, 58], "excluded_lines": []}, "CacheItem.touch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [62, 63], "excluded_lines": []}, "LRUCache.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [70, 71, 72], "excluded_lines": []}, "LRUCache.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [76, 77, 78, 79, 80, 81, 84, 85, 86, 87], "excluded_lines": []}, "LRUCache.put": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [91, 92, 94, 96, 98, 99, 100, 101, 102, 103, 106, 108, 110, 111, 113, 115, 116], "excluded_lines": []}, "LRUCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [120, 121, 122, 123, 124], "excluded_lines": []}, "LRUCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [128, 129], "excluded_lines": []}, "LRUCache.size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [133], "excluded_lines": []}, "LRUCache.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [137, 138, 139], "excluded_lines": []}, "LFUCache.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 154, 155], "excluded_lines": []}, "LFUCache.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [159, 160, 161, 163, 164, 165, 166, 169, 170, 171], "excluded_lines": []}, "LFUCache.put": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [175, 176, 177, 179, 181, 182, 183, 184, 185, 186, 189, 190, 192, 193, 194, 195, 196, 198, 200, 201], "excluded_lines": []}, "LFUCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [205, 206, 207, 208, 209], "excluded_lines": []}, "LFUCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [213, 214, 215, 216], "excluded_lines": []}, "LFUCache.size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [220], "excluded_lines": []}, "LFUCache._update_frequency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [224, 225, 226, 229, 230, 231, 232, 235, 236], "excluded_lines": []}, "LFUCache._evict_lfu": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [240, 241, 242], "excluded_lines": []}, "LFUCache._remove_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [246, 247, 248, 249], "excluded_lines": []}, "CacheManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 262, 263, 264, 265, 266, 269, 270, 271, 272, 275, 284, 286], "excluded_lines": []}, "CacheManager.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [290, 292, 293, 294, 295, 296, 297, 300, 301, 302, 304, 305, 306, 307, 308, 311, 312, 313, 315, 317, 318, 319], "excluded_lines": []}, "CacheManager.put": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [323, 324, 327, 330, 331, 333, 334, 336, 338, 339, 340], "excluded_lines": []}, "CacheManager.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [344, 345, 346, 348, 349, 351, 352, 353, 355, 357, 358, 359], "excluded_lines": []}, "CacheManager.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [363, 364, 365, 366, 369, 377, 379, 380], "excluded_lines": []}, "CacheManager.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [384, 385, 386, 387, 388, 390, 391, 393, 406, 407, 408], "excluded_lines": []}, "CacheManager.add_warmup_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [412], "excluded_lines": []}, "CacheManager.warmup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [416, 417, 419, 420, 421, 422, 423, 425, 427, 428], "excluded_lines": []}, "CacheManager._create_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [432, 433, 434, 435, 437], "excluded_lines": []}, "CacheManager._create_disk_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [442], "excluded_lines": []}, "CacheManager.cache_decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [446, 466], "excluded_lines": []}, "CacheManager.cache_decorator.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [447, 465], "excluded_lines": []}, "CacheManager.cache_decorator.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [449, 450, 452, 455, 456, 457, 460, 461, 463], "excluded_lines": []}, "DiskCache.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [473, 474, 477], "excluded_lines": []}, "DiskCache.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [481, 482, 483, 484, 485, 487, 488, 491, 492, 493, 494, 495, 497, 499, 500], "excluded_lines": []}, "DiskCache.put": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [504, 505, 506, 508, 514, 515, 517, 519, 520], "excluded_lines": []}, "DiskCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [524, 525, 526, 527, 528, 529, 530, 532, 533], "excluded_lines": []}, "DiskCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [537, 538, 539, 540, 541, 542, 544, 545], "excluded_lines": []}, "DiskCache.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [549, 550, 551, 553, 554, 555, 556, 557, 559, 565, 566], "excluded_lines": []}, "DiskCache._get_file_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [571, 572], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 60, 66, 67, 69, 74, 89, 118, 126, 131, 135, 147, 148, 150, 157, 173, 203, 211, 218, 222, 238, 244, 252, 253, 255, 288, 321, 342, 361, 382, 410, 414, 430, 439, 444, 469, 470, 472, 479, 502, 522, 535, 547, 568], "summary": {"covered_lines": 71, "num_statements": 71, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheStrategy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheItem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 62, 63], "excluded_lines": []}, "LRUCache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 41, "excluded_lines": 0}, "missing_lines": [70, 71, 72, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 91, 92, 94, 96, 98, 99, 100, 101, 102, 103, 106, 108, 110, 111, 113, 115, 116, 120, 121, 122, 123, 124, 128, 129, 133, 137, 138, 139], "excluded_lines": []}, "LFUCache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 61, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 154, 155, 159, 160, 161, 163, 164, 165, 166, 169, 170, 171, 175, 176, 177, 179, 181, 182, 183, 184, 185, 186, 189, 190, 192, 193, 194, 195, 196, 198, 200, 201, 205, 206, 207, 208, 209, 213, 214, 215, 216, 220, 224, 225, 226, 229, 230, 231, 232, 235, 236, 240, 241, 242, 246, 247, 248, 249], "excluded_lines": []}, "CacheManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 109, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 109, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 262, 263, 264, 265, 266, 269, 270, 271, 272, 275, 284, 286, 290, 292, 293, 294, 295, 296, 297, 300, 301, 302, 304, 305, 306, 307, 308, 311, 312, 313, 315, 317, 318, 319, 323, 324, 327, 330, 331, 333, 334, 336, 338, 339, 340, 344, 345, 346, 348, 349, 351, 352, 353, 355, 357, 358, 359, 363, 364, 365, 366, 369, 377, 379, 380, 384, 385, 386, 387, 388, 390, 391, 393, 406, 407, 408, 412, 416, 417, 419, 420, 421, 422, 423, 425, 427, 428, 432, 433, 434, 435, 437, 442, 446, 447, 449, 450, 452, 455, 456, 457, 460, 461, 463, 465, 466], "excluded_lines": []}, "DiskCache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 57, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 57, "excluded_lines": 0}, "missing_lines": [473, 474, 477, 481, 482, 483, 484, 485, 487, 488, 491, 492, 493, 494, 495, 497, 499, 500, 504, 505, 506, 508, 514, 515, 517, 519, 520, 524, 525, 526, 527, 528, 529, 530, 532, 533, 537, 538, 539, 540, 541, 542, 544, 545, 549, 550, 551, 553, 554, 555, 556, 557, 559, 565, 566, 571, 572], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 60, 66, 67, 69, 74, 89, 118, 126, 131, 135, 147, 148, 150, 157, 173, 203, 211, 218, 222, 238, 244, 252, 253, 255, 288, 321, 342, 361, 382, 410, 414, 430, 439, 444, 469, 470, 472, 479, 502, 522, 535, 547, 568], "summary": {"covered_lines": 71, "num_statements": 71, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\optimization\\database_pool.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 49, 53, 59, 63, 69, 70, 72, 117, 121, 122, 156, 195, 227, 258, 267, 286, 306, 323, 332, 343, 401, 429], "summary": {"covered_lines": 47, "num_statements": 263, "percent_covered": 17.870722433460077, "percent_covered_display": "18", "missing_lines": 216, "excluded_lines": 0}, "missing_lines": [47, 51, 55, 56, 57, 61, 65, 66, 74, 75, 76, 79, 80, 81, 82, 83, 84, 85, 88, 89, 90, 91, 94, 106, 107, 110, 113, 115, 119, 124, 125, 127, 129, 131, 132, 133, 139, 141, 142, 143, 145, 146, 148, 149, 153, 154, 158, 159, 161, 163, 164, 167, 168, 170, 171, 172, 173, 174, 176, 179, 180, 182, 184, 185, 187, 189, 190, 191, 192, 193, 197, 198, 199, 200, 201, 204, 208, 209, 212, 214, 215, 216, 217, 219, 221, 223, 224, 225, 229, 230, 231, 232, 235, 237, 242, 244, 245, 246, 247, 248, 250, 251, 253, 254, 255, 256, 261, 262, 265, 269, 270, 271, 273, 275, 276, 277, 278, 279, 281, 283, 284, 288, 289, 290, 292, 293, 296, 297, 298, 299, 301, 303, 304, 308, 309, 310, 311, 312, 314, 315, 316, 317, 318, 320, 321, 325, 326, 328, 329, 330, 334, 335, 336, 337, 339, 340, 341, 345, 347, 350, 351, 352, 353, 354, 355, 358, 359, 360, 363, 364, 365, 367, 370, 371, 372, 373, 374, 375, 376, 379, 380, 381, 382, 383, 384, 385, 386, 388, 389, 390, 391, 392, 394, 395, 396, 398, 399, 403, 404, 407, 431, 433, 434, 435, 438, 439, 440, 443, 444, 445, 446, 447, 448, 450, 452, 453], "excluded_lines": [], "functions": {"ConnectionInfo.is_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [47], "excluded_lines": []}, "ConnectionInfo.is_idle_timeout": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [51], "excluded_lines": []}, "ConnectionInfo.mark_used": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [55, 56, 57], "excluded_lines": []}, "ConnectionInfo.mark_idle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "ConnectionInfo.mark_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [65, 66], "excluded_lines": []}, "DatabaseConnectionPool.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 79, 80, 81, 82, 83, 84, 85, 88, 89, 90, 91, 94, 106, 107, 110, 113, 115], "excluded_lines": []}, "DatabaseConnectionPool.get_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": []}, "DatabaseConnectionPool._connection_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [124, 125, 127, 129, 131, 132, 133, 139, 141, 142, 143, 145, 146, 148, 149, 153, 154], "excluded_lines": []}, "DatabaseConnectionPool._acquire_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [158, 159, 161, 163, 164, 167, 168, 170, 171, 172, 173, 174, 176, 179, 180, 182, 184, 185, 187, 189, 190, 191, 192, 193], "excluded_lines": []}, "DatabaseConnectionPool._release_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [197, 198, 199, 200, 201, 204, 208, 209, 212, 214, 215, 216, 217, 219, 221, 223, 224, 225], "excluded_lines": []}, "DatabaseConnectionPool._create_new_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [229, 230, 231, 232, 235, 237, 242, 244, 245, 246, 247, 248, 250, 251, 253, 254, 255, 256], "excluded_lines": []}, "DatabaseConnectionPool._create_database_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [261, 262, 265], "excluded_lines": []}, "DatabaseConnectionPool._close_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [269, 270, 271, 273, 275, 276, 277, 278, 279, 281, 283, 284], "excluded_lines": []}, "DatabaseConnectionPool._is_connection_healthy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [288, 289, 290, 292, 293, 296, 297, 298, 299, 301, 303, 304], "excluded_lines": []}, "DatabaseConnectionPool._initialize_pool": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [308, 309, 310, 311, 312, 314, 315, 316, 317, 318, 320, 321], "excluded_lines": []}, "DatabaseConnectionPool._start_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [325, 326, 328, 329, 330], "excluded_lines": []}, "DatabaseConnectionPool._health_check_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [334, 335, 336, 337, 339, 340, 341], "excluded_lines": []}, "DatabaseConnectionPool._perform_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 40, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [345, 347, 350, 351, 352, 353, 354, 355, 358, 359, 360, 363, 364, 365, 367, 370, 371, 372, 373, 374, 375, 376, 379, 380, 381, 382, 383, 384, 385, 386, 388, 389, 390, 391, 392, 394, 395, 396, 398, 399], "excluded_lines": []}, "DatabaseConnectionPool.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [403, 404, 407], "excluded_lines": []}, "DatabaseConnectionPool.close_pool": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [431, 433, 434, 435, 438, 439, 440, 443, 444, 445, 446, 447, 448, 450, 452, 453], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 49, 53, 59, 63, 69, 70, 72, 117, 121, 122, 156, 195, 227, 258, 267, 286, 306, 323, 332, 343, 401, 429], "summary": {"covered_lines": 47, "num_statements": 47, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConnectionState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [47, 51, 55, 56, 57, 61, 65, 66], "excluded_lines": []}, "DatabaseConnectionPool": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 208, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 208, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 79, 80, 81, 82, 83, 84, 85, 88, 89, 90, 91, 94, 106, 107, 110, 113, 115, 119, 124, 125, 127, 129, 131, 132, 133, 139, 141, 142, 143, 145, 146, 148, 149, 153, 154, 158, 159, 161, 163, 164, 167, 168, 170, 171, 172, 173, 174, 176, 179, 180, 182, 184, 185, 187, 189, 190, 191, 192, 193, 197, 198, 199, 200, 201, 204, 208, 209, 212, 214, 215, 216, 217, 219, 221, 223, 224, 225, 229, 230, 231, 232, 235, 237, 242, 244, 245, 246, 247, 248, 250, 251, 253, 254, 255, 256, 261, 262, 265, 269, 270, 271, 273, 275, 276, 277, 278, 279, 281, 283, 284, 288, 289, 290, 292, 293, 296, 297, 298, 299, 301, 303, 304, 308, 309, 310, 311, 312, 314, 315, 316, 317, 318, 320, 321, 325, 326, 328, 329, 330, 334, 335, 336, 337, 339, 340, 341, 345, 347, 350, 351, 352, 353, 354, 355, 358, 359, 360, 363, 364, 365, 367, 370, 371, 372, 373, 374, 375, 376, 379, 380, 381, 382, 383, 384, 385, 386, 388, 389, 390, 391, 392, 394, 395, 396, 398, 399, 403, 404, 407, 431, 433, 434, 435, 438, 439, 440, 443, 444, 445, 446, 447, 448, 450, 452, 453], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 28, 29, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 49, 53, 59, 63, 69, 70, 72, 117, 121, 122, 156, 195, 227, 258, 267, 286, 306, 323, 332, 343, 401, 429], "summary": {"covered_lines": 47, "num_statements": 47, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\scenarios\\__init__.py": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 19], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\scenarios\\decision_support_scenarios.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 20, 21, 22, 23, 24, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 85, 105, 139, 159, 192, 246, 312, 316, 329, 369, 413, 435, 446, 457, 469, 482, 493, 504, 527, 537, 564, 577, 588, 613, 634, 657, 682, 720, 733, 769, 780, 787, 803, 809, 838, 869, 893, 941, 970], "summary": {"covered_lines": 87, "num_statements": 381, "percent_covered": 22.834645669291337, "percent_covered_display": "23", "missing_lines": 294, "excluded_lines": 0}, "missing_lines": [60, 66, 86, 89, 90, 91, 92, 93, 94, 97, 100, 103, 109, 110, 119, 122, 125, 128, 130, 131, 133, 135, 136, 137, 141, 142, 152, 153, 155, 156, 157, 163, 164, 167, 168, 171, 172, 175, 176, 179, 180, 183, 185, 186, 188, 189, 190, 196, 197, 208, 209, 210, 213, 214, 217, 218, 220, 222, 225, 228, 231, 234, 237, 239, 240, 242, 243, 244, 248, 249, 261, 262, 270, 271, 274, 275, 278, 279, 282, 293, 294, 295, 298, 300, 301, 303, 304, 305, 314, 318, 319, 320, 327, 331, 332, 334, 343, 344, 346, 347, 348, 349, 352, 353, 356, 357, 360, 361, 363, 364, 365, 367, 372, 383, 390, 393, 404, 411, 415, 418, 419, 420, 421, 422, 423, 424, 425, 426, 428, 429, 432, 433, 438, 448, 460, 462, 463, 464, 465, 467, 471, 484, 486, 495, 497, 506, 507, 510, 511, 512, 513, 514, 515, 516, 518, 529, 539, 542, 543, 544, 545, 546, 547, 548, 549, 550, 552, 553, 555, 566, 567, 569, 579, 581, 590, 593, 594, 596, 597, 609, 611, 615, 617, 618, 630, 632, 636, 638, 640, 653, 655, 659, 662, 664, 665, 678, 680, 684, 685, 688, 689, 690, 691, 692, 693, 695, 696, 697, 698, 700, 701, 704, 705, 707, 708, 709, 713, 715, 716, 718, 722, 723, 725, 726, 728, 729, 731, 735, 743, 744, 745, 748, 756, 759, 762, 765, 767, 771, 772, 775, 776, 778, 782, 783, 785, 789, 790, 792, 795, 796, 797, 798, 799, 801, 807, 811, 812, 815, 822, 824, 840, 842, 871, 895, 898, 912, 926, 939, 943, 946, 947, 948, 949, 952, 953, 954, 955, 958, 959, 960, 963, 964, 965, 966, 968, 972, 975, 982, 983, 986, 987, 988, 991, 993], "excluded_lines": [], "functions": {"DecisionProblem.get_time_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [60, 66], "excluded_lines": []}, "DecisionSupportScenarios.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [86, 89, 90, 91, 92, 93, 94, 97, 100, 103], "excluded_lines": []}, "DecisionSupportScenarios.create_decision_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [109, 110, 119, 122, 125, 128, 130, 131, 133, 135, 136, 137], "excluded_lines": []}, "DecisionSupportScenarios.analyze_decision_problem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [141, 142, 152, 153, 155, 156, 157], "excluded_lines": []}, "DecisionSupportScenarios.generate_decision_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [163, 164, 167, 168, 171, 172, 175, 176, 179, 180, 183, 185, 186, 188, 189, 190], "excluded_lines": []}, "DecisionSupportScenarios.provide_decision_support": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [196, 197, 208, 209, 210, 213, 214, 217, 218, 220, 222, 225, 228, 231, 234, 237, 239, 240, 242, 243, 244], "excluded_lines": []}, "DecisionSupportScenarios.execute_decision_workflow": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [248, 249, 261, 262, 270, 271, 274, 275, 278, 279, 282, 293, 294, 295, 298, 300, 301, 303, 304, 305], "excluded_lines": []}, "DecisionSupportScenarios.get_scenario_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [314], "excluded_lines": []}, "DecisionSupportScenarios.list_scenario_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [318, 319, 320, 327], "excluded_lines": []}, "DecisionSupportScenarios.get_decision_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [331, 332, 334, 343, 344, 346, 347, 348, 349, 352, 353, 356, 357, 360, 361, 363, 364, 365, 367], "excluded_lines": []}, "DecisionSupportScenarios._initialize_scenario_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 383, 390, 393, 404, 411], "excluded_lines": []}, "DecisionSupportScenarios._configure_scenario_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [415, 418, 419, 420, 421, 422, 423, 424, 425, 426, 428, 429, 432, 433], "excluded_lines": []}, "DecisionSupportScenarios._gather_available_resources": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [438], "excluded_lines": []}, "DecisionSupportScenarios._analyze_environmental_factors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [448], "excluded_lines": []}, "DecisionSupportScenarios._find_historical_cases": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [460, 462, 463, 464, 465, 467], "excluded_lines": []}, "DecisionSupportScenarios._analyze_problem_structure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [471], "excluded_lines": []}, "DecisionSupportScenarios._analyze_stakeholders": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [484, 486], "excluded_lines": []}, "DecisionSupportScenarios._analyze_constraints": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [495, 497], "excluded_lines": []}, "DecisionSupportScenarios._assess_risks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [506, 507, 510, 511, 512, 513, 514, 515, 516, 518], "excluded_lines": []}, "DecisionSupportScenarios._analyze_opportunities": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [529], "excluded_lines": []}, "DecisionSupportScenarios._analyze_resource_requirements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [539, 542, 543, 544, 545, 546, 547, 548, 549, 550, 552, 553, 555], "excluded_lines": []}, "DecisionSupportScenarios._analyze_time_sensitivity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [566, 567, 569], "excluded_lines": []}, "DecisionSupportScenarios._check_resource_availability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [579, 581], "excluded_lines": []}, "DecisionSupportScenarios._generate_knowledge_based_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [590, 593, 594, 596, 597, 609, 611], "excluded_lines": []}, "DecisionSupportScenarios._generate_historical_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [615, 617, 618, 630, 632], "excluded_lines": []}, "DecisionSupportScenarios._generate_simulation_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [636, 638, 640, 653, 655], "excluded_lines": []}, "DecisionSupportScenarios._generate_expert_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [659, 662, 664, 665, 678, 680], "excluded_lines": []}, "DecisionSupportScenarios._optimize_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [684, 685, 688, 689, 690, 691, 692, 693, 695, 696, 697, 698, 700, 701, 704, 705, 707, 708, 709, 713, 715, 716, 718], "excluded_lines": []}, "DecisionSupportScenarios._calculate_similarity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [722, 723, 725, 726, 728, 729, 731], "excluded_lines": []}, "DecisionSupportScenarios._evaluate_alternatives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [735, 743, 744, 745, 748, 756, 759, 762, 765, 767], "excluded_lines": []}, "DecisionSupportScenarios._calculate_resource_efficiency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [771, 772, 775, 776, 778], "excluded_lines": []}, "DecisionSupportScenarios._calculate_time_efficiency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [782, 783, 785], "excluded_lines": []}, "DecisionSupportScenarios._select_best_alternative": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [789, 790, 792, 795, 796, 797, 798, 799, 801], "excluded_lines": []}, "DecisionSupportScenarios._get_all_alternatives_from_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [807], "excluded_lines": []}, "DecisionSupportScenarios._analyze_alternative_risks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [811, 812, 815, 822, 824], "excluded_lines": []}, "DecisionSupportScenarios._create_implementation_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [840, 842], "excluded_lines": []}, "DecisionSupportScenarios._create_monitoring_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [871], "excluded_lines": []}, "DecisionSupportScenarios._create_contingency_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [895, 898, 912, 926, 939], "excluded_lines": []}, "DecisionSupportScenarios._get_expert_advice": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [943, 946, 947, 948, 949, 952, 953, 954, 955, 958, 959, 960, 963, 964, 965, 966, 968], "excluded_lines": []}, "DecisionSupportScenarios._assess_confidence": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [972, 975, 982, 983, 986, 987, 988, 991, 993], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 20, 21, 22, 23, 24, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 85, 105, 139, 159, 192, 246, 312, 316, 329, 369, 413, 435, 446, 457, 469, 482, 493, 504, 527, 537, 564, 577, 588, 613, 634, 657, 682, 720, 733, 769, 780, 787, 803, 809, 838, 869, 893, 941, 970], "summary": {"covered_lines": 87, "num_statements": 87, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DecisionComplexity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DecisionUrgency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DecisionProblem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [60, 66], "excluded_lines": []}, "DecisionScenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DecisionSupportScenarios": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 292, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 292, "excluded_lines": 0}, "missing_lines": [86, 89, 90, 91, 92, 93, 94, 97, 100, 103, 109, 110, 119, 122, 125, 128, 130, 131, 133, 135, 136, 137, 141, 142, 152, 153, 155, 156, 157, 163, 164, 167, 168, 171, 172, 175, 176, 179, 180, 183, 185, 186, 188, 189, 190, 196, 197, 208, 209, 210, 213, 214, 217, 218, 220, 222, 225, 228, 231, 234, 237, 239, 240, 242, 243, 244, 248, 249, 261, 262, 270, 271, 274, 275, 278, 279, 282, 293, 294, 295, 298, 300, 301, 303, 304, 305, 314, 318, 319, 320, 327, 331, 332, 334, 343, 344, 346, 347, 348, 349, 352, 353, 356, 357, 360, 361, 363, 364, 365, 367, 372, 383, 390, 393, 404, 411, 415, 418, 419, 420, 421, 422, 423, 424, 425, 426, 428, 429, 432, 433, 438, 448, 460, 462, 463, 464, 465, 467, 471, 484, 486, 495, 497, 506, 507, 510, 511, 512, 513, 514, 515, 516, 518, 529, 539, 542, 543, 544, 545, 546, 547, 548, 549, 550, 552, 553, 555, 566, 567, 569, 579, 581, 590, 593, 594, 596, 597, 609, 611, 615, 617, 618, 630, 632, 636, 638, 640, 653, 655, 659, 662, 664, 665, 678, 680, 684, 685, 688, 689, 690, 691, 692, 693, 695, 696, 697, 698, 700, 701, 704, 705, 707, 708, 709, 713, 715, 716, 718, 722, 723, 725, 726, 728, 729, 731, 735, 743, 744, 745, 748, 756, 759, 762, 765, 767, 771, 772, 775, 776, 778, 782, 783, 785, 789, 790, 792, 795, 796, 797, 798, 799, 801, 807, 811, 812, 815, 822, 824, 840, 842, 871, 895, 898, 912, 926, 939, 943, 946, 947, 948, 949, 952, 953, 954, 955, 958, 959, 960, 963, 964, 965, 966, 968, 972, 975, 982, 983, 986, 987, 988, 991, 993], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 20, 21, 22, 23, 24, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 85, 105, 139, 159, 192, 246, 312, 316, 329, 369, 413, 435, 446, 457, 469, 482, 493, 504, 527, 537, 564, 577, 588, 613, 634, 657, 682, 720, 733, 769, 780, 787, 803, 809, 838, 869, 893, 941, 970], "summary": {"covered_lines": 87, "num_statements": 87, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\security\\__init__.py": {"executed_lines": [1, 15, 24, 33, 35, 41], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 15, 24, 33, 35, 41], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 15, 24, 33, 35, 41], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\security\\enhanced_security.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 109, 127, 128, 130, 162, 184, 199, 249, 337, 361, 408, 420, 433, 456, 503, 534, 589, 602, 612, 619, 626, 639, 669], "summary": {"covered_lines": 92, "num_statements": 317, "percent_covered": 29.022082018927446, "percent_covered_display": "29", "missing_lines": 225, "excluded_lines": 0}, "missing_lines": [79, 111, 131, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 150, 151, 152, 155, 158, 160, 164, 166, 168, 169, 170, 172, 173, 174, 175, 177, 178, 180, 181, 182, 186, 187, 189, 190, 197, 204, 206, 207, 210, 211, 214, 215, 216, 219, 229, 232, 242, 243, 245, 246, 247, 252, 254, 255, 256, 257, 258, 260, 261, 272, 275, 276, 287, 290, 291, 292, 293, 294, 296, 307, 310, 311, 312, 313, 314, 316, 318, 330, 331, 333, 334, 335, 339, 340, 341, 343, 344, 346, 347, 348, 351, 352, 353, 355, 357, 358, 359, 365, 367, 368, 371, 372, 384, 387, 388, 400, 402, 404, 405, 406, 410, 411, 413, 414, 415, 416, 417, 418, 422, 423, 425, 426, 427, 428, 429, 430, 431, 435, 436, 437, 438, 449, 450, 452, 453, 454, 458, 459, 460, 461, 464, 465, 474, 477, 478, 481, 482, 484, 485, 487, 496, 497, 499, 500, 501, 509, 510, 513, 514, 515, 516, 519, 520, 523, 524, 527, 528, 530, 531, 532, 536, 537, 538, 539, 542, 543, 544, 545, 548, 549, 550, 553, 555, 559, 560, 561, 562, 564, 585, 586, 587, 591, 600, 604, 605, 606, 607, 608, 609, 610, 614, 621, 628, 629, 632, 633, 634, 635, 637, 645, 646, 661, 664, 666, 667, 671, 672, 674, 675, 676, 678, 679], "excluded_lines": [], "functions": {"User.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [79], "excluded_lines": []}, "AuditEvent.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [111], "excluded_lines": []}, "EnhancedSecurityManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [131, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 150, 151, 152, 155, 158, 160], "excluded_lines": []}, "EnhancedSecurityManager._init_encryption": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [164, 166, 168, 169, 170, 172, 173, 174, 175, 177, 178, 180, 181, 182], "excluded_lines": []}, "EnhancedSecurityManager._init_default_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [186, 187, 189, 190, 197], "excluded_lines": []}, "EnhancedSecurityManager.create_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [204, 206, 207, 210, 211, 214, 215, 216, 219, 229, 232, 242, 243, 245, 246, 247], "excluded_lines": []}, "EnhancedSecurityManager.authenticate_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [252, 254, 255, 256, 257, 258, 260, 261, 272, 275, 276, 287, 290, 291, 292, 293, 294, 296, 307, 310, 311, 312, 313, 314, 316, 318, 330, 331, 333, 334, 335], "excluded_lines": []}, "EnhancedSecurityManager.validate_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [339, 340, 341, 343, 344, 346, 347, 348, 351, 352, 353, 355, 357, 358, 359], "excluded_lines": []}, "EnhancedSecurityManager.check_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [365, 367, 368, 371, 372, 384, 387, 388, 400, 402, 404, 405, 406], "excluded_lines": []}, "EnhancedSecurityManager.encrypt_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [410, 411, 413, 414, 415, 416, 417, 418], "excluded_lines": []}, "EnhancedSecurityManager.decrypt_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [422, 423, 425, 426, 427, 428, 429, 430, 431], "excluded_lines": []}, "EnhancedSecurityManager.logout_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [435, 436, 437, 438, 449, 450, 452, 453, 454], "excluded_lines": []}, "EnhancedSecurityManager.change_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [458, 459, 460, 461, 464, 465, 474, 477, 478, 481, 482, 484, 485, 487, 496, 497, 499, 500, 501], "excluded_lines": []}, "EnhancedSecurityManager.get_audit_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [509, 510, 513, 514, 515, 516, 519, 520, 523, 524, 527, 528, 530, 531, 532], "excluded_lines": []}, "EnhancedSecurityManager.get_security_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [536, 537, 538, 539, 542, 543, 544, 545, 548, 549, 550, 553, 555, 559, 560, 561, 562, 564, 585, 586, 587], "excluded_lines": []}, "EnhancedSecurityManager._generate_session_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [591, 600], "excluded_lines": []}, "EnhancedSecurityManager._invalidate_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [604, 605, 606, 607, 608, 609, 610], "excluded_lines": []}, "EnhancedSecurityManager._hash_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [614], "excluded_lines": []}, "EnhancedSecurityManager._verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [621], "excluded_lines": []}, "EnhancedSecurityManager._validate_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [628, 629, 632, 633, 634, 635, 637], "excluded_lines": []}, "EnhancedSecurityManager._log_audit_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [645, 646, 661, 664, 666, 667], "excluded_lines": []}, "EnhancedSecurityManager._write_audit_log": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [671, 672, 674, 675, 676, 678, 679], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 109, 127, 128, 130, 162, 184, 199, 249, 337, 361, 408, 420, 433, 456, 503, 534, 589, 602, 612, 619, 626, 639, 669], "summary": {"covered_lines": 92, "num_statements": 92, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"AuthenticationMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PermissionType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuditEventType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [79], "excluded_lines": []}, "AuditEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [111], "excluded_lines": []}, "EnhancedSecurityManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 223, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 223, "excluded_lines": 0}, "missing_lines": [131, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 150, 151, 152, 155, 158, 160, 164, 166, 168, 169, 170, 172, 173, 174, 175, 177, 178, 180, 181, 182, 186, 187, 189, 190, 197, 204, 206, 207, 210, 211, 214, 215, 216, 219, 229, 232, 242, 243, 245, 246, 247, 252, 254, 255, 256, 257, 258, 260, 261, 272, 275, 276, 287, 290, 291, 292, 293, 294, 296, 307, 310, 311, 312, 313, 314, 316, 318, 330, 331, 333, 334, 335, 339, 340, 341, 343, 344, 346, 347, 348, 351, 352, 353, 355, 357, 358, 359, 365, 367, 368, 371, 372, 384, 387, 388, 400, 402, 404, 405, 406, 410, 411, 413, 414, 415, 416, 417, 418, 422, 423, 425, 426, 427, 428, 429, 430, 431, 435, 436, 437, 438, 449, 450, 452, 453, 454, 458, 459, 460, 461, 464, 465, 474, 477, 478, 481, 482, 484, 485, 487, 496, 497, 499, 500, 501, 509, 510, 513, 514, 515, 516, 519, 520, 523, 524, 527, 528, 530, 531, 532, 536, 537, 538, 539, 542, 543, 544, 545, 548, 549, 550, 553, 555, 559, 560, 561, 562, 564, 585, 586, 587, 591, 600, 604, 605, 606, 607, 608, 609, 610, 614, 621, 628, 629, 632, 633, 634, 635, 637, 645, 646, 661, 664, 666, 667, 671, 672, 674, 675, 676, 678, 679], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 109, 127, 128, 130, 162, 184, 199, 249, 337, 361, 408, 420, 433, 456, 503, 534, 589, 602, 612, 619, 626, 639, 669], "summary": {"covered_lines": 92, "num_statements": 92, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\security\\military_security.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 66, 70, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97, 114, 115, 117, 142, 195, 217, 272, 286, 309, 325, 361, 373, 379, 394, 400], "summary": {"covered_lines": 73, "num_statements": 178, "percent_covered": 41.01123595505618, "percent_covered_display": "41", "missing_lines": 105, "excluded_lines": 0}, "missing_lines": [64, 68, 72, 79, 99, 118, 119, 120, 121, 122, 125, 134, 146, 147, 153, 156, 158, 168, 171, 180, 181, 183, 186, 187, 193, 197, 198, 200, 201, 203, 204, 211, 214, 215, 221, 222, 223, 230, 233, 234, 244, 247, 248, 257, 260, 270, 274, 276, 277, 278, 283, 284, 288, 289, 290, 291, 292, 294, 295, 296, 302, 303, 304, 305, 306, 307, 311, 312, 313, 315, 322, 323, 330, 331, 332, 334, 335, 337, 338, 339, 340, 343, 344, 346, 349, 359, 365, 371, 375, 376, 377, 381, 382, 385, 386, 392, 396, 397, 398, 405, 415, 418, 419, 422, 423], "excluded_lines": [], "functions": {"SecurityCredential.is_valid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "SecurityCredential.has_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [68], "excluded_lines": []}, "SecurityCredential.can_access_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [72, 79], "excluded_lines": []}, "AuditLog.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [99], "excluded_lines": []}, "MilitarySecurityManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [118, 119, 120, 121, 122, 125, 134], "excluded_lines": []}, "MilitarySecurityManager.authenticate_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [146, 147, 153, 156, 158, 168, 171, 180, 181, 183, 186, 187, 193], "excluded_lines": []}, "MilitarySecurityManager.validate_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [197, 198, 200, 201, 203, 204, 211, 214, 215], "excluded_lines": []}, "MilitarySecurityManager.check_access_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [221, 222, 223, 230, 233, 234, 244, 247, 248, 257, 260, 270], "excluded_lines": []}, "MilitarySecurityManager.encrypt_sensitive_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [274, 276, 277, 278, 283, 284], "excluded_lines": []}, "MilitarySecurityManager.decrypt_sensitive_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [288, 289, 290, 291, 292, 294, 295, 296, 302, 303, 304, 305, 306, 307], "excluded_lines": []}, "MilitarySecurityManager.logout_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [311, 312, 313, 315, 322, 323], "excluded_lines": []}, "MilitarySecurityManager.get_audit_logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [330, 331, 332, 334, 335, 337, 338, 339, 340, 343, 344, 346, 349, 359], "excluded_lines": []}, "MilitarySecurityManager._verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [365, 371], "excluded_lines": []}, "MilitarySecurityManager._generate_session_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [375, 376, 377], "excluded_lines": []}, "MilitarySecurityManager._is_account_locked": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [381, 382, 385, 386, 392], "excluded_lines": []}, "MilitarySecurityManager._record_failed_attempt": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [396, 397, 398], "excluded_lines": []}, "MilitarySecurityManager._log_audit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [405, 415, 418, 419, 422, 423], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 66, 70, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97, 114, 115, 117, 142, 195, 217, 272, 286, 309, 325, 361, 373, 379, 394, 400], "summary": {"covered_lines": 73, "num_statements": 73, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecurityLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OperationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityCredential": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [64, 68, 72, 79], "excluded_lines": []}, "AuditLog": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [99], "excluded_lines": []}, "MilitarySecurityManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 100, "excluded_lines": 0}, "missing_lines": [118, 119, 120, 121, 122, 125, 134, 146, 147, 153, 156, 158, 168, 171, 180, 181, 183, 186, 187, 193, 197, 198, 200, 201, 203, 204, 211, 214, 215, 221, 222, 223, 230, 233, 234, 244, 247, 248, 257, 260, 270, 274, 276, 277, 278, 283, 284, 288, 289, 290, 291, 292, 294, 295, 296, 302, 303, 304, 305, 306, 307, 311, 312, 313, 315, 322, 323, 330, 331, 332, 334, 335, 337, 338, 339, 340, 343, 344, 346, 349, 359, 365, 371, 375, 376, 377, 381, 382, 385, 386, 392, 396, 397, 398, 405, 415, 418, 419, 422, 423], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 66, 70, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 97, 114, 115, 117, 142, 195, 217, 272, 286, 309, 325, 361, 373, 379, 394, 400], "summary": {"covered_lines": 73, "num_statements": 73, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\security\\security_config.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 17, 18, 19, 22, 23, 24, 25, 26, 29, 30, 31, 34, 35, 36, 39, 40, 41, 44, 45, 46, 47, 50, 51, 52, 55, 56, 57, 59, 71, 72, 75, 76, 78, 90, 91, 93, 96, 97, 99, 100, 101, 102, 105, 107, 109, 110, 111, 112, 113, 123, 133, 137, 167, 184, 188, 193, 229, 277, 307, 317], "summary": {"covered_lines": 65, "num_statements": 166, "percent_covered": 39.1566265060241, "percent_covered_display": "39", "missing_lines": 101, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 68, 69, 116, 117, 118, 119, 120, 121, 125, 126, 127, 128, 129, 130, 131, 135, 139, 141, 142, 145, 146, 148, 151, 154, 155, 158, 160, 161, 163, 164, 165, 169, 170, 171, 172, 175, 177, 178, 180, 181, 182, 186, 190, 191, 195, 197, 231, 232, 233, 236, 237, 239, 240, 243, 244, 247, 248, 251, 252, 254, 255, 258, 259, 261, 262, 265, 266, 267, 269, 279, 281, 282, 283, 284, 286, 287, 288, 289, 291, 292, 293, 294, 296, 297, 298, 299, 301, 303, 304, 305, 309, 310, 311, 312, 313], "excluded_lines": [], "functions": {"SecurityConfig.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 68, 69], "excluded_lines": []}, "SecurityConfig.from_dict": {"executed_lines": [75, 76, 78, 90, 91, 93], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfigManager.__init__": {"executed_lines": [100, 101, 102, 105], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityConfigManager._load_config": {"executed_lines": [109, 110, 111, 112, 113], "summary": {"covered_lines": 5, "num_statements": 11, "percent_covered": 45.45454545454545, "percent_covered_display": "45", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [116, 117, 118, 119, 120, 121], "excluded_lines": []}, "SecurityConfigManager._save_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 128, 129, 130, 131], "excluded_lines": []}, "SecurityConfigManager.get_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [135], "excluded_lines": []}, "SecurityConfigManager.update_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [139, 141, 142, 145, 146, 148, 151, 154, 155, 158, 160, 161, 163, 164, 165], "excluded_lines": []}, "SecurityConfigManager.reset_to_defaults": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [169, 170, 171, 172, 175, 177, 178, 180, 181, 182], "excluded_lines": []}, "SecurityConfigManager.add_change_listener": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [186], "excluded_lines": []}, "SecurityConfigManager.remove_change_listener": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [190, 191], "excluded_lines": []}, "SecurityConfigManager.get_security_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [195, 197], "excluded_lines": []}, "SecurityConfigManager.validate_security_compliance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [231, 232, 233, 236, 237, 239, 240, 243, 244, 247, 248, 251, 252, 254, 255, 258, 259, 261, 262, 265, 266, 267, 269], "excluded_lines": []}, "SecurityConfigManager._validate_config_updates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [279, 281, 282, 283, 284, 286, 287, 288, 289, 291, 292, 293, 294, 296, 297, 298, 299, 301, 303, 304, 305], "excluded_lines": []}, "SecurityConfigManager._notify_config_change": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [309, 310, 311, 312, 313], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 17, 18, 19, 22, 23, 24, 25, 26, 29, 30, 31, 34, 35, 36, 39, 40, 41, 44, 45, 46, 47, 50, 51, 52, 55, 56, 57, 59, 71, 72, 96, 97, 99, 107, 123, 133, 137, 167, 184, 188, 193, 229, 277, 307, 317], "summary": {"covered_lines": 50, "num_statements": 50, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecurityConfig": {"executed_lines": [75, 76, 78, 90, 91, 93], "summary": {"covered_lines": 6, "num_statements": 14, "percent_covered": 42.857142857142854, "percent_covered_display": "43", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 68, 69], "excluded_lines": []}, "SecurityConfigManager": {"executed_lines": [100, 101, 102, 105, 109, 110, 111, 112, 113], "summary": {"covered_lines": 9, "num_statements": 102, "percent_covered": 8.823529411764707, "percent_covered_display": "9", "missing_lines": 93, "excluded_lines": 0}, "missing_lines": [116, 117, 118, 119, 120, 121, 125, 126, 127, 128, 129, 130, 131, 135, 139, 141, 142, 145, 146, 148, 151, 154, 155, 158, 160, 161, 163, 164, 165, 169, 170, 171, 172, 175, 177, 178, 180, 181, 182, 186, 190, 191, 195, 197, 231, 232, 233, 236, 237, 239, 240, 243, 244, 247, 248, 251, 252, 254, 255, 258, 259, 261, 262, 265, 266, 267, 269, 279, 281, 282, 283, 284, 286, 287, 288, 289, 291, 292, 293, 294, 296, 297, 298, 299, 301, 303, 304, 305, 309, 310, 311, 312, 313], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 14, 17, 18, 19, 22, 23, 24, 25, 26, 29, 30, 31, 34, 35, 36, 39, 40, 41, 44, 45, 46, 47, 50, 51, 52, 55, 56, 57, 59, 71, 72, 96, 97, 99, 107, 123, 133, 137, 167, 184, 188, 193, 229, 277, 307, 317], "summary": {"covered_lines": 50, "num_statements": 50, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\security\\web_security_middleware.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 17, 18, 20, 24, 73, 80, 87, 91, 107, 163, 182, 241, 256, 266, 283], "summary": {"covered_lines": 21, "num_statements": 118, "percent_covered": 17.796610169491526, "percent_covered_display": "18", "missing_lines": 97, "excluded_lines": 0}, "missing_lines": [21, 22, 27, 28, 29, 31, 32, 33, 40, 41, 42, 49, 50, 51, 55, 56, 63, 66, 68, 70, 71, 75, 82, 89, 93, 94, 95, 97, 102, 104, 105, 109, 110, 111, 112, 113, 115, 116, 119, 120, 136, 138, 140, 141, 158, 160, 161, 165, 166, 167, 170, 171, 172, 173, 174, 175, 176, 178, 180, 184, 185, 186, 188, 189, 191, 194, 195, 196, 202, 203, 206, 207, 208, 209, 214, 215, 222, 223, 229, 230, 236, 238, 239, 244, 245, 246, 249, 250, 251, 254, 259, 260, 261, 262, 264, 268, 286], "excluded_lines": [], "functions": {"WebSecurityMiddleware.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [21, 22], "excluded_lines": []}, "WebSecurityMiddleware.require_auth": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [27, 28, 71], "excluded_lines": []}, "WebSecurityMiddleware.require_auth.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [29, 70], "excluded_lines": []}, "WebSecurityMiddleware.require_auth.decorator.decorated_function": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [31, 32, 33, 40, 41, 42, 49, 50, 51, 55, 56, 63, 66, 68], "excluded_lines": []}, "WebSecurityMiddleware.require_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [75], "excluded_lines": []}, "WebSecurityMiddleware.require_audit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [82], "excluded_lines": []}, "WebSecurityMiddleware.login_required": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [89], "excluded_lines": []}, "WebSecurityMiddleware.rate_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [93, 94, 105], "excluded_lines": []}, "WebSecurityMiddleware.rate_limit.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [95, 104], "excluded_lines": []}, "WebSecurityMiddleware.rate_limit.decorator.decorated_function": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [97, 102], "excluded_lines": []}, "WebSecurityMiddleware.audit_action": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [109, 110, 161], "excluded_lines": []}, "WebSecurityMiddleware.audit_action.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [111, 160], "excluded_lines": []}, "WebSecurityMiddleware.audit_action.decorator.decorated_function": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [112, 113, 115, 116, 119, 120, 136, 138, 140, 141, 158], "excluded_lines": []}, "WebSecurityMiddleware.secure_headers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [165, 166, 180], "excluded_lines": []}, "WebSecurityMiddleware.secure_headers.decorated_function": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [167, 170, 171, 172, 173, 174, 175, 176, 178], "excluded_lines": []}, "WebSecurityMiddleware.validate_input": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [184, 185, 239], "excluded_lines": []}, "WebSecurityMiddleware.validate_input.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [186, 238], "excluded_lines": []}, "WebSecurityMiddleware.validate_input.decorator.decorated_function": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [188, 189, 191, 194, 195, 196, 202, 203, 206, 207, 208, 209, 214, 215, 222, 223, 229, 230, 236], "excluded_lines": []}, "WebSecurityMiddleware._get_auth_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [244, 245, 246, 249, 250, 251, 254], "excluded_lines": []}, "WebSecurityMiddleware._get_client_ip": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [259, 260, 261, 262, 264], "excluded_lines": []}, "WebSecurityMiddleware._log_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [268], "excluded_lines": []}, "WebSecurityMiddleware._log_access_denied": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [286], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 17, 18, 20, 24, 73, 80, 87, 91, 107, 163, 182, 241, 256, 266, 283], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"WebSecurityMiddleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 97, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 97, "excluded_lines": 0}, "missing_lines": [21, 22, 27, 28, 29, 31, 32, 33, 40, 41, 42, 49, 50, 51, 55, 56, 63, 66, 68, 70, 71, 75, 82, 89, 93, 94, 95, 97, 102, 104, 105, 109, 110, 111, 112, 113, 115, 116, 119, 120, 136, 138, 140, 141, 158, 160, 161, 165, 166, 167, 170, 171, 172, 173, 174, 175, 176, 178, 180, 184, 185, 186, 188, 189, 191, 194, 195, 196, 202, 203, 206, 207, 208, 209, 214, 215, 222, 223, 229, 230, 236, 238, 239, 244, 245, 246, 249, 250, 251, 254, 259, 260, 261, 262, 264, 268, 286], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 17, 18, 20, 24, 73, 80, 87, 91, 107, 163, 182, 241, 256, 266, 283], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\simulation\\__init__.py": {"executed_lines": [1, 11, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\simulation\\military_simulation.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 43, 44, 45, 46, 47, 50, 51, 52, 53, 54, 55, 57, 61, 81, 82, 83, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 96, 97, 98, 101, 102, 103, 106, 107, 109, 117, 130, 138, 146, 171, 172, 173, 174, 175, 176, 177, 178, 179, 182, 183, 184, 187, 190, 192, 196, 201, 205, 210, 211, 213, 233, 247, 294, 299, 304, 310, 331, 358, 409, 435, 471, 477, 486, 497, 510, 515, 523, 543, 550], "summary": {"covered_lines": 101, "num_statements": 305, "percent_covered": 33.114754098360656, "percent_covered_display": "33", "missing_lines": 204, "excluded_lines": 0}, "missing_lines": [59, 63, 64, 65, 68, 69, 70, 73, 74, 75, 76, 78, 111, 112, 114, 115, 119, 120, 122, 123, 124, 125, 126, 128, 132, 133, 135, 136, 140, 141, 143, 144, 148, 149, 152, 153, 156, 157, 158, 159, 162, 164, 166, 167, 168, 194, 198, 199, 203, 207, 214, 217, 218, 219, 222, 223, 226, 235, 236, 237, 238, 240, 241, 243, 244, 245, 249, 250, 252, 253, 254, 256, 257, 266, 269, 270, 271, 273, 276, 277, 278, 281, 282, 283, 285, 286, 288, 290, 291, 292, 296, 297, 301, 302, 306, 307, 308, 312, 313, 315, 333, 334, 336, 337, 339, 352, 354, 355, 356, 360, 361, 363, 370, 372, 373, 376, 379, 380, 381, 382, 383, 384, 393, 402, 403, 404, 405, 407, 411, 412, 414, 420, 421, 428, 429, 431, 433, 437, 438, 440, 448, 449, 450, 452, 453, 454, 456, 458, 459, 460, 461, 462, 464, 465, 466, 467, 469, 473, 474, 475, 479, 480, 482, 483, 484, 488, 489, 491, 493, 494, 495, 499, 500, 501, 503, 504, 505, 506, 507, 508, 513, 517, 525, 526, 528, 529, 530, 531, 532, 533, 535, 537, 545, 552, 554, 555, 556, 558, 559, 560, 562, 563, 564, 566], "excluded_lines": [], "functions": {"Position.distance_to": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [59], "excluded_lines": []}, "Position.move_towards": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [63, 64, 65, 68, 69, 70, 73, 74, 75, 76, 78], "excluded_lines": []}, "SimulationEntity.update_position": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [111, 112, 114, 115], "excluded_lines": []}, "SimulationEntity.take_damage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [119, 120, 122, 123, 124, 125, 126, 128], "excluded_lines": []}, "SimulationEntity.can_detect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [132, 133, 135, 136], "excluded_lines": []}, "SimulationEntity.can_engage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [140, 141, 143, 144], "excluded_lines": []}, "SimulationEntity.engage_target": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [148, 149, 152, 153, 156, 157, 158, 159, 162, 164, 166, 167, 168], "excluded_lines": []}, "SimulationScenario.add_entity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [194], "excluded_lines": []}, "SimulationScenario.remove_entity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [198, 199], "excluded_lines": []}, "SimulationScenario.get_entities_by_allegiance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [203], "excluded_lines": []}, "SimulationScenario.get_active_entities": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [207], "excluded_lines": []}, "MilitarySimulationEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [214, 217, 218, 219, 222, 223, 226], "excluded_lines": []}, "MilitarySimulationEngine.load_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 238, 240, 241, 243, 244, 245], "excluded_lines": []}, "MilitarySimulationEngine.run_simulation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [249, 250, 252, 253, 254, 256, 257, 266, 269, 270, 271, 273, 276, 277, 278, 281, 282, 283, 285, 286, 288, 290, 291, 292], "excluded_lines": []}, "MilitarySimulationEngine.pause_simulation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [296, 297], "excluded_lines": []}, "MilitarySimulationEngine.resume_simulation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [301, 302], "excluded_lines": []}, "MilitarySimulationEngine.stop_simulation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [306, 307, 308], "excluded_lines": []}, "MilitarySimulationEngine.get_simulation_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [312, 313, 315], "excluded_lines": []}, "MilitarySimulationEngine.analyze_simulation_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [333, 334, 336, 337, 339, 352, 354, 355, 356], "excluded_lines": []}, "MilitarySimulationEngine._simulate_time_step": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [360, 361, 363, 370, 372, 373, 376, 379, 380, 381, 382, 383, 384, 393, 402, 403, 404, 405, 407], "excluded_lines": []}, "MilitarySimulationEngine._get_scenario_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [411, 412, 414, 420, 421, 428, 429, 431, 433], "excluded_lines": []}, "MilitarySimulationEngine._calculate_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [437, 438, 440, 448, 449, 450, 452, 453, 454, 456, 458, 459, 460, 461, 462, 464, 465, 466, 467, 469], "excluded_lines": []}, "MilitarySimulationEngine._process_scenario_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [473, 474, 475], "excluded_lines": []}, "MilitarySimulationEngine._handle_move_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [479, 480, 482, 483, 484], "excluded_lines": []}, "MilitarySimulationEngine._handle_attack_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [488, 489, 491, 493, 494, 495], "excluded_lines": []}, "MilitarySimulationEngine._handle_supply_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [499, 500, 501, 503, 504, 505, 506, 507, 508], "excluded_lines": []}, "MilitarySimulationEngine._handle_communication_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [513], "excluded_lines": []}, "MilitarySimulationEngine._analyze_entities": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [517], "excluded_lines": []}, "MilitarySimulationEngine._analyze_engagements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [525, 526, 528, 529, 530, 531, 532, 533, 535, 537], "excluded_lines": []}, "MilitarySimulationEngine._analyze_movements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [545], "excluded_lines": []}, "MilitarySimulationEngine._generate_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [552, 554, 555, 556, 558, 559, 560, 562, 563, 564, 566], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 43, 44, 45, 46, 47, 50, 51, 52, 53, 54, 55, 57, 61, 81, 82, 83, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 96, 97, 98, 101, 102, 103, 106, 107, 109, 117, 130, 138, 146, 171, 172, 173, 174, 175, 176, 177, 178, 179, 182, 183, 184, 187, 190, 192, 196, 201, 205, 210, 211, 213, 233, 247, 294, 299, 304, 310, 331, 358, 409, 435, 471, 477, 486, 497, 510, 515, 523, 543, 550], "summary": {"covered_lines": 101, "num_statements": 101, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SimulationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EntityType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EntityState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Position": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [59, 63, 64, 65, 68, 69, 70, 73, 74, 75, 76, 78], "excluded_lines": []}, "SimulationEntity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [111, 112, 114, 115, 119, 120, 122, 123, 124, 125, 126, 128, 132, 133, 135, 136, 140, 141, 143, 144, 148, 149, 152, 153, 156, 157, 158, 159, 162, 164, 166, 167, 168], "excluded_lines": []}, "SimulationScenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [194, 198, 199, 203, 207], "excluded_lines": []}, "MilitarySimulationEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 154, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 154, "excluded_lines": 0}, "missing_lines": [214, 217, 218, 219, 222, 223, 226, 235, 236, 237, 238, 240, 241, 243, 244, 245, 249, 250, 252, 253, 254, 256, 257, 266, 269, 270, 271, 273, 276, 277, 278, 281, 282, 283, 285, 286, 288, 290, 291, 292, 296, 297, 301, 302, 306, 307, 308, 312, 313, 315, 333, 334, 336, 337, 339, 352, 354, 355, 356, 360, 361, 363, 370, 372, 373, 376, 379, 380, 381, 382, 383, 384, 393, 402, 403, 404, 405, 407, 411, 412, 414, 420, 421, 428, 429, 431, 433, 437, 438, 440, 448, 449, 450, 452, 453, 454, 456, 458, 459, 460, 461, 462, 464, 465, 466, 467, 469, 473, 474, 475, 479, 480, 482, 483, 484, 488, 489, 491, 493, 494, 495, 499, 500, 501, 503, 504, 505, 506, 507, 508, 513, 517, 525, 526, 528, 529, 530, 531, 532, 533, 535, 537, 545, 552, 554, 555, 556, 558, 559, 560, 562, 563, 564, 566], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 43, 44, 45, 46, 47, 50, 51, 52, 53, 54, 55, 57, 61, 81, 82, 83, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 96, 97, 98, 101, 102, 103, 106, 107, 109, 117, 130, 138, 146, 171, 172, 173, 174, 175, 176, 177, 178, 179, 182, 183, 184, 187, 190, 192, 196, 201, 205, 210, 211, 213, 233, 247, 294, 299, 304, 310, 331, 358, 409, 435, 471, 477, 486, 497, 510, 515, 523, 543, 550], "summary": {"covered_lines": 101, "num_statements": 101, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\task_analysis\\__init__.py": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\task_analysis\\dynamic_task_manager.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 65, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 111, 117, 137, 138, 140, 170, 180, 206, 236, 251, 267, 287, 303, 316, 362, 384, 393, 418, 443], "summary": {"covered_lines": 76, "num_statements": 242, "percent_covered": 31.40495867768595, "percent_covered_display": "31", "missing_lines": 166, "excluded_lines": 0}, "missing_lines": [63, 68, 69, 70, 71, 74, 75, 76, 77, 79, 101, 102, 105, 106, 107, 109, 113, 114, 115, 119, 122, 123, 124, 125, 126, 127, 128, 129, 132, 134, 141, 142, 145, 146, 147, 148, 151, 161, 162, 163, 166, 167, 168, 172, 173, 174, 176, 177, 178, 185, 193, 194, 197, 198, 200, 201, 203, 204, 208, 209, 211, 214, 215, 216, 219, 222, 223, 224, 225, 228, 229, 231, 232, 234, 238, 239, 241, 242, 243, 246, 247, 249, 253, 254, 256, 259, 261, 262, 264, 265, 269, 270, 272, 273, 289, 290, 291, 301, 306, 307, 308, 311, 312, 314, 318, 326, 327, 328, 329, 332, 335, 337, 342, 344, 345, 346, 349, 350, 354, 355, 356, 360, 364, 366, 367, 368, 370, 371, 372, 375, 378, 379, 381, 382, 386, 387, 388, 389, 391, 395, 396, 397, 400, 403, 406, 408, 409, 410, 411, 412, 416, 420, 421, 423, 426, 427, 429, 434, 437, 439, 440, 441, 445, 446, 447, 448], "excluded_lines": [], "functions": {"TaskResource.is_available": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [63], "excluded_lines": []}, "TaskResource.can_handle_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [68, 69, 70, 71, 74, 75, 76, 77, 79], "excluded_lines": []}, "DynamicTask.is_ready_to_execute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [101, 102, 105, 106, 107, 109], "excluded_lines": []}, "DynamicTask.is_overdue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [113, 114, 115], "excluded_lines": []}, "DynamicTask.get_urgency_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [119, 122, 123, 124, 125, 126, 127, 128, 129, 132, 134], "excluded_lines": []}, "DynamicTaskManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [141, 142, 145, 146, 147, 148, 151, 161, 162, 163, 166, 167, 168], "excluded_lines": []}, "DynamicTaskManager.add_resource": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [172, 173, 174, 176, 177, 178], "excluded_lines": []}, "DynamicTaskManager.submit_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [185, 193, 194, 197, 198, 200, 201, 203, 204], "excluded_lines": []}, "DynamicTaskManager.reassign_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [208, 209, 211, 214, 215, 216, 219, 222, 223, 224, 225, 228, 229, 231, 232, 234], "excluded_lines": []}, "DynamicTaskManager.update_task_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [238, 239, 241, 242, 243, 246, 247, 249], "excluded_lines": []}, "DynamicTaskManager.cancel_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [253, 254, 256, 259, 261, 262, 264, 265], "excluded_lines": []}, "DynamicTaskManager.get_task_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [269, 270, 272, 273], "excluded_lines": []}, "DynamicTaskManager.get_resource_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 301], "excluded_lines": []}, "DynamicTaskManager.get_performance_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [306, 307, 308, 311, 312, 314], "excluded_lines": []}, "DynamicTaskManager.optimize_task_allocation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [318, 326, 327, 328, 329, 332, 335, 337, 342, 344, 345, 346, 349, 350, 354, 355, 356, 360], "excluded_lines": []}, "DynamicTaskManager._assign_resources": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [364, 366, 367, 368, 370, 371, 372, 375, 378, 379, 381, 382], "excluded_lines": []}, "DynamicTaskManager._release_resources": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [386, 387, 388, 389, 391], "excluded_lines": []}, "DynamicTaskManager._complete_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [395, 396, 397, 400, 403, 406, 408, 409, 410, 411, 412, 416], "excluded_lines": []}, "DynamicTaskManager._monitor_tasks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [420, 421, 423, 426, 427, 429, 434, 437, 439, 440, 441], "excluded_lines": []}, "DynamicTaskManager.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [445, 446, 447, 448], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 65, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 111, 117, 137, 138, 140, 170, 180, 206, 236, 251, 267, 287, 303, 316, 362, 384, 393, 418, 443], "summary": {"covered_lines": 76, "num_statements": 76, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TaskPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResourceType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskResource": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [63, 68, 69, 70, 71, 74, 75, 76, 77, 79], "excluded_lines": []}, "DynamicTask": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [101, 102, 105, 106, 107, 109, 113, 114, 115, 119, 122, 123, 124, 125, 126, 127, 128, 129, 132, 134], "excluded_lines": []}, "DynamicTaskManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 136, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 136, "excluded_lines": 0}, "missing_lines": [141, 142, 145, 146, 147, 148, 151, 161, 162, 163, 166, 167, 168, 172, 173, 174, 176, 177, 178, 185, 193, 194, 197, 198, 200, 201, 203, 204, 208, 209, 211, 214, 215, 216, 219, 222, 223, 224, 225, 228, 229, 231, 232, 234, 238, 239, 241, 242, 243, 246, 247, 249, 253, 254, 256, 259, 261, 262, 264, 265, 269, 270, 272, 273, 289, 290, 291, 301, 306, 307, 308, 311, 312, 314, 318, 326, 327, 328, 329, 332, 335, 337, 342, 344, 345, 346, 349, 350, 354, 355, 356, 360, 364, 366, 367, 368, 370, 371, 372, 375, 378, 379, 381, 382, 386, 387, 388, 389, 391, 395, 396, 397, 400, 403, 406, 408, 409, 410, 411, 412, 416, 420, 421, 423, 426, 427, 429, 434, 437, 439, 440, 441, 445, 446, 447, 448], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 65, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 111, 117, 137, 138, 140, 170, 180, 206, 236, 251, 267, 287, 303, 316, 362, 384, 393, 418, 443], "summary": {"covered_lines": 76, "num_statements": 76, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\task_analysis\\goms_analyzer.py": {"executed_lines": [1, 7, 8, 9, 10, 12, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 48, 49, 50, 51, 52, 53, 54, 56, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 86, 87, 89, 95, 180, 185, 196, 206, 237, 262, 295, 319, 332, 346, 362, 377, 406, 414], "summary": {"covered_lines": 53, "num_statements": 156, "percent_covered": 33.97435897435897, "percent_covered_display": "34", "missing_lines": 103, "excluded_lines": 0}, "missing_lines": [37, 57, 76, 90, 91, 92, 93, 97, 177, 178, 182, 183, 187, 193, 194, 198, 203, 204, 208, 209, 212, 215, 216, 217, 220, 221, 222, 225, 226, 227, 230, 231, 232, 233, 235, 239, 242, 243, 251, 252, 260, 264, 266, 267, 275, 276, 284, 285, 293, 297, 299, 300, 308, 309, 317, 324, 334, 335, 337, 338, 340, 341, 342, 344, 348, 349, 351, 353, 355, 356, 357, 358, 360, 364, 365, 367, 368, 370, 371, 372, 375, 379, 382, 383, 384, 386, 387, 389, 390, 393, 394, 396, 397, 399, 400, 401, 402, 404, 409, 410, 411, 412, 416], "excluded_lines": [], "functions": {"GomsOperator.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "GomsRule.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [57], "excluded_lines": []}, "GomsMethod.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [76], "excluded_lines": []}, "GomsAnalyzer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 93], "excluded_lines": []}, "GomsAnalyzer._init_standard_operators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [97, 177, 178], "excluded_lines": []}, "GomsAnalyzer.add_operator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [182, 183], "excluded_lines": []}, "GomsAnalyzer.create_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [187, 193, 194], "excluded_lines": []}, "GomsAnalyzer.add_selection_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [198, 203, 204], "excluded_lines": []}, "GomsAnalyzer.decompose_to_meta_operations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [208, 209, 212, 215, 216, 217, 220, 221, 222, 225, 226, 227, 230, 231, 232, 233, 235], "excluded_lines": []}, "GomsAnalyzer._analyze_input_operations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [239, 242, 243, 251, 252, 260], "excluded_lines": []}, "GomsAnalyzer._analyze_action_operations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [264, 266, 267, 275, 276, 284, 285, 293], "excluded_lines": []}, "GomsAnalyzer._analyze_output_operations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [297, 299, 300, 308, 309, 317], "excluded_lines": []}, "GomsAnalyzer._create_meta_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [324], "excluded_lines": []}, "GomsAnalyzer.calculate_execution_time": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [334, 335, 337, 338, 340, 341, 342, 344], "excluded_lines": []}, "GomsAnalyzer.calculate_error_probability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [348, 349, 351, 353, 355, 356, 357, 358, 360], "excluded_lines": []}, "GomsAnalyzer.calculate_mental_workload": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [364, 365, 367, 368, 370, 371, 372, 375], "excluded_lines": []}, "GomsAnalyzer.select_best_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [379, 382, 383, 384, 386, 387, 389, 390, 393, 394, 396, 397, 399, 400, 401, 402, 404], "excluded_lines": []}, "GomsAnalyzer._evaluate_condition": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [409, 410, 411, 412], "excluded_lines": []}, "GomsAnalyzer.export_goms_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [416], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 48, 49, 50, 51, 52, 53, 54, 56, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 86, 87, 89, 95, 180, 185, 196, 206, 237, 262, 295, 319, 332, 346, 362, 377, 406, 414], "summary": {"covered_lines": 53, "num_statements": 53, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GomsMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [76], "excluded_lines": []}, "GomsOperator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "GomsRule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [57], "excluded_lines": []}, "GomsAnalyzer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 100, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 93, 97, 177, 178, 182, 183, 187, 193, 194, 198, 203, 204, 208, 209, 212, 215, 216, 217, 220, 221, 222, 225, 226, 227, 230, 231, 232, 233, 235, 239, 242, 243, 251, 252, 260, 264, 266, 267, 275, 276, 284, 285, 293, 297, 299, 300, 308, 309, 317, 324, 334, 335, 337, 338, 340, 341, 342, 344, 348, 349, 351, 353, 355, 356, 357, 358, 360, 364, 365, 367, 368, 370, 371, 372, 375, 379, 382, 383, 384, 386, 387, 389, 390, 393, 394, 396, 397, 399, 400, 401, 402, 404, 409, 410, 411, 412, 416], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 48, 49, 50, 51, 52, 53, 54, 56, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 86, 87, 89, 95, 180, 185, 196, 206, 237, 262, 295, 319, 332, 346, 362, 377, 406, 414], "summary": {"covered_lines": 53, "num_statements": 53, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\task_analysis\\hierarchical_task_analyzer.py": {"executed_lines": [1, 7, 8, 9, 11, 14, 17, 18, 20, 24, 35, 41, 107, 117, 127, 131, 262, 304, 340, 347], "summary": {"covered_lines": 18, "num_statements": 116, "percent_covered": 15.517241379310345, "percent_covered_display": "16", "missing_lines": 98, "excluded_lines": 0}, "missing_lines": [21, 22, 26, 37, 38, 39, 43, 44, 46, 47, 48, 51, 52, 53, 55, 56, 58, 68, 69, 70, 85, 86, 87, 96, 97, 98, 99, 102, 103, 105, 109, 111, 112, 114, 115, 119, 121, 122, 124, 125, 129, 133, 264, 265, 267, 268, 269, 272, 274, 276, 279, 289, 290, 291, 292, 294, 295, 297, 300, 302, 306, 307, 309, 311, 313, 314, 315, 316, 317, 318, 321, 322, 323, 324, 325, 326, 329, 330, 331, 332, 333, 335, 336, 338, 342, 343, 345, 350, 353, 354, 370, 371, 374, 375, 378, 379, 381, 383], "excluded_lines": [], "functions": {"HierarchicalTaskAnalyzer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [21, 22], "excluded_lines": []}, "HierarchicalTaskAnalyzer._init_decomposition_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [26], "excluded_lines": []}, "HierarchicalTaskAnalyzer.create_task_hierarchy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [37, 38, 39], "excluded_lines": []}, "HierarchicalTaskAnalyzer.decompose_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [43, 44, 46, 47, 48, 51, 52, 53, 55, 56, 58, 68, 69, 70, 85, 86, 87, 96, 97, 98, 99, 102, 103, 105], "excluded_lines": []}, "HierarchicalTaskAnalyzer.auto_decompose_by_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [109, 111, 112, 114, 115], "excluded_lines": []}, "HierarchicalTaskAnalyzer.auto_decompose_by_military_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [119, 121, 122, 124, 125], "excluded_lines": []}, "HierarchicalTaskAnalyzer.get_available_military_scenarios": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [129], "excluded_lines": []}, "HierarchicalTaskAnalyzer._get_scenario_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [133], "excluded_lines": []}, "HierarchicalTaskAnalyzer.calculate_task_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [264, 265, 267, 268, 269, 272, 274, 276, 279, 289, 290, 291, 292, 294, 295, 297, 300, 302], "excluded_lines": []}, "HierarchicalTaskAnalyzer.validate_hierarchy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [306, 307, 309, 311, 313, 314, 315, 316, 317, 318, 321, 322, 323, 324, 325, 326, 329, 330, 331, 332, 333, 335, 336, 338], "excluded_lines": []}, "HierarchicalTaskAnalyzer.export_hierarchy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [342, 343, 345], "excluded_lines": []}, "HierarchicalTaskAnalyzer.import_hierarchy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [350, 353, 354, 370, 371, 374, 375, 378, 379, 381, 383], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 14, 17, 18, 20, 24, 35, 41, 107, 117, 127, 131, 262, 304, 340, 347], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"HierarchicalTaskAnalyzer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 98, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 98, "excluded_lines": 0}, "missing_lines": [21, 22, 26, 37, 38, 39, 43, 44, 46, 47, 48, 51, 52, 53, 55, 56, 58, 68, 69, 70, 85, 86, 87, 96, 97, 98, 99, 102, 103, 105, 109, 111, 112, 114, 115, 119, 121, 122, 124, 125, 129, 133, 264, 265, 267, 268, 269, 272, 274, 276, 279, 289, 290, 291, 292, 294, 295, 297, 300, 302, 306, 307, 309, 311, 313, 314, 315, 316, 317, 318, 321, 322, 323, 324, 325, 326, 329, 330, 331, 332, 333, 335, 336, 338, 342, 343, 345, 350, 353, 354, 370, 371, 374, 375, 378, 379, 381, 383], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 14, 17, 18, 20, 24, 35, 41, 107, 117, 127, 131, 262, 304, 340, 347], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\task_analysis\\military_scenarios.py": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 26, 27, 176, 177, 326, 327, 407, 408, 438, 439], "summary": {"covered_lines": 15, "num_statements": 21, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [18, 29, 179, 329, 410, 441], "excluded_lines": [], "functions": {"MilitaryScenarioTemplates.get_military_scenario_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [18], "excluded_lines": []}, "MilitaryScenarioTemplates._get_situation_analysis_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [29], "excluded_lines": []}, "MilitaryScenarioTemplates._get_threat_calculation_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [179], "excluded_lines": []}, "MilitaryScenarioTemplates._get_decision_support_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "MilitaryScenarioTemplates._get_combat_command_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [410], "excluded_lines": []}, "MilitaryScenarioTemplates._get_intelligence_processing_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [441], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 26, 27, 176, 177, 326, 327, 407, 408, 438, 439], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MilitaryScenarioTemplates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [18, 29, 179, 329, 410, 441], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 26, 27, 176, 177, 326, 327, 407, 408, 438, 439], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\training\\__init__.py": {"executed_lines": [1, 12, 22], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 12, 22], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 12, 22], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\training\\military_training.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 157, 158, 160, 183, 193, 207, 269, 305, 365, 412, 458, 479, 505, 522, 542, 580, 598, 614, 632, 649, 669, 679, 701, 727, 741, 751, 761], "summary": {"covered_lines": 99, "num_statements": 392, "percent_covered": 25.255102040816325, "percent_covered_display": "25", "missing_lines": 293, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70, 91, 94, 103, 105, 106, 107, 108, 115, 118, 119, 138, 139, 141, 143, 145, 146, 148, 149, 150, 151, 153, 154, 161, 164, 165, 166, 169, 172, 181, 185, 186, 187, 188, 189, 190, 191, 195, 197, 198, 200, 201, 202, 203, 204, 205, 213, 214, 225, 228, 233, 234, 235, 236, 238, 241, 243, 244, 246, 254, 255, 258, 262, 263, 265, 266, 267, 274, 275, 276, 278, 279, 281, 282, 284, 285, 287, 289, 292, 293, 296, 298, 299, 301, 302, 303, 309, 310, 318, 319, 322, 323, 326, 327, 328, 329, 330, 332, 333, 334, 341, 342, 343, 344, 345, 347, 348, 349, 356, 357, 359, 361, 362, 363, 369, 370, 372, 373, 374, 377, 383, 384, 386, 406, 408, 409, 410, 415, 427, 440, 441, 444, 456, 460, 461, 464, 465, 466, 467, 468, 469, 470, 473, 474, 475, 477, 484, 486, 488, 489, 492, 494, 497, 498, 501, 503, 509, 511, 512, 514, 515, 518, 520, 526, 528, 529, 530, 531, 534, 535, 537, 538, 540, 549, 552, 561, 562, 565, 566, 568, 569, 572, 575, 576, 578, 582, 583, 585, 586, 589, 590, 593, 594, 596, 600, 601, 604, 605, 607, 608, 609, 610, 612, 616, 618, 619, 620, 621, 622, 624, 625, 626, 627, 628, 630, 634, 636, 638, 639, 640, 641, 643, 644, 645, 647, 651, 653, 654, 655, 656, 658, 661, 662, 663, 664, 665, 667, 671, 673, 674, 677, 681, 682, 684, 687, 688, 690, 691, 693, 699, 703, 705, 706, 709, 710, 712, 713, 714, 715, 718, 719, 720, 722, 723, 725, 729, 730, 732, 733, 734, 735, 737, 739, 743, 745, 746, 747, 749, 753, 754, 756, 757, 759, 763, 764, 765, 766, 767, 768, 770], "excluded_lines": [], "functions": {"TrainingObjective.is_achievable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70], "excluded_lines": []}, "TrainingScenario.generate_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [91, 94, 103, 105, 106, 107, 108, 115, 118, 119], "excluded_lines": []}, "TrainingResult.calculate_overall_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [138, 139, 141, 143, 145, 146, 148, 149, 150, 151, 153, 154], "excluded_lines": []}, "MilitaryTrainingSystem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [161, 164, 165, 166, 169, 172, 181], "excluded_lines": []}, "MilitaryTrainingSystem.create_training_objective": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [185, 186, 187, 188, 189, 190, 191], "excluded_lines": []}, "MilitaryTrainingSystem.create_training_scenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [195, 197, 198, 200, 201, 202, 203, 204, 205], "excluded_lines": []}, "MilitaryTrainingSystem.design_training_program": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [213, 214, 225, 228, 233, 234, 235, 236, 238, 241, 243, 244, 246, 254, 255, 258, 262, 263, 265, 266, 267], "excluded_lines": []}, "MilitaryTrainingSystem.execute_training_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [274, 275, 276, 278, 279, 281, 282, 284, 285, 287, 289, 292, 293, 296, 298, 299, 301, 302, 303], "excluded_lines": []}, "MilitaryTrainingSystem.evaluate_training_effectiveness": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [309, 310, 318, 319, 322, 323, 326, 327, 328, 329, 330, 332, 333, 334, 341, 342, 343, 344, 345, 347, 348, 349, 356, 357, 359, 361, 362, 363], "excluded_lines": []}, "MilitaryTrainingSystem.generate_training_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [369, 370, 372, 373, 374, 377, 383, 384, 386, 406, 408, 409, 410], "excluded_lines": []}, "MilitaryTrainingSystem._initialize_default_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [415, 427, 440, 441, 444, 456], "excluded_lines": []}, "MilitaryTrainingSystem._analyze_participant_skills": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [460, 461, 464, 465, 466, 467, 468, 469, 470, 473, 474, 475, 477], "excluded_lines": []}, "MilitaryTrainingSystem._select_training_objectives": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [484, 486, 488, 489, 492, 494, 497, 498, 501, 503], "excluded_lines": []}, "MilitaryTrainingSystem._find_matching_scenarios": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [509, 511, 512, 514, 515, 518, 520], "excluded_lines": []}, "MilitaryTrainingSystem._predict_training_outcomes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [526, 528, 529, 530, 531, 534, 535, 537, 538, 540], "excluded_lines": []}, "MilitaryTrainingSystem._simulate_training_execution": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [549, 552, 561, 562, 565, 566, 568, 569, 572, 575, 576, 578], "excluded_lines": []}, "MilitaryTrainingSystem._update_participant_skills": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [582, 583, 585, 586, 589, 590, 593, 594, 596], "excluded_lines": []}, "MilitaryTrainingSystem._calculate_performance_trend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [600, 601, 604, 605, 607, 608, 609, 610, 612], "excluded_lines": []}, "MilitaryTrainingSystem._identify_improvement_areas": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [616, 618, 619, 620, 621, 622, 624, 625, 626, 627, 628, 630], "excluded_lines": []}, "MilitaryTrainingSystem._generate_training_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [634, 636, 638, 639, 640, 641, 643, 644, 645, 647], "excluded_lines": []}, "MilitaryTrainingSystem._analyze_performance_by_metric": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [651, 653, 654, 655, 656, 658, 661, 662, 663, 664, 665, 667], "excluded_lines": []}, "MilitaryTrainingSystem._collect_achievements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [671, 673, 674, 677], "excluded_lines": []}, "MilitaryTrainingSystem._analyze_skill_development": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [681, 682, 684, 687, 688, 690, 691, 693, 699], "excluded_lines": []}, "MilitaryTrainingSystem._generate_personal_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [703, 705, 706, 709, 710, 712, 713, 714, 715, 718, 719, 720, 722, 723, 725], "excluded_lines": []}, "MilitaryTrainingSystem._generate_training_feedback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [729, 730, 732, 733, 734, 735, 737, 739], "excluded_lines": []}, "MilitaryTrainingSystem._identify_individual_improvements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [743, 745, 746, 747, 749], "excluded_lines": []}, "MilitaryTrainingSystem._calculate_improvement_rate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [753, 754, 756, 757, 759], "excluded_lines": []}, "MilitaryTrainingSystem._get_proficiency_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [763, 764, 765, 766, 767, 768, 770], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 157, 158, 160, 183, 193, 207, 269, 305, 365, 412, 458, 479, 505, 522, 542, 580, 598, 614, 632, 649, 669, 679, 701, 727, 741, 751, 761], "summary": {"covered_lines": 99, "num_statements": 99, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TrainingType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TrainingLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceMetric": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TrainingObjective": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70], "excluded_lines": []}, "TrainingScenario": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [91, 94, 103, 105, 106, 107, 108, 115, 118, 119], "excluded_lines": []}, "TrainingResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [138, 139, 141, 143, 145, 146, 148, 149, 150, 151, 153, 154], "excluded_lines": []}, "MilitaryTrainingSystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 267, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 267, "excluded_lines": 0}, "missing_lines": [161, 164, 165, 166, 169, 172, 181, 185, 186, 187, 188, 189, 190, 191, 195, 197, 198, 200, 201, 202, 203, 204, 205, 213, 214, 225, 228, 233, 234, 235, 236, 238, 241, 243, 244, 246, 254, 255, 258, 262, 263, 265, 266, 267, 274, 275, 276, 278, 279, 281, 282, 284, 285, 287, 289, 292, 293, 296, 298, 299, 301, 302, 303, 309, 310, 318, 319, 322, 323, 326, 327, 328, 329, 330, 332, 333, 334, 341, 342, 343, 344, 345, 347, 348, 349, 356, 357, 359, 361, 362, 363, 369, 370, 372, 373, 374, 377, 383, 384, 386, 406, 408, 409, 410, 415, 427, 440, 441, 444, 456, 460, 461, 464, 465, 466, 467, 468, 469, 470, 473, 474, 475, 477, 484, 486, 488, 489, 492, 494, 497, 498, 501, 503, 509, 511, 512, 514, 515, 518, 520, 526, 528, 529, 530, 531, 534, 535, 537, 538, 540, 549, 552, 561, 562, 565, 566, 568, 569, 572, 575, 576, 578, 582, 583, 585, 586, 589, 590, 593, 594, 596, 600, 601, 604, 605, 607, 608, 609, 610, 612, 616, 618, 619, 620, 621, 622, 624, 625, 626, 627, 628, 630, 634, 636, 638, 639, 640, 641, 643, 644, 645, 647, 651, 653, 654, 655, 656, 658, 661, 662, 663, 664, 665, 667, 671, 673, 674, 677, 681, 682, 684, 687, 688, 690, 691, 693, 699, 703, 705, 706, 709, 710, 712, 713, 714, 715, 718, 719, 720, 722, 723, 725, 729, 730, 732, 733, 734, 735, 737, 739, 743, 745, 746, 747, 749, 753, 754, 756, 757, 759, 763, 764, 765, 766, 767, 768, 770], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 157, 158, 160, 183, 193, 207, 269, 305, 365, 412, 458, 479, 505, 522, 542, 580, 598, 614, 632, 649, 669, 679, 701, 727, 741, 751, 761], "summary": {"covered_lines": 99, "num_statements": 99, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\utils\\__init__.py": {"executed_lines": [1, 11, 12, 17], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 11, 12, 17], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 11, 12, 17], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\utils\\data_loader.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 26, 35, 44, 52, 60, 108, 162, 200, 217, 242, 252, 262, 279], "summary": {"covered_lines": 25, "num_statements": 152, "percent_covered": 16.44736842105263, "percent_covered_display": "16", "missing_lines": 127, "excluded_lines": 0}, "missing_lines": [24, 28, 29, 30, 31, 32, 33, 37, 38, 39, 40, 41, 42, 46, 47, 48, 49, 50, 54, 55, 56, 57, 58, 62, 63, 64, 66, 67, 69, 71, 72, 80, 83, 84, 89, 91, 101, 103, 104, 105, 106, 110, 111, 112, 114, 115, 122, 124, 125, 134, 136, 146, 149, 150, 157, 158, 159, 160, 164, 167, 168, 170, 172, 173, 175, 177, 178, 179, 185, 186, 187, 188, 189, 190, 191, 193, 195, 196, 198, 202, 204, 205, 207, 208, 209, 211, 213, 214, 215, 219, 221, 222, 224, 225, 226, 228, 229, 231, 235, 237, 238, 240, 244, 245, 246, 247, 248, 249, 250, 254, 255, 256, 257, 258, 259, 260, 264, 266, 267, 268, 269, 270, 273, 274, 275, 277, 281], "excluded_lines": [], "functions": {"DataLoader.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [24], "excluded_lines": []}, "DataLoader.load_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [28, 29, 30, 31, 32, 33], "excluded_lines": []}, "DataLoader.load_yaml": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [37, 38, 39, 40, 41, 42], "excluded_lines": []}, "DataLoader.load_excel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [46, 47, 48, 49, 50], "excluded_lines": []}, "DataLoader.load_csv": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 57, 58], "excluded_lines": []}, "DataLoader.load_task_hierarchy_from_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [62, 63, 64, 66, 67, 69, 71, 72, 80, 83, 84, 89, 91, 101, 103, 104, 105, 106], "excluded_lines": []}, "DataLoader.load_evaluation_scheme_from_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [110, 111, 112, 114, 115, 122, 124, 125, 134, 136, 146, 149, 150, 157, 158, 159, 160], "excluded_lines": []}, "DataLoader.load_alternatives_from_excel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [164, 167, 168, 170, 172, 173, 175, 177, 178, 179, 185, 186, 187, 188, 189, 190, 191, 193, 195, 196, 198], "excluded_lines": []}, "DataLoader.load_expert_judgments_from_excel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [202, 204, 205, 207, 208, 209, 211, 213, 214, 215], "excluded_lines": []}, "DataLoader.create_fuzzy_numbers_from_expert_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [219, 221, 222, 224, 225, 226, 228, 229, 231, 235, 237, 238, 240], "excluded_lines": []}, "DataLoader.save_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [244, 245, 246, 247, 248, 249, 250], "excluded_lines": []}, "DataLoader.save_yaml": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [254, 255, 256, 257, 258, 259, 260], "excluded_lines": []}, "DataLoader.validate_data_format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [264, 266, 273, 274, 275, 277], "excluded_lines": []}, "DataLoader.validate_data_format.validate_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [267, 268, 269, 270], "excluded_lines": []}, "DataLoader.create_sample_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [281], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 26, 35, 44, 52, 60, 108, 162, 200, 217, 242, 252, 262, 279], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DataLoader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 127, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 127, "excluded_lines": 0}, "missing_lines": [24, 28, 29, 30, 31, 32, 33, 37, 38, 39, 40, 41, 42, 46, 47, 48, 49, 50, 54, 55, 56, 57, 58, 62, 63, 64, 66, 67, 69, 71, 72, 80, 83, 84, 89, 91, 101, 103, 104, 105, 106, 110, 111, 112, 114, 115, 122, 124, 125, 134, 136, 146, 149, 150, 157, 158, 159, 160, 164, 167, 168, 170, 172, 173, 175, 177, 178, 179, 185, 186, 187, 188, 189, 190, 191, 193, 195, 196, 198, 202, 204, 205, 207, 208, 209, 211, 213, 214, 215, 219, 221, 222, 224, 225, 226, 228, 229, 231, 235, 237, 238, 240, 244, 245, 246, 247, 248, 249, 250, 254, 255, 256, 257, 258, 259, 260, 264, 266, 267, 268, 269, 270, 273, 274, 275, 277, 281], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 26, 35, 44, 52, 60, 108, 162, 200, 217, 242, 252, 262, 279], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\utils\\logger.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 18, 19, 20, 26, 33, 36, 37, 40, 43, 49, 50, 51, 52, 55, 57, 58, 61, 67, 68, 71, 73, 74, 76, 82, 83, 86, 87, 90, 98, 106, 107, 114, 119, 152, 161, 183, 186, 191, 193, 196, 197, 199, 205, 210, 219, 220, 227, 249, 259], "summary": {"covered_lines": 55, "num_statements": 108, "percent_covered": 50.925925925925924, "percent_covered_display": "51", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [34, 116, 117, 124, 125, 127, 130, 131, 133, 141, 145, 147, 148, 150, 154, 163, 171, 176, 177, 179, 188, 200, 201, 202, 203, 206, 207, 208, 211, 212, 214, 215, 217, 222, 223, 224, 229, 230, 233, 235, 236, 237, 239, 240, 242, 243, 244, 246, 251, 252, 253, 254, 255], "excluded_lines": [], "functions": {"LoggerManager.__init__": {"executed_lines": [19, 20], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LoggerManager.get_logger": {"executed_lines": [33, 36, 37, 40, 43, 49, 50, 51, 52, 55, 57, 58, 61, 67, 68, 71, 73, 74], "summary": {"covered_lines": 18, "num_statements": 19, "percent_covered": 94.73684210526316, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [34], "excluded_lines": []}, "LoggerManager.setup_default_logging": {"executed_lines": [82, 83, 86, 87, 90, 98, 106, 107], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LoggerManager.set_level": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [116, 117], "excluded_lines": []}, "LoggerManager.add_file_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [124, 125, 127, 130, 131, 133, 141, 145, 147, 148, 150], "excluded_lines": []}, "LoggerManager.create_performance_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [154], "excluded_lines": []}, "LoggerManager.create_error_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [163, 171, 176, 177, 179], "excluded_lines": []}, "get_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [188], "excluded_lines": []}, "setup_logging": {"executed_lines": [193], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceTimer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [200, 201, 202, 203], "excluded_lines": []}, "PerformanceTimer.__enter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [206, 207, 208], "excluded_lines": []}, "PerformanceTimer.__exit__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [211, 212, 214, 215, 217], "excluded_lines": []}, "PerformanceTimer.duration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [222, 223, 224], "excluded_lines": []}, "log_function_call": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [229, 246], "excluded_lines": []}, "log_function_call.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [230, 233, 235, 236, 237, 239, 240, 242, 243, 244], "excluded_lines": []}, "log_method_call": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [251, 252, 253, 254, 255], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 18, 26, 76, 114, 119, 152, 161, 183, 186, 191, 196, 197, 199, 205, 210, 219, 220, 227, 249, 259], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LoggerManager": {"executed_lines": [19, 20, 33, 36, 37, 40, 43, 49, 50, 51, 52, 55, 57, 58, 61, 67, 68, 71, 73, 74, 82, 83, 86, 87, 90, 98, 106, 107], "summary": {"covered_lines": 28, "num_statements": 48, "percent_covered": 58.333333333333336, "percent_covered_display": "58", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [34, 116, 117, 124, 125, 127, 130, 131, 133, 141, 145, 147, 148, 150, 154, 163, 171, 176, 177, 179], "excluded_lines": []}, "PerformanceTimer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [200, 201, 202, 203, 206, 207, 208, 211, 212, 214, 215, 217, 222, 223, 224], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 16, 18, 26, 76, 114, 119, 152, 161, 183, 186, 191, 193, 196, 197, 199, 205, 210, 219, 220, 227, 249, 259], "summary": {"covered_lines": 27, "num_statements": 45, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [188, 229, 230, 233, 235, 236, 237, 239, 240, 242, 243, 244, 246, 251, 252, 253, 254, 255], "excluded_lines": []}}}, "src\\hmdm\\utils\\realtime_processor.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 83, 84, 86, 129, 139, 147, 175, 242, 247, 261, 277, 282, 327, 389, 410, 430, 455], "summary": {"covered_lines": 65, "num_statements": 236, "percent_covered": 27.54237288135593, "percent_covered_display": "28", "missing_lines": 171, "excluded_lines": 0}, "missing_lines": [55, 87, 88, 89, 92, 93, 94, 97, 100, 110, 111, 118, 119, 120, 123, 126, 127, 131, 132, 133, 135, 136, 137, 141, 142, 143, 144, 145, 149, 151, 152, 153, 154, 157, 158, 160, 162, 165, 166, 167, 169, 171, 172, 173, 177, 178, 180, 182, 190, 192, 193, 194, 195, 196, 199, 200, 201, 202, 207, 209, 210, 211, 214, 215, 216, 219, 220, 221, 222, 226, 234, 235, 236, 244, 245, 250, 255, 256, 257, 259, 263, 264, 265, 275, 279, 280, 286, 288, 289, 290, 292, 293, 294, 295, 296, 297, 298, 299, 301, 302, 312, 313, 315, 324, 325, 331, 332, 334, 335, 337, 341, 347, 348, 350, 351, 353, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 366, 368, 377, 386, 387, 392, 397, 398, 401, 402, 407, 408, 412, 414, 415, 417, 420, 422, 424, 425, 426, 427, 428, 432, 433, 434, 437, 438, 440, 441, 444, 445, 446, 447, 449, 451, 452, 453, 457, 460, 461, 464, 467, 468, 470], "excluded_lines": [], "functions": {"RealTimeData.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [55], "excluded_lines": []}, "RealTimeProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [87, 88, 89, 92, 93, 94, 97, 100, 110, 111, 118, 119, 120, 123, 126, 127], "excluded_lines": []}, "RealTimeProcessor.add_processing_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [131, 132, 133, 135, 136, 137], "excluded_lines": []}, "RealTimeProcessor.remove_processing_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [141, 142, 143, 144, 145], "excluded_lines": []}, "RealTimeProcessor.ingest_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [149, 151, 152, 153, 154, 157, 158, 160, 162, 165, 166, 167, 169, 171, 172, 173], "excluded_lines": []}, "RealTimeProcessor.process_data_sync": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [177, 178, 180, 182, 190, 192, 193, 194, 195, 196, 199, 200, 201, 202, 207, 209, 210, 211, 214, 215, 216, 219, 220, 221, 222, 226, 234, 235, 236], "excluded_lines": []}, "RealTimeProcessor.process_data_async": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [244, 245], "excluded_lines": []}, "RealTimeProcessor.get_processing_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [250, 255, 256, 257, 259], "excluded_lines": []}, "RealTimeProcessor.get_rule_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [263, 264, 265, 275], "excluded_lines": []}, "RealTimeProcessor.get_recent_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [279, 280], "excluded_lines": []}, "RealTimeProcessor.create_alert_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [286, 288, 301, 315, 324, 325], "excluded_lines": []}, "RealTimeProcessor.create_alert_rule.alert_condition": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [289, 290, 292, 293, 294, 295, 296, 297, 298, 299], "excluded_lines": []}, "RealTimeProcessor.create_alert_rule.alert_action": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [302, 312, 313], "excluded_lines": []}, "RealTimeProcessor.create_aggregation_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [331, 332, 334, 337, 377, 386, 387], "excluded_lines": []}, "RealTimeProcessor.create_aggregation_rule.aggregation_condition": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [335], "excluded_lines": []}, "RealTimeProcessor.create_aggregation_rule.aggregation_action": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [341, 347, 348, 350, 351, 353, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 366, 368], "excluded_lines": []}, "RealTimeProcessor._start_processing_threads": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [392, 397, 398, 401, 402, 407, 408], "excluded_lines": []}, "RealTimeProcessor._process_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [412, 414, 415, 417, 420, 422, 424, 425, 426, 427, 428], "excluded_lines": []}, "RealTimeProcessor._monitor_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [432, 433, 434, 437, 438, 440, 441, 444, 445, 446, 447, 449, 451, 452, 453], "excluded_lines": []}, "RealTimeProcessor.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [457, 460, 461, 464, 467, 468, 470], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 83, 84, 86, 129, 139, 147, 175, 242, 247, 261, 277, 282, 327, 389, 410, 430, 455], "summary": {"covered_lines": 65, "num_statements": 65, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DataType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RealTimeData": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [55], "excluded_lines": []}, "ProcessingRule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RealTimeProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 170, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 170, "excluded_lines": 0}, "missing_lines": [87, 88, 89, 92, 93, 94, 97, 100, 110, 111, 118, 119, 120, 123, 126, 127, 131, 132, 133, 135, 136, 137, 141, 142, 143, 144, 145, 149, 151, 152, 153, 154, 157, 158, 160, 162, 165, 166, 167, 169, 171, 172, 173, 177, 178, 180, 182, 190, 192, 193, 194, 195, 196, 199, 200, 201, 202, 207, 209, 210, 211, 214, 215, 216, 219, 220, 221, 222, 226, 234, 235, 236, 244, 245, 250, 255, 256, 257, 259, 263, 264, 265, 275, 279, 280, 286, 288, 289, 290, 292, 293, 294, 295, 296, 297, 298, 299, 301, 302, 312, 313, 315, 324, 325, 331, 332, 334, 335, 337, 341, 347, 348, 350, 351, 353, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 366, 368, 377, 386, 387, 392, 397, 398, 401, 402, 407, 408, 412, 414, 415, 417, 420, 422, 424, 425, 426, 427, 428, 432, 433, 434, 437, 438, 440, 441, 444, 445, 446, 447, 449, 451, 452, 453, 457, 460, 461, 464, 467, 468, 470], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 83, 84, 86, 129, 139, 147, 175, 242, 247, 261, 277, 282, 327, 389, 410, 430, 455], "summary": {"covered_lines": 65, "num_statements": 65, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\web\\__init__.py": {"executed_lines": [1, 12, 14], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 12, 14], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 12, 14], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\hmdm\\web\\app.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 23, 48, 221, 242, 294, 344, 354, 385, 398, 406], "summary": {"covered_lines": 21, "num_statements": 189, "percent_covered": 11.11111111111111, "percent_covered_display": "11", "missing_lines": 168, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 32, 35, 38, 41, 42, 45, 46, 51, 52, 54, 56, 57, 59, 60, 62, 63, 65, 66, 75, 80, 81, 83, 84, 86, 87, 89, 91, 92, 94, 95, 96, 100, 101, 106, 107, 109, 110, 111, 115, 116, 121, 122, 124, 125, 126, 130, 131, 136, 137, 139, 140, 141, 145, 146, 151, 152, 154, 156, 157, 158, 159, 164, 165, 170, 171, 175, 176, 181, 182, 184, 185, 186, 187, 193, 195, 199, 200, 205, 206, 208, 210, 211, 215, 216, 224, 225, 227, 228, 230, 231, 233, 235, 236, 238, 239, 240, 244, 245, 247, 252, 254, 259, 261, 262, 267, 269, 274, 276, 283, 287, 288, 289, 296, 297, 298, 300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 319, 325, 326, 333, 335, 336, 337, 346, 347, 349, 350, 351, 352, 356, 357, 359, 362, 368, 369, 370, 371, 372, 379, 381, 382, 383, 387, 388, 389, 394, 395, 396, 400, 401, 402, 403, 408, 409, 411], "excluded_lines": [], "functions": {"HMDMWebApp.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 32, 35, 38, 41, 42, 45, 46], "excluded_lines": []}, "HMDMWebApp._register_routes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [51, 52, 56, 57, 62, 63, 80, 81, 86, 87, 91, 92, 106, 107, 121, 122, 136, 137, 151, 152, 181, 182, 205, 206], "excluded_lines": []}, "HMDMWebApp._register_routes.index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [54], "excluded_lines": []}, "HMDMWebApp._register_routes.dashboard": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [59, 60], "excluded_lines": []}, "HMDMWebApp._register_routes.modules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [65, 66, 75], "excluded_lines": []}, "HMDMWebApp._register_routes.config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [83, 84], "excluded_lines": []}, "HMDMWebApp._register_routes.logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [89], "excluded_lines": []}, "HMDMWebApp._register_routes.api_system_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [94, 95, 96, 100, 101], "excluded_lines": []}, "HMDMWebApp._register_routes.api_system_start": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 115, 116], "excluded_lines": []}, "HMDMWebApp._register_routes.api_system_stop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [124, 125, 126, 130, 131], "excluded_lines": []}, "HMDMWebApp._register_routes.api_module_restart": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [139, 140, 141, 145, 146], "excluded_lines": []}, "HMDMWebApp._register_routes.api_config_update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [154, 156, 157, 158, 159, 164, 165, 170, 171, 175, 176], "excluded_lines": []}, "HMDMWebApp._register_routes.api_module_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 187, 193, 195, 199, 200], "excluded_lines": []}, "HMDMWebApp._register_routes.api_logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [208, 210, 211, 215, 216], "excluded_lines": []}, "HMDMWebApp._register_socketio_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [224, 225, 230, 231, 235, 236], "excluded_lines": []}, "HMDMWebApp._register_socketio_events.handle_connect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [227, 228], "excluded_lines": []}, "HMDMWebApp._register_socketio_events.handle_disconnect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [233], "excluded_lines": []}, "HMDMWebApp._register_socketio_events.handle_subscribe_realtime": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [238, 239, 240], "excluded_lines": []}, "HMDMWebApp._get_module_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [244, 245, 247, 252, 254, 259, 261, 262, 267, 269, 274, 276, 283, 287, 288, 289], "excluded_lines": []}, "HMDMWebApp._read_recent_logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [296, 297, 298, 300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 319, 325, 326, 333, 335, 336, 337], "excluded_lines": []}, "HMDMWebApp._start_data_push": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [346, 347, 349, 350, 351, 352], "excluded_lines": []}, "HMDMWebApp._data_push_worker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [356, 357, 359, 362, 368, 369, 370, 371, 372, 379, 381, 382, 383], "excluded_lines": []}, "HMDMWebApp.run": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [387, 388, 389, 394, 395, 396], "excluded_lines": []}, "HMDMWebApp.stop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [400, 401, 402, 403], "excluded_lines": []}, "create_app": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [408, 409, 411], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 23, 48, 221, 242, 294, 344, 354, 385, 398, 406], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"HMDMWebApp": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 165, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 165, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 32, 35, 38, 41, 42, 45, 46, 51, 52, 54, 56, 57, 59, 60, 62, 63, 65, 66, 75, 80, 81, 83, 84, 86, 87, 89, 91, 92, 94, 95, 96, 100, 101, 106, 107, 109, 110, 111, 115, 116, 121, 122, 124, 125, 126, 130, 131, 136, 137, 139, 140, 141, 145, 146, 151, 152, 154, 156, 157, 158, 159, 164, 165, 170, 171, 175, 176, 181, 182, 184, 185, 186, 187, 193, 195, 199, 200, 205, 206, 208, 210, 211, 215, 216, 224, 225, 227, 228, 230, 231, 233, 235, 236, 238, 239, 240, 244, 245, 247, 252, 254, 259, 261, 262, 267, 269, 274, 276, 283, 287, 288, 289, 296, 297, 298, 300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 319, 325, 326, 333, 335, 336, 337, 346, 347, 349, 350, 351, 352, 356, 357, 359, 362, 368, 369, 370, 371, 372, 379, 381, 382, 383, 387, 388, 389, 394, 395, 396, 400, 401, 402, 403], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 20, 21, 23, 48, 221, 242, 294, 344, 354, 385, 398, 406], "summary": {"covered_lines": 21, "num_statements": 24, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [408, 409, 411], "excluded_lines": []}}}, "src\\hmdm\\web\\run_web.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 41, "excluded_lines": 2}, "missing_lines": [8, 9, 10, 11, 14, 16, 17, 20, 22, 23, 24, 25, 26, 27, 31, 34, 39, 41, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 62, 63, 64, 65, 66, 67, 70, 71, 72, 73, 74], "excluded_lines": [77, 78], "functions": {"main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26, 27, 31, 34, 39, 41, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 62, 63, 64, 65, 66, 67, 70, 71, 72, 73, 74], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 2}, "missing_lines": [8, 9, 10, 11, 14, 16, 17, 20], "excluded_lines": [77, 78]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 41, "excluded_lines": 2}, "missing_lines": [8, 9, 10, 11, 14, 16, 17, 20, 22, 23, 24, 25, 26, 27, 31, 34, 39, 41, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 62, 63, 64, 65, 66, 67, 70, 71, 72, 73, 74], "excluded_lines": [77, 78]}}}}, "totals": {"covered_lines": 2005, "num_statements": 7948, "percent_covered": 25.22647206844489, "percent_covered_display": "25", "missing_lines": 5943, "excluded_lines": 2}}