{"metadata": {"name": "API测试用例数据", "description": "RESTful API接口测试用例集合", "version": "1.0", "created_date": "2024-01-01", "base_url": "http://localhost:5000/api"}, "test_suites": [{"name": "会话管理API测试", "description": "测试会话状态管理相关API", "test_cases": [{"name": "获取会话状态", "method": "GET", "endpoint": "/session/status", "headers": {"Content-Type": "application/json"}, "expected_status": 200, "expected_response": {"success": true, "data": {"has_task_hierarchy": false, "has_evaluation_scheme": false, "alternatives_count": 0, "evaluation_results_count": 0, "decision_methods": []}}}, {"name": "清空会话数据", "method": "POST", "endpoint": "/session/clear", "headers": {"Content-Type": "application/json"}, "expected_status": 200, "expected_response": {"success": true, "data": {"message": "会话数据已清空"}}}]}, {"name": "任务管理API测试", "description": "测试任务创建和管理相关API", "test_cases": [{"name": "创建任务结构", "method": "POST", "endpoint": "/task/create", "headers": {"Content-Type": "application/json"}, "request_body": {"scenario": "态势分析"}, "expected_status": 200, "expected_response": {"success": true, "data": {"hierarchy": {"root_task_id": "string", "tasks": "object"}, "task_count": "number", "scenario": "态势分析"}}}, {"name": "获取任务结构", "method": "GET", "endpoint": "/task/hierarchy", "headers": {"Content-Type": "application/json"}, "expected_status": 200, "expected_response": {"success": true, "data": {"hierarchy": "object"}}}, {"name": "无效场景测试", "method": "POST", "endpoint": "/task/create", "headers": {"Content-Type": "application/json"}, "request_body": {"scenario": "invalid_scenario"}, "expected_status": 400, "expected_response": {"success": false, "error": "不支持的场景类型"}}]}, {"name": "评估方案API测试", "description": "测试评估方案管理相关API", "test_cases": [{"name": "创建默认评估方案", "method": "POST", "endpoint": "/evaluation/scheme/create", "headers": {"Content-Type": "application/json"}, "request_body": {"type": "default"}, "expected_status": 200, "expected_response": {"success": true, "data": {"scheme": {"id": "string", "name": "string", "indicators": "object"}, "indicator_count": 10}}}, {"name": "创建自定义评估方案", "method": "POST", "endpoint": "/evaluation/scheme/create", "headers": {"Content-Type": "application/json"}, "request_body": {"type": "custom", "name": "测试自定义方案", "description": "用于测试的自定义评估方案", "indicators": [{"name": "效率", "type": "EFFICIENCY", "weight": 0.4, "is_benefit": true}, {"name": "成本", "type": "COST", "weight": 0.3, "is_benefit": false}, {"name": "可靠性", "type": "RELIABILITY", "weight": 0.3, "is_benefit": true}]}, "expected_status": 200, "expected_response": {"success": true, "data": {"scheme": "object", "indicator_count": 3}}}]}, {"name": "备选方案API测试", "description": "测试备选方案管理相关API", "test_cases": [{"name": "添加备选方案", "method": "POST", "endpoint": "/alternatives/add", "headers": {"Content-Type": "application/json"}, "request_body": {"alternatives": [{"name": "测试方案A", "description": "用于API测试的方案A", "attributes": {"efficiency": 0.8, "cost": 0.3, "reliability": 0.9}}, {"name": "测试方案B", "description": "用于API测试的方案B", "attributes": {"efficiency": 0.6, "cost": 0.7, "reliability": 0.7}}]}, "expected_status": 200, "expected_response": {"success": true, "data": {"added_count": 2, "total_count": 2}}}, {"name": "获取备选方案列表", "method": "GET", "endpoint": "/alternatives/list", "headers": {"Content-Type": "application/json"}, "expected_status": 200, "expected_response": {"success": true, "data": {"alternatives": "array", "count": "number"}}}]}, {"name": "评估执行API测试", "description": "测试方案评估执行相关API", "test_cases": [{"name": "执行WRDM评估", "method": "POST", "endpoint": "/evaluation/run", "headers": {"Content-Type": "application/json"}, "request_body": {"method": "WRDM"}, "expected_status": 200, "expected_response": {"success": true, "data": {"recommended_alternative": "object", "evaluation_result": "object", "method": "WRDM"}}}, {"name": "执行方案比较", "method": "POST", "endpoint": "/comparison/run", "headers": {"Content-Type": "application/json"}, "request_body": {"methods": ["WRDM", "TOPSIS"]}, "expected_status": 200, "expected_response": {"success": true, "data": {"comparison_results": "object", "consistency_analysis": "object"}}}, {"name": "无效方法测试", "method": "POST", "endpoint": "/evaluation/run", "headers": {"Content-Type": "application/json"}, "request_body": {"method": "INVALID_METHOD"}, "expected_status": 400, "expected_response": {"success": false, "error": "不支持的决策方法"}}]}, {"name": "数据管理API测试", "description": "测试数据导入导出相关API", "test_cases": [{"name": "导出JSON数据", "method": "POST", "endpoint": "/data/export", "headers": {"Content-Type": "application/json"}, "request_body": {"type": "json", "include": ["task_hierarchy", "evaluation_scheme", "alternatives"]}, "expected_status": 200, "expected_response": {"success": true, "data": {"export_data": "object", "timestamp": "string"}}}, {"name": "导入数据", "method": "POST", "endpoint": "/data/import", "headers": {"Content-Type": "multipart/form-data"}, "request_body": "file_upload", "expected_status": 200, "expected_response": {"success": true, "data": {"imported_items": "array", "import_count": "number"}}}]}], "error_test_cases": [{"name": "HTTP方法错误", "method": "PUT", "endpoint": "/session/status", "expected_status": 405, "expected_response": {"success": false, "error": "方法不允许"}}, {"name": "端点不存在", "method": "GET", "endpoint": "/nonexistent/endpoint", "expected_status": 404, "expected_response": {"success": false, "error": "端点不存在"}}, {"name": "请求体格式错误", "method": "POST", "endpoint": "/task/create", "headers": {"Content-Type": "application/json"}, "request_body": "invalid_json", "expected_status": 400, "expected_response": {"success": false, "error": "请求体格式错误"}}, {"name": "缺少必需参数", "method": "POST", "endpoint": "/task/create", "headers": {"Content-Type": "application/json"}, "request_body": {}, "expected_status": 400, "expected_response": {"success": false, "error": "缺少必需参数: scenario"}}], "performance_test_cases": [{"name": "并发请求测试", "description": "测试API的并发处理能力", "concurrent_users": 10, "requests_per_user": 5, "test_endpoint": "/session/status", "expected_avg_response_time": 1.0, "expected_success_rate": 0.99}, {"name": "大数据量处理测试", "description": "测试处理大量数据的能力", "endpoint": "/alternatives/add", "large_dataset_size": 100, "expected_max_response_time": 10.0, "expected_success_rate": 1.0}], "security_test_cases": [{"name": "SQL注入测试", "method": "POST", "endpoint": "/task/create", "request_body": {"scenario": "'; DROP TABLE tasks; --"}, "expected_status": 400, "expected_behavior": "安全过滤，不执行恶意代码"}, {"name": "XSS攻击测试", "method": "POST", "endpoint": "/alternatives/add", "request_body": {"alternatives": [{"name": "<script>alert('xss')</script>", "description": "XSS测试"}]}, "expected_status": 400, "expected_behavior": "过滤或转义恶意脚本"}], "test_execution_order": ["会话管理API测试", "任务管理API测试", "评估方案API测试", "备选方案API测试", "评估执行API测试", "数据管理API测试"], "cleanup_procedures": [{"step": 1, "action": "清空会话数据", "endpoint": "/session/clear"}, {"step": 2, "action": "重置测试环境", "description": "确保每个测试套件都在干净的环境中运行"}]}