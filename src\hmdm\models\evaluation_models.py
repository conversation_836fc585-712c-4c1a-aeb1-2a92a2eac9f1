"""
方案评估指标体系数据模型

定义负荷、效率等多维度评估指标和权重系统
"""

from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import numpy as np


class IndicatorType(Enum):
    """指标类型枚举"""
    WORKLOAD = "负荷指标"
    EFFICIENCY = "效率指标"
    RELIABILITY = "可靠性指标"
    USABILITY = "可用性指标"
    SAFETY = "安全性指标"
    COST = "成本指标"
    PERFORMANCE = "性能指标"


class IndicatorCategory(Enum):
    """指标分类"""
    QUANTITATIVE = "定量指标"
    QUALITATIVE = "定性指标"
    SUBJECTIVE = "主观指标"
    OBJECTIVE = "客观指标"


class AggregationMethod(Enum):
    """聚合方法"""
    WEIGHTED_SUM = "加权求和"
    WEIGHTED_AVERAGE = "加权平均"
    GEOMETRIC_MEAN = "几何平均"
    HARMONIC_MEAN = "调和平均"
    MIN = "最小值"
    MAX = "最大值"


@dataclass
class IndicatorDefinition:
    """指标定义"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    indicator_type: IndicatorType = IndicatorType.WORKLOAD
    category: IndicatorCategory = IndicatorCategory.QUANTITATIVE
    
    # 指标范围和单位
    min_value: float = 0.0
    max_value: float = 1.0
    unit: str = ""
    
    # 指标方向（True表示越大越好，False表示越小越好）
    is_benefit: bool = True
    
    # 计算相关
    formula: str = ""  # 计算公式
    data_sources: List[str] = field(default_factory=list)  # 数据来源
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 军事属性（可选）
    military_attributes: Dict[str, Any] = field(default_factory=dict)
    
    def normalize_value(self, value: float) -> float:
        """标准化指标值到[0,1]区间"""
        if self.max_value == self.min_value:
            return 0.0
        
        normalized = (value - self.min_value) / (self.max_value - self.min_value)
        return max(0.0, min(1.0, normalized))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "indicator_type": self.indicator_type.value,
            "category": self.category.value,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "unit": self.unit,
            "is_benefit": self.is_benefit,
            "formula": self.formula,
            "data_sources": self.data_sources,
            "created_at": self.created_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata
        }


@dataclass
class IndicatorWeight:
    """指标权重"""
    indicator_id: str
    weight: float = 0.0  # 权重值 [0,1]
    confidence: float = 1.0  # 权重置信度 [0,1]
    source: str = "专家评分"  # 权重来源
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "indicator_id": self.indicator_id,
            "weight": self.weight,
            "confidence": self.confidence,
            "source": self.source
        }


@dataclass
class IndicatorValue:
    """指标值"""
    indicator_id: str
    raw_value: float = 0.0  # 原始值
    normalized_value: float = 0.0  # 标准化值
    confidence: float = 1.0  # 置信度
    source: str = ""  # 数据来源
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "indicator_id": self.indicator_id,
            "raw_value": self.raw_value,
            "normalized_value": self.normalized_value,
            "confidence": self.confidence,
            "source": self.source,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class EvaluationScheme:
    """评估方案"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 指标体系
    indicators: Dict[str, IndicatorDefinition] = field(default_factory=dict)
    weights: Dict[str, IndicatorWeight] = field(default_factory=dict)
    
    # 聚合方法
    aggregation_method: AggregationMethod = AggregationMethod.WEIGHTED_SUM
    
    # 层次结构（支持多层次指标体系）
    hierarchy: Dict[str, List[str]] = field(default_factory=dict)  # parent_id -> [child_ids]
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    
    def add_indicator(self, indicator: IndicatorDefinition, weight: float = 0.0) -> None:
        """添加指标"""
        self.indicators[indicator.id] = indicator
        self.weights[indicator.id] = IndicatorWeight(
            indicator_id=indicator.id,
            weight=weight
        )
        self.updated_at = datetime.now()
    
    def set_weight(self, indicator_id: str, weight: float, confidence: float = 1.0, source: str = "专家评分") -> None:
        """设置指标权重"""
        if indicator_id in self.indicators:
            self.weights[indicator_id] = IndicatorWeight(
                indicator_id=indicator_id,
                weight=weight,
                confidence=confidence,
                source=source
            )
            self.updated_at = datetime.now()
    
    def normalize_weights(self) -> None:
        """权重归一化"""
        total_weight = sum(w.weight for w in self.weights.values())
        if total_weight > 0:
            for weight in self.weights.values():
                weight.weight /= total_weight
            self.updated_at = datetime.now()
    
    def get_indicator_by_type(self, indicator_type: IndicatorType) -> List[IndicatorDefinition]:
        """根据类型获取指标"""
        return [indicator for indicator in self.indicators.values() 
                if indicator.indicator_type == indicator_type]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "indicators": {k: v.to_dict() for k, v in self.indicators.items()},
            "weights": {k: v.to_dict() for k, v in self.weights.items()},
            "aggregation_method": self.aggregation_method.value,
            "hierarchy": self.hierarchy,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version
        }


@dataclass
class EvaluationResult:
    """评估结果"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    scheme_id: str = ""  # 评估方案ID
    target_id: str = ""  # 被评估对象ID（如方案ID）
    target_name: str = ""  # 被评估对象名称
    
    # 指标值
    indicator_values: Dict[str, IndicatorValue] = field(default_factory=dict)
    
    # 综合得分
    total_score: float = 0.0
    normalized_score: float = 0.0  # 标准化得分 [0,1]
    
    # 分类得分
    type_scores: Dict[str, float] = field(default_factory=dict)  # 按指标类型的得分
    
    # 评估元数据
    evaluation_time: datetime = field(default_factory=datetime.now)
    evaluator: str = ""
    confidence: float = 1.0
    notes: str = ""
    
    def add_indicator_value(self, indicator_value: IndicatorValue) -> None:
        """添加指标值"""
        self.indicator_values[indicator_value.indicator_id] = indicator_value
    
    def calculate_total_score(self, scheme: EvaluationScheme) -> float:
        """计算综合得分"""
        if scheme.aggregation_method == AggregationMethod.WEIGHTED_SUM:
            total = 0.0
            for indicator_id, value in self.indicator_values.items():
                if indicator_id in scheme.weights:
                    weight = scheme.weights[indicator_id].weight
                    total += weight * value.normalized_value
            self.total_score = total
        
        elif scheme.aggregation_method == AggregationMethod.WEIGHTED_AVERAGE:
            total_weighted = 0.0
            total_weight = 0.0
            for indicator_id, value in self.indicator_values.items():
                if indicator_id in scheme.weights:
                    weight = scheme.weights[indicator_id].weight
                    total_weighted += weight * value.normalized_value
                    total_weight += weight
            self.total_score = total_weighted / total_weight if total_weight > 0 else 0.0
        
        # 标准化得分
        self.normalized_score = max(0.0, min(1.0, self.total_score))
        return self.total_score
    
    def calculate_type_scores(self, scheme: EvaluationScheme) -> Dict[str, float]:
        """计算各类型指标得分"""
        type_scores = {}
        
        for indicator_type in IndicatorType:
            type_indicators = scheme.get_indicator_by_type(indicator_type)
            if not type_indicators:
                continue
            
            total_weighted = 0.0
            total_weight = 0.0
            
            for indicator in type_indicators:
                if indicator.id in self.indicator_values and indicator.id in scheme.weights:
                    value = self.indicator_values[indicator.id].normalized_value
                    weight = scheme.weights[indicator.id].weight
                    total_weighted += weight * value
                    total_weight += weight
            
            if total_weight > 0:
                type_scores[indicator_type.value] = total_weighted / total_weight
        
        self.type_scores = type_scores
        return type_scores
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "scheme_id": self.scheme_id,
            "target_id": self.target_id,
            "target_name": self.target_name,
            "indicator_values": {k: v.to_dict() for k, v in self.indicator_values.items()},
            "total_score": self.total_score,
            "normalized_score": self.normalized_score,
            "type_scores": self.type_scores,
            "evaluation_time": self.evaluation_time.isoformat(),
            "evaluator": self.evaluator,
            "confidence": self.confidence,
            "notes": self.notes
        }
