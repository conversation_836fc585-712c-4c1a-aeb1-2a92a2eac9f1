[pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config --cov=src --cov-report=term-missing --cov-report=html
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    smoke: marks tests as smoke tests
    asyncio: marks tests as async (deselect with '-m "not asyncio"')
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
