"""
智能预测引擎

集成机器学习模型，提供态势预测、威胁评估和决策优化功能。
支持多种机器学习算法和深度学习模型。
"""

import numpy as np
import pandas as pd
import logging
import pickle
import json
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import os
from pathlib import Path

# 机器学习库
try:
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    from sklearn.svm import SVC, SVR
    from sklearn.neural_network import MLPClassifier, MLPRegressor
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

# 深度学习库（可选）
try:
    import tensorflow as tf
    from tensorflow import keras
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

from ..security.military_security import SecurityLevel


class ModelType(Enum):
    """模型类型"""
    CLASSIFICATION = "分类"
    REGRESSION = "回归"
    CLUSTERING = "聚类"
    TIME_SERIES = "时间序列"
    DEEP_LEARNING = "深度学习"


class PredictionTask(Enum):
    """预测任务类型"""
    SITUATION_PREDICTION = "态势预测"
    THREAT_ASSESSMENT = "威胁评估"
    DECISION_OPTIMIZATION = "决策优化"
    RESOURCE_ALLOCATION = "资源分配"
    RISK_ANALYSIS = "风险分析"


@dataclass
class ModelConfig:
    """模型配置"""
    model_id: str
    model_type: ModelType
    task_type: PredictionTask
    algorithm: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    feature_columns: List[str] = field(default_factory=list)
    target_column: str = ""
    preprocessing_config: Dict[str, Any] = field(default_factory=dict)
    training_config: Dict[str, Any] = field(default_factory=dict)
    security_level: SecurityLevel = SecurityLevel.SECRET
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model_id': self.model_id,
            'model_type': self.model_type.value,
            'task_type': self.task_type.value,
            'algorithm': self.algorithm,
            'parameters': self.parameters,
            'feature_columns': self.feature_columns,
            'target_column': self.target_column,
            'preprocessing_config': self.preprocessing_config,
            'training_config': self.training_config,
            'security_level': self.security_level.value
        }


@dataclass
class PredictionResult:
    """预测结果"""
    model_id: str
    task_type: PredictionTask
    prediction: Union[float, int, str, List[Any]]
    confidence: float
    probability: Optional[Dict[str, float]] = None
    feature_importance: Optional[Dict[str, float]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model_id': self.model_id,
            'task_type': self.task_type.value,
            'prediction': self.prediction,
            'confidence': self.confidence,
            'probability': self.probability,
            'feature_importance': self.feature_importance,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class IntelligentPredictionEngine:
    """智能预测引擎"""
    
    def __init__(self, models_directory: str = "models"):
        self.logger = logging.getLogger(__name__)
        self.models_directory = Path(models_directory)
        self.models_directory.mkdir(exist_ok=True)
        
        # 模型注册表
        self.models: Dict[str, Any] = {}
        self.model_configs: Dict[str, ModelConfig] = {}
        self.scalers: Dict[str, StandardScaler] = {}
        self.encoders: Dict[str, LabelEncoder] = {}
        
        # 预测历史
        self.prediction_history: List[PredictionResult] = []
        
        # 检查依赖
        self._check_dependencies()
        
        # 加载已保存的模型
        self._load_saved_models()
        
        self.logger.info("智能预测引擎初始化完成")
    
    def _check_dependencies(self):
        """检查依赖库"""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("scikit-learn未安装，部分功能将不可用")
        
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，深度学习功能将不可用")
    
    def create_model(self, config: ModelConfig) -> bool:
        """创建机器学习模型"""
        try:
            if not SKLEARN_AVAILABLE and config.model_type != ModelType.DEEP_LEARNING:
                raise ValueError("scikit-learn未安装，无法创建传统机器学习模型")
            
            model = None
            
            # 根据算法类型创建模型
            if config.algorithm == "random_forest":
                if config.model_type == ModelType.CLASSIFICATION:
                    model = RandomForestClassifier(**config.parameters)
                elif config.model_type == ModelType.REGRESSION:
                    model = RandomForestRegressor(**config.parameters)
            
            elif config.algorithm == "svm":
                if config.model_type == ModelType.CLASSIFICATION:
                    model = SVC(**config.parameters)
                elif config.model_type == ModelType.REGRESSION:
                    model = SVR(**config.parameters)
            
            elif config.algorithm == "neural_network":
                if config.model_type == ModelType.CLASSIFICATION:
                    model = MLPClassifier(**config.parameters)
                elif config.model_type == ModelType.REGRESSION:
                    model = MLPRegressor(**config.parameters)
            
            elif config.algorithm == "deep_learning":
                if not TENSORFLOW_AVAILABLE:
                    raise ValueError("TensorFlow未安装，无法创建深度学习模型")
                model = self._create_deep_learning_model(config)
            
            else:
                raise ValueError(f"不支持的算法类型: {config.algorithm}")
            
            if model is None:
                raise ValueError("模型创建失败")
            
            # 注册模型
            self.models[config.model_id] = model
            self.model_configs[config.model_id] = config
            
            # 创建预处理器
            self.scalers[config.model_id] = StandardScaler()
            self.encoders[config.model_id] = LabelEncoder()
            
            self.logger.info(f"模型创建成功: {config.model_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建模型失败: {e}")
            return False
    
    def train_model(self, model_id: str, training_data: pd.DataFrame) -> bool:
        """训练模型"""
        try:
            if model_id not in self.models:
                raise ValueError(f"模型不存在: {model_id}")
            
            model = self.models[model_id]
            config = self.model_configs[model_id]
            
            # 数据预处理
            X, y = self._preprocess_training_data(model_id, training_data, config)
            
            # 分割训练和验证数据
            test_size = config.training_config.get('test_size', 0.2)
            random_state = config.training_config.get('random_state', 42)
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state
            )
            
            # 训练模型
            if config.algorithm == "deep_learning":
                # 深度学习模型训练
                epochs = config.training_config.get('epochs', 100)
                batch_size = config.training_config.get('batch_size', 32)
                validation_split = config.training_config.get('validation_split', 0.2)
                
                history = model.fit(
                    X_train, y_train,
                    epochs=epochs,
                    batch_size=batch_size,
                    validation_split=validation_split,
                    verbose=0
                )
                
                # 评估模型
                test_loss = model.evaluate(X_test, y_test, verbose=0)
                self.logger.info(f"模型 {model_id} 测试损失: {test_loss}")
                
            else:
                # 传统机器学习模型训练
                model.fit(X_train, y_train)
                
                # 评估模型
                y_pred = model.predict(X_test)
                
                if config.model_type == ModelType.CLASSIFICATION:
                    accuracy = accuracy_score(y_test, y_pred)
                    precision = precision_score(y_test, y_pred, average='weighted')
                    recall = recall_score(y_test, y_pred, average='weighted')
                    f1 = f1_score(y_test, y_pred, average='weighted')
                    
                    self.logger.info(f"模型 {model_id} 分类性能:")
                    self.logger.info(f"  准确率: {accuracy:.4f}")
                    self.logger.info(f"  精确率: {precision:.4f}")
                    self.logger.info(f"  召回率: {recall:.4f}")
                    self.logger.info(f"  F1分数: {f1:.4f}")
                    
                elif config.model_type == ModelType.REGRESSION:
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    
                    self.logger.info(f"模型 {model_id} 回归性能:")
                    self.logger.info(f"  均方误差: {mse:.4f}")
                    self.logger.info(f"  平均绝对误差: {mae:.4f}")
                    self.logger.info(f"  R²分数: {r2:.4f}")
            
            # 保存模型
            self._save_model(model_id)
            
            self.logger.info(f"模型训练完成: {model_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"训练模型失败: {e}")
            return False
    
    def predict(self, model_id: str, input_data: Union[pd.DataFrame, Dict[str, Any]]) -> Optional[PredictionResult]:
        """进行预测"""
        try:
            if model_id not in self.models:
                raise ValueError(f"模型不存在: {model_id}")
            
            model = self.models[model_id]
            config = self.model_configs[model_id]
            
            # 数据预处理
            X = self._preprocess_prediction_data(model_id, input_data, config)
            
            # 进行预测
            if config.algorithm == "deep_learning":
                prediction_raw = model.predict(X)
                prediction = prediction_raw[0] if len(prediction_raw) > 0 else None
                confidence = float(np.max(prediction_raw)) if prediction_raw is not None else 0.0
                probability = None
                
            else:
                prediction = model.predict(X)[0]
                
                # 计算置信度
                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba(X)[0]
                    confidence = float(np.max(proba))
                    
                    # 获取类别概率
                    if hasattr(model, 'classes_'):
                        probability = {
                            str(cls): float(prob) 
                            for cls, prob in zip(model.classes_, proba)
                        }
                    else:
                        probability = None
                else:
                    confidence = 0.8  # 默认置信度
                    probability = None
            
            # 获取特征重要性
            feature_importance = None
            if hasattr(model, 'feature_importances_'):
                feature_importance = {
                    feature: float(importance)
                    for feature, importance in zip(config.feature_columns, model.feature_importances_)
                }
            
            # 创建预测结果
            result = PredictionResult(
                model_id=model_id,
                task_type=config.task_type,
                prediction=prediction,
                confidence=confidence,
                probability=probability,
                feature_importance=feature_importance,
                metadata={
                    'algorithm': config.algorithm,
                    'model_type': config.model_type.value,
                    'input_features': len(config.feature_columns)
                }
            )
            
            # 记录预测历史
            self.prediction_history.append(result)
            
            self.logger.info(f"预测完成: {model_id}, 结果: {prediction}, 置信度: {confidence:.4f}")
            return result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            return None

    def batch_predict(self, model_id: str, input_data_list: List[Union[pd.DataFrame, Dict[str, Any]]]) -> List[Optional[PredictionResult]]:
        """批量预测"""
        results = []
        for input_data in input_data_list:
            result = self.predict(model_id, input_data)
            results.append(result)
        return results

    def get_model_performance(self, model_id: str) -> Dict[str, Any]:
        """获取模型性能指标"""
        try:
            if model_id not in self.models:
                return {}

            config = self.model_configs[model_id]

            # 从预测历史中计算性能指标
            model_predictions = [
                p for p in self.prediction_history
                if p.model_id == model_id
            ]

            if not model_predictions:
                return {'message': '暂无预测历史数据'}

            # 计算基本统计
            confidences = [p.confidence for p in model_predictions]
            avg_confidence = np.mean(confidences)
            min_confidence = np.min(confidences)
            max_confidence = np.max(confidences)

            performance = {
                'model_id': model_id,
                'task_type': config.task_type.value,
                'algorithm': config.algorithm,
                'total_predictions': len(model_predictions),
                'average_confidence': float(avg_confidence),
                'min_confidence': float(min_confidence),
                'max_confidence': float(max_confidence),
                'last_prediction_time': model_predictions[-1].timestamp.isoformat()
            }

            return performance

        except Exception as e:
            self.logger.error(f"获取模型性能失败: {e}")
            return {}

    def get_prediction_history(self, model_id: Optional[str] = None,
                             task_type: Optional[PredictionTask] = None,
                             limit: int = 100) -> List[PredictionResult]:
        """获取预测历史"""
        try:
            history = self.prediction_history.copy()

            # 过滤条件
            if model_id:
                history = [p for p in history if p.model_id == model_id]

            if task_type:
                history = [p for p in history if p.task_type == task_type]

            # 按时间倒序排列并限制数量
            history.sort(key=lambda x: x.timestamp, reverse=True)
            return history[:limit]

        except Exception as e:
            self.logger.error(f"获取预测历史失败: {e}")
            return []

    def _create_deep_learning_model(self, config: ModelConfig) -> Optional[Any]:
        """创建深度学习模型"""
        try:
            if not TENSORFLOW_AVAILABLE:
                return None

            # 获取网络结构参数
            input_dim = len(config.feature_columns)
            hidden_layers = config.parameters.get('hidden_layers', [64, 32])
            activation = config.parameters.get('activation', 'relu')
            dropout_rate = config.parameters.get('dropout_rate', 0.2)

            # 构建模型
            model = keras.Sequential()

            # 输入层
            model.add(keras.layers.Dense(
                hidden_layers[0],
                activation=activation,
                input_shape=(input_dim,)
            ))
            model.add(keras.layers.Dropout(dropout_rate))

            # 隐藏层
            for units in hidden_layers[1:]:
                model.add(keras.layers.Dense(units, activation=activation))
                model.add(keras.layers.Dropout(dropout_rate))

            # 输出层
            if config.model_type == ModelType.CLASSIFICATION:
                output_units = config.parameters.get('num_classes', 2)
                output_activation = 'softmax' if output_units > 2 else 'sigmoid'
                model.add(keras.layers.Dense(output_units, activation=output_activation))

                # 编译模型
                optimizer = config.parameters.get('optimizer', 'adam')
                loss = 'sparse_categorical_crossentropy' if output_units > 2 else 'binary_crossentropy'
                model.compile(optimizer=optimizer, loss=loss, metrics=['accuracy'])

            elif config.model_type == ModelType.REGRESSION:
                model.add(keras.layers.Dense(1, activation='linear'))

                # 编译模型
                optimizer = config.parameters.get('optimizer', 'adam')
                loss = config.parameters.get('loss', 'mse')
                model.compile(optimizer=optimizer, loss=loss, metrics=['mae'])

            return model

        except Exception as e:
            self.logger.error(f"创建深度学习模型失败: {e}")
            return None

    def _preprocess_training_data(self, model_id: str, data: pd.DataFrame, config: ModelConfig) -> Tuple[np.ndarray, np.ndarray]:
        """预处理训练数据"""
        try:
            # 提取特征和目标
            X = data[config.feature_columns].copy()
            y = data[config.target_column].copy()

            # 处理缺失值
            X = X.fillna(X.mean() if X.select_dtypes(include=[np.number]).shape[1] > 0 else X.mode().iloc[0])

            # 特征缩放
            scaler = self.scalers[model_id]
            X_scaled = scaler.fit_transform(X)

            # 目标编码（分类任务）
            if config.model_type == ModelType.CLASSIFICATION:
                encoder = self.encoders[model_id]
                y_encoded = encoder.fit_transform(y)
                return X_scaled, y_encoded
            else:
                return X_scaled, y.values

        except Exception as e:
            self.logger.error(f"预处理训练数据失败: {e}")
            raise

    def _preprocess_prediction_data(self, model_id: str, data: Union[pd.DataFrame, Dict[str, Any]], config: ModelConfig) -> np.ndarray:
        """预处理预测数据"""
        try:
            # 转换为DataFrame
            if isinstance(data, dict):
                data = pd.DataFrame([data])

            # 提取特征
            X = data[config.feature_columns].copy()

            # 处理缺失值
            X = X.fillna(X.mean() if X.select_dtypes(include=[np.number]).shape[1] > 0 else 0)

            # 特征缩放
            scaler = self.scalers[model_id]
            X_scaled = scaler.transform(X)

            return X_scaled

        except Exception as e:
            self.logger.error(f"预处理预测数据失败: {e}")
            raise

    def _save_model(self, model_id: str):
        """保存模型"""
        try:
            model_dir = self.models_directory / model_id
            model_dir.mkdir(exist_ok=True)

            model = self.models[model_id]
            config = self.model_configs[model_id]

            # 保存配置
            config_file = model_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)

            # 保存模型
            if config.algorithm == "deep_learning":
                model_file = model_dir / "model.h5"
                model.save(str(model_file))
            else:
                model_file = model_dir / "model.pkl"
                with open(model_file, 'wb') as f:
                    pickle.dump(model, f)

            # 保存预处理器
            scaler_file = model_dir / "scaler.pkl"
            with open(scaler_file, 'wb') as f:
                pickle.dump(self.scalers[model_id], f)

            encoder_file = model_dir / "encoder.pkl"
            with open(encoder_file, 'wb') as f:
                pickle.dump(self.encoders[model_id], f)

            self.logger.info(f"模型已保存: {model_id}")

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")

    def _load_saved_models(self):
        """加载已保存的模型"""
        try:
            if not self.models_directory.exists():
                return

            for model_dir in self.models_directory.iterdir():
                if not model_dir.is_dir():
                    continue

                model_id = model_dir.name
                config_file = model_dir / "config.json"

                if not config_file.exists():
                    continue

                try:
                    # 加载配置
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_dict = json.load(f)

                    # 重建配置对象
                    config_dict['model_type'] = ModelType(config_dict['model_type'])
                    config_dict['task_type'] = PredictionTask(config_dict['task_type'])
                    config_dict['security_level'] = SecurityLevel(config_dict['security_level'])

                    config = ModelConfig(**config_dict)
                    self.model_configs[model_id] = config

                    # 加载模型
                    if config.algorithm == "deep_learning":
                        if TENSORFLOW_AVAILABLE:
                            model_file = model_dir / "model.h5"
                            if model_file.exists():
                                model = keras.models.load_model(str(model_file))
                                self.models[model_id] = model
                    else:
                        model_file = model_dir / "model.pkl"
                        if model_file.exists():
                            with open(model_file, 'rb') as f:
                                model = pickle.load(f)
                                self.models[model_id] = model

                    # 加载预处理器
                    scaler_file = model_dir / "scaler.pkl"
                    if scaler_file.exists():
                        with open(scaler_file, 'rb') as f:
                            self.scalers[model_id] = pickle.load(f)

                    encoder_file = model_dir / "encoder.pkl"
                    if encoder_file.exists():
                        with open(encoder_file, 'rb') as f:
                            self.encoders[model_id] = pickle.load(f)

                    self.logger.info(f"已加载模型: {model_id}")

                except Exception as e:
                    self.logger.error(f"加载模型 {model_id} 失败: {e}")

        except Exception as e:
            self.logger.error(f"加载已保存模型失败: {e}")

    def list_models(self) -> List[Dict[str, Any]]:
        """列出所有模型"""
        models_info = []
        for model_id, config in self.model_configs.items():
            model_info = {
                'model_id': model_id,
                'task_type': config.task_type.value,
                'algorithm': config.algorithm,
                'model_type': config.model_type.value,
                'feature_count': len(config.feature_columns),
                'is_loaded': model_id in self.models,
                'security_level': config.security_level.value
            }
            models_info.append(model_info)

        return models_info

    def delete_model(self, model_id: str) -> bool:
        """删除模型"""
        try:
            # 从内存中删除
            if model_id in self.models:
                del self.models[model_id]
            if model_id in self.model_configs:
                del self.model_configs[model_id]
            if model_id in self.scalers:
                del self.scalers[model_id]
            if model_id in self.encoders:
                del self.encoders[model_id]

            # 删除文件
            model_dir = self.models_directory / model_id
            if model_dir.exists():
                import shutil
                shutil.rmtree(model_dir)

            self.logger.info(f"模型已删除: {model_id}")
            return True

        except Exception as e:
            self.logger.error(f"删除模型失败: {e}")
            return False
