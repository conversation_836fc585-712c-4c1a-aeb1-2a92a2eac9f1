"""
军事知识库与专家系统

提供军事领域的专业知识管理、规则推理和专家建议功能。
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import re

from ..security.military_security import SecurityLevel


class KnowledgeType(Enum):
    """知识类型"""
    DOCTRINE = "军事条令"
    TACTICS = "战术知识"
    PROCEDURES = "作战程序"
    EQUIPMENT = "装备知识"
    INTELLIGENCE = "情报知识"
    LOGISTICS = "后勤知识"
    HISTORICAL = "历史经验"
    BEST_PRACTICES = "最佳实践"


class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


@dataclass
class KnowledgeItem:
    """知识条目"""
    id: str
    title: str
    content: str
    knowledge_type: KnowledgeType
    keywords: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    security_level: SecurityLevel = SecurityLevel.SECRET
    confidence: float = 0.8
    source: str = ""
    author: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    references: List[str] = field(default_factory=list)
    related_items: List[str] = field(default_factory=list)
    
    def matches_query(self, query: str, keywords: List[str] = None) -> float:
        """计算与查询的匹配度"""
        score = 0.0
        query_lower = query.lower()
        
        # 标题匹配
        if query_lower in self.title.lower():
            score += 0.4
        
        # 内容匹配
        content_matches = len(re.findall(re.escape(query_lower), self.content.lower()))
        score += min(0.3, content_matches * 0.1)
        
        # 关键词匹配
        if keywords:
            keyword_matches = len(set(keywords) & set(self.keywords))
            score += min(0.3, keyword_matches * 0.1)
        
        return min(1.0, score)


@dataclass
class Rule:
    """推理规则"""
    id: str
    name: str
    description: str
    conditions: List[str] = field(default_factory=list)  # 条件列表
    conclusions: List[str] = field(default_factory=list)  # 结论列表
    confidence: float = 0.8
    priority: int = 1  # 优先级，数字越大优先级越高
    enabled: bool = True
    
    def evaluate(self, facts: Set[str]) -> Tuple[bool, List[str]]:
        """评估规则是否适用"""
        if not self.enabled:
            return False, []
        
        # 检查所有条件是否满足
        satisfied_conditions = 0
        for condition in self.conditions:
            if condition in facts:
                satisfied_conditions += 1
        
        # 如果所有条件都满足，返回结论
        if satisfied_conditions == len(self.conditions) and len(self.conditions) > 0:
            return True, self.conclusions
        
        return False, []


@dataclass
class ExpertAdvice:
    """专家建议"""
    id: str = field(default_factory=lambda: f"advice_{int(datetime.now().timestamp())}")
    topic: str = ""
    advice: str = ""
    reasoning: str = ""
    confidence: float = 0.8
    applicable_scenarios: List[str] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


class MilitaryKnowledgeBase:
    """军事知识库"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 知识存储
        self.knowledge_items: Dict[str, KnowledgeItem] = {}
        self.rules: Dict[str, Rule] = {}
        
        # 索引
        self.keyword_index: Dict[str, Set[str]] = {}  # 关键词 -> 知识条目ID集合
        self.type_index: Dict[KnowledgeType, Set[str]] = {}  # 类型 -> 知识条目ID集合
        
        # 推理引擎状态
        self.facts: Set[str] = set()  # 当前事实集合
        self.inferred_facts: Set[str] = set()  # 推理得出的事实
        
        # 初始化默认知识
        self._initialize_default_knowledge()
    
    def add_knowledge_item(self, item: KnowledgeItem) -> bool:
        """添加知识条目"""
        try:
            self.knowledge_items[item.id] = item
            
            # 更新索引
            self._update_keyword_index(item)
            self._update_type_index(item)
            
            self.logger.info(f"添加知识条目: {item.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加知识条目失败: {e}")
            return False
    
    def add_rule(self, rule: Rule) -> bool:
        """添加推理规则"""
        try:
            self.rules[rule.id] = rule
            self.logger.info(f"添加推理规则: {rule.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加推理规则失败: {e}")
            return False
    
    def search_knowledge(self, 
                        query: str,
                        knowledge_type: Optional[KnowledgeType] = None,
                        keywords: List[str] = None,
                        limit: int = 10) -> List[Tuple[KnowledgeItem, float]]:
        """搜索知识"""
        try:
            results = []
            
            # 确定搜索范围
            search_items = []
            if knowledge_type:
                item_ids = self.type_index.get(knowledge_type, set())
                search_items = [self.knowledge_items[item_id] for item_id in item_ids]
            else:
                search_items = list(self.knowledge_items.values())
            
            # 计算匹配度
            for item in search_items:
                score = item.matches_query(query, keywords)
                if score > 0.1:  # 只返回相关度较高的结果
                    results.append((item, score))
            
            # 按相关度排序
            results.sort(key=lambda x: x[1], reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            self.logger.error(f"知识搜索失败: {e}")
            return []
    
    def get_related_knowledge(self, item_id: str, limit: int = 5) -> List[KnowledgeItem]:
        """获取相关知识"""
        if item_id not in self.knowledge_items:
            return []
        
        item = self.knowledge_items[item_id]
        related_items = []
        
        # 直接关联的知识
        for related_id in item.related_items:
            if related_id in self.knowledge_items:
                related_items.append(self.knowledge_items[related_id])
        
        # 基于关键词的相关知识
        if len(related_items) < limit:
            for keyword in item.keywords:
                if keyword in self.keyword_index:
                    for candidate_id in self.keyword_index[keyword]:
                        if (candidate_id != item_id and 
                            candidate_id not in [r.id for r in related_items] and
                            len(related_items) < limit):
                            related_items.append(self.knowledge_items[candidate_id])
        
        return related_items[:limit]
    
    def add_fact(self, fact: str) -> None:
        """添加事实"""
        self.facts.add(fact)
        self.logger.debug(f"添加事实: {fact}")
    
    def remove_fact(self, fact: str) -> None:
        """移除事实"""
        self.facts.discard(fact)
        self.inferred_facts.discard(fact)
        self.logger.debug(f"移除事实: {fact}")
    
    def infer_knowledge(self, max_iterations: int = 10) -> List[str]:
        """知识推理"""
        try:
            new_facts = []
            all_facts = self.facts | self.inferred_facts
            
            for iteration in range(max_iterations):
                iteration_new_facts = []
                
                # 按优先级排序规则
                sorted_rules = sorted(self.rules.values(), key=lambda r: r.priority, reverse=True)
                
                for rule in sorted_rules:
                    applicable, conclusions = rule.evaluate(all_facts)
                    
                    if applicable:
                        for conclusion in conclusions:
                            if conclusion not in all_facts:
                                iteration_new_facts.append(conclusion)
                                all_facts.add(conclusion)
                                self.inferred_facts.add(conclusion)
                                self.logger.debug(f"推理得出: {conclusion} (规则: {rule.name})")
                
                new_facts.extend(iteration_new_facts)
                
                # 如果没有新的推理结果，停止迭代
                if not iteration_new_facts:
                    break
            
            return new_facts
            
        except Exception as e:
            self.logger.error(f"知识推理失败: {e}")
            return []
    
    def get_expert_advice(self, 
                         topic: str,
                         context: Dict[str, Any] = None) -> List[ExpertAdvice]:
        """获取专家建议"""
        try:
            advice_list = []
            
            # 搜索相关知识
            related_knowledge = self.search_knowledge(topic, limit=5)
            
            if not related_knowledge:
                return advice_list
            
            # 基于知识生成建议
            for item, score in related_knowledge:
                if score > 0.2:  # 降低相关度阈值，使更多知识能生成建议
                    advice = self._generate_advice_from_knowledge(item, topic, context)
                    if advice:
                        advice_list.append(advice)
            
            # 基于推理规则生成建议
            if context:
                self._add_context_facts(context)
                inferred_facts = self.infer_knowledge()
                
                if inferred_facts:
                    contextual_advice = self._generate_advice_from_inference(
                        topic, inferred_facts, context
                    )
                    if contextual_advice:
                        advice_list.append(contextual_advice)
            
            return advice_list
            
        except Exception as e:
            self.logger.error(f"获取专家建议失败: {e}")
            return []
    
    def get_knowledge_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            "total_items": len(self.knowledge_items),
            "total_rules": len(self.rules),
            "by_type": {},
            "by_security_level": {},
            "average_confidence": 0.0,
            "total_facts": len(self.facts),
            "inferred_facts": len(self.inferred_facts)
        }
        
        # 按类型统计
        for item in self.knowledge_items.values():
            type_name = item.knowledge_type.value
            stats["by_type"][type_name] = stats["by_type"].get(type_name, 0) + 1
            
            # 按安全等级统计
            level_name = item.security_level.value
            stats["by_security_level"][level_name] = stats["by_security_level"].get(level_name, 0) + 1
        
        # 平均置信度
        if self.knowledge_items:
            total_confidence = sum(item.confidence for item in self.knowledge_items.values())
            stats["average_confidence"] = total_confidence / len(self.knowledge_items)
        
        return stats
    
    def _initialize_default_knowledge(self) -> None:
        """初始化默认知识"""
        # 添加基础军事知识
        basic_tactics = KnowledgeItem(
            id="basic_tactics_001",
            title="基础战术原则",
            content="军事行动应遵循集中兵力、出其不意、速战速决等基本原则。在态势分析中，应重点关注敌我双方的兵力对比、地形条件、天气因素等关键要素。",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["战术", "兵力", "态势分析", "地形", "天气"],
            tags=["基础", "原则"],
            confidence=0.9
        )
        
        threat_assessment = KnowledgeItem(
            id="threat_assessment_001",
            title="威胁评估方法",
            content="威胁评估应综合考虑敌方的意图、能力和机会。意图分析包括战略目标和战术意图；能力评估包括兵力规模、装备水平、训练程度；机会分析包括地理位置、时间窗口等。",
            knowledge_type=KnowledgeType.INTELLIGENCE,
            keywords=["威胁评估", "意图", "能力", "机会", "情报"],
            tags=["评估", "方法"],
            confidence=0.85
        )
        
        self.add_knowledge_item(basic_tactics)
        self.add_knowledge_item(threat_assessment)
        
        # 添加基础推理规则
        threat_rule = Rule(
            id="threat_level_rule",
            name="威胁等级判定规则",
            description="根据敌方兵力和距离判定威胁等级",
            conditions=["敌方兵力优势", "距离较近"],
            conclusions=["威胁等级高"],
            confidence=0.8,
            priority=2
        )
        
        defense_rule = Rule(
            id="defense_strategy_rule",
            name="防御策略规则",
            description="高威胁情况下采用防御策略",
            conditions=["威胁等级高", "兵力劣势"],
            conclusions=["建议采用防御策略", "请求增援"],
            confidence=0.9,
            priority=3
        )
        
        self.add_rule(threat_rule)
        self.add_rule(defense_rule)
    
    def _update_keyword_index(self, item: KnowledgeItem) -> None:
        """更新关键词索引"""
        for keyword in item.keywords:
            if keyword not in self.keyword_index:
                self.keyword_index[keyword] = set()
            self.keyword_index[keyword].add(item.id)
    
    def _update_type_index(self, item: KnowledgeItem) -> None:
        """更新类型索引"""
        if item.knowledge_type not in self.type_index:
            self.type_index[item.knowledge_type] = set()
        self.type_index[item.knowledge_type].add(item.id)
    
    def _generate_advice_from_knowledge(self, 
                                      item: KnowledgeItem,
                                      topic: str,
                                      context: Dict[str, Any] = None) -> Optional[ExpertAdvice]:
        """基于知识条目生成建议"""
        try:
            advice = ExpertAdvice(
                topic=topic,
                advice=f"基于{item.knowledge_type.value}的建议：{item.content[:200]}...",
                reasoning=f"根据知识条目'{item.title}'的内容分析",
                confidence=item.confidence * 0.8,  # 降低置信度以反映推理的不确定性
                applicable_scenarios=[item.knowledge_type.value]
            )
            
            return advice
            
        except Exception as e:
            self.logger.error(f"生成知识建议失败: {e}")
            return None
    
    def _generate_advice_from_inference(self,
                                      topic: str,
                                      inferred_facts: List[str],
                                      context: Dict[str, Any]) -> Optional[ExpertAdvice]:
        """基于推理结果生成建议"""
        try:
            if not inferred_facts:
                return None
            
            advice_text = "基于当前态势推理，建议："
            for fact in inferred_facts[:3]:  # 只使用前3个推理结果
                advice_text += f"\n- {fact}"
            
            advice = ExpertAdvice(
                topic=topic,
                advice=advice_text,
                reasoning="基于规则推理引擎的分析结果",
                confidence=0.7,
                applicable_scenarios=["推理分析"]
            )
            
            return advice
            
        except Exception as e:
            self.logger.error(f"生成推理建议失败: {e}")
            return None
    
    def _add_context_facts(self, context: Dict[str, Any]) -> None:
        """添加上下文事实"""
        for key, value in context.items():
            if isinstance(value, bool) and value:
                self.add_fact(key)
            elif isinstance(value, str):
                self.add_fact(f"{key}={value}")
            elif isinstance(value, (int, float)) and value > 0:
                self.add_fact(f"{key}={value}")
