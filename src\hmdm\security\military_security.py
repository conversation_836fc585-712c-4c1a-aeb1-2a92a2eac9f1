"""
军事安全模块

实现军用级安全机制，包括访问控制、数据加密、审计日志等功能。
"""

import hashlib
import hmac
import secrets
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json


class SecurityLevel(Enum):
    """安全等级枚举"""
    PUBLIC = "公开"
    INTERNAL = "内部"
    SECRET = "秘密"
    CONFIDENTIAL = "机密"
    TOP_SECRET = "绝密"


class UserRole(Enum):
    """用户角色枚举"""
    OPERATOR = "操作员"
    ANALYST = "分析员"
    COMMANDER = "指挥员"
    ADMIN = "管理员"
    SYSTEM = "系统"


class OperationType(Enum):
    """操作类型枚举"""
    LOGIN = "登录"
    LOGOUT = "登出"
    ACCESS = "访问"
    MODIFY = "修改"
    DELETE = "删除"
    EXPORT = "导出"
    DECISION = "决策"
    ANALYSIS = "分析"


@dataclass
class SecurityCredential:
    """安全凭证"""
    user_id: str
    username: str
    role: UserRole
    security_clearance: SecurityLevel
    permissions: List[str] = field(default_factory=list)
    session_token: str = ""
    expires_at: datetime = field(default_factory=lambda: datetime.now() + timedelta(hours=8))
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    
    def is_valid(self) -> bool:
        """检查凭证是否有效"""
        return datetime.now() < self.expires_at
    
    def has_permission(self, permission: str) -> bool:
        """检查是否有指定权限"""
        return permission in self.permissions or "admin" in self.permissions
    
    def can_access_level(self, level: SecurityLevel) -> bool:
        """检查是否可以访问指定安全等级的资源"""
        level_hierarchy = {
            SecurityLevel.PUBLIC: 0,
            SecurityLevel.INTERNAL: 1,
            SecurityLevel.SECRET: 2,
            SecurityLevel.CONFIDENTIAL: 3,
            SecurityLevel.TOP_SECRET: 4
        }
        return level_hierarchy[self.security_clearance] >= level_hierarchy[level]


@dataclass
class AuditLog:
    """审计日志"""
    id: str = field(default_factory=lambda: secrets.token_hex(16))
    user_id: str = ""
    username: str = ""
    operation: OperationType = OperationType.ACCESS
    resource: str = ""
    security_level: SecurityLevel = SecurityLevel.INTERNAL
    success: bool = True
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    ip_address: str = ""
    user_agent: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "username": self.username,
            "operation": self.operation.value,
            "resource": self.resource,
            "security_level": self.security_level.value,
            "success": self.success,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "ip_address": self.ip_address,
            "user_agent": self.user_agent
        }


class MilitarySecurityManager:
    """军事安全管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_sessions: Dict[str, SecurityCredential] = {}
        self.audit_logs: List[AuditLog] = []
        self.failed_attempts: Dict[str, List[datetime]] = {}
        self.secret_key = secrets.token_hex(32)  # 在生产环境中应从安全配置加载
        
        # 角色权限映射
        self.role_permissions = {
            UserRole.OPERATOR: ["read", "basic_analysis"],
            UserRole.ANALYST: ["read", "analysis", "report", "basic_decision"],
            UserRole.COMMANDER: ["read", "analysis", "decision", "command", "export"],
            UserRole.ADMIN: ["admin", "read", "write", "delete", "analysis", "decision", "command", "export", "audit"],
            UserRole.SYSTEM: ["system", "admin", "read", "write", "delete"]
        }
        
        # 角色安全等级映射
        self.role_clearance = {
            UserRole.OPERATOR: SecurityLevel.SECRET,
            UserRole.ANALYST: SecurityLevel.CONFIDENTIAL,
            UserRole.COMMANDER: SecurityLevel.TOP_SECRET,
            UserRole.ADMIN: SecurityLevel.TOP_SECRET,
            UserRole.SYSTEM: SecurityLevel.TOP_SECRET
        }
    
    def authenticate_user(self, username: str, password: str, 
                         role: UserRole = UserRole.OPERATOR) -> Optional[SecurityCredential]:
        """用户认证"""
        # 检查失败尝试次数
        if self._is_account_locked(username):
            self._log_audit(
                username=username,
                operation=OperationType.LOGIN,
                success=False,
                details={"reason": "账户被锁定"}
            )
            return None
        
        # 简化的密码验证（生产环境中应使用更安全的方法）
        if self._verify_password(username, password):
            # 创建安全凭证
            credential = SecurityCredential(
                user_id=f"user_{username}",
                username=username,
                role=role,
                security_clearance=self.role_clearance[role],
                permissions=self.role_permissions[role].copy(),
                session_token=self._generate_session_token(username)
            )
            
            # 存储活动会话
            self.active_sessions[credential.session_token] = credential
            
            # 记录审计日志
            self._log_audit(
                user_id=credential.user_id,
                username=username,
                operation=OperationType.LOGIN,
                success=True,
                details={"role": role.value, "clearance": credential.security_clearance.value}
            )
            
            # 清除失败尝试记录
            if username in self.failed_attempts:
                del self.failed_attempts[username]
            
            return credential
        else:
            # 记录失败尝试
            self._record_failed_attempt(username)
            self._log_audit(
                username=username,
                operation=OperationType.LOGIN,
                success=False,
                details={"reason": "密码错误"}
            )
            return None
    
    def validate_session(self, session_token: str) -> Optional[SecurityCredential]:
        """验证会话"""
        if session_token not in self.active_sessions:
            return None
        
        credential = self.active_sessions[session_token]
        if not credential.is_valid():
            # 会话过期，移除
            del self.active_sessions[session_token]
            self._log_audit(
                user_id=credential.user_id,
                username=credential.username,
                operation=OperationType.LOGOUT,
                success=True,
                details={"reason": "会话过期"}
            )
            return None
        
        # 更新最后活动时间
        credential.last_activity = datetime.now()
        return credential
    
    def check_access_permission(self, session_token: str, resource: str, 
                              security_level: SecurityLevel, 
                              required_permission: str) -> bool:
        """检查访问权限"""
        credential = self.validate_session(session_token)
        if not credential:
            self._log_audit(
                operation=OperationType.ACCESS,
                resource=resource,
                security_level=security_level,
                success=False,
                details={"reason": "无效会话"}
            )
            return False
        
        # 检查安全等级权限
        if not credential.can_access_level(security_level):
            self._log_audit(
                user_id=credential.user_id,
                username=credential.username,
                operation=OperationType.ACCESS,
                resource=resource,
                security_level=security_level,
                success=False,
                details={"reason": "安全等级不足", "required": security_level.value, 
                        "current": credential.security_clearance.value}
            )
            return False
        
        # 检查功能权限
        if not credential.has_permission(required_permission):
            self._log_audit(
                user_id=credential.user_id,
                username=credential.username,
                operation=OperationType.ACCESS,
                resource=resource,
                security_level=security_level,
                success=False,
                details={"reason": "权限不足", "required": required_permission}
            )
            return False
        
        # 记录成功访问
        self._log_audit(
            user_id=credential.user_id,
            username=credential.username,
            operation=OperationType.ACCESS,
            resource=resource,
            security_level=security_level,
            success=True,
            details={"permission": required_permission}
        )
        
        return True
    
    def encrypt_sensitive_data(self, data: str, security_level: SecurityLevel) -> str:
        """加密敏感数据"""
        if security_level in [SecurityLevel.CONFIDENTIAL, SecurityLevel.TOP_SECRET]:
            # 使用HMAC进行加密（简化实现，生产环境应使用AES等对称加密）
            timestamp = str(int(time.time()))
            message = f"{timestamp}:{data}"
            signature = hmac.new(
                self.secret_key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            return f"{timestamp}:{signature}:{data}"
        return data
    
    def decrypt_sensitive_data(self, encrypted_data: str, security_level: SecurityLevel) -> Optional[str]:
        """解密敏感数据"""
        if security_level in [SecurityLevel.CONFIDENTIAL, SecurityLevel.TOP_SECRET]:
            try:
                parts = encrypted_data.split(":", 2)
                if len(parts) != 3:
                    return None
                
                timestamp, signature, data = parts
                message = f"{timestamp}:{data}"
                expected_signature = hmac.new(
                    self.secret_key.encode(),
                    message.encode(),
                    hashlib.sha256
                ).hexdigest()
                
                if hmac.compare_digest(signature, expected_signature):
                    return data
                return None
            except Exception:
                return None
        return encrypted_data
    
    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        if session_token in self.active_sessions:
            credential = self.active_sessions[session_token]
            del self.active_sessions[session_token]
            
            self._log_audit(
                user_id=credential.user_id,
                username=credential.username,
                operation=OperationType.LOGOUT,
                success=True,
                details={"session_duration": str(datetime.now() - credential.created_at)}
            )
            return True
        return False
    
    def get_audit_logs(self, session_token: str, 
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      user_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取审计日志"""
        credential = self.validate_session(session_token)
        if not credential or not credential.has_permission("audit"):
            return []
        
        filtered_logs = []
        for log in self.audit_logs:
            # 时间过滤
            if start_time and log.timestamp < start_time:
                continue
            if end_time and log.timestamp > end_time:
                continue
            
            # 用户过滤
            if user_filter and user_filter not in log.username:
                continue
            
            filtered_logs.append(log.to_dict())
        
        # 记录审计日志查询
        self._log_audit(
            user_id=credential.user_id,
            username=credential.username,
            operation=OperationType.ACCESS,
            resource="audit_logs",
            security_level=SecurityLevel.CONFIDENTIAL,
            success=True,
            details={"query_params": {"start_time": start_time, "end_time": end_time, "user_filter": user_filter}}
        )
        
        return filtered_logs
    
    def _verify_password(self, username: str, password: str) -> bool:
        """验证密码（简化实现）"""
        # 在实际应用中，应该从安全的用户数据库中验证
        # 这里使用简化的验证逻辑
        default_passwords = {
            "admin": "admin123",
            "commander": "cmd123",
            "analyst": "ana123",
            "operator": "op123"
        }
        return default_passwords.get(username) == password
    
    def _generate_session_token(self, username: str) -> str:
        """生成会话令牌"""
        timestamp = str(int(time.time()))
        data = f"{username}:{timestamp}:{secrets.token_hex(16)}"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def _is_account_locked(self, username: str) -> bool:
        """检查账户是否被锁定"""
        if username not in self.failed_attempts:
            return False
        
        # 清理过期的失败尝试记录
        cutoff_time = datetime.now() - timedelta(minutes=15)
        self.failed_attempts[username] = [
            attempt for attempt in self.failed_attempts[username]
            if attempt > cutoff_time
        ]
        
        # 检查是否超过失败次数限制
        return len(self.failed_attempts[username]) >= 5
    
    def _record_failed_attempt(self, username: str) -> None:
        """记录失败尝试"""
        if username not in self.failed_attempts:
            self.failed_attempts[username] = []
        self.failed_attempts[username].append(datetime.now())
    
    def _log_audit(self, user_id: str = "", username: str = "", 
                  operation: OperationType = OperationType.ACCESS,
                  resource: str = "", security_level: SecurityLevel = SecurityLevel.INTERNAL,
                  success: bool = True, details: Dict[str, Any] = None) -> None:
        """记录审计日志"""
        log = AuditLog(
            user_id=user_id,
            username=username,
            operation=operation,
            resource=resource,
            security_level=security_level,
            success=success,
            details=details or {}
        )
        
        self.audit_logs.append(log)
        
        # 限制日志数量，避免内存溢出
        if len(self.audit_logs) > 10000:
            self.audit_logs = self.audit_logs[-5000:]  # 保留最近5000条
        
        # 记录到系统日志
        log_level = logging.INFO if success else logging.WARNING
        self.logger.log(log_level, f"审计日志: {log.to_dict()}")
