{"metadata": {"name": "威胁计算场景测试数据", "description": "用于测试威胁计算任务分解的标准数据集", "version": "1.0", "created_date": "2024-01-01", "scenario_type": "threat_calculation"}, "root_task": {"name": "威胁计算任务", "description": "完整的威胁识别、评估和预警任务", "task_type": "MISSION_TASK", "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.9, "importance": 0.95, "urgency": 0.9, "frequency": 0.5, "duration": 1200, "error_rate": 0.03, "workload": 0.8}}, "expected_subtasks": [{"name": "威胁识别", "description": "识别潜在的威胁目标和威胁类型", "task_type": "ZZ_TASK", "executor_type": "HUMAN_MACHINE", "level": 1, "attributes": {"complexity": 0.8, "importance": 0.9, "urgency": 0.95, "frequency": 0.7, "duration": 400, "error_rate": 0.05, "workload": 0.7}, "expected_children": [{"name": "目标检测", "description": "检测和识别潜在威胁目标", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "威胁分类", "description": "对识别的威胁进行分类", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}, {"name": "意图判断", "description": "判断威胁目标的意图", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN", "level": 2}]}, {"name": "威胁评估", "description": "评估威胁的严重程度和影响范围", "task_type": "ZZ_TASK", "executor_type": "HUMAN_MACHINE", "level": 1, "attributes": {"complexity": 0.95, "importance": 0.95, "urgency": 0.8, "frequency": 0.6, "duration": 600, "error_rate": 0.04, "workload": 0.9}, "expected_children": [{"name": "威胁等级评定", "description": "评定威胁的等级和严重程度", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}, {"name": "影响范围分析", "description": "分析威胁可能的影响范围", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "时间窗口估算", "description": "估算威胁发生的时间窗口", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN", "level": 2}, {"name": "损失评估", "description": "评估可能造成的损失", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}]}, {"name": "威胁预警", "description": "发布威胁预警和应对建议", "task_type": "ZZ_TASK", "executor_type": "HUMAN_MACHINE", "level": 1, "attributes": {"complexity": 0.6, "importance": 0.9, "urgency": 0.95, "frequency": 0.4, "duration": 200, "error_rate": 0.02, "workload": 0.6}, "expected_children": [{"name": "预警信息生成", "description": "生成标准化的预警信息", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "应对方案推荐", "description": "推荐相应的应对方案", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN", "level": 2}, {"name": "预警发布", "description": "向相关部门发布预警", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}]}], "test_scenarios": [{"name": "常规威胁评估", "description": "常规威胁的识别和评估流程", "input_parameters": {"threat_level": "medium", "response_time": "standard", "data_quality": "good"}, "expected_results": {"subtask_count": 3, "total_duration": 1200, "accuracy_requirement": 0.95}}, {"name": "紧急威胁处理", "description": "高威胁等级的紧急处理流程", "input_parameters": {"threat_level": "high", "response_time": "urgent", "data_quality": "partial"}, "expected_results": {"subtask_count": 3, "total_duration": 600, "urgency_level": "critical"}}, {"name": "复杂威胁分析", "description": "多重威胁的综合分析", "input_parameters": {"threat_level": "multiple", "response_time": "extended", "data_quality": "comprehensive"}, "expected_results": {"subtask_count": 4, "total_duration": 2400, "complexity_level": "high"}}], "validation_rules": [{"rule": "threat_priority", "description": "验证威胁处理的优先级设置", "criteria": ["高威胁等级任务优先级最高", "紧急威胁的响应时间最短", "威胁识别必须在评估之前", "预警发布必须及时"]}, {"rule": "accuracy_requirements", "description": "验证准确性要求的设置", "criteria": ["威胁识别准确率 > 90%", "威胁评估准确率 > 95%", "误报率 < 5%", "漏报率 < 2%"]}, {"rule": "response_time", "description": "验证响应时间要求", "criteria": ["威胁识别 < 5分钟", "威胁评估 < 10分钟", "预警发布 < 3分钟", "总体响应 < 20分钟"]}], "performance_benchmarks": {"detection_time": {"target": "< 300 seconds", "acceptable": "< 600 seconds", "description": "威胁检测的响应时间"}, "accuracy": {"target": "> 95%", "acceptable": "> 90%", "description": "威胁识别的准确率"}, "false_positive_rate": {"target": "< 3%", "acceptable": "< 5%", "description": "误报率"}}}