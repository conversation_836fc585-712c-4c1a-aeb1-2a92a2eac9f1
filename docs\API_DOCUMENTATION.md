# HMDM系统API文档

## 概述

HMDM系统提供完整的RESTful API接口，支持所有核心功能的程序化访问。API采用JSON格式进行数据交换，支持标准的HTTP状态码和错误处理机制。

## 基础信息

- **基础URL**: `http://localhost:5000/api/v1`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证机制

### 获取访问令牌

```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "Admin123!",
    "ip_address": "127.0.0.1"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_in": 3600,
        "user_info": {
            "username": "admin",
            "security_level": "TOP_SECRET",
            "permissions": ["read", "write", "admin"]
        }
    }
}
```

## 核心API接口

### 1. 态势感知API

#### 获取当前态势
```http
GET /api/v1/situation/current
Authorization: Bearer <token>
```

#### 提交态势数据
```http
POST /api/v1/situation/data
Authorization: Bearer <token>
Content-Type: application/json

{
    "timestamp": "2025-09-07T10:30:00Z",
    "location": {"lat": 39.9042, "lon": 116.4074},
    "threat_level": 3,
    "unit_status": "active"
}
```

### 2. 决策支持API

#### 创建决策问题
```http
POST /api/v1/decision/problems
Authorization: Bearer <token>
Content-Type: application/json

{
    "title": "防御策略选择",
    "criteria": ["效率", "成本", "风险"],
    "alternatives": ["方案A", "方案B", "方案C"],
    "decision_matrix": [
        [0.8, 0.6, 0.7],
        [0.7, 0.8, 0.6],
        [0.6, 0.7, 0.8]
    ],
    "weights": [0.4, 0.3, 0.3]
}
```

#### 获取决策建议
```http
GET /api/v1/decision/problems/{problem_id}/recommendation
Authorization: Bearer <token>
```

### 3. 知识管理API

#### 搜索知识
```http
GET /api/v1/knowledge/search?q=战术&limit=10&category=军事
Authorization: Bearer <token>
```

#### 添加知识条目
```http
POST /api/v1/knowledge/items
Authorization: Bearer <token>
Content-Type: application/json

{
    "title": "新战术理论",
    "content": "详细的战术理论内容...",
    "category": "军事理论",
    "security_level": "SECRET"
}
```

### 4. 训练管理API

#### 创建训练计划
```http
POST /api/v1/training/plans
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "指挥员战术训练",
    "description": "提升指挥员战术决策能力",
    "duration_hours": 8,
    "max_participants": 20,
    "difficulty_level": 4
}
```

#### 获取训练统计
```http
GET /api/v1/training/statistics
Authorization: Bearer <token>
```

### 5. 仿真系统API

#### 创建仿真场景
```http
POST /api/v1/simulation/scenarios
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "城市作战仿真",
    "description": "城市环境下的作战仿真",
    "duration": 3600,
    "environment": "urban",
    "participants": 10
}
```

#### 获取仿真结果
```http
GET /api/v1/simulation/scenarios/{scenario_id}/results
Authorization: Bearer <token>
```

### 6. 系统管理API

#### 获取系统状态
```http
GET /api/v1/system/status
Authorization: Bearer <token>
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "system_health": "healthy",
        "uptime": 86400,
        "version": "2.0.0",
        "resources": {
            "cpu_usage": 45.2,
            "memory_usage": 62.8,
            "disk_usage": 35.1
        },
        "active_users": 12,
        "active_sessions": 8
    }
}
```

#### 获取性能指标
```http
GET /api/v1/system/metrics
Authorization: Bearer <token>
```

### 7. 用户管理API

#### 创建用户
```http
POST /api/v1/users
Authorization: Bearer <token>
Content-Type: application/json

{
    "username": "new_user",
    "password": "SecurePass123!",
    "email": "<EMAIL>",
    "security_level": "SECRET",
    "roles": ["analyst", "operator"]
}
```

#### 用户权限管理
```http
GET /api/v1/users/{user_id}/permissions
PUT /api/v1/users/{user_id}/permissions
Authorization: Bearer <token>
```

## 错误处理

### 标准错误响应格式

```json
{
    "success": false,
    "error": {
        "code": "INVALID_REQUEST",
        "message": "请求参数无效",
        "details": {
            "field": "username",
            "reason": "用户名不能为空"
        }
    },
    "timestamp": "2025-09-07T10:30:00Z"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## Python SDK示例

```python
from hmdm_client import HMDMClient

# 初始化客户端
client = HMDMClient(base_url="http://localhost:5000/api/v1")

# 登录获取令牌
token = client.login("admin", "Admin123!")

# 获取当前态势
situation = client.situation.get_current()
print(f"当前威胁等级: {situation['threat_level']}")

# 创建决策问题
decision_problem = {
    "title": "资源分配决策",
    "criteria": ["效率", "成本"],
    "alternatives": ["方案1", "方案2"],
    "decision_matrix": [[0.8, 0.6], [0.7, 0.8]],
    "weights": [0.6, 0.4]
}

result = client.decision.create_problem(decision_problem)
recommendation = client.decision.get_recommendation(result['id'])
print(f"推荐方案: {recommendation['recommended_alternative']}")
```

## 限流和配额

- **请求频率限制**: 每分钟最多100次请求
- **并发连接限制**: 每用户最多10个并发连接
- **数据传输限制**: 单次请求最大10MB
- **会话超时**: 默认1小时，可配置

## 版本兼容性

- **当前版本**: v1.0
- **支持的客户端版本**: v1.0+
- **向后兼容性**: 保证向后兼容至少2个主版本

---

*本文档版本: v2.0*  
*最后更新: 2025-09-07*  
*维护团队: HMDM开发团队*
