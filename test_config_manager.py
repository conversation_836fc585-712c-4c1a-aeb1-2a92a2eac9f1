#!/usr/bin/env python3
"""
配置管理器测试脚本

测试HMDM系统的配置管理功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from src.hmdm.core.config_manager import ConfigManager, get_config_manager, reset_config_manager
from src.hmdm.core.system_manager import SystemConfig
from src.hmdm.allocation.allocation_config import AllocationConfig, AllocationMode, OptimizationObjective


def test_config_manager():
    """测试配置管理器"""
    print("开始测试配置管理器...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"使用临时目录: {temp_dir}")
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager(temp_dir)
        
        # 测试1: 基本配置加载和保存
        print("\n1. 测试基本配置加载和保存")
        
        # 创建测试系统配置
        system_config = SystemConfig()
        system_config.system_name = "测试系统"
        system_config.version = "2.0.0"
        system_config.environment = "testing"
        system_config.enabled_modules = ["knowledge_base", "human_machine_allocation"]
        
        # 保存系统配置
        success = config_manager.save_system_config(system_config)
        print(f"保存系统配置: {'成功' if success else '失败'}")
        
        # 加载系统配置
        loaded_config = config_manager.load_system_config()
        print(f"加载系统配置: {loaded_config.system_name} v{loaded_config.version}")
        
        # 测试2: 人机分配配置
        print("\n2. 测试人机分配配置")
        
        # 创建测试分配配置
        allocation_config = AllocationConfig()
        allocation_config.allocation_mode = AllocationMode.AUTOMATIC
        allocation_config.optimization_objective = OptimizationObjective.EFFICIENCY
        allocation_config.default_scheme_count = 8
        allocation_config.decision_threshold = 0.15
        
        # 保存分配配置
        success = config_manager.save_allocation_config(allocation_config)
        print(f"保存分配配置: {'成功' if success else '失败'}")
        
        # 加载分配配置
        loaded_allocation_config = config_manager.load_allocation_config()
        print(f"加载分配配置: {loaded_allocation_config.allocation_mode.value}, 方案数: {loaded_allocation_config.default_scheme_count}")
        
        # 测试3: 配置档案管理
        print("\n3. 测试配置档案管理")
        
        # 创建配置档案
        success = config_manager.create_profile("高性能配置", "针对高性能场景优化")
        print(f"创建配置档案: {'成功' if success else '失败'}")
        
        # 修改配置
        allocation_config.optimization_objective = OptimizationObjective.RELIABILITY
        config_manager.current_allocation_config = allocation_config
        
        # 创建另一个配置档案
        success = config_manager.create_profile("高可靠性配置", "针对高可靠性场景优化")
        print(f"创建第二个配置档案: {'成功' if success else '失败'}")
        
        # 列出配置档案
        profiles = config_manager.list_profiles()
        print(f"配置档案列表: {len(profiles)} 个档案")
        for profile in profiles:
            print(f"  - {profile['name']}: {profile['description']}")
        
        # 加载配置档案
        success = config_manager.load_profile("高性能配置")
        print(f"加载配置档案: {'成功' if success else '失败'}")
        if success:
            print(f"当前优化目标: {config_manager.current_allocation_config.optimization_objective.value}")
        
        # 测试4: 配置验证
        print("\n4. 测试配置验证")
        
        validation_result = config_manager.validate_config()
        print(f"配置验证: {'通过' if validation_result['is_valid'] else '失败'}")
        if validation_result['errors']:
            print(f"错误: {validation_result['errors']}")
        if validation_result['warnings']:
            print(f"警告: {validation_result['warnings']}")
        
        # 测试5: 配置导出和导入
        print("\n5. 测试配置导出和导入")
        
        # 导出配置
        export_file = os.path.join(temp_dir, "config_export.json")
        success = config_manager.export_config(export_file, include_profiles=True)
        print(f"导出配置: {'成功' if success else '失败'}")
        
        if success and os.path.exists(export_file):
            file_size = os.path.getsize(export_file)
            print(f"导出文件大小: {file_size} 字节")
        
        # 创建新的配置管理器来测试导入
        temp_dir2 = tempfile.mkdtemp()
        config_manager2 = ConfigManager(temp_dir2)
        
        # 导入配置
        success = config_manager2.import_config(export_file, import_profiles=True)
        print(f"导入配置: {'成功' if success else '失败'}")
        
        if success:
            imported_profiles = config_manager2.list_profiles()
            print(f"导入的配置档案数量: {len(imported_profiles)}")
        
        # 清理第二个临时目录
        shutil.rmtree(temp_dir2, ignore_errors=True)
        
        # 测试6: 配置摘要
        print("\n6. 测试配置摘要")
        
        summary = config_manager.get_current_config_summary()
        print("配置摘要:")
        print(f"  系统名称: {summary['system_config']['system_name']}")
        print(f"  系统版本: {summary['system_config']['version']}")
        print(f"  启用模块数: {summary['system_config']['enabled_modules_count']}")
        print(f"  分配模式: {summary['allocation_config']['allocation_mode']}")
        print(f"  优化目标: {summary['allocation_config']['optimization_objective']}")
        print(f"  配置档案数: {summary['config_files']['profiles_count']}")
        
        print("\n✓ 所有测试完成")
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_global_config_manager():
    """测试全局配置管理器"""
    print("\n开始测试全局配置管理器...")
    
    try:
        # 重置全局配置管理器
        reset_config_manager()
        
        # 获取全局配置管理器
        config_manager1 = get_config_manager()
        config_manager2 = get_config_manager()
        
        # 验证是同一个实例
        is_same_instance = config_manager1 is config_manager2
        print(f"全局配置管理器单例模式: {'正确' if is_same_instance else '错误'}")
        
        # 测试配置同步
        config_manager1.current_system_config.system_name = "全局测试系统"
        print(f"配置同步测试: {config_manager2.current_system_config.system_name}")
        
        return True
        
    except Exception as e:
        print(f"全局配置管理器测试失败: {e}")
        return False


def test_system_manager_integration():
    """测试与SystemManager的集成"""
    print("\n开始测试与SystemManager的集成...")
    
    try:
        from src.hmdm.core.system_manager import HMDMSystemManager
        
        # 创建系统管理器
        system_manager = HMDMSystemManager()
        
        # 测试配置管理器集成
        config_manager = system_manager.get_config_manager()
        print(f"配置管理器集成: {'成功' if config_manager else '失败'}")
        
        # 测试配置档案操作
        success = system_manager.create_config_profile("集成测试配置", "SystemManager集成测试")
        print(f"创建配置档案: {'成功' if success else '失败'}")
        
        # 测试配置验证
        validation_result = system_manager.validate_config()
        print(f"配置验证: {'通过' if validation_result['is_valid'] else '失败'}")
        
        # 测试配置档案列表
        profiles = system_manager.list_config_profiles()
        print(f"配置档案列表: {len(profiles)} 个档案")
        
        return True
        
    except Exception as e:
        print(f"SystemManager集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("HMDM配置管理器测试")
    print("=" * 50)
    
    tests = [
        ("配置管理器基本功能", test_config_manager),
        ("全局配置管理器", test_global_config_manager),
        ("SystemManager集成", test_system_manager_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
