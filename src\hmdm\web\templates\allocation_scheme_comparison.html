{% extends "base.html" %}

{% block title %}方案生成与比较 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-balance-scale"></i>
                    方案生成与比较
                </h1>
                <p class="page-subtitle">生成多个分配方案并进行综合比较分析</p>
            </div>
        </div>
    </div>

    <!-- 任务概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        任务概览
                    </h5>
                </div>
                <div class="card-body">
                    <div id="taskSummary" class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary mb-1" id="totalTasks">0</h4>
                                <small class="text-muted">总任务数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info mb-1" id="maxDepth">0</h4>
                                <small class="text-muted">最大层级</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning mb-1" id="avgComplexity">0.0</h4>
                                <small class="text-muted">平均复杂度</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success mb-1" id="realTimeTasks">0</h4>
                                <small class="text-muted">实时任务数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 方案生成配置 -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs"></i>
                        生成配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="generationForm">
                        <div class="mb-3">
                            <label class="form-label">方案数量</label>
                            <input type="number" class="form-control" id="schemeCount" min="2" max="10" value="5">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">优化策略</label>
                            <select class="form-select" id="optimizationStrategy">
                                <option value="balanced">平衡优化</option>
                                <option value="efficiency">效率优先</option>
                                <option value="reliability">可靠性优先</option>
                                <option value="cost_effectiveness">成本效益优先</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">约束条件</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="humanWorkloadLimit" checked>
                                <label class="form-check-label" for="humanWorkloadLimit">
                                    限制人员工作负荷
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="machineUtilizationMin" checked>
                                <label class="form-check-label" for="machineUtilizationMin">
                                    保证机器最低利用率
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="realTimeConstraint" checked>
                                <label class="form-check-label" for="realTimeConstraint">
                                    实时性约束
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="generateBtn">
                                <i class="fas fa-magic"></i> 生成方案
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 方案列表 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i>
                        生成的方案
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="selectAllSchemes()">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                            <i class="fas fa-square"></i> 清空
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="compareSelected()" disabled id="compareBtn">
                            <i class="fas fa-balance-scale"></i> 比较选中
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="schemesList">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-lightbulb fa-2x mb-2"></i>
                            <p>点击"生成方案"开始创建分配方案</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 方案比较结果 -->
    <div class="row" id="comparisonSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i>
                        方案比较结果
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 比较图表 -->
                    <div class="row mb-4">
                        <div class="col-lg-6">
                            <canvas id="performanceChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-lg-6">
                            <canvas id="radarChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- 详细比较表格 -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="comparisonTable">
                            <thead>
                                <tr>
                                    <th>方案</th>
                                    <th>任务完成率</th>
                                    <th>时间效率</th>
                                    <th>资源利用率</th>
                                    <th>错误率</th>
                                    <th>协调开销</th>
                                    <th>综合评分</th>
                                    <th>推荐度</th>
                                </tr>
                            </thead>
                            <tbody id="comparisonTableBody">
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <button type="button" class="btn btn-outline-info" onclick="exportComparison()">
                                <i class="fas fa-file-export"></i> 导出比较结果
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary" onclick="backToGeneration()">
                                <i class="fas fa-arrow-left"></i> 返回生成
                            </button>
                            <button type="button" class="btn btn-primary" onclick="proceedToEvaluation()">
                                <i class="fas fa-arrow-right"></i> 效能评估
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 方案详情模态框 -->
<div class="modal fade" id="schemeDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">方案详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="schemeDetailContent">
                    <!-- 方案详情内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="selectSchemeForComparison()">选择此方案</button>
            </div>
        </div>
    </div>
</div>

<style>
.scheme-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    transition: all 0.2s;
    cursor: pointer;
}

.scheme-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.scheme-card.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.scheme-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.scheme-body {
    padding: 1rem;
}

.metric-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.metric-badge.good {
    background-color: #d1e7dd;
    color: #0f5132;
}

.metric-badge.average {
    background-color: #fff3cd;
    color: #664d03;
}

.metric-badge.poor {
    background-color: #f8d7da;
    color: #721c24;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let taskHierarchy = null;
let generatedSchemes = [];
let selectedSchemes = [];
let comparisonResults = null;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 从sessionStorage加载任务数据
    const taskData = sessionStorage.getItem('taskHierarchy');
    if (taskData) {
        taskHierarchy = JSON.parse(taskData);
        displayTaskSummary();
    } else {
        showToast('未找到任务数据，请先输入任务', 'warning');
        setTimeout(() => {
            window.location.href = '/allocation/task-input';
        }, 2000);
        return;
    }
    
    // 绑定表单提交
    document.getElementById('generationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        generateSchemes();
    });
});

// 显示任务概览
function displayTaskSummary() {
    if (!taskHierarchy || !taskHierarchy.tasks) return;
    
    const tasks = Object.values(taskHierarchy.tasks);
    
    // 计算统计信息
    const totalTasks = tasks.length;
    const maxDepth = Math.max(...tasks.map(t => t.level)) + 1;
    const avgComplexity = tasks.reduce((sum, t) => sum + t.attributes.complexity, 0) / totalTasks;
    const realTimeTasks = tasks.filter(t => t.attributes.real_time_requirement).length;
    
    // 更新显示
    document.getElementById('totalTasks').textContent = totalTasks;
    document.getElementById('maxDepth').textContent = maxDepth;
    document.getElementById('avgComplexity').textContent = avgComplexity.toFixed(2);
    document.getElementById('realTimeTasks').textContent = realTimeTasks;
}

// 生成方案
function generateSchemes() {
    if (!taskHierarchy) {
        showToast('任务数据不可用', 'error');
        return;
    }
    
    const generateBtn = document.getElementById('generateBtn');
    generateBtn.disabled = true;
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    
    // 收集配置参数
    const config = {
        scheme_count: parseInt(document.getElementById('schemeCount').value),
        optimization_strategy: document.getElementById('optimizationStrategy').value,
        constraints: {
            human_workload_limit: document.getElementById('humanWorkloadLimit').checked,
            machine_utilization_min: document.getElementById('machineUtilizationMin').checked,
            real_time_constraint: document.getElementById('realTimeConstraint').checked
        }
    };
    
    // 模拟方案生成（实际应用中调用API）
    setTimeout(() => {
        generatedSchemes = generateMockSchemes(config.scheme_count);
        displaySchemes();
        
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成方案';
        
        showToast('方案生成完成', 'success');
    }, 2000);
}

// 生成模拟方案
function generateMockSchemes(count) {
    const schemes = [];
    const strategies = ['human_primary', 'machine_primary', 'balanced', 'adaptive'];
    
    for (let i = 0; i < count; i++) {
        const scheme = {
            id: `scheme_${i + 1}`,
            name: `方案 ${i + 1}`,
            strategy: strategies[i % strategies.length],
            description: `基于${strategies[i % strategies.length]}策略的分配方案`,
            metrics: {
                task_completion: 0.8 + Math.random() * 0.2,
                time_efficiency: 0.7 + Math.random() * 0.3,
                resource_utilization: 0.6 + Math.random() * 0.4,
                error_rate: Math.random() * 0.2,
                coordination_overhead: Math.random() * 0.3,
                adaptability: 0.5 + Math.random() * 0.5
            },
            allocations: generateMockAllocations()
        };
        
        // 计算综合评分
        scheme.overall_score = calculateOverallScore(scheme.metrics);
        
        schemes.push(scheme);
    }
    
    return schemes.sort((a, b) => b.overall_score - a.overall_score);
}

// 生成模拟分配
function generateMockAllocations() {
    const allocations = {};
    if (taskHierarchy && taskHierarchy.tasks) {
        Object.keys(taskHierarchy.tasks).forEach(taskId => {
            const executors = ['human', 'machine', 'human_machine'];
            allocations[taskId] = {
                executor: executors[Math.floor(Math.random() * executors.length)],
                confidence: 0.6 + Math.random() * 0.4
            };
        });
    }
    return allocations;
}

// 计算综合评分
function calculateOverallScore(metrics) {
    const weights = {
        task_completion: 0.25,
        time_efficiency: 0.20,
        resource_utilization: 0.15,
        error_rate: -0.15, // 负权重，错误率越低越好
        coordination_overhead: -0.15, // 负权重，开销越低越好
        adaptability: 0.10
    };
    
    let score = 0;
    for (const [metric, value] of Object.entries(metrics)) {
        score += weights[metric] * value;
    }
    
    return Math.max(0, Math.min(1, score)); // 限制在0-1范围内
}

// 显示方案
function displaySchemes() {
    const container = document.getElementById('schemesList');
    
    if (generatedSchemes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-lightbulb fa-2x mb-2"></i>
                <p>点击"生成方案"开始创建分配方案</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = '';
    
    generatedSchemes.forEach((scheme, index) => {
        const schemeElement = createSchemeElement(scheme, index);
        container.appendChild(schemeElement);
    });
}

// 创建方案元素
function createSchemeElement(scheme, index) {
    const div = document.createElement('div');
    div.className = 'scheme-card';
    div.onclick = () => toggleSchemeSelection(scheme.id);
    
    const metrics = scheme.metrics;
    const getMetricClass = (value, reverse = false) => {
        const threshold1 = reverse ? 0.3 : 0.7;
        const threshold2 = reverse ? 0.6 : 0.4;
        
        if (reverse) {
            return value <= threshold1 ? 'good' : value <= threshold2 ? 'average' : 'poor';
        } else {
            return value >= threshold1 ? 'good' : value >= threshold2 ? 'average' : 'poor';
        }
    };
    
    div.innerHTML = `
        <div class="scheme-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${scheme.name}</h6>
                    <small class="text-muted">${scheme.description}</small>
                </div>
                <div class="text-end">
                    <div class="badge bg-primary">评分: ${(scheme.overall_score * 100).toFixed(1)}</div>
                    <div class="form-check mt-1">
                        <input class="form-check-input" type="checkbox" id="scheme_${scheme.id}">
                    </div>
                </div>
            </div>
        </div>
        <div class="scheme-body">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">性能指标:</small><br>
                    <span class="metric-badge ${getMetricClass(metrics.task_completion)}">
                        完成率: ${(metrics.task_completion * 100).toFixed(1)}%
                    </span>
                    <span class="metric-badge ${getMetricClass(metrics.time_efficiency)}">
                        效率: ${(metrics.time_efficiency * 100).toFixed(1)}%
                    </span>
                    <span class="metric-badge ${getMetricClass(metrics.resource_utilization)}">
                        资源利用: ${(metrics.resource_utilization * 100).toFixed(1)}%
                    </span>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">风险指标:</small><br>
                    <span class="metric-badge ${getMetricClass(metrics.error_rate, true)}">
                        错误率: ${(metrics.error_rate * 100).toFixed(1)}%
                    </span>
                    <span class="metric-badge ${getMetricClass(metrics.coordination_overhead, true)}">
                        协调开销: ${(metrics.coordination_overhead * 100).toFixed(1)}%
                    </span>
                    <span class="metric-badge ${getMetricClass(metrics.adaptability)}">
                        适应性: ${(metrics.adaptability * 100).toFixed(1)}%
                    </span>
                </div>
            </div>
            <div class="mt-2">
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showSchemeDetail('${scheme.id}'); event.stopPropagation();">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        </div>
    `;
    
    return div;
}

// 切换方案选择
function toggleSchemeSelection(schemeId) {
    const checkbox = document.getElementById(`scheme_${schemeId}`);
    checkbox.checked = !checkbox.checked;
    
    const schemeCard = checkbox.closest('.scheme-card');
    if (checkbox.checked) {
        schemeCard.classList.add('selected');
        if (!selectedSchemes.includes(schemeId)) {
            selectedSchemes.push(schemeId);
        }
    } else {
        schemeCard.classList.remove('selected');
        selectedSchemes = selectedSchemes.filter(id => id !== schemeId);
    }
    
    // 更新比较按钮状态
    document.getElementById('compareBtn').disabled = selectedSchemes.length < 2;
}

// 全选方案
function selectAllSchemes() {
    generatedSchemes.forEach(scheme => {
        const checkbox = document.getElementById(`scheme_${scheme.id}`);
        if (!checkbox.checked) {
            checkbox.checked = true;
            checkbox.closest('.scheme-card').classList.add('selected');
            if (!selectedSchemes.includes(scheme.id)) {
                selectedSchemes.push(scheme.id);
            }
        }
    });
    
    document.getElementById('compareBtn').disabled = selectedSchemes.length < 2;
}

// 清空选择
function clearSelection() {
    selectedSchemes.forEach(schemeId => {
        const checkbox = document.getElementById(`scheme_${schemeId}`);
        checkbox.checked = false;
        checkbox.closest('.scheme-card').classList.remove('selected');
    });
    
    selectedSchemes = [];
    document.getElementById('compareBtn').disabled = true;
}

// 显示方案详情
function showSchemeDetail(schemeId) {
    const scheme = generatedSchemes.find(s => s.id === schemeId);
    if (!scheme) return;
    
    const content = document.getElementById('schemeDetailContent');
    content.innerHTML = `
        <h6>${scheme.name}</h6>
        <p class="text-muted">${scheme.description}</p>
        
        <h6 class="mt-3">性能指标</h6>
        <div class="row">
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>任务完成率:</strong> ${(scheme.metrics.task_completion * 100).toFixed(1)}%</li>
                    <li><strong>时间效率:</strong> ${(scheme.metrics.time_efficiency * 100).toFixed(1)}%</li>
                    <li><strong>资源利用率:</strong> ${(scheme.metrics.resource_utilization * 100).toFixed(1)}%</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>错误率:</strong> ${(scheme.metrics.error_rate * 100).toFixed(1)}%</li>
                    <li><strong>协调开销:</strong> ${(scheme.metrics.coordination_overhead * 100).toFixed(1)}%</li>
                    <li><strong>适应性:</strong> ${(scheme.metrics.adaptability * 100).toFixed(1)}%</li>
                </ul>
            </div>
        </div>
        
        <h6 class="mt-3">任务分配详情</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>任务</th>
                        <th>执行者</th>
                        <th>置信度</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(scheme.allocations).map(([taskId, allocation]) => {
                        const task = taskHierarchy.tasks[taskId];
                        return `
                            <tr>
                                <td>${task ? task.name : taskId}</td>
                                <td>
                                    <span class="badge ${allocation.executor === 'human' ? 'bg-info' : 
                                                        allocation.executor === 'machine' ? 'bg-warning' : 'bg-success'}">
                                        ${allocation.executor === 'human' ? '人类' : 
                                          allocation.executor === 'machine' ? '机器' : '人机协同'}
                                    </span>
                                </td>
                                <td>${(allocation.confidence * 100).toFixed(1)}%</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('schemeDetailModal')).show();
}

// 比较选中方案
function compareSelected() {
    if (selectedSchemes.length < 2) {
        showToast('请至少选择2个方案进行比较', 'warning');
        return;
    }
    
    // 模拟比较过程
    showToast('正在比较方案...', 'info');
    
    setTimeout(() => {
        comparisonResults = performComparison();
        displayComparisonResults();
        document.getElementById('comparisonSection').style.display = 'block';
        
        // 滚动到比较结果
        document.getElementById('comparisonSection').scrollIntoView({ behavior: 'smooth' });
        
        showToast('方案比较完成', 'success');
    }, 1500);
}

// 执行比较
function performComparison() {
    const selectedSchemeData = generatedSchemes.filter(s => selectedSchemes.includes(s.id));
    
    // 模拟比较结果
    return selectedSchemeData.map(scheme => ({
        ...scheme,
        recommendation_score: scheme.overall_score + (Math.random() - 0.5) * 0.1,
        rank: 0 // 将在排序后设置
    })).sort((a, b) => b.recommendation_score - a.recommendation_score)
      .map((scheme, index) => ({ ...scheme, rank: index + 1 }));
}

// 显示比较结果
function displayComparisonResults() {
    if (!comparisonResults) return;
    
    // 更新表格
    const tbody = document.getElementById('comparisonTableBody');
    tbody.innerHTML = '';
    
    comparisonResults.forEach(scheme => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>
                <strong>${scheme.name}</strong>
                <br><small class="text-muted">排名: #${scheme.rank}</small>
            </td>
            <td>${(scheme.metrics.task_completion * 100).toFixed(1)}%</td>
            <td>${(scheme.metrics.time_efficiency * 100).toFixed(1)}%</td>
            <td>${(scheme.metrics.resource_utilization * 100).toFixed(1)}%</td>
            <td>${(scheme.metrics.error_rate * 100).toFixed(1)}%</td>
            <td>${(scheme.metrics.coordination_overhead * 100).toFixed(1)}%</td>
            <td>
                <span class="badge ${scheme.overall_score >= 0.8 ? 'bg-success' : 
                                   scheme.overall_score >= 0.6 ? 'bg-warning' : 'bg-danger'}">
                    ${(scheme.overall_score * 100).toFixed(1)}
                </span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress flex-grow-1 me-2" style="height: 20px;">
                        <div class="progress-bar" style="width: ${scheme.recommendation_score * 100}%"></div>
                    </div>
                    <small>${(scheme.recommendation_score * 100).toFixed(1)}%</small>
                </div>
            </td>
        `;
    });
    
    // 绘制图表
    drawPerformanceChart();
    drawRadarChart();
}

// 绘制性能图表
function drawPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: comparisonResults.map(s => s.name),
            datasets: [{
                label: '综合评分',
                data: comparisonResults.map(s => s.overall_score * 100),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '方案综合评分比较'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 绘制雷达图
function drawRadarChart() {
    const ctx = document.getElementById('radarChart').getContext('2d');
    
    const datasets = comparisonResults.map((scheme, index) => ({
        label: scheme.name,
        data: [
            scheme.metrics.task_completion * 100,
            scheme.metrics.time_efficiency * 100,
            scheme.metrics.resource_utilization * 100,
            (1 - scheme.metrics.error_rate) * 100, // 转换为正向指标
            (1 - scheme.metrics.coordination_overhead) * 100, // 转换为正向指标
            scheme.metrics.adaptability * 100
        ],
        borderColor: `hsl(${index * 60}, 70%, 50%)`,
        backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.2)`,
        pointBackgroundColor: `hsl(${index * 60}, 70%, 50%)`,
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: `hsl(${index * 60}, 70%, 50%)`
    }));
    
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['任务完成率', '时间效率', '资源利用率', '可靠性', '协调效率', '适应性'],
            datasets: datasets
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '方案多维度比较'
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 导出比较结果
function exportComparison() {
    if (!comparisonResults) {
        showToast('没有比较结果可以导出', 'warning');
        return;
    }
    
    const data = {
        comparison_date: new Date().toISOString(),
        task_hierarchy: taskHierarchy,
        schemes: comparisonResults,
        summary: {
            total_schemes: comparisonResults.length,
            best_scheme: comparisonResults[0],
            average_score: comparisonResults.reduce((sum, s) => sum + s.overall_score, 0) / comparisonResults.length
        }
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'scheme_comparison_results.json';
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('比较结果导出成功', 'success');
}

// 返回生成
function backToGeneration() {
    document.getElementById('comparisonSection').style.display = 'none';
    comparisonResults = null;
}

// 进行效能评估
function proceedToEvaluation() {
    if (!comparisonResults) {
        showToast('请先进行方案比较', 'warning');
        return;
    }
    
    // 保存比较结果到sessionStorage
    sessionStorage.setItem('comparisonResults', JSON.stringify(comparisonResults));
    
    // 跳转到效能评估页面
    window.location.href = '/allocation/effectiveness-evaluation';
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 简单的提示实现
    alert(message);
}
</script>
{% endblock %}
