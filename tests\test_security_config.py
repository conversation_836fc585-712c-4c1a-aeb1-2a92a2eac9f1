"""
安全配置管理器测试

测试安全配置管理器的各项功能
"""

import pytest
import tempfile
import os
import json
from src.hmdm.security.security_config import SecurityConfig, SecurityConfigManager
from src.hmdm.security.military_security import SecurityLevel


class TestSecurityConfig:
    """安全配置测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = SecurityConfig()
        
        assert config.session_timeout == 3600
        assert config.max_login_attempts == 3
        assert config.password_min_length == 8
        assert config.enable_encryption is True
        assert config.default_security_level == SecurityLevel.INTERNAL
    
    def test_config_to_dict(self):
        """测试配置转字典"""
        config = SecurityConfig(
            session_timeout=1800,
            max_login_attempts=5,
            enable_encryption=False
        )
        
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict['session_timeout'] == 1800
        assert config_dict['max_login_attempts'] == 5
        assert config_dict['enable_encryption'] is False
        assert config_dict['default_security_level'] == SecurityLevel.INTERNAL.value
    
    def test_config_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            'session_timeout': 7200,
            'max_login_attempts': 2,
            'password_min_length': 10,
            'enable_encryption': True,
            'default_security_level': '机密'
        }
        
        config = SecurityConfig.from_dict(config_dict)
        
        assert config.session_timeout == 7200
        assert config.max_login_attempts == 2
        assert config.password_min_length == 10
        assert config.enable_encryption is True
        assert config.default_security_level == SecurityLevel.CONFIDENTIAL


class TestSecurityConfigManager:
    """安全配置管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'security_config.json')
        
        self.config_manager = SecurityConfigManager(self.config_file)
    
    def teardown_method(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        assert self.config_manager is not None
        assert self.config_manager.config is not None
        assert isinstance(self.config_manager.config, SecurityConfig)
    
    def test_get_config(self):
        """测试获取配置"""
        config = self.config_manager.get_config()
        
        assert isinstance(config, SecurityConfig)
        assert config.session_timeout > 0
        assert config.max_login_attempts > 0
        assert config.password_min_length >= 6
    
    def test_update_config(self):
        """测试更新配置"""
        updates = {
            'session_timeout': 1800,
            'max_login_attempts': 5,
            'password_min_length': 10,
            'enable_encryption': False
        }
        
        success = self.config_manager.update_config(updates)
        assert success is True
        
        # 验证配置已更新
        config = self.config_manager.get_config()
        assert config.session_timeout == 1800
        assert config.max_login_attempts == 5
        assert config.password_min_length == 10
        assert config.enable_encryption is False
    
    def test_update_config_validation(self):
        """测试配置更新验证"""
        # 测试无效的会话超时时间
        invalid_updates = {
            'session_timeout': 100  # 太短
        }
        
        success = self.config_manager.update_config(invalid_updates)
        assert success is False
        
        # 测试无效的登录尝试次数
        invalid_updates = {
            'max_login_attempts': 20  # 太多
        }
        
        success = self.config_manager.update_config(invalid_updates)
        assert success is False
        
        # 测试无效的密码长度
        invalid_updates = {
            'password_min_length': 3  # 太短
        }
        
        success = self.config_manager.update_config(invalid_updates)
        assert success is False
    
    def test_reset_to_defaults(self):
        """测试重置为默认配置"""
        # 先修改配置
        updates = {
            'session_timeout': 1800,
            'max_login_attempts': 5
        }
        self.config_manager.update_config(updates)
        
        # 重置为默认值
        success = self.config_manager.reset_to_defaults()
        assert success is True
        
        # 验证配置已重置
        config = self.config_manager.get_config()
        assert config.session_timeout == 3600  # 默认值
        assert config.max_login_attempts == 3   # 默认值
    
    def test_config_persistence(self):
        """测试配置持久化"""
        # 更新配置
        updates = {
            'session_timeout': 2400,
            'enable_encryption': False
        }
        self.config_manager.update_config(updates)
        
        # 创建新的配置管理器实例
        new_config_manager = SecurityConfigManager(self.config_file)
        
        # 验证配置已持久化
        config = new_config_manager.get_config()
        assert config.session_timeout == 2400
        assert config.enable_encryption is False
    
    def test_get_security_policy(self):
        """测试获取安全策略"""
        policy = self.config_manager.get_security_policy()
        
        assert isinstance(policy, dict)
        assert 'authentication' in policy
        assert 'encryption' in policy
        assert 'audit' in policy
        assert 'access_control' in policy
        assert 'network_security' in policy
        assert 'monitoring' in policy
        
        # 验证认证策略
        auth_policy = policy['authentication']
        assert 'session_timeout_minutes' in auth_policy
        assert 'max_login_attempts' in auth_policy
        assert 'password_policy' in auth_policy
        
        # 验证密码策略
        password_policy = auth_policy['password_policy']
        assert 'min_length' in password_policy
        assert 'complexity_required' in password_policy
    
    def test_validate_security_compliance(self):
        """测试安全合规性验证"""
        compliance = self.config_manager.validate_security_compliance()
        
        assert isinstance(compliance, dict)
        assert 'compliance_score' in compliance
        assert 'status' in compliance
        assert 'issues' in compliance
        assert 'recommendations' in compliance
        assert 'last_check' in compliance
        
        # 验证合规分数
        assert 0 <= compliance['compliance_score'] <= 100
        
        # 验证状态
        assert compliance['status'] in ['compliant', 'non_compliant']
        
        # 验证问题和建议列表
        assert isinstance(compliance['issues'], list)
        assert isinstance(compliance['recommendations'], list)
    
    def test_compliance_with_weak_config(self):
        """测试弱配置的合规性"""
        # 设置弱安全配置
        weak_updates = {
            'password_min_length': 6,
            'enable_encryption': False,
            'enable_audit_logging': False,
            'enable_https_only': False,
            'enable_csrf_protection': False
        }
        
        self.config_manager.update_config(weak_updates)
        
        compliance = self.config_manager.validate_security_compliance()
        
        # 应该有合规问题
        assert compliance['status'] == 'non_compliant'
        assert len(compliance['issues']) > 0
        assert compliance['compliance_score'] < 100
    
    def test_compliance_with_strong_config(self):
        """测试强安全配置的合规性"""
        # 设置强安全配置
        strong_updates = {
            'password_min_length': 12,
            'password_complexity_required': True,
            'session_timeout': 1800,
            'enable_encryption': True,
            'enable_audit_logging': True,
            'audit_log_retention_days': 180,
            'enable_https_only': True,
            'enable_csrf_protection': True,
            'enable_intrusion_detection': True
        }
        
        self.config_manager.update_config(strong_updates)
        
        compliance = self.config_manager.validate_security_compliance()
        
        # 应该合规或接近合规
        assert compliance['compliance_score'] >= 80
        assert len(compliance['issues']) == 0
    
    def test_change_listeners(self):
        """测试配置变更监听器"""
        change_events = []
        
        def test_listener(old_config, new_config):
            change_events.append({
                'old_timeout': old_config.session_timeout,
                'new_timeout': new_config.session_timeout
            })
        
        # 添加监听器
        self.config_manager.add_change_listener(test_listener)
        
        # 更新配置
        updates = {'session_timeout': 1800}
        self.config_manager.update_config(updates)
        
        # 验证监听器被调用
        assert len(change_events) == 1
        assert change_events[0]['old_timeout'] == 3600  # 默认值
        assert change_events[0]['new_timeout'] == 1800
        
        # 移除监听器
        self.config_manager.remove_change_listener(test_listener)
        
        # 再次更新配置
        updates = {'session_timeout': 2400}
        self.config_manager.update_config(updates)
        
        # 验证监听器未被调用
        assert len(change_events) == 1  # 仍然是1个事件
    
    def test_security_level_mapping(self):
        """测试安全等级映射"""
        # 测试中文安全等级
        chinese_levels = ['公开', '内部', '秘密', '机密', '绝密']
        expected_levels = [
            SecurityLevel.PUBLIC,
            SecurityLevel.INTERNAL,
            SecurityLevel.SECRET,
            SecurityLevel.CONFIDENTIAL,
            SecurityLevel.TOP_SECRET
        ]
        
        for chinese, expected in zip(chinese_levels, expected_levels):
            config_dict = {'default_security_level': chinese}
            config = SecurityConfig.from_dict(config_dict)
            assert config.default_security_level == expected
        
        # 测试英文安全等级
        english_levels = ['PUBLIC', 'INTERNAL', 'SECRET', 'CONFIDENTIAL', 'TOP_SECRET']
        
        for english, expected in zip(english_levels, expected_levels):
            config_dict = {'default_security_level': english}
            config = SecurityConfig.from_dict(config_dict)
            assert config.default_security_level == expected
    
    def test_config_file_not_exists(self):
        """测试配置文件不存在的情况"""
        non_existent_file = os.path.join(self.temp_dir, 'non_existent.json')
        
        # 创建配置管理器，配置文件不存在
        config_manager = SecurityConfigManager(non_existent_file)
        
        # 应该使用默认配置
        config = config_manager.get_config()
        assert isinstance(config, SecurityConfig)
        assert config.session_timeout == 3600  # 默认值
        
        # 配置文件应该被创建
        assert os.path.exists(non_existent_file)
