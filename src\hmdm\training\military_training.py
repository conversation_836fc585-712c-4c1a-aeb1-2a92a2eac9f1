"""
军事训练与演练模块

提供军事训练场景设计、演练执行、效果评估和能力提升建议等功能。
"""

import numpy as np
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import random

from ..models.task_models import Task, TaskType
from ..security.military_security import SecurityLevel
from ..analysis.situation_awareness import SituationAssessment


class TrainingType(Enum):
    """训练类型"""
    INDIVIDUAL = "个人训练"
    TEAM = "团队训练"
    UNIT = "单位训练"
    JOINT = "联合训练"
    SIMULATION = "模拟训练"
    LIVE_FIRE = "实弹训练"


class TrainingLevel(Enum):
    """训练等级"""
    BASIC = "基础级"
    INTERMEDIATE = "中级"
    ADVANCED = "高级"
    EXPERT = "专家级"


class PerformanceMetric(Enum):
    """性能指标"""
    ACCURACY = "准确性"
    SPEED = "速度"
    EFFICIENCY = "效率"
    COORDINATION = "协调性"
    DECISION_QUALITY = "决策质量"
    COMMUNICATION = "通信能力"
    ADAPTABILITY = "适应性"
    LEADERSHIP = "领导力"


@dataclass
class TrainingObjective:
    """训练目标"""
    id: str
    name: str
    description: str
    training_type: TrainingType
    level: TrainingLevel
    metrics: List[PerformanceMetric] = field(default_factory=list)
    target_scores: Dict[str, float] = field(default_factory=dict)  # 目标分数
    prerequisites: List[str] = field(default_factory=list)  # 前置条件
    duration: float = 1.0  # 训练时长（小时）
    difficulty: float = 0.5  # 难度系数 0-1
    
    def is_achievable(self, current_skills: Dict[str, float]) -> bool:
        """检查目标是否可达成"""
        for prereq in self.prerequisites:
            if prereq not in current_skills or current_skills[prereq] < 0.6:
                return False
        return True


@dataclass
class TrainingScenario:
    """训练场景"""
    id: str
    name: str
    description: str
    scenario_type: str  # 场景类型：combat, reconnaissance, logistics等
    environment: Dict[str, Any] = field(default_factory=dict)  # 环境设置
    participants: List[str] = field(default_factory=list)  # 参训人员/单位
    objectives: List[str] = field(default_factory=list)  # 训练目标ID
    events: List[Dict[str, Any]] = field(default_factory=list)  # 场景事件
    success_criteria: Dict[str, float] = field(default_factory=dict)
    duration: float = 2.0  # 场景时长
    complexity: float = 0.5  # 复杂度
    realism: float = 0.8  # 真实度
    
    def generate_events(self, participant_count: int) -> List[Dict[str, Any]]:
        """生成场景事件"""
        events = []
        
        # 基础事件模板
        event_templates = [
            {"type": "enemy_contact", "probability": 0.7, "impact": "high"},
            {"type": "equipment_failure", "probability": 0.3, "impact": "medium"},
            {"type": "weather_change", "probability": 0.4, "impact": "low"},
            {"type": "communication_loss", "probability": 0.2, "impact": "high"},
            {"type": "supply_shortage", "probability": 0.25, "impact": "medium"}
        ]
        
        # 根据复杂度和参与人数生成事件
        event_count = max(1, int(self.complexity * participant_count * 2))  # 至少生成1个事件

        for i in range(event_count):
            template = random.choice(event_templates)
            if random.random() < template["probability"] or len(events) == 0:  # 确保至少有一个事件
                event = {
                    "id": f"event_{i}",
                    "type": template["type"],
                    "time": random.uniform(0, self.duration),
                    "impact": template["impact"],
                    "description": f"场景事件: {template['type']}"
                }
                events.append(event)
        
        # 按时间排序
        events.sort(key=lambda x: x["time"])
        return events


@dataclass
class TrainingResult:
    """训练结果"""
    participant_id: str
    scenario_id: str
    objective_id: str
    start_time: datetime
    end_time: datetime
    scores: Dict[str, float] = field(default_factory=dict)  # 各项指标得分
    overall_score: float = 0.0
    achievements: List[str] = field(default_factory=list)  # 达成的成就
    areas_for_improvement: List[str] = field(default_factory=list)  # 改进建议
    feedback: str = ""
    
    def calculate_overall_score(self, weights: Dict[str, float] = None) -> float:
        """计算总体得分"""
        if not self.scores:
            return 0.0
        
        if not weights:
            # 默认权重
            weights = {metric: 1.0 for metric in self.scores.keys()}
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for metric, score in self.scores.items():
            weight = weights.get(metric, 1.0)
            weighted_sum += score * weight
            total_weight += weight
        
        self.overall_score = weighted_sum / max(total_weight, 1.0)
        return self.overall_score


class MilitaryTrainingSystem:
    """军事训练系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 训练数据
        self.training_objectives: Dict[str, TrainingObjective] = {}
        self.training_scenarios: Dict[str, TrainingScenario] = {}
        self.training_results: List[TrainingResult] = []
        
        # 参训人员技能档案
        self.participant_skills: Dict[str, Dict[str, float]] = {}
        
        # 评估标准
        self.evaluation_standards = {
            PerformanceMetric.ACCURACY: {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4},
            PerformanceMetric.SPEED: {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4},
            PerformanceMetric.EFFICIENCY: {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4},
            PerformanceMetric.COORDINATION: {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4},
            PerformanceMetric.DECISION_QUALITY: {"excellent": 0.9, "good": 0.8, "fair": 0.6, "poor": 0.4}
        }
        
        # 初始化默认训练目标和场景
        self._initialize_default_content()
    
    def create_training_objective(self, objective: TrainingObjective) -> bool:
        """创建训练目标"""
        try:
            self.training_objectives[objective.id] = objective
            self.logger.info(f"创建训练目标: {objective.name}")
            return True
        except Exception as e:
            self.logger.error(f"创建训练目标失败: {e}")
            return False
    
    def create_training_scenario(self, scenario: TrainingScenario) -> bool:
        """创建训练场景"""
        try:
            # 生成场景事件
            if not scenario.events:
                scenario.events = scenario.generate_events(len(scenario.participants))
            
            self.training_scenarios[scenario.id] = scenario
            self.logger.info(f"创建训练场景: {scenario.name}")
            return True
        except Exception as e:
            self.logger.error(f"创建训练场景失败: {e}")
            return False
    
    def design_training_program(self, 
                              participants: List[str],
                              training_goals: List[str],
                              available_time: float = 8.0,
                              difficulty_level: TrainingLevel = TrainingLevel.INTERMEDIATE) -> Dict[str, Any]:
        """设计训练计划"""
        try:
            program = {
                "id": f"program_{int(datetime.now().timestamp())}",
                "participants": participants,
                "goals": training_goals,
                "total_duration": available_time,
                "difficulty_level": difficulty_level.value,
                "sessions": [],
                "expected_outcomes": {}
            }
            
            # 分析参训人员当前技能水平
            avg_skills = self._analyze_participant_skills(participants)
            
            # 选择合适的训练目标
            suitable_objectives = self._select_training_objectives(
                training_goals, difficulty_level, avg_skills
            )
            
            # 分配训练时间
            remaining_time = available_time
            for objective in suitable_objectives:
                if remaining_time <= 0:
                    break
                
                session_time = min(objective.duration, remaining_time)
                
                # 选择匹配的训练场景
                matching_scenarios = self._find_matching_scenarios(objective, participants)
                
                if matching_scenarios:
                    selected_scenario = matching_scenarios[0]
                    
                    session = {
                        "objective_id": objective.id,
                        "scenario_id": selected_scenario.id,
                        "duration": session_time,
                        "participants": participants,
                        "expected_metrics": objective.target_scores
                    }
                    
                    program["sessions"].append(session)
                    remaining_time -= session_time
            
            # 预测训练效果
            program["expected_outcomes"] = self._predict_training_outcomes(
                program["sessions"], avg_skills
            )
            
            self.logger.info(f"设计训练计划: {len(program['sessions'])} 个训练科目")
            return program
            
        except Exception as e:
            self.logger.error(f"训练计划设计失败: {e}")
            return {"error": str(e)}
    
    def execute_training_session(self,
                                objective_id: str,
                                scenario_id: str,
                                participants: List[str]) -> List[TrainingResult]:
        """执行训练科目"""
        try:
            if objective_id not in self.training_objectives:
                raise ValueError(f"训练目标不存在: {objective_id}")
            
            if scenario_id not in self.training_scenarios:
                raise ValueError(f"训练场景不存在: {scenario_id}")
            
            objective = self.training_objectives[objective_id]
            scenario = self.training_scenarios[scenario_id]
            
            results = []
            start_time = datetime.now()
            
            for participant_id in participants:
                # 模拟训练执行过程
                result = self._simulate_training_execution(
                    participant_id, objective, scenario, start_time
                )
                results.append(result)
                self.training_results.append(result)
            
            # 更新参训人员技能档案
            self._update_participant_skills(results)
            
            self.logger.info(f"完成训练科目: {objective.name}, 参训人员: {len(participants)}")
            return results
            
        except Exception as e:
            self.logger.error(f"训练执行失败: {e}")
            return []
    
    def evaluate_training_effectiveness(self,
                                      results: List[TrainingResult],
                                      objectives: List[TrainingObjective]) -> Dict[str, Any]:
        """评估训练效果"""
        try:
            evaluation = {
                "overall_effectiveness": 0.0,
                "objective_achievement": {},
                "participant_performance": {},
                "improvement_areas": [],
                "recommendations": []
            }
            
            if not results:
                return evaluation
            
            # 计算总体效果
            total_score = sum(result.overall_score for result in results)
            evaluation["overall_effectiveness"] = total_score / len(results)
            
            # 按目标分析达成情况
            objective_scores = {}
            for result in results:
                if result.objective_id not in objective_scores:
                    objective_scores[result.objective_id] = []
                objective_scores[result.objective_id].append(result.overall_score)
            
            for obj_id, scores in objective_scores.items():
                avg_score = sum(scores) / len(scores)
                evaluation["objective_achievement"][obj_id] = {
                    "average_score": avg_score,
                    "achievement_rate": len([s for s in scores if s >= 0.7]) / len(scores),
                    "participant_count": len(scores)
                }
            
            # 分析个人表现
            participant_scores = {}
            for result in results:
                if result.participant_id not in participant_scores:
                    participant_scores[result.participant_id] = []
                participant_scores[result.participant_id].append(result.overall_score)
            
            for participant_id, scores in participant_scores.items():
                avg_score = sum(scores) / len(scores)
                evaluation["participant_performance"][participant_id] = {
                    "average_score": avg_score,
                    "sessions_completed": len(scores),
                    "performance_trend": self._calculate_performance_trend(scores)
                }
            
            # 生成改进建议
            evaluation["improvement_areas"] = self._identify_improvement_areas(results)
            evaluation["recommendations"] = self._generate_training_recommendations(evaluation)
            
            return evaluation
            
        except Exception as e:
            self.logger.error(f"训练效果评估失败: {e}")
            return {"error": str(e)}
    
    def generate_training_report(self,
                               participant_id: str,
                               time_period: Tuple[datetime, datetime] = None) -> Dict[str, Any]:
        """生成训练报告"""
        try:
            if not time_period:
                # 默认最近30天
                end_time = datetime.now()
                start_time = end_time - timedelta(days=30)
                time_period = (start_time, end_time)
            
            # 筛选时间范围内的训练结果
            relevant_results = [
                result for result in self.training_results
                if (result.participant_id == participant_id and
                    time_period[0] <= result.start_time <= time_period[1])
            ]
            
            if not relevant_results:
                return {"message": "指定时间段内无训练记录"}
            
            report = {
                "participant_id": participant_id,
                "report_period": {
                    "start": time_period[0].isoformat(),
                    "end": time_period[1].isoformat()
                },
                "summary": {
                    "total_sessions": len(relevant_results),
                    "total_hours": sum((r.end_time - r.start_time).total_seconds() / 3600 
                                     for r in relevant_results),
                    "average_score": sum(r.overall_score for r in relevant_results) / len(relevant_results),
                    "best_score": max(r.overall_score for r in relevant_results),
                    "improvement_rate": self._calculate_improvement_rate(relevant_results)
                },
                "performance_by_metric": self._analyze_performance_by_metric(relevant_results),
                "achievements": self._collect_achievements(relevant_results),
                "skill_development": self._analyze_skill_development(participant_id, relevant_results),
                "recommendations": self._generate_personal_recommendations(participant_id, relevant_results)
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"训练报告生成失败: {e}")
            return {"error": str(e)}
    
    def _initialize_default_content(self) -> None:
        """初始化默认训练内容"""
        # 创建默认训练目标
        basic_combat = TrainingObjective(
            id="basic_combat",
            name="基础作战训练",
            description="基本作战技能训练",
            training_type=TrainingType.INDIVIDUAL,
            level=TrainingLevel.BASIC,
            metrics=[PerformanceMetric.ACCURACY, PerformanceMetric.SPEED],
            target_scores={"accuracy": 0.8, "speed": 0.7},
            duration=2.0,
            difficulty=0.3
        )
        
        team_coordination = TrainingObjective(
            id="team_coordination",
            name="团队协同训练",
            description="团队协同作战训练",
            training_type=TrainingType.TEAM,
            level=TrainingLevel.INTERMEDIATE,
            metrics=[PerformanceMetric.COORDINATION, PerformanceMetric.COMMUNICATION],
            target_scores={"coordination": 0.8, "communication": 0.8},
            prerequisites=["basic_combat"],
            duration=3.0,
            difficulty=0.6
        )
        
        self.training_objectives["basic_combat"] = basic_combat
        self.training_objectives["team_coordination"] = team_coordination
        
        # 创建默认训练场景
        urban_combat = TrainingScenario(
            id="urban_combat",
            name="城市作战场景",
            description="城市环境下的作战训练",
            scenario_type="combat",
            environment={"terrain": "urban", "weather": "clear", "visibility": "good"},
            objectives=["basic_combat", "team_coordination"],
            duration=2.5,
            complexity=0.7,
            realism=0.8
        )
        
        self.training_scenarios["urban_combat"] = urban_combat
    
    def _analyze_participant_skills(self, participants: List[str]) -> Dict[str, float]:
        """分析参训人员技能水平"""
        if not participants:
            return {}
        
        # 收集所有参训人员的技能数据
        all_skills = {}
        for participant_id in participants:
            if participant_id in self.participant_skills:
                for skill, level in self.participant_skills[participant_id].items():
                    if skill not in all_skills:
                        all_skills[skill] = []
                    all_skills[skill].append(level)
        
        # 计算平均技能水平
        avg_skills = {}
        for skill, levels in all_skills.items():
            avg_skills[skill] = sum(levels) / len(levels)
        
        return avg_skills
    
    def _select_training_objectives(self,
                                  goals: List[str],
                                  difficulty: TrainingLevel,
                                  current_skills: Dict[str, float]) -> List[TrainingObjective]:
        """选择合适的训练目标"""
        suitable_objectives = []
        
        for objective in self.training_objectives.values():
            # 检查难度匹配
            if objective.level != difficulty:
                continue
            
            # 检查目标相关性
            if not any(goal in objective.name.lower() or goal in objective.description.lower() 
                      for goal in goals):
                continue
            
            # 检查前置条件
            if objective.is_achievable(current_skills):
                suitable_objectives.append(objective)
        
        # 按难度和相关性排序
        suitable_objectives.sort(key=lambda x: (x.difficulty, -len(x.prerequisites)))
        
        return suitable_objectives
    
    def _find_matching_scenarios(self,
                               objective: TrainingObjective,
                               participants: List[str]) -> List[TrainingScenario]:
        """查找匹配的训练场景"""
        matching_scenarios = []
        
        for scenario in self.training_scenarios.values():
            if objective.id in scenario.objectives:
                # 检查参与人数是否合适
                if len(participants) <= 20:  # 假设场景最大支持20人
                    matching_scenarios.append(scenario)
        
        # 按复杂度和真实度排序
        matching_scenarios.sort(key=lambda x: (x.complexity, x.realism), reverse=True)
        
        return matching_scenarios
    
    def _predict_training_outcomes(self,
                                 sessions: List[Dict[str, Any]],
                                 current_skills: Dict[str, float]) -> Dict[str, float]:
        """预测训练效果"""
        predicted_outcomes = {}
        
        for session in sessions:
            objective_id = session["objective_id"]
            if objective_id in self.training_objectives:
                objective = self.training_objectives[objective_id]
                
                # 基于当前技能水平和训练难度预测效果
                base_improvement = 0.1 * objective.difficulty
                skill_factor = sum(current_skills.values()) / max(len(current_skills), 1)
                
                predicted_score = min(0.9, skill_factor + base_improvement)
                predicted_outcomes[objective_id] = predicted_score
        
        return predicted_outcomes
    
    def _simulate_training_execution(self,
                                   participant_id: str,
                                   objective: TrainingObjective,
                                   scenario: TrainingScenario,
                                   start_time: datetime) -> TrainingResult:
        """模拟训练执行过程"""
        # 获取参训人员当前技能水平
        current_skills = self.participant_skills.get(participant_id, {})
        
        # 模拟训练结果
        result = TrainingResult(
            participant_id=participant_id,
            scenario_id=scenario.id,
            objective_id=objective.id,
            start_time=start_time,
            end_time=start_time + timedelta(hours=scenario.duration)
        )
        
        # 生成各项指标得分
        for metric in objective.metrics:
            base_skill = current_skills.get(metric.value.lower(), 0.5)
            
            # 考虑训练难度和个人能力
            difficulty_factor = 1.0 - objective.difficulty * 0.3
            random_factor = random.uniform(0.8, 1.2)
            
            score = min(1.0, base_skill * difficulty_factor * random_factor)
            result.scores[metric.value.lower()] = score
        
        # 计算总体得分
        result.calculate_overall_score()
        
        # 生成反馈和建议
        result.feedback = self._generate_training_feedback(result, objective)
        result.areas_for_improvement = self._identify_individual_improvements(result)
        
        return result
    
    def _update_participant_skills(self, results: List[TrainingResult]) -> None:
        """更新参训人员技能档案"""
        for result in results:
            participant_id = result.participant_id
            
            if participant_id not in self.participant_skills:
                self.participant_skills[participant_id] = {}
            
            # 基于训练结果更新技能水平
            for metric, score in result.scores.items():
                current_level = self.participant_skills[participant_id].get(metric, 0.5)
                
                # 技能提升计算（学习曲线）
                improvement = (score - current_level) * 0.1
                new_level = min(1.0, current_level + improvement)
                
                self.participant_skills[participant_id][metric] = new_level
    
    def _calculate_performance_trend(self, scores: List[float]) -> str:
        """计算表现趋势"""
        if len(scores) < 2:
            return "insufficient_data"
        
        # 简单的线性趋势分析
        recent_avg = sum(scores[-3:]) / min(3, len(scores))
        early_avg = sum(scores[:3]) / min(3, len(scores))
        
        if recent_avg > early_avg + 0.1:
            return "improving"
        elif recent_avg < early_avg - 0.1:
            return "declining"
        else:
            return "stable"
    
    def _identify_improvement_areas(self, results: List[TrainingResult]) -> List[str]:
        """识别改进领域"""
        metric_scores = {}
        
        for result in results:
            for metric, score in result.scores.items():
                if metric not in metric_scores:
                    metric_scores[metric] = []
                metric_scores[metric].append(score)
        
        improvement_areas = []
        for metric, scores in metric_scores.items():
            avg_score = sum(scores) / len(scores)
            if avg_score < 0.7:  # 低于70%认为需要改进
                improvement_areas.append(metric)
        
        return improvement_areas
    
    def _generate_training_recommendations(self, evaluation: Dict[str, Any]) -> List[str]:
        """生成训练建议"""
        recommendations = []
        
        overall_effectiveness = evaluation.get("overall_effectiveness", 0.0)
        
        if overall_effectiveness < 0.6:
            recommendations.append("建议降低训练难度，加强基础技能训练")
        elif overall_effectiveness > 0.8:
            recommendations.append("可以适当提高训练难度，增加挑战性")
        
        improvement_areas = evaluation.get("improvement_areas", [])
        if improvement_areas:
            recommendations.append(f"重点加强以下领域的训练: {', '.join(improvement_areas)}")
        
        return recommendations
    
    def _analyze_performance_by_metric(self, results: List[TrainingResult]) -> Dict[str, Dict[str, float]]:
        """按指标分析表现"""
        metric_analysis = {}
        
        for result in results:
            for metric, score in result.scores.items():
                if metric not in metric_analysis:
                    metric_analysis[metric] = {"scores": [], "average": 0.0, "best": 0.0, "trend": "stable"}
                
                metric_analysis[metric]["scores"].append(score)
        
        # 计算统计数据
        for metric, data in metric_analysis.items():
            scores = data["scores"]
            data["average"] = sum(scores) / len(scores)
            data["best"] = max(scores)
            data["trend"] = self._calculate_performance_trend(scores)
        
        return metric_analysis
    
    def _collect_achievements(self, results: List[TrainingResult]) -> List[str]:
        """收集成就"""
        achievements = []
        
        for result in results:
            achievements.extend(result.achievements)
        
        # 去重并排序
        return list(set(achievements))
    
    def _analyze_skill_development(self, participant_id: str, results: List[TrainingResult]) -> Dict[str, Any]:
        """分析技能发展"""
        if participant_id not in self.participant_skills:
            return {}
        
        current_skills = self.participant_skills[participant_id]
        
        # 计算技能提升
        skill_development = {}
        for skill, level in current_skills.items():
            # 假设初始技能水平为0.5
            initial_level = 0.5
            improvement = level - initial_level
            
            skill_development[skill] = {
                "current_level": level,
                "improvement": improvement,
                "proficiency": self._get_proficiency_level(level)
            }
        
        return skill_development
    
    def _generate_personal_recommendations(self, participant_id: str, results: List[TrainingResult]) -> List[str]:
        """生成个人建议"""
        recommendations = []
        
        if not results:
            return recommendations
        
        # 分析最近的表现
        recent_results = results[-5:]  # 最近5次训练
        avg_score = sum(r.overall_score for r in recent_results) / len(recent_results)
        
        if avg_score < 0.6:
            recommendations.append("建议加强基础技能训练，重点提升薄弱环节")
        elif avg_score > 0.8:
            recommendations.append("表现优秀，可以尝试更高难度的训练项目")
        
        # 分析技能发展趋势
        if participant_id in self.participant_skills:
            skills = self.participant_skills[participant_id]
            weak_skills = [skill for skill, level in skills.items() if level < 0.6]
            
            if weak_skills:
                recommendations.append(f"重点提升以下技能: {', '.join(weak_skills)}")
        
        return recommendations
    
    def _generate_training_feedback(self, result: TrainingResult, objective: TrainingObjective) -> str:
        """生成训练反馈"""
        feedback = f"训练目标: {objective.name}\n"
        feedback += f"总体得分: {result.overall_score:.2f}\n"
        
        if result.overall_score >= 0.8:
            feedback += "表现优秀！"
        elif result.overall_score >= 0.6:
            feedback += "表现良好，继续努力。"
        else:
            feedback += "需要加强训练。"
        
        return feedback
    
    def _identify_individual_improvements(self, result: TrainingResult) -> List[str]:
        """识别个人改进建议"""
        improvements = []
        
        for metric, score in result.scores.items():
            if score < 0.6:
                improvements.append(f"提升{metric}能力")
        
        return improvements
    
    def _calculate_improvement_rate(self, results: List[TrainingResult]) -> float:
        """计算改进率"""
        if len(results) < 2:
            return 0.0
        
        first_score = results[0].overall_score
        last_score = results[-1].overall_score
        
        return (last_score - first_score) / max(first_score, 0.1)
    
    def _get_proficiency_level(self, skill_level: float) -> str:
        """获取熟练度等级"""
        if skill_level >= 0.9:
            return "专家"
        elif skill_level >= 0.8:
            return "熟练"
        elif skill_level >= 0.6:
            return "中等"
        else:
            return "初级"
