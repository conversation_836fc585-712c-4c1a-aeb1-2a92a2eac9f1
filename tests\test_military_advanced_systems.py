"""
军事高级系统测试

测试军事知识库和仿真系统等高级功能
"""

import pytest
from datetime import datetime, timedelta
from src.hmdm.knowledge.military_knowledge_base import (
    MilitaryKnowledgeBase, KnowledgeItem, Rule, ExpertAdvice,
    KnowledgeType, ConfidenceLevel
)
from src.hmdm.simulation.military_simulation import (
    MilitarySimulationEngine, SimulationScenario, SimulationEntity, Position,
    SimulationType, EntityType, EntityState
)
from src.hmdm.security.military_security import SecurityLevel


class TestMilitaryKnowledgeBase:
    """军事知识库测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.knowledge_base = MilitaryKnowledgeBase()
    
    def test_knowledge_item_creation(self):
        """测试知识条目创建"""
        item = KnowledgeItem(
            id="test_knowledge_001",
            title="测试战术知识",
            content="这是一个测试用的战术知识条目，包含基本的战术原则和应用方法。",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["战术", "测试", "原则"],
            tags=["测试", "基础"],
            confidence=0.9
        )
        
        success = self.knowledge_base.add_knowledge_item(item)
        assert success is True
        assert item.id in self.knowledge_base.knowledge_items
        
        # 验证索引更新
        assert "战术" in self.knowledge_base.keyword_index
        assert item.id in self.knowledge_base.keyword_index["战术"]
        assert KnowledgeType.TACTICS in self.knowledge_base.type_index
        assert item.id in self.knowledge_base.type_index[KnowledgeType.TACTICS]
    
    def test_knowledge_search(self):
        """测试知识搜索"""
        # 添加测试知识
        item1 = KnowledgeItem(
            id="search_test_001",
            title="城市作战战术",
            content="城市作战需要特殊的战术和装备，重点关注建筑物清理和巷战技巧。",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["城市作战", "巷战", "建筑物"],
            confidence=0.8
        )
        
        item2 = KnowledgeItem(
            id="search_test_002",
            title="装备维护程序",
            content="装备的日常维护和保养程序，确保装备的可靠性和使用寿命。",
            knowledge_type=KnowledgeType.PROCEDURES,
            keywords=["装备", "维护", "保养"],
            confidence=0.9
        )
        
        self.knowledge_base.add_knowledge_item(item1)
        self.knowledge_base.add_knowledge_item(item2)
        
        # 搜索测试
        results = self.knowledge_base.search_knowledge("城市作战")
        assert len(results) > 0
        assert results[0][0].id == "search_test_001"
        assert results[0][1] > 0.3  # 相关度应该较高
        
        # 按类型搜索
        tactics_results = self.knowledge_base.search_knowledge(
            "作战", knowledge_type=KnowledgeType.TACTICS
        )
        assert len(tactics_results) > 0
        assert all(item.knowledge_type == KnowledgeType.TACTICS for item, _ in tactics_results)
    
    def test_rule_creation_and_inference(self):
        """测试规则创建和推理"""
        # 创建测试规则
        rule = Rule(
            id="test_rule_001",
            name="测试推理规则",
            description="用于测试的简单推理规则",
            conditions=["敌方接近", "兵力不足"],
            conclusions=["建议撤退", "请求支援"],
            confidence=0.8,
            priority=2
        )
        
        success = self.knowledge_base.add_rule(rule)
        assert success is True
        assert rule.id in self.knowledge_base.rules
        
        # 测试推理
        self.knowledge_base.add_fact("敌方接近")
        self.knowledge_base.add_fact("兵力不足")
        
        inferred_facts = self.knowledge_base.infer_knowledge()
        assert "建议撤退" in inferred_facts
        assert "请求支援" in inferred_facts
        assert "建议撤退" in self.knowledge_base.inferred_facts
    
    def test_expert_advice_generation(self):
        """测试专家建议生成"""
        # 添加相关知识
        knowledge_item = KnowledgeItem(
            id="advice_test_001",
            title="防御战术指导",
            content="在防御作战中，应选择有利地形，构筑防御工事，合理配置火力点。",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["防御", "战术", "地形", "火力"],
            confidence=0.9
        )
        
        self.knowledge_base.add_knowledge_item(knowledge_item)
        
        # 获取专家建议
        advice_list = self.knowledge_base.get_expert_advice("防御战术")
        assert len(advice_list) > 0
        
        advice = advice_list[0]
        assert isinstance(advice, ExpertAdvice)
        assert "防御" in advice.topic or "防御" in advice.advice
        assert advice.confidence > 0.5
    
    def test_related_knowledge_retrieval(self):
        """测试相关知识检索"""
        # 添加相关知识条目
        item1 = KnowledgeItem(
            id="related_test_001",
            title="主要知识",
            content="主要知识内容",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["主要", "核心"],
            related_items=["related_test_002"]
        )
        
        item2 = KnowledgeItem(
            id="related_test_002",
            title="相关知识",
            content="相关知识内容",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["相关", "辅助"]
        )
        
        self.knowledge_base.add_knowledge_item(item1)
        self.knowledge_base.add_knowledge_item(item2)
        
        # 获取相关知识
        related = self.knowledge_base.get_related_knowledge("related_test_001")
        assert len(related) > 0
        assert any(item.id == "related_test_002" for item in related)
    
    def test_knowledge_statistics(self):
        """测试知识库统计"""
        # 添加一些测试知识
        for i in range(3):
            item = KnowledgeItem(
                id=f"stats_test_{i:03d}",
                title=f"测试知识 {i}",
                content=f"测试内容 {i}",
                knowledge_type=KnowledgeType.TACTICS if i % 2 == 0 else KnowledgeType.PROCEDURES,
                confidence=0.8 + i * 0.05
            )
            self.knowledge_base.add_knowledge_item(item)
        
        stats = self.knowledge_base.get_knowledge_statistics()
        
        assert stats["total_items"] >= 3  # 包括默认知识
        assert stats["total_rules"] >= 2  # 包括默认规则
        assert "by_type" in stats
        assert "by_security_level" in stats
        assert stats["average_confidence"] > 0


class TestMilitarySimulation:
    """军事仿真系统测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.simulation_engine = MilitarySimulationEngine()
    
    def test_position_operations(self):
        """测试位置操作"""
        pos1 = Position(0, 0, 0)
        pos2 = Position(3, 4, 0)
        
        # 测试距离计算
        distance = pos1.distance_to(pos2)
        assert abs(distance - 5.0) < 0.001  # 3-4-5直角三角形
        
        # 测试移动
        new_pos = pos1.move_towards(pos2, 2.0, 1.0)  # 速度2m/s，时间1s
        assert new_pos.x == 1.2  # 2 * (3/5)
        assert new_pos.y == 1.6  # 2 * (4/5)
    
    def test_simulation_entity_creation(self):
        """测试仿真实体创建"""
        entity = SimulationEntity(
            id="test_unit_001",
            name="测试作战单位",
            entity_type=EntityType.UNIT,
            position=Position(100, 200, 0),
            allegiance="friendly",
            max_speed=15.0,
            detection_range=1500.0,
            weapon_range=800.0
        )
        
        assert entity.id == "test_unit_001"
        assert entity.entity_type == EntityType.UNIT
        assert entity.state == EntityState.ACTIVE
        assert entity.position.x == 100
        assert entity.position.y == 200
        assert entity.health == 100.0
        assert entity.ammunition == 100
    
    def test_entity_combat_mechanics(self):
        """测试实体作战机制"""
        # 创建友军单位
        friendly = SimulationEntity(
            id="friendly_001",
            name="友军单位",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly",
            weapon_range=1000.0,
            detection_range=1200.0
        )
        
        # 创建敌军单位
        enemy = SimulationEntity(
            id="enemy_001",
            name="敌军单位",
            entity_type=EntityType.UNIT,
            position=Position(500, 0, 0),  # 距离500米
            allegiance="enemy"
        )
        
        # 测试探测能力
        can_detect = friendly.can_detect(enemy)
        assert can_detect is True
        
        # 测试攻击能力
        can_engage = friendly.can_engage(enemy)
        assert can_engage is True
        
        # 测试攻击
        initial_health = enemy.health
        hit, damage = friendly.engage_target(enemy)
        
        if hit:
            assert enemy.health < initial_health
            assert damage > 0
        
        assert friendly.ammunition < 100  # 弹药应该减少
    
    def test_scenario_creation_and_loading(self):
        """测试场景创建和加载"""
        scenario = SimulationScenario(
            id="test_scenario_001",
            name="测试作战场景",
            description="用于测试的简单作战场景",
            simulation_type=SimulationType.COMBAT,
            duration=1800.0,  # 30分钟
            time_step=1.0
        )
        
        # 添加实体
        friendly_unit = SimulationEntity(
            id="friendly_001",
            name="友军单位",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly"
        )
        
        enemy_unit = SimulationEntity(
            id="enemy_001",
            name="敌军单位",
            entity_type=EntityType.UNIT,
            position=Position(1000, 0, 0),
            allegiance="enemy"
        )
        
        scenario.add_entity(friendly_unit)
        scenario.add_entity(enemy_unit)
        
        # 加载场景
        success = self.simulation_engine.load_scenario(scenario)
        assert success is True
        assert self.simulation_engine.current_scenario is not None
        assert self.simulation_engine.current_scenario.id == "test_scenario_001"
        
        # 测试按阵营获取实体
        friendly_entities = scenario.get_entities_by_allegiance("friendly")
        enemy_entities = scenario.get_entities_by_allegiance("enemy")
        
        assert len(friendly_entities) == 1
        assert len(enemy_entities) == 1
        assert friendly_entities[0].id == "friendly_001"
        assert enemy_entities[0].id == "enemy_001"
    
    def test_simulation_execution(self):
        """测试仿真执行"""
        # 创建简单场景
        scenario = SimulationScenario(
            id="execution_test",
            name="执行测试场景",
            description="测试仿真执行",
            simulation_type=SimulationType.COMBAT,
            duration=10.0,  # 10秒短时间测试
            time_step=1.0
        )
        
        # 添加两个相对的单位
        friendly = SimulationEntity(
            id="friendly_001",
            name="友军",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly",
            target_position=Position(500, 0, 0)  # 设置目标位置
        )
        
        enemy = SimulationEntity(
            id="enemy_001",
            name="敌军",
            entity_type=EntityType.UNIT,
            position=Position(1000, 0, 0),
            allegiance="enemy"
        )
        
        scenario.add_entity(friendly)
        scenario.add_entity(enemy)
        
        # 加载并运行仿真
        self.simulation_engine.load_scenario(scenario)
        result = self.simulation_engine.run_simulation()
        
        assert "error" not in result
        assert result["scenario_id"] == "execution_test"
        assert result["duration"] == 10.0
        assert len(result["time_steps"]) > 0
        assert "final_state" in result
        assert "statistics" in result
        
        # 验证实体移动
        final_state = result["final_state"]
        friendly_final = final_state["entities"]["friendly_001"]
        
        # 友军应该向目标位置移动了
        assert friendly_final["final_position"][0] > 0
    
    def test_simulation_state_monitoring(self):
        """测试仿真状态监控"""
        scenario = SimulationScenario(
            id="state_test",
            name="状态测试",
            description="测试状态监控",
            simulation_type=SimulationType.TACTICAL,
            duration=5.0
        )
        
        entity = SimulationEntity(
            id="test_entity",
            name="测试实体",
            entity_type=EntityType.UNIT,
            position=Position(100, 100, 0),
            allegiance="friendly"
        )
        
        scenario.add_entity(entity)
        self.simulation_engine.load_scenario(scenario)
        
        # 获取初始状态
        initial_state = self.simulation_engine.get_simulation_state()
        assert initial_state["scenario_id"] == "state_test"
        assert initial_state["current_time"] == 0.0
        assert initial_state["is_running"] is False
        assert "test_entity" in initial_state["entities"]
        
        entity_state = initial_state["entities"]["test_entity"]
        assert entity_state["position"] == [100, 100, 0]
        assert entity_state["state"] == "活跃"
        assert entity_state["health"] == 100.0
    
    def test_simulation_analysis(self):
        """测试仿真结果分析"""
        # 创建并运行一个简单仿真
        scenario = SimulationScenario(
            id="analysis_test",
            name="分析测试",
            description="测试结果分析",
            simulation_type=SimulationType.COMBAT,
            duration=5.0,
            time_step=1.0
        )
        
        friendly = SimulationEntity(
            id="friendly_001",
            name="友军",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly"
        )
        
        enemy = SimulationEntity(
            id="enemy_001",
            name="敌军",
            entity_type=EntityType.UNIT,
            position=Position(200, 0, 0),  # 较近距离，容易发生交战
            allegiance="enemy"
        )
        
        scenario.add_entity(friendly)
        scenario.add_entity(enemy)
        
        self.simulation_engine.load_scenario(scenario)
        result = self.simulation_engine.run_simulation()
        
        # 分析结果
        analysis = self.simulation_engine.analyze_simulation_results()
        
        assert "error" not in analysis
        assert "scenario_info" in analysis
        assert "entity_analysis" in analysis
        assert "engagement_analysis" in analysis
        assert "movement_analysis" in analysis
        assert "performance_metrics" in analysis
        assert "recommendations" in analysis
        
        # 验证分析内容
        scenario_info = analysis["scenario_info"]
        assert scenario_info["id"] == "analysis_test"
        assert scenario_info["duration"] == 5.0
        
        entity_analysis = analysis["entity_analysis"]
        assert entity_analysis["total_entities"] == 2
        assert 0 <= entity_analysis["survival_rate"] <= 1.0


class TestIntegratedAdvancedSystems:
    """高级系统集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.knowledge_base = MilitaryKnowledgeBase()
        self.simulation_engine = MilitarySimulationEngine()
    
    def test_knowledge_guided_simulation(self):
        """测试知识指导的仿真"""
        # 1. 添加战术知识
        tactical_knowledge = KnowledgeItem(
            id="tactical_guidance_001",
            title="数量劣势时的战术",
            content="当兵力处于劣势时，应采用防御战术，利用地形优势，避免正面交锋。",
            knowledge_type=KnowledgeType.TACTICS,
            keywords=["数量劣势", "防御", "地形"],
            confidence=0.9
        )
        
        self.knowledge_base.add_knowledge_item(tactical_knowledge)
        
        # 2. 添加推理规则
        tactical_rule = Rule(
            id="outnumbered_rule",
            name="数量劣势规则",
            description="数量劣势时的战术选择",
            conditions=["兵力劣势", "敌方接近"],
            conclusions=["采用防御战术", "寻找掩护"],
            confidence=0.85
        )
        
        self.knowledge_base.add_rule(tactical_rule)
        
        # 3. 设置仿真场景（模拟数量劣势）
        scenario = SimulationScenario(
            id="knowledge_guided_sim",
            name="知识指导仿真",
            description="基于知识库的战术仿真",
            simulation_type=SimulationType.TACTICAL,
            duration=30.0
        )
        
        # 友军（数量劣势）
        friendly = SimulationEntity(
            id="friendly_001",
            name="友军小队",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly"
        )
        
        # 敌军（数量优势）
        for i in range(3):
            enemy = SimulationEntity(
                id=f"enemy_{i:03d}",
                name=f"敌军单位{i}",
                entity_type=EntityType.UNIT,
                position=Position(800 + i * 100, 0, 0),
                allegiance="enemy"
            )
            scenario.add_entity(enemy)
        
        scenario.add_entity(friendly)
        
        # 4. 基于知识库获取战术建议
        self.knowledge_base.add_fact("兵力劣势")
        self.knowledge_base.add_fact("敌方接近")
        
        advice_list = self.knowledge_base.get_expert_advice("数量劣势")  # 使用关键词搜索
        inferred_facts = self.knowledge_base.infer_knowledge()

        # 5. 验证知识系统工作正常
        # 如果没有找到建议，至少验证推理系统工作
        if len(advice_list) == 0:
            # 尝试更通用的搜索
            advice_list = self.knowledge_base.get_expert_advice("防御")

        # 验证推理结果
        assert len(inferred_facts) > 0 or len(advice_list) > 0
        assert "采用防御战术" in inferred_facts
        
        # 6. 运行仿真
        self.simulation_engine.load_scenario(scenario)
        result = self.simulation_engine.run_simulation()
        
        # 7. 验证仿真结果
        assert "error" not in result
        assert result["scenario_id"] == "knowledge_guided_sim"
        
        # 分析结果
        analysis = self.simulation_engine.analyze_simulation_results()
        assert "recommendations" in analysis
    
    def test_simulation_knowledge_feedback(self):
        """测试仿真结果反馈到知识库"""
        # 1. 运行仿真获取结果
        scenario = SimulationScenario(
            id="feedback_test",
            name="反馈测试",
            description="测试仿真反馈",
            simulation_type=SimulationType.COMBAT,
            duration=20.0
        )
        
        # 创建不平衡的对战
        friendly = SimulationEntity(
            id="friendly_001",
            name="友军",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly",
            health=50.0  # 较低生命值
        )
        
        enemy = SimulationEntity(
            id="enemy_001",
            name="敌军",
            entity_type=EntityType.UNIT,
            position=Position(300, 0, 0),
            allegiance="enemy"
        )
        
        scenario.add_entity(friendly)
        scenario.add_entity(enemy)
        
        self.simulation_engine.load_scenario(scenario)
        result = self.simulation_engine.run_simulation()
        
        # 2. 基于仿真结果创建新知识
        casualty_rate = result.get("statistics", {}).get("casualty_rate", 0.0)
        
        if casualty_rate > 0.5:
            # 高伤亡率，添加相关知识
            lesson_learned = KnowledgeItem(
                id="lesson_from_sim_001",
                title="高伤亡率场景教训",
                content=f"在类似场景中观察到{casualty_rate:.1%}的伤亡率，建议加强防护措施。",
                knowledge_type=KnowledgeType.HISTORICAL,
                keywords=["高伤亡", "教训", "防护"],
                confidence=0.7,
                source="仿真分析"
            )
            
            success = self.knowledge_base.add_knowledge_item(lesson_learned)
            assert success is True
        
        # 3. 验证知识库更新
        lessons = self.knowledge_base.search_knowledge("伤亡", knowledge_type=KnowledgeType.HISTORICAL)
        if casualty_rate > 0.5:
            assert len(lessons) > 0
    
    def test_adaptive_simulation_parameters(self):
        """测试自适应仿真参数"""
        # 1. 从知识库获取参数建议
        equipment_knowledge = KnowledgeItem(
            id="equipment_params_001",
            title="装备性能参数",
            content="标准作战单位的探测距离应为1200米，武器射程800米，最大速度12m/s。",
            knowledge_type=KnowledgeType.EQUIPMENT,
            keywords=["装备", "参数", "性能"],
            confidence=0.9
        )
        
        self.knowledge_base.add_knowledge_item(equipment_knowledge)
        
        # 2. 搜索装备参数知识
        equipment_info = self.knowledge_base.search_knowledge("装备性能", knowledge_type=KnowledgeType.EQUIPMENT)
        assert len(equipment_info) > 0
        
        # 3. 基于知识创建仿真实体
        scenario = SimulationScenario(
            id="adaptive_params_test",
            name="自适应参数测试",
            description="基于知识库的参数设置",
            simulation_type=SimulationType.TACTICAL,
            duration=15.0
        )
        
        # 使用知识库建议的参数
        entity = SimulationEntity(
            id="knowledge_based_unit",
            name="知识驱动单位",
            entity_type=EntityType.UNIT,
            position=Position(0, 0, 0),
            allegiance="friendly",
            max_speed=12.0,  # 基于知识库
            detection_range=1200.0,  # 基于知识库
            weapon_range=800.0  # 基于知识库
        )
        
        scenario.add_entity(entity)
        
        # 4. 运行仿真并验证
        self.simulation_engine.load_scenario(scenario)
        result = self.simulation_engine.run_simulation()
        
        assert "error" not in result
        
        # 验证实体参数正确应用
        final_state = result["final_state"]["entities"]["knowledge_based_unit"]
        assert final_state is not None
