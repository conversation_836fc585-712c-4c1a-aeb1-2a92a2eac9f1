{"metadata": {"name": "默认评估方案测试数据", "description": "标准的7类指标评估方案", "version": "1.0", "created_date": "2024-01-01"}, "scheme": {"id": "default_scheme_test", "name": "默认人机功能分配评估方案", "description": "包含负荷、效率、可靠性、可用性、安全性、成本、性能7大类指标的标准评估方案", "indicators": {"workload_mental": {"id": "workload_mental", "name": "心理负荷", "description": "操作者的心理工作负荷", "indicator_type": "WORKLOAD", "min_value": 0.0, "max_value": 1.0, "is_benefit": false, "unit": "标准化值", "weight": 0.1}, "workload_physical": {"id": "workload_physical", "name": "物理负荷", "description": "操作者的物理工作负荷", "indicator_type": "WORKLOAD", "min_value": 0.0, "max_value": 1.0, "is_benefit": false, "unit": "标准化值", "weight": 0.1}, "efficiency_time": {"id": "efficiency_time", "name": "任务完成时间", "description": "完成任务所需的时间", "indicator_type": "EFFICIENCY", "min_value": 0.0, "max_value": 1.0, "is_benefit": true, "unit": "标准化值", "weight": 0.15}, "efficiency_speed": {"id": "efficiency_speed", "name": "处理速度", "description": "信息处理速度", "indicator_type": "EFFICIENCY", "min_value": 0.0, "max_value": 1.0, "is_benefit": true, "unit": "标准化值", "weight": 0.1}, "reliability_error": {"id": "reliability_error", "name": "错误率", "description": "操作错误发生率", "indicator_type": "RELIABILITY", "min_value": 0.0, "max_value": 1.0, "is_benefit": false, "unit": "标准化值", "weight": 0.1}, "reliability_stability": {"id": "reliability_stability", "name": "系统稳定性", "description": "系统运行的稳定性", "indicator_type": "RELIABILITY", "min_value": 0.0, "max_value": 1.0, "is_benefit": true, "unit": "标准化值", "weight": 0.1}, "usability_satisfaction": {"id": "usability_satisfaction", "name": "用户满意度", "description": "用户对系统的满意程度", "indicator_type": "USABILITY", "min_value": 0.0, "max_value": 1.0, "is_benefit": true, "unit": "标准化值", "weight": 0.15}, "safety_risk": {"id": "safety_risk", "name": "安全风险等级", "description": "系统运行的安全风险等级", "indicator_type": "SAFETY", "min_value": 0.0, "max_value": 1.0, "is_benefit": false, "unit": "标准化值", "weight": 0.1}, "cost_development": {"id": "cost_development", "name": "开发成本", "description": "系统开发所需成本", "indicator_type": "COST", "min_value": 0.0, "max_value": 1.0, "is_benefit": false, "unit": "标准化值", "weight": 0.05}, "performance_response": {"id": "performance_response", "name": "响应时间", "description": "系统响应时间", "indicator_type": "PERFORMANCE", "min_value": 0.0, "max_value": 1.0, "is_benefit": true, "unit": "标准化值", "weight": 0.05}}, "weights": {"workload_mental": 0.1, "workload_physical": 0.1, "efficiency_time": 0.15, "efficiency_speed": 0.1, "reliability_error": 0.1, "reliability_stability": 0.1, "usability_satisfaction": 0.15, "safety_risk": 0.1, "cost_development": 0.05, "performance_response": 0.05}, "total_weight": 1.0}, "test_cases": [{"name": "权重归一化测试", "description": "验证所有权重之和为1", "expected_result": 1.0, "tolerance": 0.001}, {"name": "指标类型覆盖测试", "description": "验证包含所有7类指标类型", "expected_types": ["WORKLOAD", "EFFICIENCY", "RELIABILITY", "USABILITY", "SAFETY", "COST", "PERFORMANCE"], "expected_count": 7}, {"name": "效益型指标测试", "description": "验证效益型指标的正确标识", "benefit_indicators": ["efficiency_time", "efficiency_speed", "reliability_stability", "usability_satisfaction", "performance_response"], "cost_indicators": ["workload_mental", "workload_physical", "reliability_error", "safety_risk", "cost_development"]}]}