"""
军事态势感知与预测模块

实现战场态势的实时感知、分析和预测功能，支持多源信息融合和态势评估。
"""

import numpy as np
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import math

from ..security.military_security import SecurityLevel
from ..utils.realtime_processor import RealTimeData, DataType


class ThreatLevel(Enum):
    """威胁等级"""
    MINIMAL = "极低"
    LOW = "低"
    MODERATE = "中等"
    HIGH = "高"
    CRITICAL = "极高"


class SituationType(Enum):
    """态势类型"""
    FRIENDLY = "友军态势"
    ENEMY = "敌军态势"
    NEUTRAL = "中性态势"
    UNKNOWN = "未知态势"
    ENVIRONMENT = "环境态势"


class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_LOW = 0.2
    LOW = 0.4
    MODERATE = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


@dataclass
class SituationEntity:
    """态势实体"""
    id: str
    name: str
    entity_type: str  # 实体类型：部队、装备、设施等
    position: Tuple[float, float, float] = (0.0, 0.0, 0.0)  # 经度、纬度、高度
    velocity: Tuple[float, float, float] = (0.0, 0.0, 0.0)  # 速度向量
    heading: float = 0.0  # 航向角度
    status: str = "active"  # 状态：active, inactive, destroyed
    attributes: Dict[str, Any] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    confidence: float = 0.8
    source: str = ""
    
    def distance_to(self, other: 'SituationEntity') -> float:
        """计算到另一个实体的距离"""
        dx = self.position[0] - other.position[0]
        dy = self.position[1] - other.position[1]
        dz = self.position[2] - other.position[2]
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def predict_position(self, time_delta: float) -> Tuple[float, float, float]:
        """预测未来位置"""
        return (
            self.position[0] + self.velocity[0] * time_delta,
            self.position[1] + self.velocity[1] * time_delta,
            self.position[2] + self.velocity[2] * time_delta
        )


@dataclass
class SituationAssessment:
    """态势评估结果"""
    id: str = field(default_factory=lambda: f"assessment_{int(datetime.now().timestamp())}")
    timestamp: datetime = field(default_factory=datetime.now)
    situation_type: SituationType = SituationType.UNKNOWN
    threat_level: ThreatLevel = ThreatLevel.MODERATE
    confidence: float = 0.6
    summary: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    entities: List[SituationEntity] = field(default_factory=list)
    predictions: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    security_level: SecurityLevel = SecurityLevel.SECRET
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "situation_type": self.situation_type.value,
            "threat_level": self.threat_level.value,
            "confidence": self.confidence,
            "summary": self.summary,
            "details": self.details,
            "entity_count": len(self.entities),
            "predictions": self.predictions,
            "recommendations": self.recommendations,
            "security_level": self.security_level.value
        }


class SituationAwarenessEngine:
    """态势感知引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 态势数据存储
        self.entities: Dict[str, SituationEntity] = {}
        self.assessments: List[SituationAssessment] = []
        
        # 融合规则和权重
        self.fusion_weights = {
            "radar": 0.8,
            "satellite": 0.9,
            "intelligence": 0.7,
            "reconnaissance": 0.85,
            "communication": 0.6
        }
        
        # 威胁评估参数
        self.threat_parameters = {
            "distance_threshold": 50.0,  # 公里
            "speed_threshold": 100.0,    # 公里/小时
            "approach_angle_threshold": 30.0,  # 度
            "capability_multiplier": 1.5
        }
        
        # 预测参数
        self.prediction_horizon = 3600  # 预测时间窗口（秒）
        self.prediction_intervals = [300, 900, 1800, 3600]  # 预测时间点
    
    def ingest_sensor_data(self, sensor_data: RealTimeData) -> bool:
        """接收传感器数据"""
        try:
            if sensor_data.data_type != DataType.SENSOR:
                return False
            
            # 解析传感器数据
            content = sensor_data.content
            
            # 创建或更新实体
            entity_id = content.get("entity_id", f"entity_{sensor_data.id}")
            
            if entity_id in self.entities:
                entity = self.entities[entity_id]
                # 更新现有实体
                self._update_entity_from_sensor(entity, content, sensor_data.source)
            else:
                # 创建新实体
                entity = self._create_entity_from_sensor(entity_id, content, sensor_data.source)
                self.entities[entity_id] = entity
            
            self.logger.info(f"处理传感器数据: {entity_id} from {sensor_data.source}")
            return True
            
        except Exception as e:
            self.logger.error(f"传感器数据处理失败: {e}")
            return False
    
    def ingest_intelligence_data(self, intel_data: RealTimeData) -> bool:
        """接收情报数据"""
        try:
            if intel_data.data_type != DataType.INTELLIGENCE:
                return False
            
            content = intel_data.content
            
            # 处理情报数据
            entity_id = content.get("target_id", f"intel_{intel_data.id}")
            
            if entity_id in self.entities:
                entity = self.entities[entity_id]
                # 用情报数据增强实体信息
                self._enhance_entity_with_intelligence(entity, content, intel_data.source)
            else:
                # 从情报创建新实体
                entity = self._create_entity_from_intelligence(entity_id, content, intel_data.source)
                self.entities[entity_id] = entity
            
            self.logger.info(f"处理情报数据: {entity_id} from {intel_data.source}")
            return True
            
        except Exception as e:
            self.logger.error(f"情报数据处理失败: {e}")
            return False
    
    def perform_situation_assessment(self) -> SituationAssessment:
        """执行态势评估"""
        assessment = SituationAssessment()
        
        try:
            # 分析当前实体
            friendly_entities = []
            enemy_entities = []
            unknown_entities = []
            
            for entity in self.entities.values():
                entity_type = entity.attributes.get("allegiance", "unknown")
                if entity_type == "friendly":
                    friendly_entities.append(entity)
                elif entity_type == "enemy":
                    enemy_entities.append(entity)
                else:
                    unknown_entities.append(entity)
            
            # 威胁评估
            threat_level = self._assess_threat_level(enemy_entities, friendly_entities)
            assessment.threat_level = threat_level
            
            # 态势类型判断
            if len(enemy_entities) > len(friendly_entities) * 1.5:
                assessment.situation_type = SituationType.ENEMY
            elif len(friendly_entities) > len(enemy_entities) * 1.5:
                assessment.situation_type = SituationType.FRIENDLY
            else:
                assessment.situation_type = SituationType.NEUTRAL
            
            # 生成摘要
            assessment.summary = self._generate_situation_summary(
                friendly_entities, enemy_entities, unknown_entities, threat_level
            )
            
            # 详细信息
            assessment.details = {
                "friendly_count": len(friendly_entities),
                "enemy_count": len(enemy_entities),
                "unknown_count": len(unknown_entities),
                "total_entities": len(self.entities),
                "assessment_time": datetime.now().isoformat()
            }
            
            # 复制实体信息
            assessment.entities = list(self.entities.values())
            
            # 生成预测
            assessment.predictions = self._generate_predictions()
            
            # 生成建议
            assessment.recommendations = self._generate_recommendations(assessment)
            
            # 计算置信度
            assessment.confidence = self._calculate_assessment_confidence()
            
            # 保存评估结果
            self.assessments.append(assessment)
            
            # 限制历史记录数量
            if len(self.assessments) > 100:
                self.assessments = self.assessments[-50:]
            
            self.logger.info(f"态势评估完成: {assessment.situation_type.value}, 威胁等级: {assessment.threat_level.value}")
            return assessment
            
        except Exception as e:
            self.logger.error(f"态势评估失败: {e}")
            assessment.summary = f"态势评估失败: {str(e)}"
            return assessment
    
    def predict_situation(self, time_horizon: int = 3600) -> Dict[str, Any]:
        """预测未来态势"""
        predictions = {
            "time_horizon": time_horizon,
            "predictions": [],
            "confidence": 0.0,
            "key_events": []
        }
        
        try:
            for interval in self.prediction_intervals:
                if interval > time_horizon:
                    break
                
                # 预测各实体位置
                predicted_entities = {}
                for entity_id, entity in self.entities.items():
                    if entity.status == "active":
                        predicted_pos = entity.predict_position(interval)
                        predicted_entities[entity_id] = {
                            "position": predicted_pos,
                            "confidence": max(0.1, entity.confidence - interval/3600*0.2)
                        }
                
                # 分析预测态势
                interval_prediction = {
                    "time_offset": interval,
                    "predicted_entities": predicted_entities,
                    "potential_conflicts": self._detect_potential_conflicts(predicted_entities),
                    "threat_assessment": self._predict_threat_level(predicted_entities)
                }
                
                predictions["predictions"].append(interval_prediction)
            
            # 识别关键事件
            predictions["key_events"] = self._identify_key_events(predictions["predictions"])
            
            # 计算整体置信度
            predictions["confidence"] = self._calculate_prediction_confidence(predictions["predictions"])
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"态势预测失败: {e}")
            predictions["error"] = str(e)
            return predictions
    
    def get_situation_summary(self) -> Dict[str, Any]:
        """获取态势摘要"""
        if not self.assessments:
            return {"error": "无可用态势评估"}
        
        latest_assessment = self.assessments[-1]
        
        return {
            "current_situation": latest_assessment.to_dict(),
            "entity_statistics": self._get_entity_statistics(),
            "threat_trends": self._analyze_threat_trends(),
            "system_status": {
                "total_entities": len(self.entities),
                "active_entities": len([e for e in self.entities.values() if e.status == "active"]),
                "last_update": max([e.last_updated for e in self.entities.values()]).isoformat() if self.entities else None,
                "assessment_count": len(self.assessments)
            }
        }
    
    def _update_entity_from_sensor(self, entity: SituationEntity, content: Dict[str, Any], source: str) -> None:
        """从传感器数据更新实体"""
        if "position" in content:
            entity.position = tuple(content["position"])
        if "velocity" in content:
            entity.velocity = tuple(content["velocity"])
        if "heading" in content:
            entity.heading = content["heading"]
        
        # 更新属性
        for key, value in content.items():
            if key not in ["position", "velocity", "heading"]:
                entity.attributes[key] = value
        
        # 更新元数据
        entity.last_updated = datetime.now()
        entity.source = source
        
        # 根据数据源调整置信度
        source_confidence = self.fusion_weights.get(source.lower(), 0.5)
        entity.confidence = (entity.confidence + source_confidence) / 2
    
    def _create_entity_from_sensor(self, entity_id: str, content: Dict[str, Any], source: str) -> SituationEntity:
        """从传感器数据创建实体"""
        entity = SituationEntity(
            id=entity_id,
            name=content.get("name", f"Entity_{entity_id}"),
            entity_type=content.get("type", "unknown"),
            source=source
        )
        
        self._update_entity_from_sensor(entity, content, source)
        return entity
    
    def _enhance_entity_with_intelligence(self, entity: SituationEntity, content: Dict[str, Any], source: str) -> None:
        """用情报数据增强实体信息"""
        # 更新实体属性
        for key, value in content.items():
            if key.startswith("intel_"):
                entity.attributes[key] = value
        
        # 更新威胁评估相关信息
        if "threat_level" in content:
            entity.attributes["threat_level"] = content["threat_level"]
        if "capabilities" in content:
            entity.attributes["capabilities"] = content["capabilities"]
        if "allegiance" in content:
            entity.attributes["allegiance"] = content["allegiance"]
        
        entity.last_updated = datetime.now()
    
    def _create_entity_from_intelligence(self, entity_id: str, content: Dict[str, Any], source: str) -> SituationEntity:
        """从情报数据创建实体"""
        entity = SituationEntity(
            id=entity_id,
            name=content.get("target_name", f"Intel_{entity_id}"),
            entity_type=content.get("target_type", "unknown"),
            source=source,
            confidence=0.6  # 情报数据置信度通常较低
        )
        
        self._enhance_entity_with_intelligence(entity, content, source)
        return entity
    
    def _assess_threat_level(self, enemy_entities: List[SituationEntity], 
                           friendly_entities: List[SituationEntity]) -> ThreatLevel:
        """评估威胁等级"""
        if not enemy_entities:
            return ThreatLevel.MINIMAL
        
        threat_score = 0.0
        
        # 数量因素
        enemy_count = len(enemy_entities)
        friendly_count = len(friendly_entities)
        ratio = enemy_count / max(1, friendly_count)
        threat_score += ratio * 20
        
        # 距离因素
        min_distance = float('inf')
        for enemy in enemy_entities:
            for friendly in friendly_entities:
                distance = enemy.distance_to(friendly)
                min_distance = min(min_distance, distance)
        
        if min_distance < self.threat_parameters["distance_threshold"]:
            threat_score += (self.threat_parameters["distance_threshold"] - min_distance) / self.threat_parameters["distance_threshold"] * 30
        
        # 能力因素
        for enemy in enemy_entities:
            capabilities = enemy.attributes.get("capabilities", [])
            if isinstance(capabilities, list):
                threat_score += len(capabilities) * 5
        
        # 转换为威胁等级
        if threat_score < 10:
            return ThreatLevel.MINIMAL
        elif threat_score < 25:
            return ThreatLevel.LOW
        elif threat_score < 50:
            return ThreatLevel.MODERATE
        elif threat_score < 75:
            return ThreatLevel.HIGH
        else:
            return ThreatLevel.CRITICAL
    
    def _generate_situation_summary(self, friendly: List[SituationEntity], 
                                  enemy: List[SituationEntity], 
                                  unknown: List[SituationEntity],
                                  threat_level: ThreatLevel) -> str:
        """生成态势摘要"""
        summary = f"当前态势：发现友军目标{len(friendly)}个，敌军目标{len(enemy)}个，未知目标{len(unknown)}个。"
        summary += f"威胁等级：{threat_level.value}。"
        
        if enemy:
            closest_enemy = min(enemy, key=lambda e: min([e.distance_to(f) for f in friendly]) if friendly else float('inf'))
            if friendly:
                min_dist = min([closest_enemy.distance_to(f) for f in friendly])
                summary += f"最近敌军目标距离{min_dist:.1f}公里。"
        
        return summary
    
    def _generate_predictions(self) -> Dict[str, Any]:
        """生成态势预测"""
        return self.predict_situation(self.prediction_horizon)
    
    def _generate_recommendations(self, assessment: SituationAssessment) -> List[str]:
        """生成行动建议"""
        recommendations = []
        
        if assessment.threat_level == ThreatLevel.CRITICAL:
            recommendations.append("建议立即采取防御措施")
            recommendations.append("考虑请求增援")
        elif assessment.threat_level == ThreatLevel.HIGH:
            recommendations.append("提高警戒等级")
            recommendations.append("准备应急预案")
        elif assessment.threat_level == ThreatLevel.MODERATE:
            recommendations.append("保持正常警戒")
            recommendations.append("继续监控态势发展")
        
        # 基于实体数量的建议
        enemy_count = len([e for e in assessment.entities if e.attributes.get("allegiance") == "enemy"])
        friendly_count = len([e for e in assessment.entities if e.attributes.get("allegiance") == "friendly"])
        
        if enemy_count > friendly_count * 1.5:
            recommendations.append("考虑战术撤退或重新部署")
        elif friendly_count > enemy_count * 2:
            recommendations.append("可考虑主动出击")
        
        return recommendations
    
    def _calculate_assessment_confidence(self) -> float:
        """计算评估置信度"""
        if not self.entities:
            return 0.0
        
        total_confidence = sum(entity.confidence for entity in self.entities.values())
        avg_confidence = total_confidence / len(self.entities)
        
        # 考虑数据新鲜度
        now = datetime.now()
        freshness_scores = []
        for entity in self.entities.values():
            age = (now - entity.last_updated).total_seconds()
            freshness = max(0.1, 1.0 - age / 3600)  # 1小时后置信度开始下降
            freshness_scores.append(freshness)
        
        avg_freshness = sum(freshness_scores) / len(freshness_scores)
        
        return (avg_confidence + avg_freshness) / 2
    
    def _detect_potential_conflicts(self, predicted_entities: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检测潜在冲突"""
        conflicts = []
        
        entity_list = list(predicted_entities.items())
        for i, (id1, data1) in enumerate(entity_list):
            for id2, data2 in entity_list[i+1:]:
                pos1 = data1["position"]
                pos2 = data2["position"]
                
                # 计算距离
                distance = math.sqrt(sum((a-b)**2 for a, b in zip(pos1, pos2)))
                
                if distance < 10.0:  # 10公里内认为可能冲突
                    conflicts.append({
                        "entity1": id1,
                        "entity2": id2,
                        "distance": distance,
                        "risk_level": "high" if distance < 5.0 else "moderate"
                    })
        
        return conflicts
    
    def _predict_threat_level(self, predicted_entities: Dict[str, Any]) -> str:
        """预测威胁等级"""
        # 简化的威胁预测逻辑
        conflict_count = len(self._detect_potential_conflicts(predicted_entities))
        
        if conflict_count > 5:
            return "critical"
        elif conflict_count > 2:
            return "high"
        elif conflict_count > 0:
            return "moderate"
        else:
            return "low"
    
    def _identify_key_events(self, predictions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别关键事件"""
        key_events = []
        
        for prediction in predictions:
            conflicts = prediction.get("potential_conflicts", [])
            for conflict in conflicts:
                if conflict["risk_level"] == "high":
                    key_events.append({
                        "time_offset": prediction["time_offset"],
                        "event_type": "potential_collision",
                        "description": f"实体 {conflict['entity1']} 和 {conflict['entity2']} 可能发生冲突",
                        "risk_level": conflict["risk_level"]
                    })
        
        return key_events
    
    def _calculate_prediction_confidence(self, predictions: List[Dict[str, Any]]) -> float:
        """计算预测置信度"""
        if not predictions:
            return 0.0
        
        # 预测置信度随时间递减
        confidences = []
        for prediction in predictions:
            time_offset = prediction["time_offset"]
            base_confidence = 0.8
            time_decay = max(0.1, 1.0 - time_offset / 7200)  # 2小时后置信度显著下降
            confidences.append(base_confidence * time_decay)
        
        return sum(confidences) / len(confidences)
    
    def _get_entity_statistics(self) -> Dict[str, Any]:
        """获取实体统计信息"""
        stats = {
            "total": len(self.entities),
            "by_type": {},
            "by_allegiance": {},
            "by_status": {}
        }
        
        for entity in self.entities.values():
            # 按类型统计
            entity_type = entity.entity_type
            stats["by_type"][entity_type] = stats["by_type"].get(entity_type, 0) + 1
            
            # 按阵营统计
            allegiance = entity.attributes.get("allegiance", "unknown")
            stats["by_allegiance"][allegiance] = stats["by_allegiance"].get(allegiance, 0) + 1
            
            # 按状态统计
            status = entity.status
            stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
        
        return stats
    
    def _analyze_threat_trends(self) -> Dict[str, Any]:
        """分析威胁趋势"""
        if len(self.assessments) < 2:
            return {"trend": "insufficient_data"}
        
        recent_assessments = self.assessments[-10:]  # 最近10次评估
        threat_levels = [a.threat_level for a in recent_assessments]
        
        # 转换为数值进行趋势分析
        threat_values = [list(ThreatLevel).index(level) for level in threat_levels]
        
        if len(threat_values) >= 2:
            trend = threat_values[-1] - threat_values[0]
            if trend > 0:
                trend_direction = "increasing"
            elif trend < 0:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
        else:
            trend_direction = "stable"
        
        return {
            "trend": trend_direction,
            "current_level": threat_levels[-1].value,
            "previous_level": threat_levels[-2].value if len(threat_levels) > 1 else None,
            "assessment_count": len(recent_assessments)
        }
