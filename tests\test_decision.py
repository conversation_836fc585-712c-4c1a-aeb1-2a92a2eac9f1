"""
决策模块测试
"""

import pytest
import numpy as np
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from hmdm.decision import FuzzyDecisionEngine
from hmdm.models.decision_models import (
    DecisionMatrix, Alternative, FuzzyNumber, DecisionResult, DecisionMethod
)
from hmdm.models.evaluation_models import (
    EvaluationScheme, IndicatorDefinition, IndicatorType
)


class TestFuzzyDecisionEngine:
    """模糊决策引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = FuzzyDecisionEngine()
        self.decision_matrix = self._create_test_decision_matrix()
    
    def _create_test_decision_matrix(self):
        """创建测试用决策矩阵"""
        # 创建评估方案
        scheme = EvaluationScheme(
            name="测试评估方案",
            description="用于测试的评估方案"
        )
        
        # 添加指标
        indicators = [
            IndicatorDefinition(
                name="效率",
                indicator_type=IndicatorType.EFFICIENCY,
                is_benefit=True
            ),
            IndicatorDefinition(
                name="成本",
                indicator_type=IndicatorType.COST,
                is_benefit=False
            ),
            IndicatorDefinition(
                name="可靠性",
                indicator_type=IndicatorType.RELIABILITY,
                is_benefit=True
            )
        ]
        
        for i, indicator in enumerate(indicators):
            scheme.add_indicator(indicator, weight=1.0/len(indicators))
        
        scheme.normalize_weights()
        
        # 创建决策矩阵
        matrix = DecisionMatrix(
            name="测试决策矩阵",
            evaluation_scheme=scheme
        )
        
        # 创建备选方案
        alternatives_data = [
            {"name": "方案A", "values": [0.8, 0.6, 0.7]},
            {"name": "方案B", "values": [0.6, 0.8, 0.9]},
            {"name": "方案C", "values": [0.9, 0.4, 0.6]}
        ]
        
        for alt_data in alternatives_data:
            alternative = Alternative(
                name=alt_data["name"],
                description=f"测试{alt_data['name']}"
            )
            
            # 为每个指标添加模糊值
            for i, value in enumerate(alt_data["values"]):
                indicator_id = indicators[i].id
                fuzzy_value = FuzzyNumber(parameters=[
                    max(0.0, value - 0.1),
                    value,
                    min(1.0, value + 0.1)
                ])
                alternative.add_fuzzy_value(indicator_id, fuzzy_value)
            
            matrix.add_alternative(alternative)
        
        return matrix
    
    def test_weighted_relative_deviation_method(self):
        """测试加权相对偏差距离最小法"""
        result = self.engine.weighted_relative_deviation_method(self.decision_matrix)
        
        assert isinstance(result, DecisionResult)
        assert result.method == DecisionMethod.WEIGHTED_RELATIVE_DEVIATION
        assert len(result.rankings) == len(self.decision_matrix.alternatives)
        assert result.recommended_alternative_id in self.decision_matrix.alternatives
        
        # 验证排序结果
        scores = [score for _, score in result.rankings]
        assert scores == sorted(scores, reverse=True)  # 应该按得分降序排列
        
        # 验证得分范围
        assert all(0 <= score <= 1 for score in scores)
        
        # 验证距离信息
        assert len(result.distances) == len(self.decision_matrix.alternatives)
        for alt_id, distance_info in result.distances.items():
            assert "positive" in distance_info
            assert "negative" in distance_info
            assert distance_info["positive"] >= 0
            assert distance_info["negative"] >= 0
    
    def test_topsis_method(self):
        """测试TOPSIS方法"""
        result = self.engine.topsis_method(self.decision_matrix)
        
        assert isinstance(result, DecisionResult)
        assert result.method == DecisionMethod.TOPSIS
        assert len(result.rankings) == len(self.decision_matrix.alternatives)
        
        # 验证TOPSIS特有的计算结果
        scores = [score for _, score in result.rankings]
        assert all(0 <= score <= 1 for score in scores)
        
        # 验证距离计算
        for alt_id, distance_info in result.distances.items():
            pos_dist = distance_info["positive"]
            neg_dist = distance_info["negative"]
            expected_score = neg_dist / (pos_dist + neg_dist) if (pos_dist + neg_dist) > 0 else 0.5
            actual_score = result.scores[alt_id]
            assert abs(actual_score - expected_score) < 0.001
    
    def test_fuzzy_ahp_method(self):
        """测试模糊层次分析法"""
        result = self.engine.fuzzy_ahp_method(self.decision_matrix)
        
        assert isinstance(result, DecisionResult)
        assert result.method == DecisionMethod.FUZZY_AHP
        assert len(result.rankings) == len(self.decision_matrix.alternatives)
        
        # 验证参数信息
        assert "fuzzy_weights" in result.parameters
        assert "crisp_weights" in result.parameters
        
        fuzzy_weights = result.parameters["fuzzy_weights"]
        crisp_weights = result.parameters["crisp_weights"]
        
        assert len(fuzzy_weights) == len(self.decision_matrix.indicator_ids)
        assert len(crisp_weights) == len(self.decision_matrix.indicator_ids)
        assert all(isinstance(weight, float) for weight in crisp_weights)
    
    def test_compare_methods(self):
        """测试方法比较"""
        results = self.engine.compare_methods(self.decision_matrix)
        
        assert isinstance(results, dict)
        assert len(results) > 0
        
        # 验证每个方法都有结果
        expected_methods = ["WRDM", "TOPSIS", "Fuzzy_AHP"]
        for method in expected_methods:
            if method in results:
                assert isinstance(results[method], DecisionResult)
                assert len(results[method].rankings) > 0
    
    def test_sensitivity_analysis(self):
        """测试敏感性分析"""
        # 先执行一次决策
        original_result = self.engine.weighted_relative_deviation_method(self.decision_matrix)
        
        # 执行敏感性分析
        sensitivity_results = self.engine.sensitivity_analysis(self.decision_matrix, original_result)
        
        assert isinstance(sensitivity_results, dict)
        assert len(sensitivity_results) > 0
        
        # 验证每个指标都有敏感性分析结果
        for indicator_id in self.decision_matrix.indicator_ids:
            if indicator_id in sensitivity_results:
                sensitivity_data = sensitivity_results[indicator_id]
                
                assert "sensitivity_coefficient" in sensitivity_data
                assert "weight_variations" in sensitivity_data
                assert "score_variations" in sensitivity_data
                assert "score_range" in sensitivity_data
                
                assert isinstance(sensitivity_data["sensitivity_coefficient"], float)
                assert len(sensitivity_data["weight_variations"]) == len(sensitivity_data["score_variations"])


class TestFuzzyNumber:
    """模糊数测试"""
    
    def test_triangular_fuzzy_number(self):
        """测试三角模糊数"""
        fuzzy_num = FuzzyNumber(parameters=[0.2, 0.5, 0.8])
        
        assert fuzzy_num.center == 0.5
        
        defuzzified = fuzzy_num.defuzzify()
        assert 0.2 <= defuzzified <= 0.8
        assert abs(defuzzified - 0.5) < 0.2  # 应该接近中心值
    
    def test_interval_fuzzy_number(self):
        """测试区间模糊数"""
        from hmdm.models.decision_models import FuzzySetType
        
        fuzzy_num = FuzzyNumber(
            fuzzy_type=FuzzySetType.INTERVAL,
            parameters=[0.3, 0.7]
        )
        
        assert fuzzy_num.center == 0.5
        
        defuzzified = fuzzy_num.defuzzify()
        assert 0.3 <= defuzzified <= 0.7
    
    def test_fuzzy_number_to_dict(self):
        """测试模糊数序列化"""
        fuzzy_num = FuzzyNumber(parameters=[0.1, 0.5, 0.9])
        
        data = fuzzy_num.to_dict()
        
        assert "fuzzy_type" in data
        assert "parameters" in data
        assert "center" in data
        assert "defuzzified" in data
        
        assert data["parameters"] == [0.1, 0.5, 0.9]
        assert data["center"] == 0.5


class TestDecisionMatrix:
    """决策矩阵测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.matrix = self._create_simple_matrix()
    
    def _create_simple_matrix(self):
        """创建简单的测试矩阵"""
        scheme = EvaluationScheme(name="简单方案")
        
        # 添加两个指标
        indicator1 = IndicatorDefinition(name="指标1", is_benefit=True)
        indicator2 = IndicatorDefinition(name="指标2", is_benefit=False)
        
        scheme.add_indicator(indicator1, 0.6)
        scheme.add_indicator(indicator2, 0.4)
        
        matrix = DecisionMatrix(evaluation_scheme=scheme)
        
        # 添加两个备选方案
        alt1 = Alternative(name="方案1")
        alt1.add_fuzzy_value(indicator1.id, FuzzyNumber(parameters=[0.7, 0.8, 0.9]))
        alt1.add_fuzzy_value(indicator2.id, FuzzyNumber(parameters=[0.2, 0.3, 0.4]))
        
        alt2 = Alternative(name="方案2")
        alt2.add_fuzzy_value(indicator1.id, FuzzyNumber(parameters=[0.5, 0.6, 0.7]))
        alt2.add_fuzzy_value(indicator2.id, FuzzyNumber(parameters=[0.1, 0.2, 0.3]))
        
        matrix.add_alternative(alt1)
        matrix.add_alternative(alt2)
        
        return matrix
    
    def test_build_matrix(self):
        """测试构建决策矩阵"""
        decision_matrix = self.matrix.build_matrix()
        
        assert isinstance(decision_matrix, np.ndarray)
        assert decision_matrix.shape == (2, 2)  # 2个方案，2个指标
        
        # 验证矩阵值
        assert np.all(decision_matrix >= 0)
        assert np.all(decision_matrix <= 1)
    
    def test_normalize_matrix(self):
        """测试矩阵标准化"""
        # 先构建原始矩阵
        self.matrix.build_matrix()
        
        # 测试向量标准化
        normalized = self.matrix.normalize_matrix(method="vector")
        assert isinstance(normalized, np.ndarray)
        assert normalized.shape == self.matrix.matrix.shape
        
        # 测试线性标准化
        normalized_linear = self.matrix.normalize_matrix(method="linear")
        assert isinstance(normalized_linear, np.ndarray)
        
        # 验证标准化后的值范围
        assert np.all(normalized_linear >= 0)
        assert np.all(normalized_linear <= 1)
    
    def test_apply_weights(self):
        """测试应用权重"""
        self.matrix.build_matrix()
        self.matrix.normalize_matrix()
        
        weighted_matrix = self.matrix.apply_weights()
        
        assert isinstance(weighted_matrix, np.ndarray)
        assert weighted_matrix.shape == self.matrix.normalized_matrix.shape
        
        # 验证权重应用
        weights = np.array([0.6, 0.4])  # 从评估方案中获取的权重
        expected_matrix = self.matrix.normalized_matrix * weights
        
        np.testing.assert_array_almost_equal(weighted_matrix, expected_matrix)
    
    def test_calculate_ideal_solutions(self):
        """测试理想解计算"""
        self.matrix.build_matrix()
        self.matrix.normalize_matrix()
        self.matrix.apply_weights()
        
        positive_ideal, negative_ideal = self.matrix.calculate_ideal_solutions()
        
        assert isinstance(positive_ideal, np.ndarray)
        assert isinstance(negative_ideal, np.ndarray)
        assert len(positive_ideal) == len(self.matrix.indicator_ids)
        assert len(negative_ideal) == len(self.matrix.indicator_ids)
        
        # 对于效益型指标，正理想解应该大于等于负理想解
        # 对于成本型指标，正理想解应该小于等于负理想解
        for i, indicator_id in enumerate(self.matrix.indicator_ids):
            indicator = self.matrix.evaluation_scheme.indicators[indicator_id]
            if indicator.is_benefit:
                assert positive_ideal[i] >= negative_ideal[i]
            else:
                assert positive_ideal[i] <= negative_ideal[i]
    
    def test_to_dict(self):
        """测试序列化"""
        self.matrix.build_matrix()
        
        data = self.matrix.to_dict()
        
        assert "id" in data
        assert "name" in data
        assert "alternatives" in data
        assert "matrix" in data
        assert "indicator_ids" in data
        assert "alternative_ids" in data
        
        assert len(data["alternatives"]) == 2
        assert len(data["matrix"]) == 2
        assert len(data["matrix"][0]) == 2


if __name__ == "__main__":
    pytest.main([__file__])
