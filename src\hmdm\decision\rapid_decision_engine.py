"""
快速决策引擎

针对军事应用的实时性要求，实现快速决策算法，
支持毫秒级响应时间的人机功能分配决策。
"""

from typing import List, Dict, Optional, Any, Tuple
import numpy as np
import time
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from ..models.decision_models import (
    DecisionMatrix, DecisionResult, DecisionMethod, Alternative, FuzzyNumber
)
from ..models.evaluation_models import EvaluationScheme


class RapidDecisionEngine:
    """快速决策引擎"""
    
    def __init__(self, max_workers: int = 4):
        self.logger = logging.getLogger(__name__)
        self.max_workers = max_workers
        self.decision_cache = {}  # 决策缓存
        self.template_cache = {}  # 模板缓存
    
    def rapid_wrdm_decision(self, matrix: DecisionMatrix, time_limit: float = 0.1) -> DecisionResult:
        """
        快速WRDM决策算法
        
        Args:
            matrix: 决策矩阵
            time_limit: 时间限制（秒），默认100ms
            
        Returns:
            决策结果
        """
        start_time = time.time()
        
        if not matrix.alternatives or not matrix.evaluation_scheme:
            raise ValueError("决策矩阵或评估方案为空")
        
        # 检查缓存
        cache_key = self._generate_cache_key(matrix)
        if cache_key in self.decision_cache:
            cached_result = self.decision_cache[cache_key]
            cached_result.calculation_time = time.time() - start_time
            return cached_result
        
        try:
            # 快速构建决策矩阵
            decision_matrix = self._rapid_build_matrix(matrix)
            
            # 检查时间限制
            if time.time() - start_time > time_limit * 0.5:
                return self._emergency_decision(matrix, start_time)
            
            # 快速标准化
            normalized_matrix = self._rapid_normalize(decision_matrix)
            
            # 检查时间限制
            if time.time() - start_time > time_limit * 0.8:
                return self._emergency_decision(matrix, start_time)
            
            # 快速计算权重
            weights = self._rapid_calculate_weights(matrix.evaluation_scheme)
            
            # 快速计算得分
            scores = self._rapid_calculate_scores(normalized_matrix, weights)
            
            # 生成结果
            result = self._generate_rapid_result(matrix, scores, start_time)
            
            # 缓存结果
            if len(self.decision_cache) < 100:  # 限制缓存大小
                self.decision_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.warning(f"快速决策失败，使用应急决策: {e}")
            return self._emergency_decision(matrix, start_time)
    
    def parallel_rapid_decision(self, 
                              matrices: List[DecisionMatrix], 
                              time_limit: float = 0.5) -> List[DecisionResult]:
        """
        并行快速决策
        
        Args:
            matrices: 决策矩阵列表
            time_limit: 总时间限制（秒）
            
        Returns:
            决策结果列表
        """
        start_time = time.time()
        results = []
        
        # 计算每个决策的时间限制
        per_decision_limit = min(time_limit / len(matrices), 0.1)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_matrix = {
                executor.submit(self.rapid_wrdm_decision, matrix, per_decision_limit): matrix
                for matrix in matrices
            }
            
            # 收集结果
            for future in as_completed(future_to_matrix, timeout=time_limit):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    matrix = future_to_matrix[future]
                    self.logger.error(f"并行决策失败: {e}")
                    # 使用应急决策
                    emergency_result = self._emergency_decision(matrix, start_time)
                    results.append(emergency_result)
        
        return results
    
    def precompute_decision_templates(self, 
                                    common_scenarios: List[str],
                                    common_alternatives: List[Alternative]) -> None:
        """预计算常用场景的决策模板"""
        self.logger.info("开始预计算决策模板...")
        
        for scenario in common_scenarios:
            for alt in common_alternatives:
                # 创建模板矩阵
                template_matrix = self._create_template_matrix(scenario, [alt])
                
                # 预计算结果
                template_result = self.rapid_wrdm_decision(template_matrix)
                
                # 存储模板
                template_key = f"{scenario}_{alt.id}"
                self.template_cache[template_key] = {
                    "matrix": template_matrix,
                    "result": template_result,
                    "timestamp": time.time()
                }
        
        self.logger.info(f"预计算完成，共生成{len(self.template_cache)}个模板")
    
    def _rapid_build_matrix(self, matrix: DecisionMatrix) -> np.ndarray:
        """快速构建决策矩阵"""
        n_alternatives = len(matrix.alternatives)
        n_indicators = len(matrix.evaluation_scheme.indicators)
        
        decision_matrix = np.zeros((n_alternatives, n_indicators))
        
        for i, alternative in enumerate(matrix.alternatives):
            for j, (indicator_id, _) in enumerate(matrix.evaluation_scheme.indicators.items()):
                # 快速获取指标值
                value = alternative.attributes.get(indicator_id, 0.5)  # 默认值0.5
                decision_matrix[i, j] = float(value)
        
        return decision_matrix
    
    def _rapid_normalize(self, matrix: np.ndarray) -> np.ndarray:
        """快速标准化矩阵"""
        # 使用简单的最大值标准化
        max_values = np.max(matrix, axis=0)
        max_values[max_values == 0] = 1  # 避免除零
        return matrix / max_values
    
    def _rapid_calculate_weights(self, scheme: EvaluationScheme) -> np.ndarray:
        """快速计算权重"""
        weights = []
        for indicator_id, indicator_def in scheme.indicators.items():
            # 如果是IndicatorDefinition对象，尝试从military_attributes获取权重
            if hasattr(indicator_def, 'military_attributes') and isinstance(indicator_def.military_attributes, dict):
                weight = indicator_def.military_attributes.get('weight', 1.0)
            elif isinstance(indicator_def, (int, float)):
                weight = float(indicator_def)
            else:
                weight = 1.0  # 默认权重
            weights.append(weight)

        weights = np.array(weights, dtype=float)
        # 归一化权重
        weight_sum = np.sum(weights)
        if weight_sum > 0:
            weights = weights / weight_sum
        else:
            weights = np.ones(len(weights)) / len(weights)

        return weights
    
    def _rapid_calculate_scores(self, matrix: np.ndarray, weights: np.ndarray) -> np.ndarray:
        """快速计算得分"""
        # 简单的加权求和
        scores = np.dot(matrix, weights)
        return scores
    
    def _generate_rapid_result(self, 
                             matrix: DecisionMatrix, 
                             scores: np.ndarray, 
                             start_time: float) -> DecisionResult:
        """生成快速决策结果"""
        # 找到最佳方案
        best_index = np.argmax(scores)
        best_alternative = matrix.alternatives[best_index]
        
        # 创建决策结果
        result = DecisionResult(
            method=DecisionMethod.RAPID_WRDM,
            recommended_alternative_id=best_alternative.id,
            scores={alt.id: float(scores[i]) for i, alt in enumerate(matrix.alternatives)},
            parameters={
                "algorithm": "快速WRDM",
                "optimization": "实时性优化",
                "accuracy_level": "快速",
                "cache_used": False,
                "calculation_time": time.time() - start_time
            }
        )

        # 设置排序结果
        rankings = [(matrix.alternatives[i].id, float(scores[i])) for i in range(len(scores))]
        result.set_rankings(rankings)
        
        return result
    
    def _emergency_decision(self, matrix: DecisionMatrix, start_time: float) -> DecisionResult:
        """应急决策算法"""
        if not matrix.alternatives:
            raise ValueError("无可用备选方案")
        
        # 简单选择第一个方案作为应急决策
        best_alternative = matrix.alternatives[0]
        
        # 如果有多个方案，选择属性值总和最大的
        if len(matrix.alternatives) > 1:
            max_sum = -1
            for alt in matrix.alternatives:
                attr_sum = sum(float(v) for v in alt.attributes.values() if isinstance(v, (int, float)))
                if attr_sum > max_sum:
                    max_sum = attr_sum
                    best_alternative = alt
        
        result = DecisionResult(
            method=DecisionMethod.EMERGENCY,
            recommended_alternative_id=best_alternative.id,
            scores={alt.id: 0.5 for alt in matrix.alternatives},
            parameters={
                "algorithm": "应急决策",
                "reason": "时间限制或计算异常",
                "accuracy_level": "应急",
                "warning": "结果可能不够准确",
                "calculation_time": time.time() - start_time
            }
        )

        # 设置排序结果
        rankings = [(alt.id, 0.5) for alt in matrix.alternatives]
        result.set_rankings(rankings)
        
        return result
    
    def _generate_cache_key(self, matrix: DecisionMatrix) -> str:
        """生成缓存键"""
        # 简化的缓存键生成
        alt_ids = sorted([alt.id for alt in matrix.alternatives])
        scheme_id = matrix.evaluation_scheme.id if matrix.evaluation_scheme else "default"
        return f"{scheme_id}_{hash(tuple(alt_ids))}"
    
    def _create_template_matrix(self, scenario: str, alternatives: List[Alternative]) -> DecisionMatrix:
        """创建模板矩阵"""
        # 这里应该根据场景创建相应的评估方案
        # 简化实现，使用默认方案
        from ..evaluation.scheme_evaluator import SchemeEvaluator
        evaluator = SchemeEvaluator()
        scheme = evaluator.create_default_evaluation_scheme()
        
        return DecisionMatrix(
            alternatives=alternatives,
            evaluation_scheme=scheme
        )
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.decision_cache.clear()
        self.template_cache.clear()
        self.logger.info("决策缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "decision_cache_size": len(self.decision_cache),
            "template_cache_size": len(self.template_cache),
            "max_workers": self.max_workers
        }
