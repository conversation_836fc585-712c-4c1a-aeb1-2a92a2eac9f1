"""
多目标模糊决策数据模型

实现加权相对偏差距离最小法的多目标模糊决策模型
"""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import numpy as np
from .evaluation_models import EvaluationResult, EvaluationScheme


class DecisionMethod(Enum):
    """决策方法枚举"""
    WEIGHTED_RELATIVE_DEVIATION = "加权相对偏差距离最小法"
    TOPSIS = "TOPSIS法"
    ELECTRE = "ELECTRE法"
    PROMETHEE = "PROMETHEE法"
    AHP = "层次分析法"
    FUZZY_AHP = "模糊层次分析法"
    RAPID_WRDM = "快速WRDM法"
    EMERGENCY = "应急决策法"
    MILITARY_OPTIMIZED = "军事优化决策法"


class FuzzySetType(Enum):
    """模糊集类型"""
    TRIANGULAR = "三角模糊数"
    TRAPEZOIDAL = "梯形模糊数"
    GAUSSIAN = "高斯模糊数"
    INTERVAL = "区间模糊数"


@dataclass
class FuzzyNumber:
    """模糊数"""
    fuzzy_type: FuzzySetType = FuzzySetType.TRIANGULAR
    parameters: List[float] = field(default_factory=list)  # 模糊数参数
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.parameters:
            if self.fuzzy_type == FuzzySetType.TRIANGULAR:
                self.parameters = [0.0, 0.5, 1.0]  # [a, b, c]
            elif self.fuzzy_type == FuzzySetType.TRAPEZOIDAL:
                self.parameters = [0.0, 0.25, 0.75, 1.0]  # [a, b, c, d]
            elif self.fuzzy_type == FuzzySetType.INTERVAL:
                self.parameters = [0.0, 1.0]  # [min, max]
    
    @property
    def center(self) -> float:
        """模糊数中心值"""
        if self.fuzzy_type == FuzzySetType.TRIANGULAR:
            return self.parameters[1]
        elif self.fuzzy_type == FuzzySetType.TRAPEZOIDAL:
            return (self.parameters[1] + self.parameters[2]) / 2
        elif self.fuzzy_type == FuzzySetType.INTERVAL:
            return (self.parameters[0] + self.parameters[1]) / 2
        return 0.0
    
    def defuzzify(self) -> float:
        """去模糊化（重心法）"""
        if self.fuzzy_type == FuzzySetType.TRIANGULAR:
            a, b, c = self.parameters
            return (a + b + c) / 3
        elif self.fuzzy_type == FuzzySetType.TRAPEZOIDAL:
            a, b, c, d = self.parameters
            return (a + b + c + d) / 4
        elif self.fuzzy_type == FuzzySetType.INTERVAL:
            return (self.parameters[0] + self.parameters[1]) / 2
        return self.center
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "fuzzy_type": self.fuzzy_type.value,
            "parameters": self.parameters,
            "center": self.center,
            "defuzzified": self.defuzzify()
        }


@dataclass
class Alternative:
    """决策备选方案"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 方案属性
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    # 评估结果
    evaluation_results: Dict[str, EvaluationResult] = field(default_factory=dict)
    
    # 模糊评价值
    fuzzy_values: Dict[str, FuzzyNumber] = field(default_factory=dict)  # indicator_id -> fuzzy_value
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_fuzzy_value(self, indicator_id: str, fuzzy_value: FuzzyNumber) -> None:
        """添加模糊评价值"""
        self.fuzzy_values[indicator_id] = fuzzy_value
    
    def get_crisp_values(self) -> Dict[str, float]:
        """获取去模糊化后的精确值"""
        return {indicator_id: fuzzy_val.defuzzify() 
                for indicator_id, fuzzy_val in self.fuzzy_values.items()}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "attributes": self.attributes,
            "evaluation_results": {k: v.to_dict() for k, v in self.evaluation_results.items()},
            "fuzzy_values": {k: v.to_dict() for k, v in self.fuzzy_values.items()},
            "created_at": self.created_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata
        }


@dataclass
class DecisionMatrix:
    """决策矩阵"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 备选方案
    alternatives: Dict[str, Alternative] = field(default_factory=dict)
    
    # 评估方案
    evaluation_scheme: Optional[EvaluationScheme] = None
    
    # 决策矩阵数据
    matrix: np.ndarray = field(default_factory=lambda: np.array([]))  # m×n矩阵 (方案×指标)
    normalized_matrix: np.ndarray = field(default_factory=lambda: np.array([]))  # 标准化矩阵
    weighted_matrix: np.ndarray = field(default_factory=lambda: np.array([]))  # 加权矩阵
    
    # 指标信息
    indicator_ids: List[str] = field(default_factory=list)
    alternative_ids: List[str] = field(default_factory=list)
    
    # 理想解和负理想解
    positive_ideal_solution: np.ndarray = field(default_factory=lambda: np.array([]))
    negative_ideal_solution: np.ndarray = field(default_factory=lambda: np.array([]))
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def add_alternative(self, alternative: Alternative) -> None:
        """添加备选方案"""
        self.alternatives[alternative.id] = alternative
        self.updated_at = datetime.now()
    
    def build_matrix(self) -> np.ndarray:
        """构建决策矩阵"""
        if not self.alternatives or not self.evaluation_scheme:
            return np.array([])
        
        # 获取指标ID列表
        self.indicator_ids = list(self.evaluation_scheme.indicators.keys())
        self.alternative_ids = list(self.alternatives.keys())
        
        # 构建矩阵
        m = len(self.alternative_ids)  # 方案数
        n = len(self.indicator_ids)    # 指标数
        
        matrix = np.zeros((m, n))
        
        for i, alt_id in enumerate(self.alternative_ids):
            alternative = self.alternatives[alt_id]
            for j, indicator_id in enumerate(self.indicator_ids):
                if indicator_id in alternative.fuzzy_values:
                    # 使用去模糊化值
                    matrix[i, j] = alternative.fuzzy_values[indicator_id].defuzzify()
                else:
                    # 使用默认值0
                    matrix[i, j] = 0.0
        
        self.matrix = matrix
        self.updated_at = datetime.now()
        return matrix
    
    def normalize_matrix(self, method: str = "vector") -> np.ndarray:
        """矩阵标准化"""
        if self.matrix.size == 0:
            self.build_matrix()
        
        if method == "vector":
            # 向量标准化
            norms = np.linalg.norm(self.matrix, axis=0)
            norms[norms == 0] = 1  # 避免除零
            self.normalized_matrix = self.matrix / norms
        
        elif method == "linear":
            # 线性标准化
            max_vals = np.max(self.matrix, axis=0)
            min_vals = np.min(self.matrix, axis=0)
            ranges = max_vals - min_vals
            ranges[ranges == 0] = 1  # 避免除零
            self.normalized_matrix = (self.matrix - min_vals) / ranges
        
        elif method == "z_score":
            # Z-score标准化
            means = np.mean(self.matrix, axis=0)
            stds = np.std(self.matrix, axis=0)
            stds[stds == 0] = 1  # 避免除零
            self.normalized_matrix = (self.matrix - means) / stds
        
        self.updated_at = datetime.now()
        return self.normalized_matrix
    
    def apply_weights(self) -> np.ndarray:
        """应用权重"""
        if self.normalized_matrix.size == 0:
            self.normalize_matrix()
        
        if not self.evaluation_scheme:
            return self.normalized_matrix
        
        # 获取权重向量
        weights = np.zeros(len(self.indicator_ids))
        for i, indicator_id in enumerate(self.indicator_ids):
            if indicator_id in self.evaluation_scheme.weights:
                weights[i] = self.evaluation_scheme.weights[indicator_id].weight
        
        # 应用权重
        self.weighted_matrix = self.normalized_matrix * weights
        self.updated_at = datetime.now()
        return self.weighted_matrix
    
    def calculate_ideal_solutions(self) -> Tuple[np.ndarray, np.ndarray]:
        """计算理想解和负理想解"""
        if self.weighted_matrix.size == 0:
            self.apply_weights()
        
        if not self.evaluation_scheme:
            return np.array([]), np.array([])
        
        positive_ideal = np.zeros(len(self.indicator_ids))
        negative_ideal = np.zeros(len(self.indicator_ids))
        
        for i, indicator_id in enumerate(self.indicator_ids):
            indicator = self.evaluation_scheme.indicators[indicator_id]
            col_values = self.weighted_matrix[:, i]
            
            if indicator.is_benefit:  # 效益型指标
                positive_ideal[i] = np.max(col_values)
                negative_ideal[i] = np.min(col_values)
            else:  # 成本型指标
                positive_ideal[i] = np.min(col_values)
                negative_ideal[i] = np.max(col_values)
        
        self.positive_ideal_solution = positive_ideal
        self.negative_ideal_solution = negative_ideal
        self.updated_at = datetime.now()
        
        return positive_ideal, negative_ideal
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "alternatives": {k: v.to_dict() for k, v in self.alternatives.items()},
            "evaluation_scheme_id": self.evaluation_scheme.id if self.evaluation_scheme else None,
            "matrix": self.matrix.tolist() if self.matrix.size > 0 else [],
            "normalized_matrix": self.normalized_matrix.tolist() if self.normalized_matrix.size > 0 else [],
            "weighted_matrix": self.weighted_matrix.tolist() if self.weighted_matrix.size > 0 else [],
            "indicator_ids": self.indicator_ids,
            "alternative_ids": self.alternative_ids,
            "positive_ideal_solution": self.positive_ideal_solution.tolist() if self.positive_ideal_solution.size > 0 else [],
            "negative_ideal_solution": self.negative_ideal_solution.tolist() if self.negative_ideal_solution.size > 0 else [],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class DecisionResult:
    """决策结果"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    matrix_id: str = ""
    method: DecisionMethod = DecisionMethod.WEIGHTED_RELATIVE_DEVIATION
    
    # 方案排序结果
    rankings: List[Tuple[str, float]] = field(default_factory=list)  # [(alternative_id, score), ...]
    
    # 详细计算结果
    scores: Dict[str, float] = field(default_factory=dict)  # alternative_id -> score
    distances: Dict[str, Dict[str, float]] = field(default_factory=dict)  # alternative_id -> {"positive": d+, "negative": d-}
    
    # 推荐方案
    recommended_alternative_id: str = ""
    confidence: float = 1.0
    
    # 敏感性分析结果
    sensitivity_analysis: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    calculation_time: datetime = field(default_factory=datetime.now)
    parameters: Dict[str, Any] = field(default_factory=dict)
    notes: str = ""
    
    def set_rankings(self, rankings: List[Tuple[str, float]]) -> None:
        """设置排序结果"""
        self.rankings = sorted(rankings, key=lambda x: x[1], reverse=True)
        if self.rankings:
            self.recommended_alternative_id = self.rankings[0][0]
        
        # 更新得分字典
        self.scores = {alt_id: score for alt_id, score in self.rankings}
    
    def get_top_n(self, n: int = 3) -> List[Tuple[str, float]]:
        """获取前N个方案"""
        return self.rankings[:n]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "matrix_id": self.matrix_id,
            "method": self.method.value,
            "rankings": self.rankings,
            "scores": self.scores,
            "distances": self.distances,
            "recommended_alternative_id": self.recommended_alternative_id,
            "confidence": self.confidence,
            "sensitivity_analysis": self.sensitivity_analysis,
            "calculation_time": self.calculation_time.isoformat(),
            "parameters": self.parameters,
            "notes": self.notes
        }
