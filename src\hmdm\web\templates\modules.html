{% extends "base.html" %}

{% block title %}模块管理 - HMDM{% endblock %}

{% block page_title %}模块管理{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-primary" id="refresh-modules-btn">
        <i class="fas fa-sync-alt"></i> 刷新状态
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 模块统计概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">运行中</h6>
                        <h4 class="mb-0" id="running-count">
                            {% set running = modules.values() | selectattr('status', 'equalto', '运行中') | list %}
                            {{ running | length }}
                        </h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-secondary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">已停止</h6>
                        <h4 class="mb-0" id="stopped-count">
                            {% set stopped = modules.values() | rejectattr('status', 'equalto', '运行中') | rejectattr('status', 'equalto', '错误') | list %}
                            {{ stopped | length }}
                        </h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">错误状态</h6>
                        <h4 class="mb-0" id="error-count">
                            {% set errors = modules.values() | selectattr('status', 'equalto', '错误') | list %}
                            {{ errors | length }}
                        </h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总模块数</h6>
                        <h4 class="mb-0">{{ modules | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cubes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块详细信息 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 模块详细信息
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="modules-table">
                        <thead>
                            <tr>
                                <th>模块名称</th>
                                <th>状态</th>
                                <th>启动时间</th>
                                <th>最后错误</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for module_name, module_info in modules.items() %}
                            <tr data-module="{{ module_name }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            {% if module_info.status == '运行中' %}
                                                <i class="fas fa-check-circle text-success fa-lg"></i>
                                            {% elif module_info.status == '错误' %}
                                                <i class="fas fa-exclamation-circle text-danger fa-lg"></i>
                                            {% else %}
                                                <i class="fas fa-pause-circle text-secondary fa-lg"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <strong>{{ module_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ module_descriptions.get(module_name, '未知模块') }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if module_info.status == '运行中' %}bg-success
                                        {% elif module_info.status == '错误' %}bg-danger
                                        {% else %}bg-secondary{% endif %} module-status-badge">
                                        {{ module_info.status }}
                                    </span>
                                </td>
                                <td>
                                    {% if module_info.start_time %}
                                        <span class="module-start-time">{{ module_info.start_time[:19] }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if module_info.last_error %}
                                        <span class="text-danger" title="{{ module_info.last_error }}">
                                            {{ module_info.last_error[:50] }}{% if module_info.last_error|length > 50 %}...{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary restart-module-btn" 
                                                data-module="{{ module_name }}"
                                                {% if module_info.status != '运行中' %}disabled{% endif %}>
                                            <i class="fas fa-redo"></i> 重启
                                        </button>
                                        <button type="button" class="btn btn-outline-info view-module-btn" 
                                                data-module="{{ module_name }}">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块详情模态框 -->
<div class="modal fade" id="moduleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cube"></i> 模块详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>模块名称:</strong></td>
                                <td id="modal-module-name">-</td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td id="modal-module-status">-</td>
                            </tr>
                            <tr>
                                <td><strong>启动时间:</strong></td>
                                <td id="modal-module-start-time">-</td>
                            </tr>
                            <tr>
                                <td><strong>最后错误:</strong></td>
                                <td id="modal-module-error">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>运行数据</h6>
                        <div id="modal-module-data">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>实时数据图表</h6>
                        <canvas id="module-data-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="modal-restart-btn">
                    <i class="fas fa-redo"></i> 重启模块
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentModule = null;
    let moduleDataChart = null;
    
    // 模块描述映射
    const moduleDescriptions = {
        'situation_awareness': '态势感知与预测系统',
        'communication': '军事通信与协同系统',
        'decision_support': '军事决策支持系统',
        'training': '军事训练与演练系统',
        'knowledge_base': '军事知识库与专家系统',
        'simulation': '军事仿真与建模系统',
        'scenarios': '辅助决策场景模板系统'
    };
    
    // 获取模块描述
    function getModuleDescription(moduleName) {
        return moduleDescriptions[moduleName] || '未知模块';
    }
    
    // 重启模块
    function restartModule(moduleName) {
        if (!confirm(`确定要重启模块 "${moduleName}" 吗？`)) {
            return;
        }
        
        makeRequest(`/api/module/${moduleName}/restart`, 'POST')
            .then(response => {
                if (response.success) {
                    showNotification(response.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showNotification(response.error || '重启失败', 'danger');
                }
            });
    }
    
    // 查看模块详情
    function viewModuleDetail(moduleName) {
        currentModule = moduleName;
        
        // 更新模态框基本信息
        document.getElementById('modal-module-name').textContent = moduleName;
        
        // 获取模块状态
        makeRequest('/api/system/status')
            .then(response => {
                if (response.success && response.data.modules[moduleName]) {
                    const moduleInfo = response.data.modules[moduleName];
                    document.getElementById('modal-module-status').innerHTML = 
                        `<span class="badge ${getStatusBadgeClass(moduleInfo.status)}">${moduleInfo.status}</span>`;
                    document.getElementById('modal-module-start-time').textContent = 
                        moduleInfo.start_time ? moduleInfo.start_time.substring(0, 19) : '-';
                    document.getElementById('modal-module-error').textContent = 
                        moduleInfo.last_error || '无';
                }
            });
        
        // 获取模块数据
        loadModuleData(moduleName);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('moduleDetailModal'));
        modal.show();
    }
    
    // 加载模块数据
    function loadModuleData(moduleName) {
        makeRequest(`/api/modules/${moduleName}/data`)
            .then(response => {
                if (response.success) {
                    displayModuleData(response.data);
                } else {
                    document.getElementById('modal-module-data').innerHTML = 
                        `<div class="text-danger">加载失败: ${response.error}</div>`;
                }
            });
    }
    
    // 显示模块数据
    function displayModuleData(data) {
        const container = document.getElementById('modal-module-data');
        let html = '<table class="table table-sm">';
        
        for (const [key, value] of Object.entries(data)) {
            html += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
        }
        
        html += '</table>';
        container.innerHTML = html;
        
        // 更新图表
        updateModuleDataChart(data);
    }
    
    // 更新模块数据图表
    function updateModuleDataChart(data) {
        const ctx = document.getElementById('module-data-chart').getContext('2d');
        
        if (moduleDataChart) {
            moduleDataChart.destroy();
        }
        
        // 提取数值数据
        const numericData = {};
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'number') {
                numericData[key] = value;
            }
        }
        
        if (Object.keys(numericData).length > 0) {
            moduleDataChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(numericData),
                    datasets: [{
                        label: '数值',
                        data: Object.values(numericData),
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数值数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
        }
    }
    
    // 获取状态徽章样式类
    function getStatusBadgeClass(status) {
        switch (status) {
            case '运行中': return 'bg-success';
            case '错误': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }
    
    // 更新模块统计
    function updateModuleStats(modules) {
        let running = 0, stopped = 0, error = 0;
        
        Object.values(modules).forEach(module => {
            switch (module.status) {
                case '运行中': running++; break;
                case '错误': error++; break;
                default: stopped++; break;
            }
        });
        
        document.getElementById('running-count').textContent = running;
        document.getElementById('stopped-count').textContent = stopped;
        document.getElementById('error-count').textContent = error;
    }
    
    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 重启模块按钮
        document.querySelectorAll('.restart-module-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const moduleName = this.getAttribute('data-module');
                restartModule(moduleName);
            });
        });
        
        // 查看模块按钮
        document.querySelectorAll('.view-module-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const moduleName = this.getAttribute('data-module');
                viewModuleDetail(moduleName);
            });
        });
        
        // 刷新按钮
        document.getElementById('refresh-modules-btn').addEventListener('click', function() {
            location.reload();
        });
        
        // 模态框重启按钮
        document.getElementById('modal-restart-btn').addEventListener('click', function() {
            if (currentModule) {
                restartModule(currentModule);
                bootstrap.Modal.getInstance(document.getElementById('moduleDetailModal')).hide();
            }
        });
    });
    
    // Socket.IO事件监听
    socket.on('system_status_update', function(data) {
        updateModuleStats(data.data.modules || {});
    });
    
    socket.on('module_data_update', function(data) {
        if (currentModule === data.module) {
            displayModuleData(data.data);
        }
    });
</script>

{% set module_descriptions = {
    'situation_awareness': '态势感知与预测系统',
    'communication': '军事通信与协同系统',
    'decision_support': '军事决策支持系统',
    'training': '军事训练与演练系统',
    'knowledge_base': '军事知识库与专家系统',
    'simulation': '军事仿真与建模系统',
    'scenarios': '辅助决策场景模板系统'
} %}
{% endblock %}
