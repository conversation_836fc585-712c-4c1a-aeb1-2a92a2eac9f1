"""
辅助决策场景模板

提供决策问题分析、方案生成、决策支持等专业场景模板，
整合HMDM系统的各个模块为复杂决策问题提供全面支持。
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..decision.military_decision_support import (
    MilitaryDecisionSupport, MilitaryResource, MilitaryObjective, 
    DecisionType, OperationalPhase
)
from ..knowledge.military_knowledge_base import MilitaryKnowledgeBase, KnowledgeType
from ..analysis.situation_awareness import SituationAwarenessEngine, ThreatLevel
from ..training.military_training import MilitaryTrainingSystem, TrainingLevel
from ..simulation.military_simulation import MilitarySimulationEngine, SimulationType
from ..communication.military_comms import MilitaryCommunicationSystem
from ..models.decision_models import DecisionMethod, DecisionResult
from ..security.military_security import SecurityLevel


class DecisionComplexity(Enum):
    """决策复杂度"""
    SIMPLE = "简单决策"
    MODERATE = "中等复杂决策"
    COMPLEX = "复杂决策"
    CRITICAL = "关键决策"


class DecisionUrgency(Enum):
    """决策紧急程度"""
    ROUTINE = "常规"
    URGENT = "紧急"
    IMMEDIATE = "立即"
    CRITICAL = "危急"


@dataclass
class DecisionProblem:
    """决策问题定义"""
    id: str
    title: str
    description: str
    complexity: DecisionComplexity
    urgency: DecisionUrgency
    decision_type: DecisionType
    stakeholders: List[str] = field(default_factory=list)
    constraints: Dict[str, Any] = field(default_factory=dict)
    success_criteria: Dict[str, float] = field(default_factory=dict)
    deadline: Optional[datetime] = None
    security_level: SecurityLevel = SecurityLevel.SECRET
    
    def get_time_limit(self) -> float:
        """获取决策时间限制（小时）"""
        urgency_limits = {
            DecisionUrgency.ROUTINE: 24.0,
            DecisionUrgency.URGENT: 4.0,
            DecisionUrgency.IMMEDIATE: 1.0,
            DecisionUrgency.CRITICAL: 0.5
        }
        return urgency_limits.get(self.urgency, 24.0)


@dataclass
class DecisionScenario:
    """决策场景"""
    id: str
    name: str
    description: str
    problem: DecisionProblem
    context: Dict[str, Any] = field(default_factory=dict)
    available_resources: Dict[str, Any] = field(default_factory=dict)
    environmental_factors: Dict[str, Any] = field(default_factory=dict)
    historical_cases: List[str] = field(default_factory=list)


class DecisionSupportScenarios:
    """辅助决策场景模板系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 集成各个子系统
        self.decision_support = MilitaryDecisionSupport()
        self.knowledge_base = MilitaryKnowledgeBase()
        self.situation_engine = SituationAwarenessEngine()
        self.training_system = MilitaryTrainingSystem()
        self.simulation_engine = MilitarySimulationEngine()
        self.communication_system = MilitaryCommunicationSystem()
        
        # 场景模板库
        self.scenario_templates: Dict[str, DecisionScenario] = {}
        
        # 决策历史
        self.decision_history: List[Dict[str, Any]] = []
        
        # 初始化预定义场景
        self._initialize_scenario_templates()
    
    def create_decision_scenario(self, 
                               problem: DecisionProblem,
                               context: Dict[str, Any] = None) -> DecisionScenario:
        """创建决策场景"""
        try:
            scenario = DecisionScenario(
                id=f"scenario_{int(datetime.now().timestamp())}",
                name=f"{problem.title}决策场景",
                description=f"针对{problem.description}的综合决策支持场景",
                problem=problem,
                context=context or {}
            )
            
            # 根据问题类型和复杂度设置场景参数
            self._configure_scenario_parameters(scenario)
            
            # 收集相关资源信息
            self._gather_available_resources(scenario)
            
            # 分析环境因素
            self._analyze_environmental_factors(scenario)
            
            # 查找历史案例
            self._find_historical_cases(scenario)
            
            self.scenario_templates[scenario.id] = scenario
            self.logger.info(f"创建决策场景: {scenario.name}")
            
            return scenario
            
        except Exception as e:
            self.logger.error(f"创建决策场景失败: {e}")
            raise
    
    def analyze_decision_problem(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析决策问题"""
        try:
            analysis = {
                "problem_structure": self._analyze_problem_structure(scenario),
                "stakeholder_analysis": self._analyze_stakeholders(scenario),
                "constraint_analysis": self._analyze_constraints(scenario),
                "risk_assessment": self._assess_risks(scenario),
                "opportunity_analysis": self._analyze_opportunities(scenario),
                "resource_requirements": self._analyze_resource_requirements(scenario),
                "time_sensitivity": self._analyze_time_sensitivity(scenario)
            }
            
            self.logger.info(f"完成决策问题分析: {scenario.name}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"决策问题分析失败: {e}")
            return {"error": str(e)}
    
    def generate_decision_alternatives(self, 
                                     scenario: DecisionScenario,
                                     analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成决策备选方案"""
        try:
            alternatives = []
            
            # 基于知识库生成方案
            knowledge_alternatives = self._generate_knowledge_based_alternatives(scenario)
            alternatives.extend(knowledge_alternatives)
            
            # 基于历史案例生成方案
            historical_alternatives = self._generate_historical_alternatives(scenario)
            alternatives.extend(historical_alternatives)
            
            # 基于仿真生成方案
            simulation_alternatives = self._generate_simulation_alternatives(scenario)
            alternatives.extend(simulation_alternatives)
            
            # 基于专家系统生成方案
            expert_alternatives = self._generate_expert_alternatives(scenario, analysis)
            alternatives.extend(expert_alternatives)
            
            # 去重和优化
            optimized_alternatives = self._optimize_alternatives(alternatives, scenario)
            
            self.logger.info(f"生成 {len(optimized_alternatives)} 个决策备选方案")
            return optimized_alternatives
            
        except Exception as e:
            self.logger.error(f"生成决策方案失败: {e}")
            return []
    
    def provide_decision_support(self, 
                               scenario: DecisionScenario,
                               alternatives: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提供决策支持"""
        try:
            support = {
                "recommended_alternative": None,
                "evaluation_matrix": {},
                "risk_analysis": {},
                "implementation_plan": {},
                "monitoring_plan": {},
                "contingency_plans": [],
                "expert_advice": [],
                "confidence_assessment": {}
            }
            
            if not alternatives:
                support["message"] = "无可用决策方案"
                return support
            
            # 评估所有方案
            evaluation_results = self._evaluate_alternatives(alternatives, scenario)
            support["evaluation_matrix"] = evaluation_results
            
            # 选择最佳方案
            best_alternative = self._select_best_alternative(evaluation_results, scenario)
            support["recommended_alternative"] = best_alternative
            
            if best_alternative:
                # 风险分析
                support["risk_analysis"] = self._analyze_alternative_risks(best_alternative, scenario)
                
                # 实施计划
                support["implementation_plan"] = self._create_implementation_plan(best_alternative, scenario)
                
                # 监控计划
                support["monitoring_plan"] = self._create_monitoring_plan(best_alternative, scenario)
                
                # 应急预案
                support["contingency_plans"] = self._create_contingency_plans(best_alternative, scenario)
                
                # 专家建议
                support["expert_advice"] = self._get_expert_advice(best_alternative, scenario)
                
                # 置信度评估
                support["confidence_assessment"] = self._assess_confidence(best_alternative, scenario)
            
            self.logger.info(f"完成决策支持分析: {scenario.name}")
            return support
            
        except Exception as e:
            self.logger.error(f"决策支持失败: {e}")
            return {"error": str(e)}
    
    def execute_decision_workflow(self, problem: DecisionProblem) -> Dict[str, Any]:
        """执行完整的决策工作流程"""
        try:
            workflow_result = {
                "problem_id": problem.id,
                "start_time": datetime.now(),
                "scenario": None,
                "analysis": None,
                "alternatives": [],
                "support": None,
                "decision": None,
                "status": "in_progress"
            }
            
            # 1. 创建决策场景
            scenario = self.create_decision_scenario(problem)
            workflow_result["scenario"] = {
                "id": scenario.id,
                "name": scenario.name,
                "complexity": scenario.problem.complexity.value,
                "urgency": scenario.problem.urgency.value
            }
            
            # 2. 分析决策问题
            analysis = self.analyze_decision_problem(scenario)
            workflow_result["analysis"] = analysis
            
            # 3. 生成备选方案
            alternatives = self.generate_decision_alternatives(scenario, analysis)
            workflow_result["alternatives"] = alternatives
            
            # 4. 提供决策支持
            support = self.provide_decision_support(scenario, alternatives)
            workflow_result["support"] = support
            
            # 5. 记录决策结果
            decision_record = {
                "scenario_id": scenario.id,
                "problem_type": problem.decision_type.value,
                "complexity": problem.complexity.value,
                "urgency": problem.urgency.value,
                "alternatives_count": len(alternatives),
                "recommended_alternative": support.get("recommended_alternative"),
                "timestamp": datetime.now(),
                "execution_time": (datetime.now() - workflow_result["start_time"]).total_seconds()
            }
            
            workflow_result["decision"] = decision_record
            workflow_result["status"] = "completed"
            workflow_result["end_time"] = datetime.now()
            
            # 保存到历史记录
            self.decision_history.append(workflow_result)
            
            self.logger.info(f"完成决策工作流程: {problem.title}")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"决策工作流程执行失败: {e}")
            return {
                "problem_id": problem.id,
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now()
            }
    
    def get_scenario_template(self, template_id: str) -> Optional[DecisionScenario]:
        """获取场景模板"""
        return self.scenario_templates.get(template_id)
    
    def list_scenario_templates(self) -> List[Dict[str, Any]]:
        """列出所有场景模板"""
        templates = []
        for scenario in self.scenario_templates.values():
            templates.append({
                "id": scenario.id,
                "name": scenario.name,
                "description": scenario.description,
                "complexity": scenario.problem.complexity.value,
                "decision_type": scenario.problem.decision_type.value
            })
        return templates
    
    def get_decision_statistics(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        if not self.decision_history:
            return {"message": "暂无决策历史记录"}
        
        stats = {
            "total_decisions": len(self.decision_history),
            "by_complexity": {},
            "by_urgency": {},
            "by_type": {},
            "average_execution_time": 0.0,
            "success_rate": 0.0
        }
        
        total_time = 0.0
        successful_decisions = 0
        
        for record in self.decision_history:
            if record["status"] == "completed":
                successful_decisions += 1
                total_time += record.get("execution_time", 0.0)
                
                # 统计复杂度分布
                complexity = record.get("complexity", "unknown")
                stats["by_complexity"][complexity] = stats["by_complexity"].get(complexity, 0) + 1
                
                # 统计紧急程度分布
                urgency = record.get("urgency", "unknown")
                stats["by_urgency"][urgency] = stats["by_urgency"].get(urgency, 0) + 1
                
                # 统计决策类型分布
                decision_type = record.get("problem_type", "unknown")
                stats["by_type"][decision_type] = stats["by_type"].get(decision_type, 0) + 1
        
        if successful_decisions > 0:
            stats["average_execution_time"] = total_time / successful_decisions
            stats["success_rate"] = successful_decisions / len(self.decision_history)
        
        return stats
    
    def _initialize_scenario_templates(self) -> None:
        """初始化预定义场景模板"""
        # 战术决策场景
        tactical_problem = DecisionProblem(
            id="tactical_001",
            title="战术行动决策",
            description="在战术层面选择最优行动方案",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.URGENT,
            decision_type=DecisionType.TACTICAL,
            stakeholders=["指挥官", "参谋", "作战单位"],
            success_criteria={"effectiveness": 0.8, "risk": 0.3, "time": 0.9}
        )
        
        tactical_scenario = DecisionScenario(
            id="tactical_scenario_001",
            name="战术决策场景",
            description="标准战术决策支持场景",
            problem=tactical_problem
        )
        
        self.scenario_templates[tactical_scenario.id] = tactical_scenario
        
        # 资源配置决策场景
        resource_problem = DecisionProblem(
            id="resource_001",
            title="资源配置决策",
            description="优化军事资源的分配和使用",
            complexity=DecisionComplexity.COMPLEX,
            urgency=DecisionUrgency.ROUTINE,
            decision_type=DecisionType.RESOURCE_ALLOCATION,
            stakeholders=["后勤部门", "作战部门", "指挥部"],
            success_criteria={"efficiency": 0.9, "cost": 0.7, "availability": 0.8}
        )
        
        resource_scenario = DecisionScenario(
            id="resource_scenario_001",
            name="资源配置决策场景",
            description="军事资源优化配置场景",
            problem=resource_problem
        )
        
        self.scenario_templates[resource_scenario.id] = resource_scenario

    def _configure_scenario_parameters(self, scenario: DecisionScenario) -> None:
        """配置场景参数"""
        problem = scenario.problem

        # 根据复杂度设置参数
        if problem.complexity == DecisionComplexity.SIMPLE:
            scenario.context["max_alternatives"] = 3
            scenario.context["analysis_depth"] = "basic"
        elif problem.complexity == DecisionComplexity.MODERATE:
            scenario.context["max_alternatives"] = 5
            scenario.context["analysis_depth"] = "standard"
        elif problem.complexity == DecisionComplexity.COMPLEX:
            scenario.context["max_alternatives"] = 8
            scenario.context["analysis_depth"] = "comprehensive"
        else:  # CRITICAL
            scenario.context["max_alternatives"] = 10
            scenario.context["analysis_depth"] = "exhaustive"

        # 根据紧急程度设置时间约束
        scenario.context["time_limit"] = problem.get_time_limit()
        scenario.context["requires_simulation"] = problem.complexity in [DecisionComplexity.COMPLEX, DecisionComplexity.CRITICAL]

    def _gather_available_resources(self, scenario: DecisionScenario) -> None:
        """收集可用资源信息"""
        # 模拟资源收集过程
        scenario.available_resources = {
            "personnel": {"count": 100, "readiness": 0.8},
            "equipment": {"count": 50, "operational": 0.9},
            "intelligence": {"quality": 0.7, "timeliness": 0.8},
            "time": {"available_hours": scenario.context.get("time_limit", 24.0)},
            "budget": {"allocated": 1000000, "available": 800000}
        }

    def _analyze_environmental_factors(self, scenario: DecisionScenario) -> None:
        """分析环境因素"""
        scenario.environmental_factors = {
            "threat_level": "moderate",
            "weather_conditions": "favorable",
            "terrain": "mixed",
            "political_climate": "stable",
            "international_situation": "normal",
            "public_opinion": "supportive"
        }

    def _find_historical_cases(self, scenario: DecisionScenario) -> None:
        """查找历史案例"""
        # 基于决策类型查找相关历史案例
        decision_type = scenario.problem.decision_type

        if decision_type == DecisionType.TACTICAL:
            scenario.historical_cases = ["tactical_case_001", "tactical_case_002"]
        elif decision_type == DecisionType.RESOURCE_ALLOCATION:
            scenario.historical_cases = ["resource_case_001", "resource_case_002"]
        else:
            scenario.historical_cases = ["general_case_001"]

    def _analyze_problem_structure(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析问题结构"""
        return {
            "problem_type": scenario.problem.decision_type.value,
            "complexity_level": scenario.problem.complexity.value,
            "urgency_level": scenario.problem.urgency.value,
            "stakeholder_count": len(scenario.problem.stakeholders),
            "constraint_count": len(scenario.problem.constraints),
            "success_criteria_count": len(scenario.problem.success_criteria),
            "has_deadline": scenario.problem.deadline is not None,
            "security_classification": scenario.problem.security_level.value
        }

    def _analyze_stakeholders(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析利益相关者"""
        stakeholders = scenario.problem.stakeholders

        return {
            "primary_stakeholders": stakeholders[:3] if len(stakeholders) > 3 else stakeholders,
            "secondary_stakeholders": stakeholders[3:] if len(stakeholders) > 3 else [],
            "stakeholder_influence": {stakeholder: 0.8 for stakeholder in stakeholders},
            "conflict_potential": "low" if len(stakeholders) <= 3 else "moderate"
        }

    def _analyze_constraints(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析约束条件"""
        constraints = scenario.problem.constraints

        return {
            "hard_constraints": [k for k, v in constraints.items() if isinstance(v, bool) and v],
            "soft_constraints": [k for k, v in constraints.items() if not isinstance(v, bool)],
            "constraint_severity": {k: "high" if isinstance(v, bool) else "medium" for k, v in constraints.items()},
            "total_constraints": len(constraints)
        }

    def _assess_risks(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """评估风险"""
        complexity = scenario.problem.complexity
        urgency = scenario.problem.urgency

        # 基于复杂度和紧急程度评估风险
        risk_level = "low"
        if complexity in [DecisionComplexity.COMPLEX, DecisionComplexity.CRITICAL]:
            risk_level = "high"
        elif urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL]:
            risk_level = "high"
        elif complexity == DecisionComplexity.MODERATE or urgency == DecisionUrgency.URGENT:
            risk_level = "medium"

        return {
            "overall_risk_level": risk_level,
            "time_risk": "high" if urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL] else "low",
            "complexity_risk": "high" if complexity in [DecisionComplexity.COMPLEX, DecisionComplexity.CRITICAL] else "low",
            "resource_risk": "medium",
            "implementation_risk": risk_level,
            "mitigation_required": risk_level in ["medium", "high"]
        }

    def _analyze_opportunities(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析机会"""
        return {
            "strategic_opportunities": ["提升作战能力", "优化资源配置"],
            "tactical_opportunities": ["改进作战效率", "降低风险"],
            "learning_opportunities": ["积累经验", "完善流程"],
            "innovation_opportunities": ["技术创新", "方法改进"],
            "opportunity_score": 0.7
        }

    def _analyze_resource_requirements(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析资源需求"""
        complexity = scenario.problem.complexity

        # 根据复杂度估算资源需求
        if complexity == DecisionComplexity.SIMPLE:
            personnel_needed = 5
            time_needed = 2
        elif complexity == DecisionComplexity.MODERATE:
            personnel_needed = 10
            time_needed = 8
        elif complexity == DecisionComplexity.COMPLEX:
            personnel_needed = 20
            time_needed = 24
        else:  # CRITICAL
            personnel_needed = 50
            time_needed = 72

        return {
            "personnel_required": personnel_needed,
            "time_required_hours": time_needed,
            "budget_required": personnel_needed * 1000,
            "equipment_required": ["通信设备", "计算设备", "分析工具"],
            "expertise_required": ["决策分析", "军事专业", "系统分析"],
            "resource_availability": self._check_resource_availability(scenario, personnel_needed, time_needed)
        }

    def _analyze_time_sensitivity(self, scenario: DecisionScenario) -> Dict[str, Any]:
        """分析时间敏感性"""
        urgency = scenario.problem.urgency
        time_limit = scenario.problem.get_time_limit()

        return {
            "urgency_level": urgency.value,
            "time_limit_hours": time_limit,
            "time_pressure": "high" if time_limit < 4 else "medium" if time_limit < 24 else "low",
            "deadline_flexibility": "none" if urgency == DecisionUrgency.CRITICAL else "limited" if urgency == DecisionUrgency.IMMEDIATE else "moderate",
            "time_buffer_recommended": max(0.2 * time_limit, 0.5)
        }

    def _check_resource_availability(self, scenario: DecisionScenario, personnel_needed: int, time_needed: float) -> Dict[str, bool]:
        """检查资源可用性"""
        available_resources = scenario.available_resources

        return {
            "personnel_available": available_resources.get("personnel", {}).get("count", 0) >= personnel_needed,
            "time_available": available_resources.get("time", {}).get("available_hours", 0) >= time_needed,
            "equipment_available": available_resources.get("equipment", {}).get("operational", 0) > 0.8,
            "budget_available": available_resources.get("budget", {}).get("available", 0) >= personnel_needed * 1000
        }

    def _generate_knowledge_based_alternatives(self, scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """基于知识库生成方案"""
        alternatives = []

        # 搜索相关知识
        decision_type = scenario.problem.decision_type.value
        knowledge_results = self.knowledge_base.search_knowledge(decision_type, limit=3)

        for i, (knowledge_item, score) in enumerate(knowledge_results):
            alternative = {
                "id": f"knowledge_alt_{i+1}",
                "name": f"基于{knowledge_item.title}的方案",
                "description": knowledge_item.content[:200] + "...",
                "source": "knowledge_base",
                "confidence": knowledge_item.confidence * score,
                "feasibility": 0.8,
                "effectiveness": 0.7,
                "risk_level": 0.3,
                "resource_requirements": {"personnel": 10, "time": 4, "budget": 50000},
                "implementation_complexity": "medium"
            }
            alternatives.append(alternative)

        return alternatives

    def _generate_historical_alternatives(self, scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """基于历史案例生成方案"""
        alternatives = []

        for i, case_id in enumerate(scenario.historical_cases[:2]):  # 最多2个历史案例
            alternative = {
                "id": f"historical_alt_{i+1}",
                "name": f"基于历史案例{case_id}的方案",
                "description": f"参考历史案例{case_id}的成功经验制定的方案",
                "source": "historical_cases",
                "confidence": 0.8,
                "feasibility": 0.9,
                "effectiveness": 0.8,
                "risk_level": 0.2,
                "resource_requirements": {"personnel": 8, "time": 3, "budget": 40000},
                "implementation_complexity": "low"
            }
            alternatives.append(alternative)

        return alternatives

    def _generate_simulation_alternatives(self, scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """基于仿真生成方案"""
        alternatives = []

        if scenario.context.get("requires_simulation", False):
            # 创建仿真方案
            alternative = {
                "id": "simulation_alt_1",
                "name": "仿真优化方案",
                "description": "通过军事仿真系统优化得出的最佳方案",
                "source": "simulation",
                "confidence": 0.9,
                "feasibility": 0.8,
                "effectiveness": 0.9,
                "risk_level": 0.25,
                "resource_requirements": {"personnel": 15, "time": 6, "budget": 75000},
                "implementation_complexity": "high",
                "simulation_validated": True
            }
            alternatives.append(alternative)

        return alternatives

    def _generate_expert_alternatives(self, scenario: DecisionScenario, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于专家系统生成方案"""
        alternatives = []

        # 获取专家建议
        expert_advice = self.knowledge_base.get_expert_advice(scenario.problem.title)

        for i, advice in enumerate(expert_advice[:2]):  # 最多2个专家建议
            alternative = {
                "id": f"expert_alt_{i+1}",
                "name": f"专家建议方案{i+1}",
                "description": advice.advice,
                "source": "expert_system",
                "confidence": advice.confidence,
                "feasibility": 0.7,
                "effectiveness": 0.8,
                "risk_level": 0.3,
                "resource_requirements": {"personnel": 12, "time": 5, "budget": 60000},
                "implementation_complexity": "medium",
                "expert_reasoning": advice.reasoning
            }
            alternatives.append(alternative)

        return alternatives

    def _optimize_alternatives(self, alternatives: List[Dict[str, Any]], scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """优化备选方案"""
        if not alternatives:
            return alternatives

        # 去重：基于描述相似度
        unique_alternatives = []
        for alt in alternatives:
            is_duplicate = False
            for unique_alt in unique_alternatives:
                if self._calculate_similarity(alt["description"], unique_alt["description"]) > 0.8:
                    is_duplicate = True
                    # 保留置信度更高的方案
                    if alt["confidence"] > unique_alt["confidence"]:
                        unique_alternatives.remove(unique_alt)
                        unique_alternatives.append(alt)
                    break

            if not is_duplicate:
                unique_alternatives.append(alt)

        # 限制方案数量
        max_alternatives = scenario.context.get("max_alternatives", 5)
        if len(unique_alternatives) > max_alternatives:
            # 按综合得分排序并选择前N个
            scored_alternatives = []
            for alt in unique_alternatives:
                score = (alt["confidence"] * 0.3 +
                        alt["feasibility"] * 0.3 +
                        alt["effectiveness"] * 0.3 -
                        alt["risk_level"] * 0.1)
                scored_alternatives.append((alt, score))

            scored_alternatives.sort(key=lambda x: x[1], reverse=True)
            unique_alternatives = [alt for alt, score in scored_alternatives[:max_alternatives]]

        return unique_alternatives

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化实现）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

    def _evaluate_alternatives(self, alternatives: List[Dict[str, Any]], scenario: DecisionScenario) -> Dict[str, Any]:
        """评估备选方案"""
        evaluation_matrix = {
            "alternatives": [],
            "criteria": ["feasibility", "effectiveness", "risk_level", "resource_efficiency", "time_efficiency"],
            "weights": {"feasibility": 0.25, "effectiveness": 0.3, "risk_level": 0.2, "resource_efficiency": 0.15, "time_efficiency": 0.1},
            "scores": {},
            "rankings": []
        }

        for alt in alternatives:
            alt_id = alt["id"]
            evaluation_matrix["alternatives"].append(alt_id)

            # 计算各项评分
            scores = {
                "feasibility": alt.get("feasibility", 0.5),
                "effectiveness": alt.get("effectiveness", 0.5),
                "risk_level": 1.0 - alt.get("risk_level", 0.5),  # 风险越低分数越高
                "resource_efficiency": self._calculate_resource_efficiency(alt, scenario),
                "time_efficiency": self._calculate_time_efficiency(alt, scenario)
            }

            evaluation_matrix["scores"][alt_id] = scores

            # 计算加权总分
            weighted_score = sum(scores[criterion] * evaluation_matrix["weights"][criterion]
                               for criterion in evaluation_matrix["criteria"])

            evaluation_matrix["rankings"].append((alt_id, weighted_score))

        # 按得分排序
        evaluation_matrix["rankings"].sort(key=lambda x: x[1], reverse=True)

        return evaluation_matrix

    def _calculate_resource_efficiency(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> float:
        """计算资源效率"""
        required = alternative.get("resource_requirements", {})
        available = scenario.available_resources

        # 简化的资源效率计算
        personnel_efficiency = min(1.0, available.get("personnel", {}).get("count", 0) / max(1, required.get("personnel", 1)))
        budget_efficiency = min(1.0, available.get("budget", {}).get("available", 0) / max(1, required.get("budget", 1)))

        return (personnel_efficiency + budget_efficiency) / 2

    def _calculate_time_efficiency(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> float:
        """计算时间效率"""
        required_time = alternative.get("resource_requirements", {}).get("time", 1)
        available_time = scenario.available_resources.get("time", {}).get("available_hours", 24)

        return min(1.0, available_time / max(1, required_time))

    def _select_best_alternative(self, evaluation_results: Dict[str, Any], scenario: DecisionScenario) -> Optional[Dict[str, Any]]:
        """选择最佳方案"""
        if not evaluation_results.get("rankings"):
            return None

        best_alt_id, best_score = evaluation_results["rankings"][0]

        # 查找对应的方案详情
        for alt in self._get_all_alternatives_from_scenario(scenario):
            if alt["id"] == best_alt_id:
                alt["evaluation_score"] = best_score
                alt["ranking"] = 1
                return alt

        return None

    def _get_all_alternatives_from_scenario(self, scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """从场景获取所有方案（辅助方法）"""
        # 这里应该从场景中获取所有生成的方案
        # 为了简化，返回空列表，实际使用时需要保存方案到场景中
        return []

    def _analyze_alternative_risks(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> Dict[str, Any]:
        """分析方案风险"""
        base_risk = alternative.get("risk_level", 0.5)
        complexity = scenario.problem.complexity

        # 根据复杂度调整风险
        complexity_multiplier = {
            DecisionComplexity.SIMPLE: 0.8,
            DecisionComplexity.MODERATE: 1.0,
            DecisionComplexity.COMPLEX: 1.3,
            DecisionComplexity.CRITICAL: 1.5
        }

        adjusted_risk = min(1.0, base_risk * complexity_multiplier.get(complexity, 1.0))

        return {
            "overall_risk": adjusted_risk,
            "implementation_risk": adjusted_risk * 0.8,
            "resource_risk": 0.3,
            "time_risk": 0.4 if scenario.problem.urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL] else 0.2,
            "technical_risk": 0.3 if alternative.get("implementation_complexity") == "high" else 0.1,
            "mitigation_strategies": [
                "定期风险评估",
                "建立应急预案",
                "加强监控机制",
                "准备备选方案"
            ]
        }

    def _create_implementation_plan(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> Dict[str, Any]:
        """创建实施计划"""
        required_time = alternative.get("resource_requirements", {}).get("time", 4)

        return {
            "phases": [
                {
                    "name": "准备阶段",
                    "duration_hours": required_time * 0.2,
                    "activities": ["资源准备", "人员配置", "计划细化"],
                    "deliverables": ["实施方案", "资源清单", "人员分工"]
                },
                {
                    "name": "执行阶段",
                    "duration_hours": required_time * 0.6,
                    "activities": ["方案执行", "进度监控", "问题处理"],
                    "deliverables": ["执行报告", "进度更新", "问题记录"]
                },
                {
                    "name": "验收阶段",
                    "duration_hours": required_time * 0.2,
                    "activities": ["结果验证", "效果评估", "经验总结"],
                    "deliverables": ["验收报告", "效果评估", "经验文档"]
                }
            ],
            "total_duration": required_time,
            "critical_path": ["准备阶段", "执行阶段"],
            "resource_allocation": alternative.get("resource_requirements", {}),
            "success_criteria": scenario.problem.success_criteria
        }

    def _create_monitoring_plan(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> Dict[str, Any]:
        """创建监控计划"""
        return {
            "monitoring_frequency": "每日" if scenario.problem.urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL] else "每周",
            "key_indicators": [
                {"name": "进度完成率", "target": ">90%", "threshold": "80%"},
                {"name": "资源利用率", "target": "80-90%", "threshold": "70%"},
                {"name": "质量指标", "target": ">85%", "threshold": "75%"},
                {"name": "风险等级", "target": "<30%", "threshold": "50%"}
            ],
            "reporting_schedule": {
                "daily_reports": scenario.problem.urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL],
                "weekly_reports": True,
                "milestone_reports": True,
                "exception_reports": True
            },
            "escalation_triggers": [
                "进度延迟超过20%",
                "资源超支超过15%",
                "质量指标低于阈值",
                "出现重大风险"
            ]
        }

    def _create_contingency_plans(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> List[Dict[str, Any]]:
        """创建应急预案"""
        contingency_plans = []

        # 资源不足应急预案
        contingency_plans.append({
            "trigger": "资源不足",
            "description": "当关键资源不足时的应对措施",
            "actions": [
                "重新评估资源需求",
                "寻找替代资源",
                "调整实施计划",
                "请求额外支持"
            ],
            "responsible_party": "项目经理",
            "activation_criteria": "资源可用性低于70%"
        })

        # 时间延迟应急预案
        contingency_plans.append({
            "trigger": "时间延迟",
            "description": "当项目进度延迟时的应对措施",
            "actions": [
                "分析延迟原因",
                "重新安排优先级",
                "增加资源投入",
                "简化实施方案"
            ],
            "responsible_party": "指挥官",
            "activation_criteria": "进度延迟超过15%"
        })

        # 质量问题应急预案
        contingency_plans.append({
            "trigger": "质量问题",
            "description": "当出现质量问题时的应对措施",
            "actions": [
                "暂停相关活动",
                "分析问题根因",
                "制定纠正措施",
                "加强质量控制"
            ],
            "responsible_party": "质量负责人",
            "activation_criteria": "质量指标低于75%"
        })

        return contingency_plans

    def _get_expert_advice(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> List[str]:
        """获取专家建议"""
        advice = []

        # 基于方案复杂度给出建议
        complexity = alternative.get("implementation_complexity", "medium")
        if complexity == "high":
            advice.append("建议分阶段实施，降低实施风险")
            advice.append("加强技术支持和专家指导")

        # 基于资源需求给出建议
        resource_req = alternative.get("resource_requirements", {})
        if resource_req.get("personnel", 0) > 20:
            advice.append("建议建立专门的项目团队")
            advice.append("确保充足的人员储备")

        # 基于时间压力给出建议
        if scenario.problem.urgency in [DecisionUrgency.IMMEDIATE, DecisionUrgency.CRITICAL]:
            advice.append("建议启动快速决策流程")
            advice.append("准备应急资源和备选方案")

        # 基于风险等级给出建议
        risk_level = alternative.get("risk_level", 0.5)
        if risk_level > 0.6:
            advice.append("建议加强风险监控和控制")
            advice.append("制定详细的风险缓解措施")

        return advice

    def _assess_confidence(self, alternative: Dict[str, Any], scenario: DecisionScenario) -> Dict[str, Any]:
        """评估置信度"""
        base_confidence = alternative.get("confidence", 0.5)

        # 根据数据来源调整置信度
        source_confidence = {
            "knowledge_base": 0.8,
            "historical_cases": 0.9,
            "simulation": 0.95,
            "expert_system": 0.85
        }

        source = alternative.get("source", "unknown")
        source_factor = source_confidence.get(source, 0.5)

        # 根据验证程度调整置信度
        validation_factor = 1.0
        if alternative.get("simulation_validated", False):
            validation_factor = 1.1

        # 计算最终置信度
        final_confidence = min(1.0, base_confidence * source_factor * validation_factor)

        return {
            "overall_confidence": final_confidence,
            "data_quality": source_factor,
            "validation_level": "high" if alternative.get("simulation_validated") else "medium",
            "uncertainty_factors": [
                "环境变化",
                "资源可用性",
                "执行能力",
                "外部干扰"
            ],
            "confidence_interval": [max(0.0, final_confidence - 0.1), min(1.0, final_confidence + 0.1)]
        }
