"""
测试报告生成器

生成详细的测试报告，包括测试覆盖率、性能指标和质量评估
"""

import os
import json
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any
import tempfile


class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.report_data = {}
        self.start_time = None
        self.end_time = None
    
    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """运行全面测试并生成报告"""
        print("开始运行全面测试...")
        self.start_time = datetime.now()
        
        # 1. 运行所有测试
        test_results = self._run_all_tests()
        
        # 2. 生成覆盖率报告
        coverage_report = self._generate_coverage_report()
        
        # 3. 收集性能指标
        performance_metrics = self._collect_performance_metrics()
        
        # 4. 分析代码质量
        quality_metrics = self._analyze_code_quality()
        
        # 5. 生成系统信息
        system_info = self._collect_system_info()
        
        self.end_time = datetime.now()
        
        # 汇总报告数据
        self.report_data = {
            'test_execution': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat(),
                'duration_seconds': (self.end_time - self.start_time).total_seconds(),
                'results': test_results
            },
            'coverage': coverage_report,
            'performance': performance_metrics,
            'quality': quality_metrics,
            'system_info': system_info,
            'summary': self._generate_summary()
        }
        
        return self.report_data
    
    def _run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("运行测试套件...")
        
        try:
            # 运行pytest并收集结果
            cmd = [
                'python', '-m', 'pytest', 
                'tests/', 
                '-v', 
                '--tb=short',
                '--json-report',
                '--json-report-file=test_results.json'
            ]
            
            result = subprocess.run(
                cmd, 
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            # 解析测试结果
            test_results = {
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
            # 尝试读取JSON报告
            json_report_path = os.path.join(self.project_root, 'test_results.json')
            if os.path.exists(json_report_path):
                try:
                    with open(json_report_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                        test_results['detailed_results'] = json_data
                except Exception as e:
                    test_results['json_parse_error'] = str(e)
            
            return test_results
            
        except subprocess.TimeoutExpired:
            return {
                'return_code': -1,
                'error': 'Test execution timeout',
                'success': False
            }
        except Exception as e:
            return {
                'return_code': -1,
                'error': str(e),
                'success': False
            }
    
    def _generate_coverage_report(self) -> Dict[str, Any]:
        """生成覆盖率报告"""
        print("生成覆盖率报告...")
        
        try:
            # 运行coverage
            coverage_cmd = [
                'python', '-m', 'pytest',
                'tests/',
                '--cov=src/hmdm',
                '--cov-report=json',
                '--cov-report=term-missing'
            ]
            
            result = subprocess.run(
                coverage_cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            coverage_data = {
                'command_output': result.stdout,
                'success': result.returncode == 0
            }
            
            # 读取JSON覆盖率报告
            coverage_json_path = os.path.join(self.project_root, 'coverage.json')
            if os.path.exists(coverage_json_path):
                try:
                    with open(coverage_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                        coverage_data['detailed_coverage'] = json_data
                        
                        # 提取关键指标
                        if 'totals' in json_data:
                            totals = json_data['totals']
                            coverage_data['summary'] = {
                                'lines_covered': totals.get('covered_lines', 0),
                                'lines_total': totals.get('num_statements', 0),
                                'coverage_percent': totals.get('percent_covered', 0),
                                'missing_lines': totals.get('missing_lines', 0)
                            }
                except Exception as e:
                    coverage_data['json_parse_error'] = str(e)
            
            return coverage_data
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        print("收集性能指标...")
        
        try:
            # 运行性能测试
            perf_cmd = [
                'python', '-m', 'pytest',
                'tests/test_stress_performance.py',
                '-v',
                '--tb=short'
            ]
            
            result = subprocess.run(
                perf_cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            performance_data = {
                'stress_test_output': result.stdout,
                'stress_test_success': result.returncode == 0,
                'metrics': self._extract_performance_metrics(result.stdout)
            }
            
            return performance_data
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def _extract_performance_metrics(self, output: str) -> Dict[str, Any]:
        """从测试输出中提取性能指标"""
        metrics = {}
        
        lines = output.split('\n')
        for line in lines:
            # 提取缓存性能指标
            if 'ops/sec' in line and '缓存并发测试完成' in line:
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'ops/sec' in part:
                            ops_sec = float(parts[i-1])
                            metrics['cache_operations_per_second'] = ops_sec
                            break
                except:
                    pass
            
            # 提取数据库性能指标
            if 'queries/sec' in line and '数据库连接池压力测试完成' in line:
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'queries/sec' in part:
                            queries_sec = float(parts[i-1])
                            metrics['database_queries_per_second'] = queries_sec
                            break
                except:
                    pass
            
            # 提取认证性能指标
            if 'auths/sec' in line and '安全认证负载测试完成' in line:
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'auths/sec' in part:
                            auths_sec = float(parts[i-1])
                            metrics['authentication_per_second'] = auths_sec
                            break
                except:
                    pass
        
        return metrics
    
    def _analyze_code_quality(self) -> Dict[str, Any]:
        """分析代码质量"""
        print("分析代码质量...")
        
        quality_metrics = {
            'file_count': 0,
            'line_count': 0,
            'python_files': [],
            'module_analysis': {}
        }
        
        try:
            # 统计代码文件
            src_dir = os.path.join(self.project_root, 'src', 'hmdm')
            if os.path.exists(src_dir):
                for root, dirs, files in os.walk(src_dir):
                    for file in files:
                        if file.endswith('.py'):
                            file_path = os.path.join(root, file)
                            quality_metrics['file_count'] += 1
                            
                            # 统计行数
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    lines = f.readlines()
                                    line_count = len(lines)
                                    quality_metrics['line_count'] += line_count
                                    
                                    # 分析模块
                                    module_name = os.path.relpath(file_path, src_dir)
                                    quality_metrics['python_files'].append({
                                        'file': module_name,
                                        'lines': line_count,
                                        'size_bytes': os.path.getsize(file_path)
                                    })
                            except Exception as e:
                                pass
            
            # 计算平均指标
            if quality_metrics['file_count'] > 0:
                quality_metrics['average_lines_per_file'] = (
                    quality_metrics['line_count'] / quality_metrics['file_count']
                )
            
            return quality_metrics
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            import platform
            import sys
            
            system_info = {
                'platform': {
                    'system': platform.system(),
                    'release': platform.release(),
                    'version': platform.version(),
                    'machine': platform.machine(),
                    'processor': platform.processor()
                },
                'python': {
                    'version': sys.version,
                    'executable': sys.executable,
                    'platform': sys.platform
                },
                'environment': {
                    'working_directory': os.getcwd(),
                    'project_root': self.project_root
                }
            }
            
            # 尝试获取内存信息
            try:
                import psutil
                memory = psutil.virtual_memory()
                system_info['memory'] = {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'percent_used': memory.percent
                }
            except ImportError:
                pass
            
            return system_info
            
        except Exception as e:
            return {
                'error': str(e)
            }
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成测试摘要"""
        summary = {
            'overall_status': 'UNKNOWN',
            'test_success': False,
            'coverage_percent': 0,
            'performance_score': 'N/A',
            'recommendations': []
        }
        
        try:
            # 测试成功状态
            if 'test_execution' in self.report_data:
                test_results = self.report_data['test_execution']['results']
                summary['test_success'] = test_results.get('success', False)
            
            # 覆盖率
            if 'coverage' in self.report_data and 'summary' in self.report_data['coverage']:
                coverage_summary = self.report_data['coverage']['summary']
                summary['coverage_percent'] = coverage_summary.get('coverage_percent', 0)
            
            # 性能评分
            if 'performance' in self.report_data and 'metrics' in self.report_data['performance']:
                metrics = self.report_data['performance']['metrics']
                performance_scores = []
                
                if 'cache_operations_per_second' in metrics:
                    cache_ops = metrics['cache_operations_per_second']
                    if cache_ops > 100:
                        performance_scores.append('GOOD')
                    elif cache_ops > 50:
                        performance_scores.append('FAIR')
                    else:
                        performance_scores.append('POOR')
                
                if performance_scores:
                    if all(score == 'GOOD' for score in performance_scores):
                        summary['performance_score'] = 'EXCELLENT'
                    elif any(score == 'GOOD' for score in performance_scores):
                        summary['performance_score'] = 'GOOD'
                    else:
                        summary['performance_score'] = 'NEEDS_IMPROVEMENT'
            
            # 总体状态
            if summary['test_success'] and summary['coverage_percent'] >= 70:
                if summary['performance_score'] in ['EXCELLENT', 'GOOD']:
                    summary['overall_status'] = 'EXCELLENT'
                else:
                    summary['overall_status'] = 'GOOD'
            elif summary['test_success']:
                summary['overall_status'] = 'FAIR'
            else:
                summary['overall_status'] = 'POOR'
            
            # 生成建议
            if summary['coverage_percent'] < 80:
                summary['recommendations'].append('提高测试覆盖率至80%以上')
            
            if not summary['test_success']:
                summary['recommendations'].append('修复失败的测试用例')
            
            if summary['performance_score'] == 'NEEDS_IMPROVEMENT':
                summary['recommendations'].append('优化系统性能')
            
        except Exception as e:
            summary['error'] = str(e)
        
        return summary
    
    def save_report(self, filename: str = None) -> str:
        """保存测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'test_report_{timestamp}.json'
        
        report_path = os.path.join(self.project_root, filename)
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report_data, f, indent=2, ensure_ascii=False)
            
            print(f"测试报告已保存到: {report_path}")
            return report_path
            
        except Exception as e:
            print(f"保存测试报告失败: {e}")
            return ""
    
    def print_summary(self):
        """打印测试摘要"""
        if not self.report_data:
            print("没有可用的测试报告数据")
            return
        
        summary = self.report_data.get('summary', {})
        
        print("\n" + "="*60)
        print("HMDM系统测试报告摘要")
        print("="*60)
        
        print(f"总体状态: {summary.get('overall_status', 'UNKNOWN')}")
        print(f"测试成功: {'✓' if summary.get('test_success', False) else '✗'}")
        print(f"代码覆盖率: {summary.get('coverage_percent', 0):.1f}%")
        print(f"性能评分: {summary.get('performance_score', 'N/A')}")
        
        if 'test_execution' in self.report_data:
            duration = self.report_data['test_execution']['duration_seconds']
            print(f"测试耗时: {duration:.1f}秒")
        
        if 'quality' in self.report_data:
            quality = self.report_data['quality']
            print(f"代码文件数: {quality.get('file_count', 0)}")
            print(f"代码行数: {quality.get('line_count', 0)}")
        
        recommendations = summary.get('recommendations', [])
        if recommendations:
            print("\n建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        print("="*60)


def run_comprehensive_test_report():
    """运行全面测试报告"""
    generator = TestReportGenerator()
    
    print("开始生成HMDM系统测试报告...")
    report_data = generator.run_comprehensive_tests()
    
    # 打印摘要
    generator.print_summary()
    
    # 保存报告
    report_path = generator.save_report()
    
    return report_data, report_path


if __name__ == "__main__":
    run_comprehensive_test_report()
