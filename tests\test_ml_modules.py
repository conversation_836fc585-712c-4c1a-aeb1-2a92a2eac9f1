"""
机器学习模块测试

测试智能预测引擎、NLP处理器和推荐引擎的功能
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
from datetime import datetime

from src.hmdm.ml.intelligent_prediction_engine import (
    IntelligentPredictionEngine, ModelConfig, ModelType, PredictionTask
)
from src.hmdm.ml.nlp_processor import (
    MilitaryNLPProcessor, TextType, SentimentType
)
from src.hmdm.ml.recommendation_engine import (
    IntelligentRecommendationEngine, RecommendationItem, RecommendationType
)
from src.hmdm.security.military_security import SecurityLevel


class TestIntelligentPredictionEngine:
    """智能预测引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.prediction_engine = IntelligentPredictionEngine(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_engine_initialization(self):
        """测试预测引擎初始化"""
        assert self.prediction_engine is not None
        assert os.path.exists(self.temp_dir)
        assert len(self.prediction_engine.models) == 0
        assert len(self.prediction_engine.model_configs) == 0
    
    def test_create_random_forest_model(self):
        """测试创建随机森林模型"""
        config = ModelConfig(
            model_id="test_rf_classifier",
            model_type=ModelType.CLASSIFICATION,
            task_type=PredictionTask.THREAT_ASSESSMENT,
            algorithm="random_forest",
            parameters={"n_estimators": 10, "random_state": 42},
            feature_columns=["feature1", "feature2", "feature3"],
            target_column="target"
        )
        
        success = self.prediction_engine.create_model(config)
        assert success is True
        assert "test_rf_classifier" in self.prediction_engine.models
        assert "test_rf_classifier" in self.prediction_engine.model_configs
    
    def test_create_neural_network_model(self):
        """测试创建神经网络模型"""
        config = ModelConfig(
            model_id="test_nn_regressor",
            model_type=ModelType.REGRESSION,
            task_type=PredictionTask.SITUATION_PREDICTION,
            algorithm="neural_network",
            parameters={"hidden_layer_sizes": (50, 30), "random_state": 42},
            feature_columns=["x1", "x2", "x3", "x4"],
            target_column="y"
        )
        
        success = self.prediction_engine.create_model(config)
        assert success is True
        assert "test_nn_regressor" in self.prediction_engine.models
    
    def test_train_classification_model(self):
        """测试训练分类模型"""
        # 创建模型
        config = ModelConfig(
            model_id="test_classifier",
            model_type=ModelType.CLASSIFICATION,
            task_type=PredictionTask.THREAT_ASSESSMENT,
            algorithm="random_forest",
            parameters={"n_estimators": 10, "random_state": 42},
            feature_columns=["x1", "x2"],
            target_column="y"
        )
        
        self.prediction_engine.create_model(config)
        
        # 创建训练数据
        np.random.seed(42)
        data = pd.DataFrame({
            'x1': np.random.randn(100),
            'x2': np.random.randn(100),
            'y': np.random.choice([0, 1], 100)
        })
        
        # 训练模型
        success = self.prediction_engine.train_model("test_classifier", data)
        assert success is True
    
    def test_train_regression_model(self):
        """测试训练回归模型"""
        # 创建模型
        config = ModelConfig(
            model_id="test_regressor",
            model_type=ModelType.REGRESSION,
            task_type=PredictionTask.SITUATION_PREDICTION,
            algorithm="random_forest",
            parameters={"n_estimators": 10, "random_state": 42},
            feature_columns=["x1", "x2", "x3"],
            target_column="y"
        )
        
        self.prediction_engine.create_model(config)
        
        # 创建训练数据
        np.random.seed(42)
        data = pd.DataFrame({
            'x1': np.random.randn(100),
            'x2': np.random.randn(100),
            'x3': np.random.randn(100),
            'y': np.random.randn(100)
        })
        
        # 训练模型
        success = self.prediction_engine.train_model("test_regressor", data)
        assert success is True
    
    def test_prediction(self):
        """测试预测功能"""
        # 创建并训练模型
        config = ModelConfig(
            model_id="test_predictor",
            model_type=ModelType.CLASSIFICATION,
            task_type=PredictionTask.THREAT_ASSESSMENT,
            algorithm="random_forest",
            parameters={"n_estimators": 10, "random_state": 42},
            feature_columns=["x1", "x2"],
            target_column="y"
        )
        
        self.prediction_engine.create_model(config)
        
        # 训练数据
        np.random.seed(42)
        train_data = pd.DataFrame({
            'x1': np.random.randn(100),
            'x2': np.random.randn(100),
            'y': np.random.choice([0, 1], 100)
        })
        
        self.prediction_engine.train_model("test_predictor", train_data)
        
        # 预测
        test_input = {'x1': 0.5, 'x2': -0.3}
        result = self.prediction_engine.predict("test_predictor", test_input)
        
        assert result is not None
        assert result.model_id == "test_predictor"
        assert result.task_type == PredictionTask.THREAT_ASSESSMENT
        assert result.prediction in [0, 1]
        assert 0 <= result.confidence <= 1
    
    def test_batch_prediction(self):
        """测试批量预测"""
        # 创建并训练模型
        config = ModelConfig(
            model_id="test_batch_predictor",
            model_type=ModelType.REGRESSION,
            task_type=PredictionTask.DECISION_OPTIMIZATION,
            algorithm="random_forest",
            parameters={"n_estimators": 10, "random_state": 42},
            feature_columns=["feature1", "feature2"],
            target_column="target"
        )
        
        self.prediction_engine.create_model(config)
        
        # 训练数据
        np.random.seed(42)
        train_data = pd.DataFrame({
            'feature1': np.random.randn(50),
            'feature2': np.random.randn(50),
            'target': np.random.randn(50)
        })
        
        self.prediction_engine.train_model("test_batch_predictor", train_data)
        
        # 批量预测
        test_inputs = [
            {'feature1': 0.1, 'feature2': 0.2},
            {'feature1': -0.5, 'feature2': 1.0},
            {'feature1': 0.8, 'feature2': -0.3}
        ]
        
        results = self.prediction_engine.batch_predict("test_batch_predictor", test_inputs)
        
        assert len(results) == 3
        assert all(result is not None for result in results)
        assert all(isinstance(result.prediction, (int, float)) for result in results)
    
    def test_model_performance(self):
        """测试模型性能获取"""
        # 创建并训练模型
        config = ModelConfig(
            model_id="test_performance",
            model_type=ModelType.CLASSIFICATION,
            task_type=PredictionTask.RISK_ANALYSIS,
            algorithm="random_forest",
            parameters={"n_estimators": 5, "random_state": 42},
            feature_columns=["x1", "x2"],
            target_column="y"
        )
        
        self.prediction_engine.create_model(config)
        
        # 训练数据
        np.random.seed(42)
        train_data = pd.DataFrame({
            'x1': np.random.randn(50),
            'x2': np.random.randn(50),
            'y': np.random.choice([0, 1], 50)
        })
        
        self.prediction_engine.train_model("test_performance", train_data)
        
        # 进行一些预测
        for i in range(5):
            test_input = {'x1': np.random.randn(), 'x2': np.random.randn()}
            self.prediction_engine.predict("test_performance", test_input)
        
        # 获取性能指标
        performance = self.prediction_engine.get_model_performance("test_performance")
        
        assert 'model_id' in performance
        assert 'total_predictions' in performance
        assert 'average_confidence' in performance
        assert performance['total_predictions'] == 5
    
    def test_list_models(self):
        """测试列出模型"""
        # 创建几个模型
        configs = [
            ModelConfig(
                model_id="model_1",
                model_type=ModelType.CLASSIFICATION,
                task_type=PredictionTask.THREAT_ASSESSMENT,
                algorithm="random_forest",
                feature_columns=["x1", "x2"],
                target_column="y"
            ),
            ModelConfig(
                model_id="model_2",
                model_type=ModelType.REGRESSION,
                task_type=PredictionTask.SITUATION_PREDICTION,
                algorithm="neural_network",
                feature_columns=["a", "b", "c"],
                target_column="target"
            )
        ]
        
        for config in configs:
            self.prediction_engine.create_model(config)
        
        models_list = self.prediction_engine.list_models()
        
        assert len(models_list) == 2
        assert any(model['model_id'] == 'model_1' for model in models_list)
        assert any(model['model_id'] == 'model_2' for model in models_list)


class TestMilitaryNLPProcessor:
    """军事NLP处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.nlp_processor = MilitaryNLPProcessor()
    
    def test_processor_initialization(self):
        """测试NLP处理器初始化"""
        assert self.nlp_processor is not None
        assert len(self.nlp_processor.military_terms) > 0
        assert len(self.nlp_processor.command_patterns) > 0
        assert len(self.nlp_processor.stop_words) > 0
    
    def test_text_analysis_command(self):
        """测试指令文本分析"""
        command_text = "立即前进到A区域，攻击敌方目标"
        
        result = self.nlp_processor.analyze_text(command_text)
        
        assert result is not None
        assert result.text == command_text
        assert result.text_type == TextType.COMMAND
        assert result.sentiment in [SentimentType.URGENT, SentimentType.NEUTRAL]
        assert result.urgency_level > 0
        assert len(result.keywords) > 0
    
    def test_text_analysis_intelligence(self):
        """测试情报文本分析"""
        intel_text = "侦察发现敌方在B区域部署了装甲部队，数量约50辆坦克"
        
        result = self.nlp_processor.analyze_text(intel_text)
        
        assert result is not None
        assert result.text_type == TextType.INTELLIGENCE
        assert len(result.keywords) > 0
        assert len(result.entities) > 0
        assert result.summary != ""
    
    def test_urgent_text_analysis(self):
        """测试紧急文本分析"""
        urgent_text = "紧急！敌方正在攻击我方阵地，请立即支援！"
        
        result = self.nlp_processor.analyze_text(urgent_text)
        
        assert result is not None
        assert result.sentiment == SentimentType.URGENT
        assert result.urgency_level >= 5
        assert result.confidence > 0.5
    
    def test_extract_commands(self):
        """测试指令提取"""
        text = "第一步：前进到A区域。第二步：攻击目标B。第三步：防守阵地C。"
        
        commands = self.nlp_processor.extract_commands(text)
        
        assert len(commands) > 0
        assert any('前进' in cmd['text'] for cmd in commands)
        assert any('攻击' in cmd['text'] for cmd in commands)
    
    def test_keyword_extraction(self):
        """测试关键词提取"""
        text = "装甲部队在高地执行防御任务，需要炮兵支援和空中掩护"
        
        result = self.nlp_processor.analyze_text(text)
        
        assert len(result.keywords) > 0
        assert any(keyword in ['装甲', '部队', '防御', '任务', '支援'] for keyword in result.keywords)
    
    def test_entity_extraction(self):
        """测试实体提取"""
        text = "第3军第1师在15:30时刻占领了205高地"
        
        result = self.nlp_processor.analyze_text(text)
        
        assert len(result.entities) > 0
        # 检查是否提取到军事单位、时间、地点等实体
        entity_types = [entity['type'] for entity in result.entities]
        assert len(entity_types) > 0
    
    def test_security_classification(self):
        """测试安全等级分类"""
        secret_text = "秘密作战计划：明日凌晨对敌方指挥部实施突袭"
        confidential_text = "机密情报：敌方部队调动情况"
        normal_text = "今日天气晴朗，适合训练"
        
        secret_result = self.nlp_processor.analyze_text(secret_text)
        confidential_result = self.nlp_processor.analyze_text(confidential_text)
        normal_result = self.nlp_processor.analyze_text(normal_text)
        
        assert secret_result.security_classification == SecurityLevel.SECRET
        assert confidential_result.security_classification == SecurityLevel.CONFIDENTIAL
        assert normal_result.security_classification == SecurityLevel.INTERNAL
    
    def test_analysis_statistics(self):
        """测试分析统计"""
        # 分析多个文本
        texts = [
            "紧急指令：立即撤退",
            "情报报告：发现敌方动向",
            "通信测试：信号正常",
            "分析报告：态势评估完成"
        ]
        
        for text in texts:
            self.nlp_processor.analyze_text(text)
        
        stats = self.nlp_processor.get_analysis_statistics()
        
        assert 'total_analyses' in stats
        assert 'text_type_distribution' in stats
        assert 'sentiment_distribution' in stats
        assert stats['total_analyses'] == len(texts)


class TestIntelligentRecommendationEngine:
    """智能推荐引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.recommendation_engine = IntelligentRecommendationEngine()
    
    def test_engine_initialization(self):
        """测试推荐引擎初始化"""
        assert self.recommendation_engine is not None
        assert len(self.recommendation_engine.items) > 0  # 应该有初始化的项目
    
    def test_add_recommendation_item(self):
        """测试添加推荐项目"""
        item = RecommendationItem(
            item_id="test_item_001",
            title="测试推荐项目",
            description="这是一个测试用的推荐项目",
            category="test",
            features={"priority": 5, "complexity": 3}
        )
        
        success = self.recommendation_engine.add_item(item)
        assert success is True
        assert "test_item_001" in self.recommendation_engine.items
    
    def test_record_user_interaction(self):
        """测试记录用户交互"""
        success = self.recommendation_engine.record_interaction(
            user_id="user_001",
            item_id="decision_001",  # 使用初始化的项目
            interaction_type="like",
            rating=4.5
        )
        
        assert success is True
        assert "user_001" in self.recommendation_engine.user_interactions
        assert len(self.recommendation_engine.user_interactions["user_001"]) == 1
    
    def test_get_recommendations_new_user(self):
        """测试新用户推荐（基于内容）"""
        recommendations = self.recommendation_engine.get_recommendations(
            user_id="new_user",
            recommendation_type=RecommendationType.DECISION,
            top_k=3
        )
        
        assert len(recommendations) <= 3
        if recommendations:
            assert all(rec.item.category in ['decision'] for rec in recommendations)
            assert all(0 <= rec.score <= 1 for rec in recommendations)
            assert all(0 <= rec.confidence <= 1 for rec in recommendations)
    
    def test_get_recommendations_existing_user(self):
        """测试现有用户推荐（协同过滤）"""
        # 先记录一些用户交互
        interactions = [
            ("user_001", "decision_001", "like", 5.0),
            ("user_001", "training_001", "use", 4.0),
            ("user_001", "equipment_001", "view", None),
            ("user_002", "decision_001", "like", 4.5),
            ("user_002", "decision_002", "use", 3.5),
        ]
        
        for user_id, item_id, interaction_type, rating in interactions:
            self.recommendation_engine.record_interaction(user_id, item_id, interaction_type, rating)
        
        # 为user_001获取推荐
        recommendations = self.recommendation_engine.get_recommendations(
            user_id="user_001",
            recommendation_type=RecommendationType.DECISION,
            top_k=2
        )
        
        assert len(recommendations) <= 2
        if recommendations:
            assert all(rec.score > 0 for rec in recommendations)
    
    def test_get_similar_items(self):
        """测试获取相似项目"""
        similar_items = self.recommendation_engine.get_similar_items(
            item_id="decision_001",
            top_k=3
        )
        
        # 可能没有足够的相似项目，但不应该出错
        assert isinstance(similar_items, list)
        if similar_items:
            assert all(isinstance(similarity, float) for _, similarity in similar_items)
            assert all(0 <= similarity <= 1 for _, similarity in similar_items)
    
    def test_recommendation_statistics(self):
        """测试推荐统计"""
        # 记录一些交互和推荐
        self.recommendation_engine.record_interaction("user_001", "decision_001", "like")
        self.recommendation_engine.record_interaction("user_002", "training_001", "use")
        
        self.recommendation_engine.get_recommendations("user_001", RecommendationType.DECISION)
        
        stats = self.recommendation_engine.get_recommendation_statistics()
        
        assert 'total_items' in stats
        assert 'total_users' in stats
        assert 'total_interactions' in stats
        assert stats['total_items'] > 0
        assert stats['total_users'] >= 2
        assert stats['total_interactions'] >= 2
