"""
系统管理器人机分配功能测试

测试SystemManager对人机分配配置的管理功能
"""

import pytest
import tempfile
import os
import json
from unittest.mock import Mock, patch

from src.hmdm.core.system_manager import HMDMSystemManager, SystemStatus
from src.hmdm.allocation.allocation_config import (
    AllocationConfig, 
    AllocationMode, 
    OptimizationObjective
)


class TestSystemManagerAllocation:
    """系统管理器人机分配功能测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建系统管理器
        self.system_manager = HMDMSystemManager()
        
        # 修改配置以使用临时目录
        self.system_manager.config.data_directory = os.path.join(self.temp_dir, "data")
        self.system_manager.config.backup_directory = os.path.join(self.temp_dir, "backups")
        self.system_manager.config.temp_directory = os.path.join(self.temp_dir, "temp")
        self.system_manager.config.log_file = os.path.join(self.temp_dir, "test.log")
        
        # 只启用必要的模块
        self.system_manager.config.enabled_modules = [
            "knowledge_base",
            "human_machine_allocation"
        ]
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'system_manager'):
            self.system_manager.stop_system()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_allocation_config_loading(self):
        """测试默认allocation配置加载"""
        assert self.system_manager.config.allocation_config is not None
        assert isinstance(self.system_manager.config.allocation_config, AllocationConfig)
        
        # 验证默认配置值
        config = self.system_manager.config.allocation_config
        assert config.allocation_mode == AllocationMode.SEMI_AUTOMATIC
        assert config.optimization_objective == OptimizationObjective.BALANCED
        assert config.default_scheme_count == 5
        assert config.decision_threshold == 0.1
        assert config.confidence_threshold == 0.7
    
    def test_get_allocation_config(self):
        """测试获取allocation配置"""
        config = self.system_manager.get_allocation_config()
        assert config is not None
        assert isinstance(config, AllocationConfig)
        assert config == self.system_manager.config.allocation_config
    
    def test_update_allocation_config(self):
        """测试更新allocation配置"""
        # 创建新配置
        new_config = AllocationConfig()
        new_config.allocation_mode = AllocationMode.AUTOMATIC
        new_config.optimization_objective = OptimizationObjective.EFFICIENCY
        new_config.default_scheme_count = 8
        new_config.decision_threshold = 0.15
        
        # 更新配置
        success = self.system_manager.update_allocation_config(new_config)
        assert success
        
        # 验证配置已更新
        updated_config = self.system_manager.get_allocation_config()
        assert updated_config.allocation_mode == AllocationMode.AUTOMATIC
        assert updated_config.optimization_objective == OptimizationObjective.EFFICIENCY
        assert updated_config.default_scheme_count == 8
        assert updated_config.decision_threshold == 0.15
    
    def test_update_invalid_allocation_config(self):
        """测试更新无效的allocation配置"""
        # 创建无效配置
        invalid_config = AllocationConfig()
        invalid_config.decision_threshold = 1.5  # 超出有效范围
        
        # 尝试更新配置
        success = self.system_manager.update_allocation_config(invalid_config)
        assert not success
        
        # 验证原配置未被更改
        original_config = self.system_manager.get_allocation_config()
        assert original_config.decision_threshold == 0.1  # 默认值
    
    def test_save_allocation_config(self):
        """测试保存allocation配置"""
        # 创建配置文件路径
        config_file = os.path.join(self.temp_dir, "allocation_config.json")
        
        # 修改配置
        config = self.system_manager.get_allocation_config()
        config.allocation_mode = AllocationMode.MANUAL
        config.default_scheme_count = 7
        
        # 保存配置
        success = self.system_manager.save_allocation_config(config_file)
        assert success
        assert os.path.exists(config_file)
        
        # 验证保存的内容
        with open(config_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        assert saved_data['allocation_mode'] == 'manual'
        assert saved_data['default_scheme_count'] == 7
    
    def test_load_allocation_config_from_file(self):
        """测试从文件加载allocation配置"""
        # 创建配置文件
        config_file = os.path.join(self.temp_dir, "test_allocation_config.json")
        config_data = {
            'allocation_mode': 'automatic',
            'optimization_objective': 'reliability',
            'default_scheme_count': 6,
            'decision_threshold': 0.2
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f)
        
        # 创建新的系统管理器并指定配置文件
        new_system_manager = HMDMSystemManager()

        # 直接加载allocation配置
        from src.hmdm.allocation.allocation_config import load_allocation_config
        new_system_manager.config.allocation_config = load_allocation_config(config_file)

        # 获取配置
        new_config = new_system_manager.config
        
        # 验证配置已正确加载
        assert new_config.allocation_config.allocation_mode == AllocationMode.AUTOMATIC
        assert new_config.allocation_config.optimization_objective == OptimizationObjective.RELIABILITY
        assert new_config.allocation_config.default_scheme_count == 6
        assert new_config.allocation_config.decision_threshold == 0.2
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_allocation_system_config_integration(self, mock_knowledge_base):
        """测试allocation系统与配置的集成"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 修改allocation配置
        config = self.system_manager.get_allocation_config()
        config.default_scheme_count = 8
        config.decision_threshold = 0.25
        
        # 启动系统
        success = self.system_manager.start_system()
        assert success
        
        # 获取allocation系统实例
        allocation_system = self.system_manager.get_allocation_system()
        assert allocation_system is not None
        
        # 验证配置已传递给allocation系统
        assert hasattr(allocation_system, 'config')
        assert allocation_system.config['default_scheme_count'] == 8
        assert allocation_system.config['decision_threshold'] == 0.25
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_runtime_config_update(self, mock_knowledge_base):
        """测试运行时配置更新"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 启动系统
        self.system_manager.start_system()
        allocation_system = self.system_manager.get_allocation_system()
        assert allocation_system is not None
        
        # 运行时更新配置
        new_config = AllocationConfig()
        new_config.default_scheme_count = 10
        new_config.confidence_threshold = 0.8
        
        success = self.system_manager.update_allocation_config(new_config)
        assert success
        
        # 验证allocation系统的配置已更新
        assert allocation_system.config['default_scheme_count'] == 10
        assert allocation_system.config['confidence_threshold'] == 0.8
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = AllocationConfig()
        assert valid_config.validate()
        
        # 测试无效配置 - 权重总和不为1
        invalid_config = AllocationConfig()
        invalid_config.capability_weights = {
            "cognitive": 0.5,
            "physical": 0.3,
            "perceptual": 0.1
            # 总和为0.9，不等于1.0
        }
        assert not invalid_config.validate()
        
        # 测试无效配置 - 阈值超出范围
        invalid_config2 = AllocationConfig()
        invalid_config2.decision_threshold = 1.5
        assert not invalid_config2.validate()
    
    def test_config_serialization(self):
        """测试配置序列化和反序列化"""
        # 创建配置
        original_config = AllocationConfig()
        original_config.allocation_mode = AllocationMode.AUTOMATIC
        original_config.optimization_objective = OptimizationObjective.COST_EFFECTIVENESS
        original_config.default_scheme_count = 7
        
        # 序列化
        config_dict = original_config.to_dict()
        assert isinstance(config_dict, dict)
        assert config_dict['allocation_mode'] == 'automatic'
        assert config_dict['optimization_objective'] == 'cost_effectiveness'
        assert config_dict['default_scheme_count'] == 7
        
        # 反序列化
        restored_config = AllocationConfig.from_dict(config_dict)
        assert restored_config.allocation_mode == AllocationMode.AUTOMATIC
        assert restored_config.optimization_objective == OptimizationObjective.COST_EFFECTIVENESS
        assert restored_config.default_scheme_count == 7
    
    def test_config_weight_updates(self):
        """测试配置权重更新"""
        config = AllocationConfig()
        
        # 更新能力权重
        new_capability_weights = {
            "cognitive": 0.3,
            "physical": 0.2,
            "perceptual": 0.2,
            "decision_making": 0.2,
            "execution": 0.05,
            "coordination": 0.05
        }
        
        config.update_weights(capability_weights=new_capability_weights)
        assert config.capability_weights["cognitive"] == 0.3
        assert config.capability_weights["physical"] == 0.2
        
        # 更新评估权重
        new_evaluation_weights = {
            "task_completion": 0.3,
            "time_efficiency": 0.25,
            "resource_utilization": 0.15,
            "error_rate": 0.1,
            "coordination_overhead": 0.1,
            "adaptability": 0.1
        }
        
        config.update_weights(evaluation_weights=new_evaluation_weights)
        assert config.evaluation_weights["task_completion"] == 0.3
        assert config.evaluation_weights["time_efficiency"] == 0.25
    
    def test_config_constraints_and_preferences(self):
        """测试配置约束和偏好设置"""
        config = AllocationConfig()
        
        # 更新约束条件
        new_constraints = {
            'max_human_workload': 0.7,
            'min_machine_utilization': 0.4
        }
        config.update_constraints(new_constraints)
        assert config.constraints['max_human_workload'] == 0.7
        assert config.constraints['min_machine_utilization'] == 0.4
        
        # 更新偏好设置
        new_preferences = {
            'prefer_human_decision': False,
            'allow_full_automation': True
        }
        config.update_preferences(new_preferences)
        assert config.preferences['prefer_human_decision'] == False
        assert config.preferences['allow_full_automation'] == True


if __name__ == '__main__':
    pytest.main([__file__])
