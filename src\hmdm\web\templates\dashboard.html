{% extends "base.html" %}

{% block title %}系统仪表板 - HMDM{% endblock %}

{% block page_title %}系统仪表板{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-success" id="start-system-btn">
        <i class="fas fa-play"></i> 启动系统
    </button>
    <button type="button" class="btn btn-danger" id="stop-system-btn">
        <i class="fas fa-stop"></i> 停止系统
    </button>
    <button type="button" class="btn btn-primary" id="refresh-btn">
        <i class="fas fa-sync-alt"></i> 刷新
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card status-card status-running">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">系统状态</h6>
                        <h4 class="mb-0" id="system-status">{{ system_status.system_status or '未知' }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">运行时间</h6>
                        <h4 class="mb-0" id="uptime">{{ "%.0f"|format(system_status.statistics.uptime or 0) }}秒</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">活跃模块</h6>
                        <h4 class="mb-0" id="active-modules">
                            {% set running_modules = system_status.modules.values() | selectattr('status', 'equalto', '运行中') | list %}
                            {{ running_modules | length }} / {{ system_status.modules | length }}
                        </h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cubes fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">处理请求</h6>
                        <h4 class="mb-0" id="processed-requests">{{ system_status.statistics.processed_requests or 0 }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块状态概览 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cubes"></i> 模块状态概览
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="modules-overview">
                    {% for module_name, module_info in system_status.modules.items() %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="module-card card h-100">
                            <div class="card-body text-center">
                                <div class="mb-2">
                                    {% if module_info.status == '运行中' %}
                                        <i class="fas fa-check-circle fa-2x text-success"></i>
                                    {% elif module_info.status == '错误' %}
                                        <i class="fas fa-exclamation-circle fa-2x text-danger"></i>
                                    {% else %}
                                        <i class="fas fa-pause-circle fa-2x text-secondary"></i>
                                    {% endif %}
                                </div>
                                <h6 class="card-title">{{ module_name }}</h6>
                                <span class="module-status-badge badge 
                                    {% if module_info.status == '运行中' %}bg-success
                                    {% elif module_info.status == '错误' %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {{ module_info.status }}
                                </span>
                                {% if module_info.start_time %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        启动时间: {{ module_info.start_time[:19] }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统性能图表 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area"></i> 系统负载
                </h5>
            </div>
            <div class="card-body">
                <canvas id="system-load-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> 模块状态分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="module-status-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt"></i> 实时系统日志
                </h5>
                <button class="btn btn-sm btn-outline-primary" id="clear-logs-btn">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="log-container" style="height: 300px; overflow-y: auto;">
                    <div class="p-3 text-muted text-center">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载日志...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let systemLoadChart;
    let moduleStatusChart;
    let logContainer;
    
    // 初始化图表
    function initCharts() {
        // 系统负载图表
        const loadCtx = document.getElementById('system-load-chart').getContext('2d');
        systemLoadChart = new Chart(loadCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU使用率',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: '内存使用率',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
        
        // 模块状态分布图表
        const statusCtx = document.getElementById('module-status-chart').getContext('2d');
        moduleStatusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['运行中', '已停止', '错误'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(108, 117, 125, 0.8)',
                        'rgba(220, 53, 69, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(108, 117, 125, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // 更新系统状态
    function updateSystemStatus(data) {
        document.getElementById('system-status').textContent = data.system_status || '未知';
        
        const uptime = data.statistics?.uptime || 0;
        document.getElementById('uptime').textContent = formatUptime(uptime);
        
        const modules = data.modules || {};
        const totalModules = Object.keys(modules).length;
        const runningModules = Object.values(modules).filter(m => m.status === '运行中').length;
        document.getElementById('active-modules').textContent = `${runningModules} / ${totalModules}`;
        
        document.getElementById('processed-requests').textContent = data.statistics?.processed_requests || 0;
        
        // 更新模块状态分布图表
        updateModuleStatusChart(modules);
    }
    
    // 更新模块状态图表
    function updateModuleStatusChart(modules) {
        const statusCounts = { '运行中': 0, '已停止': 0, '错误': 0 };
        
        Object.values(modules).forEach(module => {
            const status = module.status;
            if (status === '运行中') {
                statusCounts['运行中']++;
            } else if (status === '错误') {
                statusCounts['错误']++;
            } else {
                statusCounts['已停止']++;
            }
        });
        
        moduleStatusChart.data.datasets[0].data = [
            statusCounts['运行中'],
            statusCounts['已停止'],
            statusCounts['错误']
        ];
        moduleStatusChart.update();
    }
    
    // 更新系统负载图表
    function updateSystemLoadChart() {
        const now = new Date().toLocaleTimeString();
        const cpuUsage = Math.random() * 100; // 模拟CPU使用率
        const memUsage = Math.random() * 100; // 模拟内存使用率
        
        // 保持最近20个数据点
        if (systemLoadChart.data.labels.length >= 20) {
            systemLoadChart.data.labels.shift();
            systemLoadChart.data.datasets[0].data.shift();
            systemLoadChart.data.datasets[1].data.shift();
        }
        
        systemLoadChart.data.labels.push(now);
        systemLoadChart.data.datasets[0].data.push(cpuUsage);
        systemLoadChart.data.datasets[1].data.push(memUsage);
        systemLoadChart.update();
    }
    
    // 格式化运行时间
    function formatUptime(seconds) {
        if (seconds < 60) {
            return Math.floor(seconds) + '秒';
        } else if (seconds < 3600) {
            return Math.floor(seconds / 60) + '分钟';
        } else if (seconds < 86400) {
            return Math.floor(seconds / 3600) + '小时';
        } else {
            return Math.floor(seconds / 86400) + '天';
        }
    }
    
    // 加载日志
    function loadLogs() {
        makeRequest('/api/logs')
            .then(response => {
                if (response.success) {
                    displayLogs(response.data);
                }
            })
            .catch(error => {
                console.error('加载日志失败:', error);
            });
    }
    
    // 显示日志
    function displayLogs(logs) {
        const container = document.getElementById('log-container');
        container.innerHTML = '';
        
        logs.forEach(log => {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">${log.timestamp}</span>
                <span class="log-level-${log.level}">[${log.level}]</span>
                <span class="log-logger">${log.logger}</span>
                <span class="log-message">${log.message}</span>
            `;
            container.appendChild(logEntry);
        });
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }
    
    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        logContainer = document.getElementById('log-container');
        
        // 初始化图表
        initCharts();
        
        // 加载初始日志
        loadLogs();
        
        // 定期更新系统负载图表
        setInterval(updateSystemLoadChart, 5000);
        
        // 按钮事件
        document.getElementById('start-system-btn').addEventListener('click', function() {
            makeRequest('/api/system/start', 'POST')
                .then(response => {
                    if (response.success) {
                        showNotification(response.message, 'success');
                    } else {
                        showNotification(response.error || '启动失败', 'danger');
                    }
                });
        });
        
        document.getElementById('stop-system-btn').addEventListener('click', function() {
            if (confirm('确定要停止系统吗？')) {
                makeRequest('/api/system/stop', 'POST')
                    .then(response => {
                        if (response.success) {
                            showNotification(response.message, 'success');
                        } else {
                            showNotification(response.error || '停止失败', 'danger');
                        }
                    });
            }
        });
        
        document.getElementById('refresh-btn').addEventListener('click', function() {
            location.reload();
        });
        
        document.getElementById('clear-logs-btn').addEventListener('click', function() {
            document.getElementById('log-container').innerHTML = '';
        });
    });
    
    // Socket.IO事件监听
    socket.on('system_status_update', function(data) {
        updateSystemStatus(data.data);
    });

    // 监听模块数据更新
    socket.on('module_data_update', function(data) {
        console.log('模块数据更新:', data);
    });
</script>
{% endblock %}
