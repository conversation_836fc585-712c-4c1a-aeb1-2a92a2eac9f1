# HMDM系统用户操作手册

## 手册概述

本手册为HMDM（Human-Machine Decision Making）军事综合决策支持系统的用户操作指南，详细介绍系统的功能特性、操作流程和使用方法，帮助用户快速掌握系统的使用技巧。

### 目标用户
- **军事指挥官**：高级决策制定者
- **作战参谋**：作战计划制定人员  
- **系统操作员**：系统日常操作人员
- **技术维护人员**：系统维护和管理人员

### 手册结构
- 系统概述和功能介绍
- 基本操作和界面说明
- 核心功能使用指南
- 高级功能和配置管理
- 常见问题和故障排除

## 系统概述

### 系统简介
HMDM系统是一个专为军事指挥决策设计的智能化系统，基于人机功能分配理论，提供科学化的决策支持服务。系统集成了任务分析、决策支持、效能评估等核心功能。

### 核心功能
1. **智能人机功能分配**：基于任务特性的智能分配
2. **多目标模糊决策**：复杂环境下的多准则决策
3. **任务分析与分解**：基于HTA和GOMS模型的分析
4. **协作效能评估**：人机协作效果的量化评估
5. **决策支持服务**：全流程的决策支持

### 系统特色
- **智能化**：先进的算法和模型
- **模块化**：清晰的功能模块划分
- **易用性**：直观的用户界面
- **安全性**：军用级安全保障
- **高性能**：快速的响应时间

## 系统访问

### 访问方式
- **Web界面**：通过浏览器访问 `http://localhost:5000`
- **API接口**：程序化访问 `http://localhost:5000/api`
- **命令行工具**：使用 `hmdm` 命令行工具

### 浏览器要求
- **Chrome** 90+ （推荐）
- **Firefox** 88+
- **Edge** 90+
- **Safari** 14+

### 登录系统
1. 打开浏览器，访问系统地址
2. 输入用户名和密码（如果启用认证）
3. 点击"登录"按钮进入系统
4. 首次使用建议查看系统概览

## 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    HMDM系统主界面                            │
├─────────────────────────────────────────────────────────────┤
│  导航栏                                                     │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────────┐     │
│  │  首页   │人机分配 │配置管理 │API文档  │  系统日志   │     │
│  └─────────┴─────────┴─────────┴─────────┴─────────────┘     │
├─────────────────────────────────────────────────────────────┤
│  内容区域                                                   │
│  ┌─────────────────────────────────────────────────────┐     │
│  │                                                     │     │
│  │              页面内容显示区域                        │     │
│  │                                                     │     │
│  └─────────────────────────────────────────────────────┘     │
├─────────────────────────────────────────────────────────────┤
│  状态栏                                                     │
│  系统状态：运行中 | 用户：admin | 时间：2025-09-08 16:45    │
└─────────────────────────────────────────────────────────────┘
```

### 导航菜单
- **首页**：系统概览和快速操作
- **人机分配**：核心的人机功能分配功能
- **配置管理**：系统配置和参数管理
- **API文档**：API接口文档和在线测试
- **系统日志**：系统运行日志查看

### 状态指示
- **系统状态**：显示系统运行状态
- **用户信息**：当前登录用户
- **时间信息**：当前系统时间
- **性能指标**：CPU和内存使用情况

## 核心功能使用

### 1. 人机功能分配

#### 1.1 基本分配流程
1. **进入分配页面**：点击导航栏"人机分配"
2. **输入任务信息**：填写任务层次结构
3. **设置约束条件**：配置分配约束
4. **设置用户偏好**：调整偏好参数
5. **执行分配**：点击"开始分配"按钮
6. **查看结果**：分析分配结果和建议

#### 1.2 任务信息输入
**任务层次结构输入**：
```json
{
  "id": "task_001",
  "name": "作战任务",
  "description": "执行作战任务的人机分配",
  "subtasks": [
    {
      "id": "subtask_001",
      "name": "态势感知",
      "complexity": "MEDIUM",
      "time_constraint": 300,
      "required_capabilities": ["perception", "analysis"]
    }
  ]
}
```

**操作步骤**：
1. 在"任务信息"区域点击"添加任务"
2. 填写任务ID、名称和描述
3. 设置任务复杂度和时间约束
4. 添加所需能力要求
5. 如有子任务，重复上述步骤

#### 1.3 约束条件设置
**可设置的约束**：
- **最大人类任务数**：人类可承担的最大任务数量
- **最小机器任务数**：机器必须承担的最小任务数量
- **时间限制**：总体时间约束
- **资源限制**：可用资源约束

**设置方法**：
1. 在"约束条件"区域调整滑块或输入数值
2. 根据实际情况设置合理的约束范围
3. 系统会自动验证约束的合理性

#### 1.4 用户偏好配置
**偏好选项**：
- **偏好人类决策**：是否倾向于人类进行关键决策
- **效率优先级**：对效率的重视程度（0-1）
- **可靠性优先级**：对可靠性的重视程度（0-1）
- **成本优先级**：对成本的重视程度（0-1）

**配置方法**：
1. 使用滑块调整各项优先级权重
2. 权重总和建议保持在合理范围内
3. 可以保存常用的偏好配置

#### 1.5 结果分析
**分配结果包含**：
- **推荐方案**：最优的人机分配方案
- **效能指标**：各项效能评估结果
- **备选方案**：其他可行的分配方案
- **决策置信度**：对推荐方案的置信程度
- **改进建议**：进一步优化的建议

**结果解读**：
1. **查看推荐方案**：重点关注任务分配和分配理由
2. **分析效能指标**：评估方案的综合效能
3. **比较备选方案**：了解其他可能的选择
4. **参考改进建议**：考虑进一步的优化措施

### 2. 配置管理

#### 2.1 系统配置
**配置项目**：
- **系统参数**：基本系统设置
- **算法参数**：决策算法的参数
- **界面设置**：用户界面偏好
- **安全设置**：安全相关配置

**配置方法**：
1. 进入"配置管理"页面
2. 选择相应的配置选项卡
3. 修改需要调整的参数
4. 点击"保存配置"按钮
5. 系统会自动验证配置的有效性

#### 2.2 配置档案管理
**档案功能**：
- **创建档案**：保存当前配置为档案
- **加载档案**：应用已保存的配置档案
- **删除档案**：删除不需要的配置档案
- **导入导出**：配置档案的导入和导出

**操作步骤**：
1. **创建档案**：
   - 调整好配置参数
   - 点击"保存为档案"
   - 输入档案名称和描述
   - 确认保存

2. **加载档案**：
   - 在档案列表中选择目标档案
   - 点击"应用档案"
   - 确认应用操作

3. **导出档案**：
   - 选择要导出的档案
   - 点击"导出"按钮
   - 选择保存位置

### 3. 系统监控

#### 3.1 系统状态监控
**监控内容**：
- **系统运行状态**：正常/异常/维护中
- **模块状态**：各功能模块的运行状态
- **性能指标**：CPU、内存、响应时间
- **用户活动**：当前在线用户和活动

**查看方法**：
1. 在首页查看系统概览
2. 点击"系统状态"查看详细信息
3. 使用刷新按钮获取最新状态
4. 设置自动刷新间隔

#### 3.2 日志查看
**日志类型**：
- **系统日志**：系统运行日志
- **操作日志**：用户操作记录
- **错误日志**：系统错误信息
- **安全日志**：安全相关事件

**查看操作**：
1. 进入"系统日志"页面
2. 选择日志类型和时间范围
3. 使用搜索功能查找特定日志
4. 可以导出日志文件

## 高级功能

### 1. 批量操作
**支持的批量操作**：
- **批量任务分配**：一次处理多个任务
- **批量配置应用**：批量应用配置更改
- **批量数据导入**：批量导入任务数据

**操作方法**：
1. 准备批量数据文件（Excel或JSON格式）
2. 使用"批量导入"功能上传文件
3. 系统验证数据格式和内容
4. 确认后执行批量操作

### 2. 数据导入导出
**支持的数据格式**：
- **Excel格式**：.xlsx, .xls
- **JSON格式**：.json
- **CSV格式**：.csv

**导入步骤**：
1. 点击"数据导入"按钮
2. 选择数据文件
3. 选择导入类型和选项
4. 预览导入数据
5. 确认导入

**导出步骤**：
1. 选择要导出的数据
2. 选择导出格式
3. 设置导出选项
4. 点击"导出"按钮
5. 下载导出文件

### 3. 自定义报告
**报告类型**：
- **分配结果报告**：详细的分配分析报告
- **效能评估报告**：协作效能评估报告
- **系统使用报告**：系统使用统计报告

**生成报告**：
1. 进入"报告生成"功能
2. 选择报告类型和时间范围
3. 设置报告参数和格式
4. 点击"生成报告"
5. 下载或查看报告

## 常见问题

### 1. 系统访问问题

**Q: 无法访问系统页面**
A: 检查以下几点：
- 确认系统服务已启动
- 检查网络连接是否正常
- 验证访问地址是否正确
- 清除浏览器缓存后重试

**Q: 页面加载缓慢**
A: 可能的解决方案：
- 检查网络带宽是否充足
- 清理浏览器缓存和Cookie
- 关闭不必要的浏览器标签页
- 联系管理员检查服务器性能

### 2. 功能使用问题

**Q: 分配结果不合理**
A: 检查以下设置：
- 任务信息是否准确完整
- 约束条件是否设置合理
- 用户偏好是否符合实际需求
- 尝试调整参数后重新分配

**Q: 配置保存失败**
A: 可能的原因：
- 配置参数超出有效范围
- 权限不足无法保存配置
- 系统存储空间不足
- 配置文件被锁定或损坏

### 3. 性能问题

**Q: 系统响应缓慢**
A: 优化建议：
- 减少同时处理的任务数量
- 简化任务层次结构
- 调整系统配置参数
- 定期清理系统缓存

**Q: 内存使用过高**
A: 解决方法：
- 关闭不使用的功能模块
- 清理历史数据和日志
- 重启系统服务
- 增加系统内存配置

## 故障排除

### 1. 常见错误信息

**"任务分配失败"**
- 检查任务数据格式是否正确
- 验证约束条件是否可满足
- 确认系统资源是否充足

**"配置验证失败"**
- 检查配置参数是否在有效范围内
- 验证配置文件格式是否正确
- 确认配置权限是否足够

**"系统内部错误"**
- 查看系统日志获取详细错误信息
- 重启相关服务模块
- 联系技术支持人员

### 2. 日志分析
**查看错误日志**：
1. 进入"系统日志"页面
2. 筛选"错误"级别的日志
3. 查看错误发生的时间和详细信息
4. 根据错误信息采取相应措施

### 3. 联系支持
**技术支持渠道**：
- **在线帮助**：系统内置帮助文档
- **技术支持邮箱**：<EMAIL>
- **支持热线**：400-HMDM-HELP
- **用户社区**：HMDM用户交流论坛

## 最佳实践

### 1. 系统使用建议
- **定期备份**：定期备份重要配置和数据
- **参数调优**：根据实际使用情况调整系统参数
- **权限管理**：合理设置用户权限和访问控制
- **监控维护**：定期检查系统状态和性能指标

### 2. 数据管理建议
- **数据质量**：确保输入数据的准确性和完整性
- **数据备份**：定期备份重要的任务和配置数据
- **数据清理**：定期清理过期和无用的数据
- **数据安全**：注意保护敏感数据的安全

### 3. 性能优化建议
- **合理配置**：根据硬件条件合理配置系统参数
- **资源监控**：定期监控系统资源使用情况
- **负载均衡**：在高负载情况下考虑负载均衡
- **定期维护**：定期进行系统维护和优化

---

**手册版本**：v1.0.0  
**最后更新**：2025年9月8日  
**编写人员**：HMDM用户体验团队  
**审核状态**：已审核

*如有疑问或建议，请联系技术支持团队。*
