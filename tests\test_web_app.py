"""
Web应用测试

测试HMDM Web管理界面的功能
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch
from src.hmdm.web.app import HMDMWebApp, create_app
from src.hmdm.core.system_manager import HMDMSystemManager, SystemConfig


class TestHMDMWebApp:
    """HMDM Web应用测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        config_data = {
            "system_name": "测试HMDM系统",
            "version": "1.0.0-test",
            "environment": "testing",
            "log_level": "DEBUG",
            "enabled_modules": ["knowledge_base", "situation_awareness"]
        }
        json.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        # 创建系统管理器
        self.system_manager = HMDMSystemManager(self.temp_config.name)
        
        # 创建Web应用
        self.web_app = HMDMWebApp(self.system_manager, host="127.0.0.1", port=5001)
        self.client = self.web_app.app.test_client()
        self.web_app.app.config['TESTING'] = True
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'system_manager'):
            self.system_manager.stop_system()
        
        if hasattr(self, 'web_app'):
            self.web_app.stop()
        
        # 清理临时文件
        if os.path.exists(self.temp_config.name):
            os.unlink(self.temp_config.name)
    
    def test_web_app_initialization(self):
        """测试Web应用初始化"""
        assert self.web_app.system_manager is not None
        assert self.web_app.host == "127.0.0.1"
        assert self.web_app.port == 5001
        assert self.web_app.app is not None
        assert self.web_app.socketio is not None
    
    def test_index_route(self):
        """测试主页路由"""
        response = self.client.get('/')
        assert response.status_code == 200
        assert b'HMDM' in response.data
    
    def test_dashboard_route(self):
        """测试仪表板路由"""
        response = self.client.get('/dashboard')
        assert response.status_code == 200
        assert b'dashboard' in response.data or '仪表板'.encode('utf-8') in response.data
    
    def test_modules_route(self):
        """测试模块管理路由"""
        response = self.client.get('/modules')
        assert response.status_code == 200
        assert b'modules' in response.data or '模块'.encode('utf-8') in response.data
    
    def test_config_route(self):
        """测试配置管理路由"""
        response = self.client.get('/config')
        assert response.status_code == 200
        assert b'config' in response.data or '配置'.encode('utf-8') in response.data
    
    def test_logs_route(self):
        """测试日志查看路由"""
        response = self.client.get('/logs')
        assert response.status_code == 200
        assert b'logs' in response.data or '日志'.encode('utf-8') in response.data
    
    def test_api_system_status(self):
        """测试系统状态API"""
        response = self.client.get('/api/system/status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert data['success'] is True
        assert 'data' in data
        
        system_data = data['data']
        assert 'system_status' in system_data
        assert 'modules' in system_data
        assert 'statistics' in system_data
    
    def test_api_system_start(self):
        """测试系统启动API"""
        response = self.client.post('/api/system/start')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'message' in data
    
    def test_api_system_stop(self):
        """测试系统停止API"""
        # 先启动系统
        self.system_manager.start_system()
        
        response = self.client.post('/api/system/stop')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'message' in data
    
    def test_api_module_restart(self):
        """测试模块重启API"""
        # 先启动系统
        self.system_manager.start_system()
        
        response = self.client.post('/api/module/knowledge_base/restart')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'message' in data
    
    def test_api_config_update(self):
        """测试配置更新API"""
        config_update = {
            "log_level": "ERROR",
            "max_concurrent_tasks": 15
        }
        
        response = self.client.post('/api/config/update',
                                  data=json.dumps(config_update),
                                  content_type='application/json')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'message' in data
    
    def test_api_config_update_invalid_data(self):
        """测试无效配置更新"""
        response = self.client.post('/api/config/update',
                                  data='invalid json',
                                  content_type='application/json')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_api_module_data(self):
        """测试模块数据API"""
        # 先启动系统
        self.system_manager.start_system()
        
        response = self.client.get('/api/modules/knowledge_base/data')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'data' in data
    
    def test_api_module_data_not_found(self):
        """测试不存在的模块数据API"""
        response = self.client.get('/api/modules/non_existent_module/data')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_api_logs(self):
        """测试日志API"""
        response = self.client.get('/api/logs')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'success' in data
        assert 'data' in data
        assert isinstance(data['data'], list)
    
    def test_get_module_data_situation_awareness(self):
        """测试态势感知模块数据获取"""
        # 创建模拟的态势感知模块实例
        mock_instance = Mock()
        mock_instance.entities = {'entity1': {}, 'entity2': {}}
        mock_instance.assessments = [{'assessment1': {}}, {'assessment2': {}}]
        
        data = self.web_app._get_module_data('situation_awareness', mock_instance)
        
        assert 'entities_count' in data
        assert 'assessments_count' in data
        assert 'last_update' in data
        assert data['entities_count'] == 2
        assert data['assessments_count'] == 2
    
    def test_get_module_data_communication(self):
        """测试通信模块数据获取"""
        mock_instance = Mock()
        mock_instance.units = {'unit1': {}, 'unit2': {}, 'unit3': {}}
        mock_instance.message_count = 150
        mock_instance.channels = {'ch1': {}, 'ch2': {}}
        
        data = self.web_app._get_module_data('communication', mock_instance)
        
        assert 'units_count' in data
        assert 'messages_sent' in data
        assert 'active_channels' in data
        assert data['units_count'] == 3
        assert data['messages_sent'] == 150
        assert data['active_channels'] == 2
    
    def test_get_module_data_knowledge_base(self):
        """测试知识库模块数据获取"""
        mock_instance = Mock()
        mock_instance.get_knowledge_statistics.return_value = {
            'total_items': 100,
            'total_rules': 50,
            'average_confidence': 0.85
        }
        
        data = self.web_app._get_module_data('knowledge_base', mock_instance)
        
        assert 'knowledge_items' in data
        assert 'rules_count' in data
        assert 'average_confidence' in data
        assert data['knowledge_items'] == 100
        assert data['rules_count'] == 50
        assert data['average_confidence'] == 0.85
    
    def test_get_module_data_training(self):
        """测试训练模块数据获取"""
        mock_instance = Mock()
        mock_instance.training_objectives = {'obj1': {}, 'obj2': {}}
        mock_instance.training_scenarios = {'sc1': {}, 'sc2': {}, 'sc3': {}}
        mock_instance.training_results = [{'result1': {}}, {'result2': {}}]
        
        data = self.web_app._get_module_data('training', mock_instance)
        
        assert 'objectives_count' in data
        assert 'scenarios_count' in data
        assert 'results_count' in data
        assert data['objectives_count'] == 2
        assert data['scenarios_count'] == 3
        assert data['results_count'] == 2
    
    def test_get_module_data_simulation(self):
        """测试仿真模块数据获取"""
        mock_instance = Mock()
        mock_instance.current_scenario = 'test_scenario'
        mock_instance.simulation_results = [{'result1': {}}, {'result2': {}}]
        mock_instance.is_running = True
        
        data = self.web_app._get_module_data('simulation', mock_instance)
        
        assert 'current_scenario' in data
        assert 'simulation_results' in data
        assert 'is_running' in data
        assert data['current_scenario'] == 'test_scenario'
        assert data['simulation_results'] == 2
        assert data['is_running'] is True
    
    def test_get_module_data_unknown_module(self):
        """测试未知模块数据获取"""
        mock_instance = Mock()
        
        data = self.web_app._get_module_data('unknown_module', mock_instance)
        
        assert 'status' in data
        assert 'last_update' in data
        assert data['status'] == 'running'
    
    def test_get_module_data_error_handling(self):
        """测试模块数据获取错误处理"""
        mock_instance = Mock()
        mock_instance.get_knowledge_statistics.side_effect = Exception("测试错误")
        
        data = self.web_app._get_module_data('knowledge_base', mock_instance)
        
        assert 'error' in data
        assert 'last_update' in data
        assert '测试错误' in data['error']
    
    @patch('builtins.open')
    def test_read_recent_logs_success(self, mock_open):
        """测试成功读取日志"""
        mock_file_content = [
            "2024-01-01 10:00:00 - test_logger - INFO - 测试日志消息1\n",
            "2024-01-01 10:01:00 - test_logger - ERROR - 测试错误消息\n",
            "2024-01-01 10:02:00 - test_logger - WARNING - 测试警告消息\n"
        ]
        
        mock_open.return_value.__enter__.return_value.readlines.return_value = mock_file_content
        
        logs = self.web_app._read_recent_logs(10)
        
        assert len(logs) == 3
        assert logs[0]['level'] == 'INFO'
        assert logs[1]['level'] == 'ERROR'
        assert logs[2]['level'] == 'WARNING'
        assert '测试日志消息1' in logs[0]['message']
    
    @patch('builtins.open')
    def test_read_recent_logs_file_not_found(self, mock_open):
        """测试日志文件不存在"""
        mock_open.side_effect = FileNotFoundError("文件不存在")
        
        logs = self.web_app._read_recent_logs(10)
        
        assert len(logs) == 1
        assert logs[0]['level'] == 'WARNING'
        assert '日志文件不存在' in logs[0]['message']
    
    def test_create_app_function(self):
        """测试create_app函数"""
        app = create_app()
        assert isinstance(app, HMDMWebApp)
        assert app.system_manager is not None
        
        # 测试传入自定义系统管理器
        custom_manager = HMDMSystemManager()
        app_with_manager = create_app(custom_manager, host="0.0.0.0", port=8080)
        assert app_with_manager.system_manager is custom_manager
        assert app_with_manager.host == "0.0.0.0"
        assert app_with_manager.port == 8080
