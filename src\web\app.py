"""
HMDM Web应用主程序

提供人机功能分配模型系统的Web界面
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import json
import io
import sys
from pathlib import Path
from typing import Dict, Any, List
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from hmdm.task_analysis import HierarchicalTaskAnalyzer, GomsAnalyzer
from hmdm.decision import FuzzyDecisionEngine
from hmdm.evaluation import SchemeEvaluator
from hmdm.models import (
    Task, TaskType, Alternative, EvaluationScheme, DecisionMatrix
)
from hmdm.utils import DataLoader, get_logger, setup_logging

# 初始化Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 设置日志
setup_logging()
logger = get_logger("hmdm.web")

# 全局变量存储当前会话数据
session_data = {
    "task_hierarchy": None,
    "evaluation_scheme": None,
    "alternatives": [],
    "evaluation_results": [],
    "decision_results": {}
}


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/task/create', methods=['POST'])
def create_task():
    """创建任务分解结构"""
    try:
        data = request.get_json()
        scenario = data.get('scenario', '态势分析')
        
        # 创建任务分析器
        analyzer = HierarchicalTaskAnalyzer()
        
        # 创建根任务
        root_task = Task(
            name=f"{scenario}任务",
            description=f"执行{scenario}相关的任务",
            task_type=TaskType.MISSION_TASK
        )
        
        # 创建任务层次结构
        hierarchy = analyzer.create_task_hierarchy(root_task)
        
        # 自动分解任务
        subtasks = analyzer.auto_decompose_by_scenario(root_task.id, scenario)
        
        # 保存到会话
        session_data["task_hierarchy"] = hierarchy
        
        # 返回结果
        return jsonify({
            "success": True,
            "data": {
                "hierarchy": analyzer.export_hierarchy(),
                "task_count": len(hierarchy.tasks),
                "scenario": scenario
            }
        })
        
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500


@app.route('/api/evaluation/scheme/create', methods=['POST'])
def create_evaluation_scheme():
    """创建评估方案"""
    try:
        data = request.get_json()
        scheme_type = data.get('type', 'default')
        
        evaluator = SchemeEvaluator()
        
        if scheme_type == 'default':
            scheme = evaluator.create_default_evaluation_scheme()
        else:
            # 自定义方案
            scheme = EvaluationScheme(
                name=data.get('name', '自定义评估方案'),
                description=data.get('description', '')
            )
            
            # 添加指标（这里简化处理，实际应用中需要更复杂的逻辑）
            for indicator_data in data.get('indicators', []):
                from hmdm.models.evaluation_models import IndicatorDefinition, IndicatorType
                
                indicator = IndicatorDefinition(
                    name=indicator_data['name'],
                    description=indicator_data.get('description', ''),
                    indicator_type=IndicatorType(indicator_data['type']),
                    min_value=indicator_data.get('min_value', 0.0),
                    max_value=indicator_data.get('max_value', 1.0),
                    is_benefit=indicator_data.get('is_benefit', True)
                )
                
                weight = indicator_data.get('weight', 0.1)
                scheme.add_indicator(indicator, weight)
            
            scheme.normalize_weights()
        
        # 保存到会话
        session_data["evaluation_scheme"] = scheme
        
        return jsonify({
            "success": True,
            "data": {
                "scheme": scheme.to_dict(),
                "indicator_count": len(scheme.indicators)
            }
        })
        
    except Exception as e:
        logger.error(f"创建评估方案失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/alternatives/add', methods=['POST'])
def add_alternatives():
    """添加备选方案"""
    try:
        data = request.get_json()
        alternatives_data = data.get('alternatives', [])
        
        alternatives = []
        for alt_data in alternatives_data:
            alternative = Alternative(
                name=alt_data['name'],
                description=alt_data.get('description', '')
            )
            
            # 添加属性
            for key, value in alt_data.get('attributes', {}).items():
                alternative.attributes[key] = value
            
            alternatives.append(alternative)
        
        # 保存到会话
        session_data["alternatives"] = alternatives
        
        return jsonify({
            "success": True,
            "data": {
                "alternatives": [alt.to_dict() for alt in alternatives],
                "count": len(alternatives)
            }
        })
        
    except Exception as e:
        logger.error(f"添加备选方案失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/evaluation/run', methods=['POST'])
def run_evaluation():
    """执行方案评估"""
    try:
        data = request.get_json()
        method = data.get('method', 'WRDM')
        
        # 检查必要数据
        if not session_data["alternatives"]:
            return jsonify({
                "success": False,
                "error": "没有备选方案"
            }), 400
        
        if not session_data["evaluation_scheme"]:
            return jsonify({
                "success": False,
                "error": "没有评估方案"
            }), 400
        
        # 准备数据源
        data_sources = {}
        for alt in session_data["alternatives"]:
            data_sources[alt.id] = alt.attributes
        
        # 执行评估
        evaluator = SchemeEvaluator()
        recommended_alt, recommended_eval, report = evaluator.recommend_best_scheme(
            session_data["alternatives"],
            session_data["evaluation_scheme"],
            data_sources,
            method
        )
        
        # 保存结果
        session_data["evaluation_results"] = report["all_evaluations"]
        session_data["decision_results"][method] = report["decision_result"]
        
        return jsonify({
            "success": True,
            "data": {
                "recommended_alternative": recommended_alt.to_dict(),
                "evaluation_result": recommended_eval.to_dict(),
                "report": report,
                "method": method
            }
        })
        
    except Exception as e:
        logger.error(f"执行评估失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500


@app.route('/api/comparison/run', methods=['POST'])
def run_comparison():
    """执行方案比较"""
    try:
        # 检查必要数据
        if len(session_data["alternatives"]) < 2:
            return jsonify({
                "success": False,
                "error": "至少需要2个备选方案进行比较"
            }), 400
        
        if not session_data["evaluation_scheme"]:
            return jsonify({
                "success": False,
                "error": "没有评估方案"
            }), 400
        
        # 准备数据源
        data_sources = {}
        for alt in session_data["alternatives"]:
            data_sources[alt.id] = alt.attributes
        
        # 执行比较
        evaluator = SchemeEvaluator()
        comparison_report = evaluator.compare_schemes(
            session_data["alternatives"],
            session_data["evaluation_scheme"],
            data_sources
        )
        
        return jsonify({
            "success": True,
            "data": comparison_report
        })
        
    except Exception as e:
        logger.error(f"执行比较失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/data/export', methods=['POST'])
def export_data():
    """导出数据"""
    try:
        data = request.get_json()
        export_type = data.get('type', 'json')
        
        export_data = {
            "task_hierarchy": session_data["task_hierarchy"].to_dict() if session_data["task_hierarchy"] else None,
            "evaluation_scheme": session_data["evaluation_scheme"].to_dict() if session_data["evaluation_scheme"] else None,
            "alternatives": [alt.to_dict() for alt in session_data["alternatives"]],
            "evaluation_results": session_data["evaluation_results"],
            "decision_results": session_data["decision_results"]
        }
        
        if export_type == 'json':
            # 创建JSON文件
            json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
            buffer = io.BytesIO(json_str.encode('utf-8'))
            
            return send_file(
                buffer,
                as_attachment=True,
                download_name='hmdm_export.json',
                mimetype='application/json'
            )
        
        else:
            return jsonify({
                "success": False,
                "error": "不支持的导出格式"
            }), 400
        
    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/data/import', methods=['POST'])
def import_data():
    """导入数据"""
    try:
        if 'file' not in request.files:
            return jsonify({
                "success": False,
                "error": "没有上传文件"
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                "success": False,
                "error": "文件名为空"
            }), 400
        
        # 读取文件内容
        if file.filename.endswith('.json'):
            data = json.load(file)
            
            # 重建对象（简化处理）
            if data.get("alternatives"):
                alternatives = []
                for alt_data in data["alternatives"]:
                    alt = Alternative(
                        name=alt_data["name"],
                        description=alt_data["description"]
                    )
                    alt.attributes = alt_data.get("attributes", {})
                    alternatives.append(alt)
                session_data["alternatives"] = alternatives
            
            return jsonify({
                "success": True,
                "data": {
                    "alternatives_count": len(session_data["alternatives"])
                }
            })
        
        else:
            return jsonify({
                "success": False,
                "error": "不支持的文件格式"
            }), 400
        
    except Exception as e:
        logger.error(f"导入数据失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/session/status')
def session_status():
    """获取会话状态"""
    return jsonify({
        "success": True,
        "data": {
            "has_task_hierarchy": session_data["task_hierarchy"] is not None,
            "has_evaluation_scheme": session_data["evaluation_scheme"] is not None,
            "alternatives_count": len(session_data["alternatives"]),
            "evaluation_results_count": len(session_data["evaluation_results"]),
            "decision_methods": list(session_data["decision_results"].keys())
        }
    })


@app.route('/api/session/clear', methods=['POST'])
def clear_session():
    """清空会话数据"""
    global session_data
    session_data = {
        "task_hierarchy": None,
        "evaluation_scheme": None,
        "alternatives": [],
        "evaluation_results": [],
        "decision_results": {}
    }
    
    return jsonify({
        "success": True,
        "message": "会话数据已清空"
    })


@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "error": "API端点不存在"
    }), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "error": "服务器内部错误"
    }), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
