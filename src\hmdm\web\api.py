"""
HMDM API接口模块

提供RESTful API接口，支持前端与allocation模块的交互
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from flask import Blueprint, request, jsonify
from src.hmdm.core.system_manager import HMDMSystemManager
from src.hmdm.allocation.allocation_config import AllocationConfig
from src.hmdm.models.task_models import TaskHierarchy
from src.hmdm.allocation.allocation_scheme_generator import AllocationScheme


class AllocationAPI:
    """人机分配API接口类"""
    
    def __init__(self, system_manager: HMDMSystemManager):
        self.system_manager = system_manager
        self.logger = logging.getLogger(__name__)
        
        # 创建Blueprint
        self.bp = Blueprint('allocation_api', __name__, url_prefix='/api/allocation')
        self._register_routes()
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.bp.route('/config', methods=['GET'])
        def get_allocation_config():
            """获取人机分配配置"""
            try:
                config = self.system_manager.get_allocation_config()
                return jsonify({
                    'success': True,
                    'data': config.to_dict() if config else None
                })
            except Exception as e:
                self.logger.error(f"获取分配配置失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/config', methods=['POST'])
        def update_allocation_config():
            """更新人机分配配置"""
            try:
                config_data = request.get_json()
                if not config_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的配置数据'
                    }), 400
                
                new_config = AllocationConfig.from_dict(config_data)
                success = self.system_manager.update_allocation_config(new_config)
                
                return jsonify({
                    'success': success,
                    'message': '配置更新成功' if success else '配置更新失败'
                })
            except Exception as e:
                self.logger.error(f"更新分配配置失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/config/validate', methods=['POST'])
        def validate_allocation_config():
            """验证人机分配配置"""
            try:
                config_data = request.get_json()
                if not config_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的配置数据'
                    }), 400
                
                config = AllocationConfig.from_dict(config_data)
                is_valid = config.validate()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'is_valid': is_valid,
                        'config': config.to_dict()
                    }
                })
            except Exception as e:
                self.logger.error(f"验证分配配置失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/system/status', methods=['GET'])
        def get_allocation_system_status():
            """获取人机分配系统状态"""
            try:
                allocation_system = self.system_manager.get_allocation_system()
                
                if not allocation_system:
                    return jsonify({
                        'success': True,
                        'data': {
                            'status': 'not_running',
                            'message': '人机分配系统未启动'
                        }
                    })
                
                # 获取系统状态信息
                status_info = {
                    'status': 'running',
                    'config': getattr(allocation_system, 'config', {}),
                    'last_allocation_time': getattr(allocation_system, 'last_allocation_time', None),
                    'total_allocations': getattr(allocation_system, 'total_allocations', 0)
                }
                
                return jsonify({
                    'success': True,
                    'data': status_info
                })
            except Exception as e:
                self.logger.error(f"获取分配系统状态失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/tasks/validate', methods=['POST'])
        def validate_task_hierarchy():
            """验证任务层次结构"""
            try:
                task_data = request.get_json()
                if not task_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的任务数据'
                    }), 400
                
                # 验证任务层次结构
                validation_result = self._validate_task_hierarchy(task_data)
                
                return jsonify({
                    'success': True,
                    'data': validation_result
                })
            except Exception as e:
                self.logger.error(f"验证任务层次结构失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/allocate', methods=['POST'])
        def execute_allocation():
            """执行人机功能分配"""
            try:
                request_data = request.get_json()
                if not request_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的请求数据'
                    }), 400
                
                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503
                
                # 解析任务层次结构
                task_hierarchy_data = request_data.get('task_hierarchy')
                if not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少任务层次结构数据'
                    }), 400
                
                # 转换为TaskHierarchy对象
                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)
                
                # 获取约束条件和偏好设置
                constraints = request_data.get('constraints')
                preferences = request_data.get('preferences')
                optimization_criteria = request_data.get('optimization_criteria')
                
                # 执行分配
                result = allocation_system.allocate_functions(
                    task_hierarchy,
                    constraints=constraints,
                    preferences=preferences,
                    optimization_criteria=optimization_criteria
                )
                
                return jsonify({
                    'success': True,
                    'data': result.to_dict()
                })
                
            except Exception as e:
                self.logger.error(f"执行人机分配失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/schemes/generate', methods=['POST'])
        def generate_allocation_schemes():
            """生成分配方案"""
            try:
                request_data = request.get_json()
                if not request_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的请求数据'
                    }), 400
                
                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503
                
                # 解析参数
                task_hierarchy_data = request_data.get('task_hierarchy')
                scheme_count = request_data.get('scheme_count', 5)
                strategy = request_data.get('strategy', 'balanced')
                constraints = request_data.get('constraints', {})
                
                if not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少任务层次结构数据'
                    }), 400
                
                # 转换为TaskHierarchy对象
                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)
                
                # 生成方案
                schemes = allocation_system.scheme_generator.generate_schemes(
                    task_hierarchy,
                    scheme_count=scheme_count,
                    strategy=strategy,
                    constraints=constraints
                )
                
                # 转换为字典格式
                schemes_data = [scheme.to_dict() for scheme in schemes]
                
                return jsonify({
                    'success': True,
                    'data': {
                        'schemes': schemes_data,
                        'count': len(schemes_data),
                        'generation_time': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                self.logger.error(f"生成分配方案失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/schemes/compare', methods=['POST'])
        def compare_allocation_schemes():
            """比较分配方案"""
            try:
                request_data = request.get_json()
                schemes_data = request_data.get('schemes', [])
                task_hierarchy_data = request_data.get('task_hierarchy')
                
                if not schemes_data or not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少方案或任务数据'
                    }), 400
                
                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503
                
                # 转换数据格式
                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)
                schemes = [AllocationScheme.from_dict(scheme_data) for scheme_data in schemes_data]
                
                # 比较方案
                comparison_results = allocation_system.effectiveness_evaluator.compare_schemes(
                    schemes, task_hierarchy
                )
                
                # 转换结果为JSON格式
                results_dict = {}
                for scheme_id, report in comparison_results.items():
                    results_dict[scheme_id] = report.to_dict()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'comparison_results': results_dict,
                        'comparison_time': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                self.logger.error(f"比较分配方案失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/schemes/evaluate', methods=['POST'])
        def evaluate_allocation_schemes():
            """评估分配方案效能"""
            try:
                request_data = request.get_json()
                schemes_data = request_data.get('schemes', [])
                task_hierarchy_data = request_data.get('task_hierarchy')
                evaluation_criteria = request_data.get('evaluation_criteria', {})
                
                if not schemes_data or not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少方案或任务数据'
                    }), 400
                
                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503
                
                # 转换数据格式
                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)
                schemes = [AllocationScheme.from_dict(scheme_data) for scheme_data in schemes_data]
                
                # 评估方案
                evaluation_results = []
                for scheme in schemes:
                    evaluation_report = allocation_system.effectiveness_evaluator.evaluate_scheme(
                        scheme, task_hierarchy, evaluation_criteria
                    )
                    evaluation_results.append({
                        'scheme_id': scheme.scheme_id,
                        'evaluation': evaluation_report.to_dict()
                    })
                
                return jsonify({
                    'success': True,
                    'data': {
                        'evaluation_results': evaluation_results,
                        'evaluation_time': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                self.logger.error(f"评估分配方案失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.bp.route('/capabilities/analyze', methods=['POST'])
        def analyze_capabilities():
            """分析人机能力"""
            try:
                request_data = request.get_json()
                task_data = request_data.get('task')
                context = request_data.get('context', {})
                
                if not task_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少任务数据'
                    }), 400
                
                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503
                
                # 分析能力
                capability_analysis = allocation_system.capability_analyzer.analyze_task_requirements(
                    task_data, context
                )
                
                return jsonify({
                    'success': True,
                    'data': {
                        'capability_analysis': capability_analysis,
                        'analysis_time': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                self.logger.error(f"分析人机能力失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    def _validate_task_hierarchy(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证任务层次结构"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            tasks = task_data.get('tasks', {})
            root_task_id = task_data.get('rootTaskId')
            
            # 基本验证
            if not tasks:
                validation_result['is_valid'] = False
                validation_result['errors'].append('任务列表为空')
                return validation_result
            
            if not root_task_id:
                validation_result['warnings'].append('未指定根任务')
            
            # 验证任务完整性
            for task_id, task in tasks.items():
                if not task.get('name'):
                    validation_result['errors'].append(f'任务 {task_id} 缺少名称')
                
                if 'attributes' not in task:
                    validation_result['warnings'].append(f'任务 {task_id} 缺少属性信息')
                else:
                    attrs = task['attributes']
                    for attr in ['complexity', 'importance', 'urgency']:
                        if attr not in attrs:
                            validation_result['warnings'].append(f'任务 {task_id} 缺少 {attr} 属性')
                        elif not (0 <= attrs[attr] <= 1):
                            validation_result['errors'].append(f'任务 {task_id} 的 {attr} 属性值超出范围 [0,1]')
            
            # 验证层次结构
            orphan_tasks = []
            for task_id, task in tasks.items():
                parent_id = task.get('parent_id')
                if parent_id and parent_id not in tasks:
                    orphan_tasks.append(task_id)
            
            if orphan_tasks:
                validation_result['errors'].extend([f'任务 {tid} 的父任务不存在' for tid in orphan_tasks])
            
            # 统计信息
            validation_result['statistics'] = {
                'total_tasks': len(tasks),
                'max_depth': self._calculate_max_depth(tasks),
                'real_time_tasks': len([t for t in tasks.values() if t.get('attributes', {}).get('real_time_requirement', False)]),
                'avg_complexity': sum(t.get('attributes', {}).get('complexity', 0) for t in tasks.values()) / len(tasks) if tasks else 0
            }
            
            # 如果有错误，标记为无效
            if validation_result['errors']:
                validation_result['is_valid'] = False
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f'验证过程中发生错误: {str(e)}')
        
        return validation_result
    
    def _calculate_max_depth(self, tasks: Dict[str, Any]) -> int:
        """计算任务层次结构的最大深度"""
        max_depth = 0
        for task in tasks.values():
            depth = task.get('level', 0)
            max_depth = max(max_depth, depth)
        return max_depth + 1  # 层级从0开始，深度从1开始


def create_allocation_api(system_manager: HMDMSystemManager) -> Blueprint:
    """创建人机分配API Blueprint"""
    api = AllocationAPI(system_manager)
    return api.bp
