"""
协同效能评估器测试
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.hmdm.allocation.collaboration_effectiveness_evaluator import (
    CollaborationEffectivenessEvaluator,
    EffectivenessMetrics,
    EvaluationReport
)
from src.hmdm.allocation.allocation_scheme_generator import AllocationScheme, AllocationStrategy
from src.hmdm.models.task_models import TaskHierarchy, Task, TaskType, TaskAttribute, ExecutorType


class TestCollaborationEffectivenessEvaluator:
    """协同效能评估器测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.evaluator = CollaborationEffectivenessEvaluator()
        
        # 创建测试任务层次结构
        self.task_hierarchy = self._create_test_task_hierarchy()
        
        # 创建测试分配方案
        self.test_scheme = self._create_test_allocation_scheme()
    
    def _create_test_task_hierarchy(self) -> TaskHierarchy:
        """创建测试用的任务层次结构"""
        hierarchy = TaskHierarchy(root_task_id="root_task")
        
        # 创建根任务
        root_task = Task(
            id="root_task",
            name="综合作战任务",
            description="综合性军事作战任务",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        root_task.attributes = TaskAttribute(
            complexity=0.8,
            importance=0.9,
            urgency=0.7,
            real_time_requirement=True
        )
        hierarchy.add_task(root_task)
        
        # 创建子任务
        tasks_data = [
            {
                "id": "data_collection",
                "name": "数据收集任务",
                "description": "收集和预处理数据",
                "complexity": 0.3,
                "importance": 0.7,
                "real_time_requirement": True
            },
            {
                "id": "situation_analysis",
                "name": "态势分析任务",
                "description": "分析当前态势和威胁",
                "complexity": 0.9,
                "importance": 0.9,
                "real_time_requirement": False
            },
            {
                "id": "decision_support",
                "name": "决策支持任务",
                "description": "提供决策建议",
                "complexity": 0.8,
                "importance": 0.95,
                "real_time_requirement": False
            },
            {
                "id": "execution_control",
                "name": "执行控制任务",
                "description": "控制和监督任务执行",
                "complexity": 0.6,
                "importance": 0.8,
                "real_time_requirement": True
            }
        ]
        
        for task_data in tasks_data:
            task = Task(
                id=task_data["id"],
                name=task_data["name"],
                description=task_data["description"],
                task_type=TaskType.ZZ_TASK,
                parent_id="root_task",
                level=1,
                executor_type=ExecutorType.HUMAN_MACHINE
            )
            task.attributes = TaskAttribute(
                complexity=task_data["complexity"],
                importance=task_data["importance"],
                urgency=0.7,
                real_time_requirement=task_data["real_time_requirement"]
            )
            hierarchy.add_task(task)
        
        return hierarchy
    
    def _create_test_allocation_scheme(self) -> AllocationScheme:
        """创建测试分配方案"""
        scheme = AllocationScheme(
            name="测试分配方案",
            description="用于测试的人机分配方案",
            strategy=AllocationStrategy.CAPABILITY_BASED
        )
        
        scheme.task_allocations = {
            "root_task": "collaboration",
            "data_collection": "machine",
            "situation_analysis": "collaboration",
            "decision_support": "human",
            "execution_control": "machine"
        }
        
        scheme.collaboration_details = {
            "root_task": {
                "human_role": "总体指挥和决策",
                "machine_role": "数据处理和执行",
                "coordination_method": "实时协同",
                "confidence": 0.85
            },
            "situation_analysis": {
                "human_role": "态势判断和分析",
                "machine_role": "数据计算和模式识别",
                "coordination_method": "分阶段协同",
                "confidence": 0.9
            }
        }
        
        return scheme
    
    def test_effectiveness_metrics_creation(self):
        """测试效能指标创建"""
        metrics = EffectivenessMetrics()
        
        # 测试默认值
        assert metrics.overall_effectiveness == 0.0
        assert metrics.task_completion_rate == 0.0
        assert metrics.time_efficiency == 0.0
        assert metrics.error_rate == 0.0
        
        # 测试转换为字典
        metrics_dict = metrics.to_dict()
        assert isinstance(metrics_dict, dict)
        assert 'overall_effectiveness' in metrics_dict
        assert 'task_completion_rate' in metrics_dict
        assert len(metrics_dict) == 11  # 11个指标
        
        # 测试从字典创建
        test_data = {
            'overall_effectiveness': 0.8,
            'task_completion_rate': 0.9,
            'time_efficiency': 0.85
        }
        new_metrics = EffectivenessMetrics.from_dict(test_data)
        assert new_metrics.overall_effectiveness == 0.8
        assert new_metrics.task_completion_rate == 0.9
        assert new_metrics.time_efficiency == 0.85
    
    def test_evaluation_report_creation(self):
        """测试评估报告创建"""
        metrics = EffectivenessMetrics(overall_effectiveness=0.8)
        report = EvaluationReport(
            scheme_id="test_scheme",
            metrics=metrics,
            strengths=["高效率"],
            weaknesses=["协调复杂"],
            confidence_level=0.85
        )
        
        assert report.scheme_id == "test_scheme"
        assert report.metrics.overall_effectiveness == 0.8
        assert "高效率" in report.strengths
        assert "协调复杂" in report.weaknesses
        assert report.confidence_level == 0.85
        
        # 测试转换为字典
        report_dict = report.to_dict()
        assert isinstance(report_dict, dict)
        assert report_dict['scheme_id'] == "test_scheme"
        assert 'metrics' in report_dict
        assert 'strengths' in report_dict
    
    def test_evaluate_scheme(self):
        """测试方案评估"""
        report = self.evaluator.evaluate_scheme(self.test_scheme, self.task_hierarchy)
        
        # 验证报告基本属性
        assert isinstance(report, EvaluationReport)
        assert report.scheme_id == self.test_scheme.scheme_id
        assert isinstance(report.metrics, EffectivenessMetrics)
        
        # 验证效能指标
        metrics = report.metrics
        assert 0 <= metrics.overall_effectiveness <= 1
        assert 0 <= metrics.task_completion_rate <= 1
        assert 0 <= metrics.time_efficiency <= 1
        assert 0 <= metrics.resource_utilization <= 1
        assert 0 <= metrics.error_rate <= 1
        assert 0 <= metrics.coordination_overhead <= 1
        assert 0 <= metrics.adaptability <= 1
        
        # 验证报告内容
        assert isinstance(report.strengths, list)
        assert isinstance(report.weaknesses, list)
        assert isinstance(report.improvement_suggestions, list)
        assert isinstance(report.risk_factors, list)
        assert 0 <= report.confidence_level <= 1
    
    def test_calculate_effectiveness_metrics(self):
        """测试效能指标计算"""
        metrics = self.evaluator._calculate_effectiveness_metrics(
            self.test_scheme, self.task_hierarchy
        )
        
        assert isinstance(metrics, EffectivenessMetrics)
        
        # 验证所有指标都在合理范围内
        assert 0 <= metrics.overall_effectiveness <= 1
        assert 0 <= metrics.task_completion_rate <= 1
        assert 0 <= metrics.time_efficiency <= 1
        assert 0 <= metrics.resource_utilization <= 1
        assert 0 <= metrics.error_rate <= 1
        assert 0 <= metrics.coordination_overhead <= 1
        assert 0 <= metrics.adaptability <= 1
        assert 0 <= metrics.cost_effectiveness <= 1
        assert 0 <= metrics.human_workload <= 1
        assert 0 <= metrics.machine_utilization <= 1
        assert 0 <= metrics.collaboration_quality <= 1
    
    def test_get_allocation_statistics(self):
        """测试分配统计"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        stats = self.evaluator._get_allocation_statistics(self.test_scheme, all_tasks)
        
        assert isinstance(stats, dict)
        assert 'human' in stats
        assert 'machine' in stats
        assert 'collaboration' in stats
        
        # 验证统计数据
        total_allocated = sum(stats.values())
        assert total_allocated == len(all_tasks)
        
        # 验证具体分配数量
        assert stats['human'] >= 0
        assert stats['machine'] >= 0
        assert stats['collaboration'] >= 0
    
    def test_calculate_task_completion_rate(self):
        """测试任务完成率计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        completion_rate = self.evaluator._calculate_task_completion_rate(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(completion_rate, float)
        assert 0 <= completion_rate <= 1
        
        # 测试空任务列表
        empty_completion_rate = self.evaluator._calculate_task_completion_rate(
            self.test_scheme, []
        )
        assert empty_completion_rate == 1.0
    
    def test_calculate_time_efficiency(self):
        """测试时间效率计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        time_efficiency = self.evaluator._calculate_time_efficiency(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(time_efficiency, float)
        assert 0 <= time_efficiency <= 1
    
    def test_calculate_resource_utilization(self):
        """测试资源利用率计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        resource_utilization = self.evaluator._calculate_resource_utilization(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(resource_utilization, float)
        assert 0 <= resource_utilization <= 1
    
    def test_calculate_error_rate(self):
        """测试错误率计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        error_rate = self.evaluator._calculate_error_rate(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(error_rate, float)
        assert 0 <= error_rate <= 1
    
    def test_calculate_coordination_overhead(self):
        """测试协调开销计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        coordination_overhead = self.evaluator._calculate_coordination_overhead(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(coordination_overhead, float)
        assert 0 <= coordination_overhead <= 1
        
        # 测试没有协同任务的情况
        no_collaboration_scheme = AllocationScheme()
        no_collaboration_scheme.task_allocations = {
            task.id: "human" for task in all_tasks
        }
        
        no_collab_overhead = self.evaluator._calculate_coordination_overhead(
            no_collaboration_scheme, all_tasks
        )
        assert no_collab_overhead == 0.0
    
    def test_calculate_adaptability(self):
        """测试适应性计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        adaptability = self.evaluator._calculate_adaptability(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(adaptability, float)
        assert 0 <= adaptability <= 1
    
    def test_calculate_human_workload(self):
        """测试人类工作负荷计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        human_workload = self.evaluator._calculate_human_workload(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(human_workload, float)
        assert 0 <= human_workload <= 1
    
    def test_calculate_machine_utilization(self):
        """测试机器利用率计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        machine_utilization = self.evaluator._calculate_machine_utilization(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(machine_utilization, float)
        assert 0 <= machine_utilization <= 1
    
    def test_calculate_collaboration_quality(self):
        """测试协同质量计算"""
        all_tasks = self.task_hierarchy.get_all_tasks()
        collaboration_quality = self.evaluator._calculate_collaboration_quality(
            self.test_scheme, all_tasks
        )
        
        assert isinstance(collaboration_quality, float)
        assert 0 <= collaboration_quality <= 1
        
        # 测试没有协同任务的情况
        no_collaboration_scheme = AllocationScheme()
        no_collaboration_scheme.task_allocations = {
            task.id: "human" for task in all_tasks
        }
        
        no_collab_quality = self.evaluator._calculate_collaboration_quality(
            no_collaboration_scheme, all_tasks
        )
        assert no_collab_quality == 1.0  # 没有协同任务时质量为满分
    
    def test_compare_schemes(self):
        """测试方案比较"""
        # 创建多个测试方案
        scheme1 = self.test_scheme
        
        scheme2 = AllocationScheme(
            name="机器优先方案",
            strategy=AllocationStrategy.MACHINE_PRIORITY
        )
        scheme2.task_allocations = {
            task.id: "machine" for task in self.task_hierarchy.get_all_tasks()
        }
        
        scheme3 = AllocationScheme(
            name="人类优先方案",
            strategy=AllocationStrategy.HUMAN_PRIORITY
        )
        scheme3.task_allocations = {
            task.id: "human" for task in self.task_hierarchy.get_all_tasks()
        }
        
        schemes = [scheme1, scheme2, scheme3]
        results = self.evaluator.compare_schemes(schemes, self.task_hierarchy)
        
        # 验证比较结果
        assert isinstance(results, dict)
        assert len(results) == 3
        
        for scheme_id, report in results.items():
            assert isinstance(report, EvaluationReport)
            assert scheme_id in [s.scheme_id for s in schemes]
            assert isinstance(report.metrics, EffectivenessMetrics)
    
    def test_analyze_strengths_weaknesses(self):
        """测试优势劣势分析"""
        # 创建高效能指标
        high_metrics = EffectivenessMetrics(
            task_completion_rate=0.95,
            time_efficiency=0.9,
            error_rate=0.05,
            coordination_overhead=0.1,
            adaptability=0.85
        )
        
        strengths, weaknesses = self.evaluator._analyze_strengths_weaknesses(
            high_metrics, self.test_scheme, self.task_hierarchy
        )
        
        assert isinstance(strengths, list)
        assert isinstance(weaknesses, list)
        assert len(strengths) > 0  # 高效能应该有优势
        
        # 创建低效能指标
        low_metrics = EffectivenessMetrics(
            task_completion_rate=0.6,
            time_efficiency=0.5,
            error_rate=0.25,
            coordination_overhead=0.5,
            adaptability=0.4
        )
        
        strengths_low, weaknesses_low = self.evaluator._analyze_strengths_weaknesses(
            low_metrics, self.test_scheme, self.task_hierarchy
        )
        
        assert len(weaknesses_low) > 0  # 低效能应该有劣势
    
    def test_generate_improvement_suggestions(self):
        """测试改进建议生成"""
        # 创建需要改进的指标
        poor_metrics = EffectivenessMetrics(
            task_completion_rate=0.7,
            time_efficiency=0.6,
            error_rate=0.2,
            coordination_overhead=0.4,
            human_workload=0.9,
            machine_utilization=0.3
        )
        
        suggestions = self.evaluator._generate_improvement_suggestions(
            poor_metrics, self.test_scheme, self.task_hierarchy
        )
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        
        # 验证建议内容
        suggestion_text = " ".join(suggestions)
        assert any(keyword in suggestion_text for keyword in 
                  ["完成率", "效率", "错误", "协调", "工作负荷", "利用率"])
    
    def test_identify_risk_factors(self):
        """测试风险因素识别"""
        # 创建高风险指标
        risky_metrics = EffectivenessMetrics(
            human_workload=0.9,
            coordination_overhead=0.5,
            error_rate=0.25,
            adaptability=0.4
        )
        
        risks = self.evaluator._identify_risk_factors(
            risky_metrics, self.test_scheme, self.task_hierarchy
        )
        
        assert isinstance(risks, list)
        assert len(risks) > 0
        
        # 验证风险内容
        risk_text = " ".join(risks)
        assert any(keyword in risk_text for keyword in 
                  ["过载", "协调", "质量", "适应", "依赖"])
    
    def test_calculate_confidence_level(self):
        """测试置信度计算"""
        metrics = EffectivenessMetrics(
            task_completion_rate=0.8,
            time_efficiency=0.8,
            resource_utilization=0.8,
            error_rate=0.1,
            coordination_overhead=0.2,
            adaptability=0.8
        )
        
        confidence = self.evaluator._calculate_confidence_level(
            metrics, self.test_scheme, self.task_hierarchy
        )
        
        assert isinstance(confidence, float)
        assert 0 <= confidence <= 1
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空任务层次结构
        empty_hierarchy = TaskHierarchy(root_task_id="empty")
        empty_scheme = AllocationScheme()
        
        report = self.evaluator.evaluate_scheme(empty_scheme, empty_hierarchy)
        assert isinstance(report, EvaluationReport)
        assert isinstance(report.metrics, EffectivenessMetrics)
        
        # 测试单个任务
        single_task_hierarchy = TaskHierarchy(root_task_id="single")
        single_task = Task(
            id="single",
            name="单个任务",
            task_type=TaskType.META_OPERATION
        )
        single_task.attributes = TaskAttribute()
        single_task_hierarchy.add_task(single_task)
        
        single_scheme = AllocationScheme()
        single_scheme.task_allocations = {"single": "human"}
        
        single_report = self.evaluator.evaluate_scheme(single_scheme, single_task_hierarchy)
        assert isinstance(single_report, EvaluationReport)
    
    def test_evaluation_weights_configuration(self):
        """测试评估权重配置"""
        weights = self.evaluator.evaluation_weights
        
        assert isinstance(weights, dict)
        assert len(weights) == 6
        
        # 验证权重总和接近1
        total_weight = sum(weights.values())
        assert abs(total_weight - 1.0) < 0.01
        
        # 验证所有权重都是正数
        for weight in weights.values():
            assert weight > 0
    
    def test_baseline_performance_configuration(self):
        """测试基准性能配置"""
        baseline = self.evaluator.baseline_performance
        
        assert isinstance(baseline, dict)
        assert 'human_only' in baseline
        assert 'machine_only' in baseline
        assert 'collaboration' in baseline
        
        # 验证每种类型都有必要的性能指标
        for perf_type, metrics in baseline.items():
            assert 'completion_rate' in metrics
            assert 'error_rate' in metrics
            assert 'efficiency' in metrics
            
            # 验证指标值在合理范围内
            assert 0 <= metrics['completion_rate'] <= 1
            assert 0 <= metrics['error_rate'] <= 1
            assert 0 <= metrics['efficiency'] <= 1


if __name__ == '__main__':
    pytest.main([__file__])
