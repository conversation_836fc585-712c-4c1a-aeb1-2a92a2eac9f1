{"root_task_id": "1b403e5a-4984-442a-9d61-b0e3ce1e1759", "tasks": {"1b403e5a-4984-442a-9d61-b0e3ce1e1759": {"id": "1b403e5a-4984-442a-9d61-b0e3ce1e1759", "name": "态势分析任务", "description": "执行态势分析相关的任务", "task_type": "使命任务", "parent_id": null, "children_ids": ["2add6972-0d8d-40aa-8946-c1cdf6054a0d", "785a1dac-6bd1-47f3-bd50-5bc0f17307af", "ff1add2a-2f8d-4070-b6b4-0223afc24e61"], "level": 0, "attributes": {"complexity": 0.0, "importance": 0.0, "urgency": 0.0, "frequency": 0.0, "duration": 0.0, "error_rate": 0.0, "workload": 0.0}, "io_pattern": {"input_description": "", "action_description": "", "output_description": "", "input_type": "", "output_type": ""}, "executor_type": "人机协作", "prerequisites": [], "constraints": [], "created_at": "2025-09-07T04:51:03.257484", "updated_at": "2025-09-07T04:51:03.259484", "tags": [], "metadata": {}}, "2add6972-0d8d-40aa-8946-c1cdf6054a0d": {"id": "2add6972-0d8d-40aa-8946-c1cdf6054a0d", "name": "数据收集", "description": "收集相关态势数据", "task_type": "ZZ任务", "parent_id": "1b403e5a-4984-442a-9d61-b0e3ce1e1759", "children_ids": [], "level": 1, "attributes": {"complexity": 0.3, "importance": 0.9, "urgency": 0.0, "frequency": 0.0, "duration": 30.0, "error_rate": 0.0, "workload": 0.2}, "io_pattern": {"input_description": "传感器数据、情报信息", "action_description": "数据采集和预处理", "output_description": "结构化态势数据", "input_type": "多源数据", "output_type": "标准化数据"}, "executor_type": "机", "prerequisites": [], "constraints": [], "created_at": "2025-09-07T04:51:03.259484", "updated_at": "2025-09-07T04:51:03.259484", "tags": [], "metadata": {}}, "785a1dac-6bd1-47f3-bd50-5bc0f17307af": {"id": "785a1dac-6bd1-47f3-bd50-5bc0f17307af", "name": "数据分析", "description": "分析态势数据并识别模式", "task_type": "ZZ任务", "parent_id": "1b403e5a-4984-442a-9d61-b0e3ce1e1759", "children_ids": [], "level": 1, "attributes": {"complexity": 0.7, "importance": 0.9, "urgency": 0.0, "frequency": 0.0, "duration": 120.0, "error_rate": 0.0, "workload": 0.6}, "io_pattern": {"input_description": "结构化态势数据", "action_description": "模式识别和趋势分析", "output_description": "态势评估结果", "input_type": "标准化数据", "output_type": "分析报告"}, "executor_type": "人机协作", "prerequisites": [], "constraints": [], "created_at": "2025-09-07T04:51:03.259484", "updated_at": "2025-09-07T04:51:03.259484", "tags": [], "metadata": {}}, "ff1add2a-2f8d-4070-b6b4-0223afc24e61": {"id": "ff1add2a-2f8d-4070-b6b4-0223afc24e61", "name": "态势展示", "description": "可视化展示态势分析结果", "task_type": "ZZ任务", "parent_id": "1b403e5a-4984-442a-9d61-b0e3ce1e1759", "children_ids": [], "level": 1, "attributes": {"complexity": 0.4, "importance": 0.7, "urgency": 0.0, "frequency": 0.0, "duration": 15.0, "error_rate": 0.0, "workload": 0.3}, "io_pattern": {"input_description": "态势评估结果", "action_description": "数据可视化和界面展示", "output_description": "态势显示界面", "input_type": "分析报告", "output_type": "可视化界面"}, "executor_type": "机", "prerequisites": [], "constraints": [], "created_at": "2025-09-07T04:51:03.259484", "updated_at": "2025-09-07T04:51:03.259484", "tags": [], "metadata": {}}}}