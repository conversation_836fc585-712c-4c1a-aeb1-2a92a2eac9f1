"""
辅助决策场景模板测试

测试决策问题分析、方案生成、决策支持等功能
"""

import pytest
from datetime import datetime, timedelta
from src.hmdm.scenarios.decision_support_scenarios import (
    DecisionSupportScenarios, DecisionProblem, DecisionScenario,
    DecisionComplexity, DecisionUrgency
)
from src.hmdm.decision.military_decision_support import DecisionType
from src.hmdm.security.military_security import SecurityLevel


class TestDecisionSupportScenarios:
    """辅助决策场景模板测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.decision_scenarios = DecisionSupportScenarios()
    
    def test_decision_problem_creation(self):
        """测试决策问题创建"""
        problem = DecisionProblem(
            id="test_problem_001",
            title="测试决策问题",
            description="这是一个用于测试的决策问题",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.URGENT,
            decision_type=DecisionType.TACTICAL,
            stakeholders=["指挥官", "参谋", "作战单位"],
            success_criteria={"effectiveness": 0.8, "risk": 0.3}
        )
        
        assert problem.id == "test_problem_001"
        assert problem.complexity == DecisionComplexity.MODERATE
        assert problem.urgency == DecisionUrgency.URGENT
        assert problem.decision_type == DecisionType.TACTICAL
        assert len(problem.stakeholders) == 3
        assert problem.get_time_limit() == 4.0  # URGENT = 4小时
    
    def test_decision_scenario_creation(self):
        """测试决策场景创建"""
        problem = DecisionProblem(
            id="scenario_test_001",
            title="场景测试问题",
            description="测试场景创建功能",
            complexity=DecisionComplexity.SIMPLE,
            urgency=DecisionUrgency.ROUTINE,
            decision_type=DecisionType.TACTICAL
        )
        
        scenario = self.decision_scenarios.create_decision_scenario(problem)
        
        assert scenario is not None
        assert scenario.problem.id == problem.id
        assert scenario.name.endswith("决策场景")
        assert "max_alternatives" in scenario.context
        assert "time_limit" in scenario.context
        assert len(scenario.available_resources) > 0
        assert len(scenario.environmental_factors) > 0
    
    def test_problem_analysis(self):
        """测试决策问题分析"""
        problem = DecisionProblem(
            id="analysis_test_001",
            title="分析测试问题",
            description="测试问题分析功能",
            complexity=DecisionComplexity.COMPLEX,
            urgency=DecisionUrgency.IMMEDIATE,
            decision_type=DecisionType.OPERATIONAL,
            stakeholders=["指挥官", "参谋", "后勤", "情报"],
            constraints={"budget": 1000000, "time_limit": True},
            success_criteria={"effectiveness": 0.9, "efficiency": 0.8}
        )
        
        scenario = self.decision_scenarios.create_decision_scenario(problem)
        analysis = self.decision_scenarios.analyze_decision_problem(scenario)
        
        assert "error" not in analysis
        assert "problem_structure" in analysis
        assert "stakeholder_analysis" in analysis
        assert "constraint_analysis" in analysis
        assert "risk_assessment" in analysis
        assert "opportunity_analysis" in analysis
        assert "resource_requirements" in analysis
        assert "time_sensitivity" in analysis
        
        # 验证问题结构分析
        problem_structure = analysis["problem_structure"]
        assert problem_structure["complexity_level"] == "复杂决策"
        assert problem_structure["urgency_level"] == "立即"
        assert problem_structure["stakeholder_count"] == 4
        
        # 验证风险评估
        risk_assessment = analysis["risk_assessment"]
        assert risk_assessment["overall_risk_level"] == "high"  # 复杂+立即 = 高风险
    
    def test_alternative_generation(self):
        """测试方案生成"""
        problem = DecisionProblem(
            id="alternatives_test_001",
            title="方案生成测试",
            description="测试决策方案生成功能",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.URGENT,
            decision_type=DecisionType.TACTICAL
        )
        
        scenario = self.decision_scenarios.create_decision_scenario(problem)
        analysis = self.decision_scenarios.analyze_decision_problem(scenario)
        alternatives = self.decision_scenarios.generate_decision_alternatives(scenario, analysis)
        
        assert len(alternatives) > 0
        
        # 验证方案结构
        for alt in alternatives:
            assert "id" in alt
            assert "name" in alt
            assert "description" in alt
            assert "source" in alt
            assert "confidence" in alt
            assert "feasibility" in alt
            assert "effectiveness" in alt
            assert "risk_level" in alt
            assert "resource_requirements" in alt
            
            # 验证数值范围
            assert 0 <= alt["confidence"] <= 1
            assert 0 <= alt["feasibility"] <= 1
            assert 0 <= alt["effectiveness"] <= 1
            assert 0 <= alt["risk_level"] <= 1
    
    def test_decision_support(self):
        """测试决策支持"""
        problem = DecisionProblem(
            id="support_test_001",
            title="决策支持测试",
            description="测试决策支持功能",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.URGENT,
            decision_type=DecisionType.TACTICAL
        )
        
        scenario = self.decision_scenarios.create_decision_scenario(problem)
        analysis = self.decision_scenarios.analyze_decision_problem(scenario)
        alternatives = self.decision_scenarios.generate_decision_alternatives(scenario, analysis)
        
        if alternatives:
            support = self.decision_scenarios.provide_decision_support(scenario, alternatives)
            
            assert "error" not in support
            assert "evaluation_matrix" in support
            assert "recommended_alternative" in support
            
            # 如果有推荐方案，验证其结构
            if support["recommended_alternative"]:
                recommended = support["recommended_alternative"]
                assert "id" in recommended
                assert "evaluation_score" in recommended
                assert "ranking" in recommended
                
                # 验证支持信息
                assert "risk_analysis" in support
                assert "implementation_plan" in support
                assert "monitoring_plan" in support
                assert "contingency_plans" in support
                assert "expert_advice" in support
                assert "confidence_assessment" in support
    
    def test_complete_workflow(self):
        """测试完整决策工作流程"""
        problem = DecisionProblem(
            id="workflow_test_001",
            title="工作流程测试",
            description="测试完整的决策工作流程",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.URGENT,
            decision_type=DecisionType.TACTICAL,
            stakeholders=["指挥官", "参谋"],
            success_criteria={"effectiveness": 0.8}
        )
        
        workflow_result = self.decision_scenarios.execute_decision_workflow(problem)
        
        assert workflow_result["status"] in ["completed", "failed"]
        assert workflow_result["problem_id"] == problem.id
        assert "start_time" in workflow_result
        
        if workflow_result["status"] == "completed":
            assert "scenario" in workflow_result
            assert "analysis" in workflow_result
            assert "alternatives" in workflow_result
            assert "support" in workflow_result
            assert "decision" in workflow_result
            assert "end_time" in workflow_result
            
            # 验证场景信息
            scenario_info = workflow_result["scenario"]
            assert scenario_info["complexity"] == "中等复杂决策"
            assert scenario_info["urgency"] == "紧急"
            
            # 验证决策记录
            decision_record = workflow_result["decision"]
            assert decision_record["scenario_id"] is not None
            assert decision_record["problem_type"] == "战术决策"
            assert "execution_time" in decision_record
    
    def test_scenario_templates(self):
        """测试场景模板"""
        # 获取预定义模板
        templates = self.decision_scenarios.list_scenario_templates()
        
        assert len(templates) >= 2  # 至少有战术和资源配置两个模板
        
        # 验证模板结构
        for template in templates:
            assert "id" in template
            assert "name" in template
            assert "description" in template
            assert "complexity" in template
            assert "decision_type" in template
        
        # 获取特定模板
        if templates:
            template_id = templates[0]["id"]
            template = self.decision_scenarios.get_scenario_template(template_id)
            
            assert template is not None
            assert template.id == template_id
            assert isinstance(template.problem, DecisionProblem)
    
    def test_decision_statistics(self):
        """测试决策统计"""
        # 执行几个决策工作流程
        for i in range(3):
            problem = DecisionProblem(
                id=f"stats_test_{i:03d}",
                title=f"统计测试问题{i}",
                description=f"第{i}个统计测试问题",
                complexity=DecisionComplexity.SIMPLE if i % 2 == 0 else DecisionComplexity.MODERATE,
                urgency=DecisionUrgency.ROUTINE,
                decision_type=DecisionType.TACTICAL
            )
            
            self.decision_scenarios.execute_decision_workflow(problem)
        
        # 获取统计信息
        stats = self.decision_scenarios.get_decision_statistics()
        
        if "message" not in stats:  # 如果有决策记录
            assert "total_decisions" in stats
            assert stats["total_decisions"] >= 3
            assert "by_complexity" in stats
            assert "by_urgency" in stats
            assert "by_type" in stats
            assert "average_execution_time" in stats
            assert "success_rate" in stats
            
            # 验证统计数据
            assert 0 <= stats["success_rate"] <= 1
            assert stats["average_execution_time"] >= 0
    
    def test_complex_decision_scenario(self):
        """测试复杂决策场景"""
        problem = DecisionProblem(
            id="complex_test_001",
            title="复杂军事决策",
            description="多目标、多约束的复杂军事决策问题",
            complexity=DecisionComplexity.CRITICAL,
            urgency=DecisionUrgency.IMMEDIATE,
            decision_type=DecisionType.STRATEGIC,
            stakeholders=["总指挥", "各军种指挥官", "参谋部", "后勤部", "情报部"],
            constraints={
                "budget_limit": 10000000,
                "time_critical": True,
                "resource_limited": True,
                "political_sensitive": True
            },
            success_criteria={
                "mission_success": 0.95,
                "casualty_minimization": 0.9,
                "resource_efficiency": 0.8,
                "time_compliance": 0.9
            },
            deadline=datetime.now() + timedelta(hours=0.5)
        )
        
        workflow_result = self.decision_scenarios.execute_decision_workflow(problem)
        
        assert workflow_result["status"] in ["completed", "failed"]
        
        if workflow_result["status"] == "completed":
            # 验证复杂场景的特殊处理
            scenario_info = workflow_result["scenario"]
            assert scenario_info["complexity"] == "关键决策"
            assert scenario_info["urgency"] == "立即"
            
            # 验证生成了足够的备选方案
            alternatives = workflow_result["alternatives"]
            assert len(alternatives) > 0
            
            # 验证决策支持的完整性
            support = workflow_result["support"]
            if support.get("recommended_alternative"):
                assert "risk_analysis" in support
                assert "implementation_plan" in support
                assert "contingency_plans" in support
                assert len(support["contingency_plans"]) >= 3  # 复杂决策需要更多应急预案
    
    def test_time_sensitive_decisions(self):
        """测试时间敏感决策"""
        problem = DecisionProblem(
            id="time_sensitive_001",
            title="紧急响应决策",
            description="需要立即响应的紧急军事决策",
            complexity=DecisionComplexity.MODERATE,
            urgency=DecisionUrgency.CRITICAL,
            decision_type=DecisionType.EMERGENCY,
            deadline=datetime.now() + timedelta(minutes=30)
        )
        
        start_time = datetime.now()
        workflow_result = self.decision_scenarios.execute_decision_workflow(problem)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 验证执行时间合理（应该很快完成）
        assert execution_time < 10.0  # 10秒内完成
        
        if workflow_result["status"] == "completed":
            # 验证时间敏感决策的特殊处理
            decision_record = workflow_result["decision"]
            assert decision_record["urgency"] == "危急"
            
            # 验证监控计划适应紧急情况
            support = workflow_result["support"]
            if support.get("monitoring_plan"):
                monitoring = support["monitoring_plan"]
                assert monitoring["monitoring_frequency"] == "每日"  # 紧急情况需要频繁监控
