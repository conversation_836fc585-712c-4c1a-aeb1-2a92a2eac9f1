# HMDM系统用户手册

## 1. 系统概述

HMDM (Human-Machine Decision Making) 系统是一个专为军事指挥决策设计的综合性智能化系统。系统基于人机功能分配理论，集成了态势感知、决策支持、训练管理、知识管理等多个核心模块，为军事指挥人员提供全方位的决策支持服务。

### 1.1 核心功能模块

#### 分析决策层
- **态势感知引擎**：实时态势监控、威胁评估和态势预测
- **军事决策支持**：多目标模糊决策和方案优化
- **快速决策引擎**：紧急情况下的快速决策支持
- **模糊决策引擎**：复杂不确定环境下的决策分析

#### 知识管理层
- **军事知识库**：专业军事知识存储和管理
- **专家系统**：基于规则的专家知识推理
- **推理引擎**：智能化的知识推理和分析

#### 训练仿真层
- **军事训练系统**：个性化训练计划和效果评估
- **军事仿真引擎**：逼真的军事仿真环境
- **场景模板系统**：丰富的训练场景库

#### 智能化层
- **机器学习引擎**：深度学习模型和预测分析
- **自然语言处理**：智能文本分析和理解
- **推荐引擎**：个性化推荐和优化建议
- **智能预测引擎**：基于AI的态势预测

#### 安全管理层
- **增强安全管理器**：多层次安全防护
- **军事安全模块**：军事级别的安全保障
- **Web安全中间件**：网络安全防护

#### 性能优化层
- **性能监控器**：实时系统性能监控
- **缓存管理器**：高性能多级缓存系统
- **数据库连接池**：高效的数据库访问优化

### 1.2 系统特色

- **军事专业化**：专门针对军事指挥决策场景设计
- **智能化决策**：集成AI技术的智能决策支持
- **高性能架构**：支持高并发和大数据处理
- **安全可靠**：多层次安全防护和权限控制
- **模块化设计**：灵活的模块化架构，易于扩展

### 1.3 适用场景

- **态势感知**：实时态势监控和威胁评估
- **指挥决策**：复杂环境下的多目标决策支持
- **训练演练**：军事训练和仿真演练
- **知识管理**：军事知识的存储、检索和应用
- **系统管理**：军事信息系统的统一管理

## 2. 系统安装与配置

### 2.1 系统要求

#### 硬件要求
- **处理器**：Intel Core i5 或同等性能处理器
- **内存**：最低4GB，推荐8GB或更多
- **存储**：至少2GB可用磁盘空间
- **网络**：支持HTTP/HTTPS协议的网络连接

#### 软件要求
- **操作系统**：Windows 10+, Linux (Ubuntu 18.04+), macOS 10.15+
- **Python版本**：Python 3.11 或更高版本
- **浏览器**：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### 2.2 快速安装

#### 方法一：自动安装脚本
```bash
# 下载并运行安装脚本
curl -sSL https://install.hmdm.com/install.sh | bash
```

#### 方法二：手动安装
1. **下载系统**
```bash
git clone <repository-url>
cd HMDM
```

2. **创建虚拟环境**
```bash
python -m venv venv

# Windows激活
venv\Scripts\activate

# Linux/Mac激活
source venv/bin/activate
```

3. **安装依赖包**
```bash
pip install -r requirements.txt
```

4. **初始化系统**
```bash
python -m src.hmdm.core.system_manager init
```

5. **启动系统**
```bash
python -m src.hmdm.web.run_web
```

### 2.3 系统配置

#### 基础配置
系统启动后，首次访问需要进行基础配置：

1. **访问配置页面**：http://localhost:5000/setup
2. **设置管理员账户**：
   - 用户名：admin（默认）
   - 密码：Admin123!（首次登录后请修改）
3. **配置数据库连接**：默认使用SQLite，可配置为PostgreSQL或MySQL
4. **设置安全参数**：配置加密密钥和安全级别

#### 高级配置
编辑配置文件 `config/system_config.yaml`：

```yaml
# 系统基础配置
system:
  name: "HMDM系统"
  version: "2.0.0"
  debug: false

# 数据库配置
database:
  type: "sqlite"
  path: "data/hmdm.db"

# 安全配置
security:
  encryption_enabled: true
  session_timeout: 3600
  max_login_attempts: 5

# 性能配置
performance:
  cache_enabled: true
  cache_size: 1000
  monitoring_enabled: true
```
```

4. **验证安装**
```bash
python -m pytest tests/ -v
```

## 3. 快速开始

### 3.1 命令行界面

#### 创建任务结构
```bash
python -m src.cli.main create-task --scenario "态势分析" --output task_structure.json
```

#### 评估备选方案
```bash
python -m src.cli.main evaluate alternatives.xlsx --method WRDM --output results.json
```

#### 比较多个方案
```bash
python -m src.cli.main compare alternatives.xlsx --output comparison.json
```

#### 创建示例数据
```bash
python -m src.cli.main create-sample
```

### 3.2 Web界面

1. **启动Web服务**
```bash
python -m src.web.app
```

2. **访问界面**
打开浏览器访问 `http://localhost:5000`

3. **使用流程**
   - 创建任务结构
   - 设置评估方案
   - 添加备选方案
   - 执行评估和比较
   - 查看结果和导出

## 4. 详细使用指南

### 4.1 任务分解

#### 4.1.1 支持的任务场景

- **态势分析**：包含数据收集、数据分析、态势展示等子任务
- **威胁计算**：包含威胁识别、威胁评估、威胁预警等子任务
- **辅助决策**：包含方案生成、方案评估、决策推荐等子任务

#### 4.1.2 任务层次结构

```
使命任务
├── ZZ任务
│   ├── 典型功能
│   │   ├── 人机交互流程
│   │   │   ├── 操作序列
│   │   │   │   └── 元操作
```

#### 4.1.3 任务属性

每个任务包含以下属性：
- **复杂度**：任务的复杂程度 (0-1)
- **重要性**：任务的重要程度 (0-1)
- **紧急性**：任务的紧急程度 (0-1)
- **频率**：任务执行频率 (0-1)
- **持续时间**：预计执行时间（秒）
- **错误率**：预期错误发生率 (0-1)
- **工作负荷**：对操作者的负荷 (0-1)

### 4.2 评估方案设置

#### 4.2.1 默认评估指标

系统提供以下默认评估指标：

| 指标类型 | 指标名称 | 权重 | 说明 |
|---------|---------|------|------|
| 负荷指标 | 心理负荷 | 0.10 | 操作者心理工作负荷 |
| 负荷指标 | 物理负荷 | 0.10 | 操作者物理工作负荷 |
| 效率指标 | 任务完成时间 | 0.15 | 完成任务所需时间 |
| 效率指标 | 处理速度 | 0.10 | 信息处理速度 |
| 可靠性指标 | 错误率 | 0.10 | 操作错误发生率 |
| 可靠性指标 | 系统稳定性 | 0.10 | 系统运行稳定性 |
| 可用性指标 | 用户满意度 | 0.15 | 用户满意程度 |
| 安全性指标 | 安全风险等级 | 0.10 | 系统安全风险 |
| 成本指标 | 开发成本 | 0.05 | 系统开发成本 |
| 性能指标 | 响应时间 | 0.05 | 系统响应时间 |

#### 4.2.2 自定义评估方案

用户可以根据具体需求自定义评估方案：

1. **添加指标**：定义新的评估指标
2. **设置权重**：调整各指标的重要性权重
3. **配置属性**：设置指标的取值范围和单位

### 4.3 备选方案管理

#### 4.3.1 方案属性

每个备选方案需要包含以下信息：
- **方案名称**：方案的标识名称
- **方案描述**：方案的详细说明
- **属性值**：各评估指标对应的数值

#### 4.3.2 数据格式

支持以下数据格式：
- **Excel格式**：用于批量导入备选方案
- **JSON格式**：用于配置文件和结果导出

Excel文件格式示例：
| name | description | workload | efficiency | cost |
|------|-------------|----------|------------|------|
| 方案A | 人工主导方案 | 0.7 | 0.6 | 100000 |
| 方案B | 机器主导方案 | 0.3 | 0.8 | 200000 |

### 4.4 决策方法

#### 4.4.1 加权相对偏差距离最小法 (WRDM)

**原理**：通过计算各方案到理想解的加权相对偏差距离，选择距离最小的方案。

**适用场景**：适用于多指标决策问题，特别是指标间存在冲突的情况。

**优点**：
- 考虑了指标的相对重要性
- 能够处理不同量纲的指标
- 计算结果稳定可靠

#### 4.4.2 TOPSIS法

**原理**：基于理想解和负理想解的距离比较进行决策。

**适用场景**：适用于有明确理想解概念的决策问题。

**优点**：
- 概念清晰易理解
- 计算简单高效
- 结果直观

#### 4.4.3 模糊层次分析法 (Fuzzy AHP)

**原理**：结合模糊数学和层次分析法，处理决策中的不确定性。

**适用场景**：适用于存在主观判断和不确定性的决策问题。

**优点**：
- 能处理模糊和不确定信息
- 支持专家经验的融入
- 适合复杂决策环境

## 5. 结果分析

### 5.1 评估结果

系统提供以下评估结果：
- **推荐方案**：综合得分最高的方案
- **方案排序**：所有方案按得分排序
- **详细得分**：各方案在各指标上的得分
- **统计分析**：得分分布的统计信息

### 5.2 敏感性分析

系统自动进行敏感性分析，评估：
- **权重敏感性**：权重变化对结果的影响
- **稳定性指标**：结果的稳定程度
- **关键指标**：对结果影响最大的指标

### 5.3 结果导出

支持多种格式的结果导出：
- **JSON格式**：完整的结果数据
- **Excel格式**：表格形式的结果
- **图表**：可视化的分析图表

## 6. 常见问题

### 6.1 安装问题

**Q: 安装依赖时出现错误怎么办？**
A: 请确保Python版本>=3.8，并尝试升级pip：`pip install --upgrade pip`

**Q: 在Windows上安装scikit-fuzzy失败？**
A: 可能需要安装Visual Studio Build Tools或使用conda安装。

### 6.2 使用问题

**Q: 如何处理缺失的指标数据？**
A: 系统会自动使用默认值填充缺失数据，建议尽量提供完整数据。

**Q: 评估结果不符合预期怎么办？**
A: 检查指标权重设置是否合理，可以调整权重后重新评估。

**Q: 如何选择合适的决策方法？**
A: 建议使用多种方法进行比较，选择结果最稳定的方法。

### 6.3 性能问题

**Q: 处理大量方案时速度很慢？**
A: 可以考虑减少指标数量或使用更高性能的硬件。

**Q: Web界面响应慢？**
A: 检查网络连接，或尝试重启Web服务。

## 7. 技术支持

如需技术支持，请联系：
- 邮箱：<EMAIL>
- 文档：查看项目文档目录
- 问题反馈：通过GitHub Issues提交

## 8. 版本历史

- **v1.0.0**：初始版本，包含基本功能
  - 任务分解功能
  - 多目标决策功能
  - Web界面和命令行界面
  - 基础测试用例
