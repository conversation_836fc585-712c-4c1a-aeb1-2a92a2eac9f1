"""
性能优化和监控模块测试

测试性能监控器、缓存管理器和数据库连接池的功能
"""

import pytest
import time
import tempfile
import os
import threading
from datetime import datetime, timedelta

from src.hmdm.monitoring.performance_monitor import (
    PerformanceMonitor, PerformanceMetric, MetricType, AlertLevel, performance_timer
)
from src.hmdm.optimization.cache_manager import (
    CacheManager, LRUCache, LFUCache, CacheStrategy
)
from src.hmdm.optimization.database_pool import (
    DatabaseConnectionPool, ConnectionState
)


class TestPerformanceMonitor:
    """性能监控器测试"""
    
    def setup_method(self):
        """测试前准备"""
        config = {
            'monitoring_enabled': True,
            'collection_interval': 1,  # 1秒收集间隔
            'retention_period': 3600   # 1小时保留期
        }
        self.monitor = PerformanceMonitor(config)
    
    def teardown_method(self):
        """测试后清理"""
        if self.monitor:
            self.monitor.stop_monitoring_service()
    
    def test_monitor_initialization(self):
        """测试监控器初始化"""
        assert self.monitor is not None
        assert self.monitor.monitoring_enabled is True
        assert self.monitor.collection_interval == 1
        assert len(self.monitor.current_metrics) >= 0
    
    def test_record_metric(self):
        """测试记录指标"""
        self.monitor.record_metric(
            name="test_metric",
            value=100.0,
            metric_type=MetricType.GAUGE,
            description="测试指标"
        )
        
        assert "test_metric" in self.monitor.current_metrics
        metric = self.monitor.current_metrics["test_metric"]
        assert metric.value == 100.0
        assert metric.metric_type == MetricType.GAUGE
        assert metric.description == "测试指标"
    
    def test_record_request_time(self):
        """测试记录请求时间"""
        self.monitor.record_request_time(0.5, "/api/test")
        
        assert len(self.monitor.request_times) == 1
        assert "response_time" in self.monitor.current_metrics
        
        request = list(self.monitor.request_times)[0]
        assert request['duration'] == 0.5
        assert request['endpoint'] == "/api/test"
    
    def test_record_error(self):
        """测试记录错误"""
        self.monitor.record_error("timeout_error", "/api/test")
        
        assert self.monitor.error_counts["timeout_error:/api/test"] == 1
        assert "error_count" in self.monitor.current_metrics
    
    def test_cache_hit_miss(self):
        """测试缓存命中和未命中"""
        # 记录缓存未命中
        self.monitor.record_cache_miss("test_cache")
        assert self.monitor.cache_stats['misses'] == 1
        
        # 记录缓存命中
        self.monitor.record_cache_hit("test_cache")
        assert self.monitor.cache_stats['hits'] == 1
        
        # 检查命中率
        assert "cache_hit_rate" in self.monitor.current_metrics
        hit_rate = self.monitor.current_metrics["cache_hit_rate"].value
        assert hit_rate == 0.5  # 1 hit / (1 hit + 1 miss)
    
    def test_alert_rules(self):
        """测试告警规则"""
        # 添加告警规则
        self.monitor.add_alert_rule(
            metric_name="test_alert_metric",
            threshold=50.0,
            level=AlertLevel.WARNING,
            operator="gt",
            message="测试告警"
        )
        
        # 记录超过阈值的指标
        self.monitor.record_metric("test_alert_metric", 60.0)
        
        # 检查是否触发告警
        alerts = self.monitor.get_alerts(resolved=False)
        assert len(alerts) > 0
        
        alert = alerts[0]
        assert alert.metric_name == "test_alert_metric"
        assert alert.level == AlertLevel.WARNING
        assert alert.current_value == 60.0
        assert alert.threshold == 50.0
    
    def test_performance_summary(self):
        """测试性能摘要"""
        # 记录一些指标
        self.monitor.record_metric("cpu_usage", 75.0)
        self.monitor.record_request_time(0.3, "/api/test")
        self.monitor.record_error("test_error", "/api/test")
        
        summary = self.monitor.get_performance_summary()
        
        assert 'timestamp' in summary
        assert 'system_resources' in summary
        assert 'performance_metrics' in summary
        assert 'top_metrics' in summary
        
        # 检查系统资源信息
        resources = summary['system_resources']
        assert 'cpu_percent' in resources
        assert 'memory_percent' in resources
        assert 'disk_percent' in resources
    
    def test_metrics_history(self):
        """测试指标历史"""
        # 记录多个指标
        for i in range(5):
            self.monitor.record_metric("history_test", float(i))
            time.sleep(0.1)
        
        # 获取历史记录
        history = self.monitor.get_metrics_history("history_test")
        assert len(history) == 5
        
        # 检查时间范围过滤
        now = datetime.now()
        recent_history = self.monitor.get_metrics_history(
            "history_test",
            start_time=now - timedelta(seconds=1)
        )
        assert len(recent_history) <= 5
    
    def test_performance_timer_decorator(self):
        """测试性能计时装饰器"""
        @performance_timer(self.monitor, "test_function_timer")
        def test_function():
            time.sleep(0.1)
            return "test_result"
        
        result = test_function()
        assert result == "test_result"
        assert "test_function_timer" in self.monitor.current_metrics
        
        timer_metric = self.monitor.current_metrics["test_function_timer"]
        assert timer_metric.value >= 0.1  # 至少0.1秒
        assert 'function' in timer_metric.tags
        assert timer_metric.tags['function'] == 'test_function'


class TestCacheManager:
    """缓存管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        config = {
            'default_strategy': CacheStrategy.LRU.value,
            'max_memory_size': 100,
            'enable_disk_cache': True,
            'disk_cache_dir': self.temp_dir
        }
        self.cache_manager = CacheManager(config)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_manager_initialization(self):
        """测试缓存管理器初始化"""
        assert self.cache_manager is not None
        assert self.cache_manager.default_strategy == CacheStrategy.LRU
        assert self.cache_manager.l1_cache is not None
        assert self.cache_manager.l2_cache is not None
    
    def test_cache_put_get(self):
        """测试缓存存取"""
        # 存储值
        success = self.cache_manager.put("test_key", "test_value")
        assert success is True
        
        # 获取值
        value = self.cache_manager.get("test_key")
        assert value == "test_value"
        
        # 获取不存在的键
        value = self.cache_manager.get("nonexistent_key", "default")
        assert value == "default"
    
    def test_cache_delete(self):
        """测试缓存删除"""
        # 存储值
        self.cache_manager.put("delete_test", "value")
        assert self.cache_manager.get("delete_test") == "value"
        
        # 删除值
        success = self.cache_manager.delete("delete_test")
        assert success is True
        
        # 确认已删除
        value = self.cache_manager.get("delete_test")
        assert value is None
    
    def test_cache_ttl(self):
        """测试缓存TTL"""
        # 存储带TTL的值
        self.cache_manager.put("ttl_test", "value", ttl=1)  # 1秒TTL
        
        # 立即获取应该成功
        value = self.cache_manager.get("ttl_test")
        assert value == "value"
        
        # 等待TTL过期
        time.sleep(1.5)
        
        # 再次获取应该失败
        value = self.cache_manager.get("ttl_test")
        assert value is None
    
    def test_cache_stats(self):
        """测试缓存统计"""
        # 执行一些缓存操作
        self.cache_manager.put("stats_test1", "value1")
        self.cache_manager.put("stats_test2", "value2")
        self.cache_manager.get("stats_test1")  # 命中
        self.cache_manager.get("nonexistent")  # 未命中
        
        stats = self.cache_manager.get_stats()
        
        assert 'hit_rate' in stats
        assert 'total_requests' in stats
        assert 'hits' in stats
        assert 'misses' in stats
        assert 'l1_cache' in stats
        
        assert stats['hits'] >= 1
        assert stats['misses'] >= 1
        assert stats['total_requests'] >= 2
    
    def test_cache_decorator(self):
        """测试缓存装饰器"""
        call_count = 0
        
        @self.cache_manager.cache_decorator(ttl=60)
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # 第一次调用
        result1 = expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 第二次调用相同参数，应该从缓存获取
        result2 = expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 没有增加
        
        # 不同参数的调用
        result3 = expensive_function(2, 3)
        assert result3 == 5
        assert call_count == 2  # 增加了


class TestLRUCache:
    """LRU缓存测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.cache = LRUCache(max_size=3)
    
    def test_lru_basic_operations(self):
        """测试LRU基本操作"""
        # 存储值
        assert self.cache.put("key1", "value1") is True
        assert self.cache.put("key2", "value2") is True
        assert self.cache.put("key3", "value3") is True
        
        # 获取值
        assert self.cache.get("key1") == "value1"
        assert self.cache.get("key2") == "value2"
        assert self.cache.get("key3") == "value3"
        
        # 缓存大小
        assert self.cache.size() == 3
    
    def test_lru_eviction(self):
        """测试LRU淘汰策略"""
        # 填满缓存
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        self.cache.put("key3", "value3")
        
        # 访问key1，使其成为最近使用
        self.cache.get("key1")
        
        # 添加新项，应该淘汰key2（最久未使用）
        self.cache.put("key4", "value4")
        
        assert self.cache.get("key1") == "value1"  # 仍然存在
        assert self.cache.get("key2") is None      # 被淘汰
        assert self.cache.get("key3") == "value3"  # 仍然存在
        assert self.cache.get("key4") == "value4"  # 新添加的


class TestLFUCache:
    """LFU缓存测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.cache = LFUCache(max_size=3)
    
    def test_lfu_basic_operations(self):
        """测试LFU基本操作"""
        # 存储值
        assert self.cache.put("key1", "value1") is True
        assert self.cache.put("key2", "value2") is True
        assert self.cache.put("key3", "value3") is True
        
        # 获取值
        assert self.cache.get("key1") == "value1"
        assert self.cache.get("key2") == "value2"
        assert self.cache.get("key3") == "value3"
        
        # 缓存大小
        assert self.cache.size() == 3
    
    def test_lfu_eviction(self):
        """测试LFU淘汰策略"""
        # 填满缓存
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        self.cache.put("key3", "value3")
        
        # 多次访问key1和key3，增加其使用频率
        self.cache.get("key1")
        self.cache.get("key1")
        self.cache.get("key3")
        
        # 添加新项，应该淘汰key2（使用频率最低）
        self.cache.put("key4", "value4")
        
        assert self.cache.get("key1") == "value1"  # 仍然存在
        assert self.cache.get("key2") is None      # 被淘汰
        assert self.cache.get("key3") == "value3"  # 仍然存在
        assert self.cache.get("key4") == "value4"  # 新添加的


class TestDatabaseConnectionPool:
    """数据库连接池测试"""
    
    def setup_method(self):
        """测试前准备"""
        config = {
            'database_url': ':memory:',
            'min_connections': 2,
            'max_connections': 5,
            'connection_timeout': 5,
            'health_check_interval': 1
        }
        self.pool = DatabaseConnectionPool(config)
    
    def teardown_method(self):
        """测试后清理"""
        if self.pool:
            self.pool.close_pool()
    
    def test_pool_initialization(self):
        """测试连接池初始化"""
        assert self.pool is not None
        assert self.pool.min_connections == 2
        assert self.pool.max_connections == 5
        
        # 检查初始连接数
        stats = self.pool.get_stats()
        assert stats['current_state']['total_connections'] >= 2
    
    def test_get_connection(self):
        """测试获取连接"""
        with self.pool.get_connection() as conn:
            assert conn is not None
            
            # 执行简单查询
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
            cursor.close()
    
    def test_multiple_connections(self):
        """测试多个连接"""
        connections = []
        
        # 获取多个连接
        for i in range(3):
            conn_context = self.pool.get_connection()
            connections.append(conn_context)
        
        # 使用连接
        for i, conn_context in enumerate(connections):
            with conn_context as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT {i + 1}")
                result = cursor.fetchone()
                assert result[0] == i + 1
                cursor.close()
    
    def test_connection_pool_stats(self):
        """测试连接池统计"""
        # 获取一个连接
        with self.pool.get_connection() as conn:
            stats = self.pool.get_stats()
            
            assert 'pool_config' in stats
            assert 'current_state' in stats
            assert 'lifetime_stats' in stats
            
            # 检查配置信息
            config = stats['pool_config']
            assert config['min_connections'] == 2
            assert config['max_connections'] == 5
            
            # 检查当前状态
            current = stats['current_state']
            assert current['active_connections'] >= 1
            assert current['total_connections'] >= 2
    
    def test_connection_timeout(self):
        """测试连接超时"""
        # 获取所有可用连接
        contexts = []
        for _ in range(self.pool.max_connections):
            try:
                ctx = self.pool.get_connection()
                contexts.append(ctx)
            except:
                break
        
        # 尝试获取超出限制的连接，应该超时
        start_time = time.time()
        try:
            with self.pool.get_connection() as conn:
                pass
            # 如果没有抛出异常，说明获取到了连接（可能是因为其他连接被释放了）
        except Exception:
            # 预期的超时异常
            elapsed = time.time() - start_time
            assert elapsed >= 1  # 至少等待了一段时间
        
        # 释放连接
        for ctx in contexts:
            try:
                ctx.__exit__(None, None, None)
            except:
                pass
