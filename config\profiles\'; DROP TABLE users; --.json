{"name": "'; DROP TABLE users; --", "description": "测试描述", "created_time": "2025-09-08T15:51:53.447734", "modified_time": "2025-09-08T15:51:53.447734", "system_config": {"system_name": "测试HMDM系统", "version": "1.0.0-test", "description": "基于人机协同的军事综合决策支持系统", "environment": "testing", "debug_mode": false, "log_level": "ERROR", "log_file": "logs/hmdm.log", "log_max_size": 10485760, "log_backup_count": 5, "enable_console_log": true, "enabled_modules": ["knowledge_base", "situation_awareness"], "host": "127.0.0.1", "port": 5000, "max_connections": 100, "data_directory": "data", "backup_directory": "backups", "temp_directory": "temp", "allocation_config_file": null, "allocation_config": {"allocation_mode": "semi_automatic", "optimization_objective": "balanced", "default_scheme_count": 5, "max_scheme_count": 10, "min_scheme_count": 2, "decision_threshold": 0.1, "confidence_threshold": 0.7, "max_decision_iterations": 3, "capability_weights": {"cognitive": 0.25, "physical": 0.15, "perceptual": 0.2, "decision_making": 0.25, "execution": 0.1, "coordination": 0.05}, "evaluation_weights": {"task_completion": 0.25, "time_efficiency": 0.2, "resource_utilization": 0.15, "error_rate": 0.15, "coordination_overhead": 0.15, "adaptability": 0.1}, "baseline_performance": {"human_only": {"completion_rate": 0.85, "error_rate": 0.12, "efficiency": 0.7}, "machine_only": {"completion_rate": 0.95, "error_rate": 0.05, "efficiency": 0.9}, "collaboration": {"completion_rate": 0.92, "error_rate": 0.08, "efficiency": 0.85}}, "constraints": {"max_human_workload": 0.8, "min_machine_utilization": 0.3, "max_coordination_overhead": 0.4, "min_task_completion_rate": 0.8}, "preferences": {"prefer_human_decision": true, "prefer_machine_execution": true, "allow_full_automation": false, "require_human_oversight": true}, "enable_real_time_optimization": true, "optimization_interval": 300, "performance_monitoring_interval": 60, "enable_detailed_logging": true, "log_allocation_decisions": true, "log_performance_metrics": true, "debug_mode": false}, "security_level": "秘密", "enable_encryption": true, "enable_audit_log": true, "max_concurrent_tasks": 15, "task_timeout": 300, "memory_limit": 1024, "enable_monitoring": true, "monitoring_interval": 60, "health_check_interval": 30}, "allocation_config": {"allocation_mode": "semi_automatic", "optimization_objective": "balanced", "default_scheme_count": 5, "max_scheme_count": 10, "min_scheme_count": 2, "decision_threshold": 0.1, "confidence_threshold": 0.7, "max_decision_iterations": 3, "capability_weights": {"cognitive": 0.25, "physical": 0.15, "perceptual": 0.2, "decision_making": 0.25, "execution": 0.1, "coordination": 0.05}, "evaluation_weights": {"task_completion": 0.25, "time_efficiency": 0.2, "resource_utilization": 0.15, "error_rate": 0.15, "coordination_overhead": 0.15, "adaptability": 0.1}, "baseline_performance": {"human_only": {"completion_rate": 0.85, "error_rate": 0.12, "efficiency": 0.7}, "machine_only": {"completion_rate": 0.95, "error_rate": 0.05, "efficiency": 0.9}, "collaboration": {"completion_rate": 0.92, "error_rate": 0.08, "efficiency": 0.85}}, "constraints": {"max_human_workload": 0.8, "min_machine_utilization": 0.3, "max_coordination_overhead": 0.4, "min_task_completion_rate": 0.8}, "preferences": {"prefer_human_decision": true, "prefer_machine_execution": true, "allow_full_automation": false, "require_human_oversight": true}, "enable_real_time_optimization": true, "optimization_interval": 300, "performance_monitoring_interval": 60, "enable_detailed_logging": true, "log_allocation_decisions": true, "log_performance_metrics": true, "debug_mode": false}, "custom_settings": {}}