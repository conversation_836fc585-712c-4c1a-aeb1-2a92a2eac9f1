"""
Web安全中间件

为Web应用提供安全认证、权限控制和审计功能。
"""

import functools
import logging
from typing import Optional, List, Callable, Any
from flask import request, jsonify, session, g
from datetime import datetime

from .enhanced_security import EnhancedSecurityManager, PermissionType, AuditEventType
from .military_security import SecurityLevel


class WebSecurityMiddleware:
    """Web安全中间件"""
    
    def __init__(self, security_manager: EnhancedSecurityManager):
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
    
    def require_auth(self, required_permissions: List[PermissionType] = None,
                    required_security_level: SecurityLevel = SecurityLevel.INTERNAL):
        """认证装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                # 获取认证令牌
                token = self._get_auth_token()
                if not token:
                    return jsonify({
                        'success': False,
                        'error': '未提供认证令牌',
                        'code': 'AUTH_TOKEN_MISSING'
                    }), 401
                
                # 验证会话
                user = self.security_manager.validate_session(token)
                if not user:
                    return jsonify({
                        'success': False,
                        'error': '认证令牌无效或已过期',
                        'code': 'AUTH_TOKEN_INVALID'
                    }), 401
                
                # 检查权限
                if required_permissions:
                    for permission in required_permissions:
                        if not self.security_manager.check_permission(
                            user, request.endpoint or 'unknown', 
                            permission, required_security_level):
                            
                            self._log_access_denied(user, permission, required_security_level)
                            return jsonify({
                                'success': False,
                                'error': '权限不足',
                                'code': 'PERMISSION_DENIED'
                            }), 403
                
                # 将用户信息存储到请求上下文
                g.current_user = user
                
                # 记录访问日志
                self._log_access(user, request.endpoint or 'unknown')
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def require_admin(self):
        """管理员权限装饰器"""
        return self.require_auth(
            required_permissions=[PermissionType.ADMIN],
            required_security_level=SecurityLevel.SECRET
        )
    
    def require_audit(self):
        """审计权限装饰器"""
        return self.require_auth(
            required_permissions=[PermissionType.AUDIT],
            required_security_level=SecurityLevel.CONFIDENTIAL
        )
    
    def login_required(self):
        """登录要求装饰器（仅验证登录状态）"""
        return self.require_auth(required_permissions=[])
    
    def rate_limit(self, max_requests: int = 100, window_seconds: int = 3600):
        """请求频率限制装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                # 获取客户端IP
                client_ip = self._get_client_ip()
                
                # 这里可以实现更复杂的频率限制逻辑
                # 简化实现，实际应用中可以使用Redis等存储
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def audit_action(self, action: str, resource: str = None):
        """审计动作装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                user = getattr(g, 'current_user', None)
                start_time = datetime.now()
                
                try:
                    result = f(*args, **kwargs)
                    
                    # 记录成功的审计事件
                    if user:
                        self.security_manager._log_audit_event(
                            event_type=AuditEventType.ACCESS,
                            user_id=user.user_id,
                            username=user.username,
                            resource=resource or request.endpoint or 'unknown',
                            action=action,
                            result='success',
                            ip_address=self._get_client_ip(),
                            user_agent=request.headers.get('User-Agent', ''),
                            details={
                                'method': request.method,
                                'path': request.path,
                                'duration_ms': (datetime.now() - start_time).total_seconds() * 1000
                            }
                        )
                    
                    return result
                    
                except Exception as e:
                    # 记录失败的审计事件
                    if user:
                        self.security_manager._log_audit_event(
                            event_type=AuditEventType.ACCESS,
                            user_id=user.user_id,
                            username=user.username,
                            resource=resource or request.endpoint or 'unknown',
                            action=action,
                            result='failure',
                            ip_address=self._get_client_ip(),
                            user_agent=request.headers.get('User-Agent', ''),
                            details={
                                'method': request.method,
                                'path': request.path,
                                'error': str(e),
                                'duration_ms': (datetime.now() - start_time).total_seconds() * 1000
                            }
                        )
                    
                    raise
            
            return decorated_function
        return decorator
    
    def secure_headers(self, f: Callable) -> Callable:
        """安全头装饰器"""
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)
            
            # 添加安全头
            if hasattr(response, 'headers'):
                response.headers['X-Content-Type-Options'] = 'nosniff'
                response.headers['X-Frame-Options'] = 'DENY'
                response.headers['X-XSS-Protection'] = '1; mode=block'
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
                response.headers['Content-Security-Policy'] = "default-src 'self'"
                response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            return response
        
        return decorated_function
    
    def validate_input(self, schema: dict):
        """输入验证装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                # 获取请求数据
                if request.is_json:
                    data = request.get_json()
                else:
                    data = request.form.to_dict()
                
                # 验证数据（简化实现）
                for field, rules in schema.items():
                    if rules.get('required', False) and field not in data:
                        return jsonify({
                            'success': False,
                            'error': f'缺少必需字段: {field}',
                            'code': 'VALIDATION_ERROR'
                        }), 400
                    
                    if field in data:
                        value = data[field]
                        
                        # 类型验证
                        if 'type' in rules:
                            expected_type = rules['type']
                            if expected_type == 'string' and not isinstance(value, str):
                                return jsonify({
                                    'success': False,
                                    'error': f'字段 {field} 必须是字符串',
                                    'code': 'VALIDATION_ERROR'
                                }), 400
                            elif expected_type == 'integer' and not isinstance(value, int):
                                return jsonify({
                                    'success': False,
                                    'error': f'字段 {field} 必须是整数',
                                    'code': 'VALIDATION_ERROR'
                                }), 400
                        
                        # 长度验证
                        if 'min_length' in rules and len(str(value)) < rules['min_length']:
                            return jsonify({
                                'success': False,
                                'error': f'字段 {field} 长度不能少于 {rules["min_length"]} 个字符',
                                'code': 'VALIDATION_ERROR'
                            }), 400
                        
                        if 'max_length' in rules and len(str(value)) > rules['max_length']:
                            return jsonify({
                                'success': False,
                                'error': f'字段 {field} 长度不能超过 {rules["max_length"]} 个字符',
                                'code': 'VALIDATION_ERROR'
                            }), 400
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def _get_auth_token(self) -> Optional[str]:
        """获取认证令牌"""
        # 从Authorization头获取
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header[7:]
        
        # 从查询参数获取
        token = request.args.get('token')
        if token:
            return token
        
        # 从会话获取
        return session.get('auth_token')
    
    def _get_client_ip(self) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or 'unknown'
    
    def _log_access(self, user, resource: str):
        """记录访问日志"""
        self.security_manager._log_audit_event(
            event_type=AuditEventType.ACCESS,
            user_id=user.user_id,
            username=user.username,
            resource=resource,
            action=request.method.lower(),
            result='success',
            ip_address=self._get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            details={
                'path': request.path,
                'method': request.method
            }
        )
    
    def _log_access_denied(self, user, required_permission: PermissionType, 
                          required_security_level: SecurityLevel):
        """记录访问拒绝日志"""
        self.security_manager._log_audit_event(
            event_type=AuditEventType.SECURITY_VIOLATION,
            user_id=user.user_id,
            username=user.username,
            resource=request.endpoint or 'unknown',
            action='access_denied',
            result='permission_denied',
            ip_address=self._get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            details={
                'required_permission': required_permission.value,
                'required_security_level': required_security_level.value,
                'user_security_level': user.security_level.value,
                'user_permissions': [p.value for p in user.permissions]
            }
        )
