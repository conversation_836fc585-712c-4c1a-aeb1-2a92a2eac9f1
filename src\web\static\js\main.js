// HMDM Web界面JavaScript

class HMDMApp {
    constructor() {
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStatus();
        this.showWelcomeMessage();
    }

    bindEvents() {
        // 任务分解相关
        document.getElementById('create-task-btn').addEventListener('click', () => this.createTask());
        
        // 评估方案相关
        document.getElementById('create-scheme-btn').addEventListener('click', () => this.createEvaluationScheme());
        document.getElementById('add-alternatives-btn').addEventListener('click', () => this.showAddAlternativesModal());
        document.getElementById('save-alternatives-btn').addEventListener('click', () => this.saveAlternatives());
        document.getElementById('add-alternative-item-btn').addEventListener('click', () => this.addAlternativeItem());
        
        // 评估执行相关
        document.getElementById('run-evaluation-btn').addEventListener('click', () => this.runEvaluation());
        document.getElementById('run-comparison-btn').addEventListener('click', () => this.runComparison());
        
        // 数据管理相关
        document.getElementById('import-data-btn').addEventListener('click', () => this.showImportDataModal());
        document.getElementById('upload-file-btn').addEventListener('click', () => this.uploadFile());
        document.getElementById('export-results-btn').addEventListener('click', () => this.exportResults());
        document.getElementById('clear-session-btn').addEventListener('click', () => this.clearSession());
    }

    showWelcomeMessage() {
        this.showAlert('欢迎使用人机功能分配模型系统！请按照步骤创建任务结构、设置评估方案、添加备选方案，然后执行评估。', 'info');
    }

    async updateStatus() {
        try {
            const response = await fetch(`${this.apiBase}/session/status`);
            const result = await response.json();
            
            if (result.success) {
                const data = result.data;
                
                // 更新状态显示
                document.getElementById('task-status').textContent = data.has_task_hierarchy ? '已创建' : '未创建';
                document.getElementById('task-status').className = `status-value ${data.has_task_hierarchy ? 'success' : 'danger'}`;
                
                document.getElementById('scheme-status').textContent = data.has_evaluation_scheme ? '已创建' : '未创建';
                document.getElementById('scheme-status').className = `status-value ${data.has_evaluation_scheme ? 'success' : 'danger'}`;
                
                document.getElementById('alternatives-status').textContent = `${data.alternatives_count}个`;
                document.getElementById('alternatives-status').className = `status-value ${data.alternatives_count > 0 ? 'success' : 'warning'}`;
                
                document.getElementById('results-status').textContent = data.evaluation_results_count > 0 ? '已完成' : '无';
                document.getElementById('results-status').className = `status-value ${data.evaluation_results_count > 0 ? 'success' : 'warning'}`;
                
                // 更新按钮状态
                document.getElementById('export-results-btn').disabled = data.evaluation_results_count === 0;
            }
        } catch (error) {
            console.error('更新状态失败:', error);
        }
    }

    async createTask() {
        const scenario = document.getElementById('scenario-select').value;
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBase}/task/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ scenario })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayTaskHierarchy(result.data.hierarchy);
                this.showAlert(`成功创建${scenario}任务结构，包含${result.data.task_count}个任务`, 'success');
                this.updateStatus();
            } else {
                this.showAlert(`创建任务失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`创建任务失败: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displayTaskHierarchy(hierarchy) {
        const display = document.getElementById('task-hierarchy-display');
        const rootTask = hierarchy.tasks[hierarchy.root_task_id];
        
        if (rootTask) {
            const treeHtml = this.buildTaskTree(hierarchy, rootTask, 0);
            display.innerHTML = `<ul class="task-tree">${treeHtml}</ul>`;
        }
    }

    buildTaskTree(hierarchy, task, level) {
        let html = `<li>
            <span class="task-item">${task.name}</span>
            <span class="task-type">(${task.task_type})</span>
        `;
        
        if (task.children_ids && task.children_ids.length > 0) {
            html += '<ul>';
            for (const childId of task.children_ids) {
                const childTask = hierarchy.tasks[childId];
                if (childTask) {
                    html += this.buildTaskTree(hierarchy, childTask, level + 1);
                }
            }
            html += '</ul>';
        }
        
        html += '</li>';
        return html;
    }

    async createEvaluationScheme() {
        const schemeType = document.getElementById('scheme-type').value;
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBase}/evaluation/scheme/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: schemeType })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert(`成功创建评估方案，包含${result.data.indicator_count}个指标`, 'success');
                this.updateStatus();
            } else {
                this.showAlert(`创建评估方案失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`创建评估方案失败: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    showAddAlternativesModal() {
        const modal = new bootstrap.Modal(document.getElementById('addAlternativesModal'));
        modal.show();
    }

    addAlternativeItem() {
        const form = document.getElementById('alternatives-form');
        const newItem = document.createElement('div');
        newItem.className = 'alternative-item mb-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control" placeholder="方案名称" name="name">
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control" placeholder="方案描述" name="description">
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-4">
                    <input type="number" class="form-control" placeholder="工作负荷 (0-1)" name="workload" step="0.01" min="0" max="1">
                </div>
                <div class="col-md-4">
                    <input type="number" class="form-control" placeholder="效率 (0-1)" name="efficiency" step="0.01" min="0" max="1">
                </div>
                <div class="col-md-4">
                    <input type="number" class="form-control" placeholder="成本" name="cost" step="1000">
                </div>
            </div>
            <div class="mt-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="this.parentElement.parentElement.remove()">
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        `;
        form.appendChild(newItem);
    }

    async saveAlternatives() {
        const form = document.getElementById('alternatives-form');
        const items = form.querySelectorAll('.alternative-item');
        const alternatives = [];
        
        items.forEach(item => {
            const name = item.querySelector('input[name="name"]').value;
            const description = item.querySelector('input[name="description"]').value;
            const workload = parseFloat(item.querySelector('input[name="workload"]').value) || 0;
            const efficiency = parseFloat(item.querySelector('input[name="efficiency"]').value) || 0;
            const cost = parseFloat(item.querySelector('input[name="cost"]').value) || 0;
            
            if (name) {
                alternatives.push({
                    name,
                    description,
                    attributes: {
                        workload,
                        efficiency,
                        cost
                    }
                });
            }
        });
        
        if (alternatives.length === 0) {
            this.showAlert('请至少添加一个备选方案', 'warning');
            return;
        }
        
        try {
            const response = await fetch(`${this.apiBase}/alternatives/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ alternatives })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayAlternatives(result.data.alternatives);
                this.showAlert(`成功添加${result.data.count}个备选方案`, 'success');
                this.updateStatus();
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addAlternativesModal'));
                modal.hide();
            } else {
                this.showAlert(`添加备选方案失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`添加备选方案失败: ${error.message}`, 'danger');
        }
    }

    displayAlternatives(alternatives) {
        const display = document.getElementById('alternatives-list');
        
        if (alternatives.length === 0) {
            display.innerHTML = '<p class="text-muted text-center">暂无备选方案</p>';
            return;
        }
        
        let html = '';
        alternatives.forEach(alt => {
            html += `
                <div class="alternative-item-display">
                    <div class="alternative-name">${alt.name}</div>
                    <div class="alternative-description">${alt.description}</div>
                    <div class="alternative-attributes">
                        ${Object.entries(alt.attributes).map(([key, value]) => 
                            `<span class="attribute-tag">${key}: ${value}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        });
        
        display.innerHTML = html;
    }

    async runEvaluation() {
        const method = document.getElementById('decision-method').value;
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBase}/evaluation/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ method })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayEvaluationResults(result.data);
                this.showAlert(`使用${method}方法评估完成`, 'success');
                this.updateStatus();
            } else {
                this.showAlert(`评估失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`评估失败: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displayEvaluationResults(data) {
        const display = document.getElementById('results-display');
        const recommended = data.recommended_alternative;
        const evaluation = data.evaluation_result;
        
        let html = `
            <div class="result-section">
                <div class="result-title">推荐方案</div>
                <div class="recommended-alternative">
                    <div class="alternative-name">${recommended.name}</div>
                    <div class="alternative-description">${recommended.description}</div>
                    <div class="score-display">总分: ${evaluation.total_score.toFixed(4)}</div>
                    <div class="score-display">标准化得分: ${evaluation.normalized_score.toFixed(4)}</div>
                </div>
            </div>
        `;
        
        // 显示详细指标得分
        if (evaluation.indicator_values) {
            html += `
                <div class="result-section">
                    <div class="result-title">详细指标得分</div>
                    <table class="table table-striped results-table">
                        <thead>
                            <tr>
                                <th>指标</th>
                                <th>原始值</th>
                                <th>标准化值</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            Object.values(evaluation.indicator_values).forEach(indicator => {
                html += `
                    <tr>
                        <td>${indicator.indicator_id}</td>
                        <td>${indicator.raw_value.toFixed(4)}</td>
                        <td>${indicator.normalized_value.toFixed(4)}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
        }
        
        display.innerHTML = html;
    }

    async runComparison() {
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBase}/comparison/run`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayComparisonResults(result.data);
                this.showAlert('方案比较完成', 'success');
                this.updateStatus();
            } else {
                this.showAlert(`比较失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`比较失败: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displayComparisonResults(data) {
        const display = document.getElementById('results-display');
        
        let html = `
            <div class="result-section">
                <div class="result-title">方案排序结果</div>
                <table class="table table-striped results-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>方案名称</th>
                            <th>总分</th>
                            <th>标准化得分</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.ranking_by_total_score.forEach(item => {
            const rankClass = item.rank <= 3 ? `rank-${item.rank}` : '';
            html += `
                <tr class="${rankClass}">
                    <td><strong>${item.rank}</strong></td>
                    <td>${item.alternative_name}</td>
                    <td>${item.total_score.toFixed(4)}</td>
                    <td>${item.normalized_score.toFixed(4)}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        
        // 显示统计信息
        const stats = data.statistical_analysis;
        html += `
            <div class="result-section">
                <div class="result-title">统计信息</div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="status-item">
                            <span class="status-label">平均得分</span>
                            <span class="status-value">${stats.mean_score.toFixed(4)}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <span class="status-label">标准差</span>
                            <span class="status-value">${stats.std_score.toFixed(4)}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <span class="status-label">最高分</span>
                            <span class="status-value">${stats.max_score.toFixed(4)}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-item">
                            <span class="status-label">得分范围</span>
                            <span class="status-value">${stats.score_range.toFixed(4)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        display.innerHTML = html;
    }

    showImportDataModal() {
        const modal = new bootstrap.Modal(document.getElementById('importDataModal'));
        modal.show();
    }

    async uploadFile() {
        const fileInput = document.getElementById('import-file');
        const file = fileInput.files[0];
        
        if (!file) {
            this.showAlert('请选择文件', 'warning');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', file);
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBase}/data/import`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('数据导入成功', 'success');
                this.updateStatus();
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('importDataModal'));
                modal.hide();
            } else {
                this.showAlert(`导入失败: ${result.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`导入失败: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    async exportResults() {
        try {
            const response = await fetch(`${this.apiBase}/data/export`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: 'json' })
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'hmdm_results.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showAlert('结果导出成功', 'success');
            } else {
                this.showAlert('导出失败', 'danger');
            }
        } catch (error) {
            this.showAlert(`导出失败: ${error.message}`, 'danger');
        }
    }

    async clearSession() {
        if (!confirm('确定要清空所有数据吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiBase}/session/clear`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 清空显示
                document.getElementById('task-hierarchy-display').innerHTML = '<p class="text-muted text-center">任务层次结构将在这里显示</p>';
                document.getElementById('alternatives-list').innerHTML = '<p class="text-muted text-center">备选方案列表</p>';
                document.getElementById('results-display').innerHTML = '<p class="text-muted text-center">评估结果将在这里显示</p>';
                
                this.showAlert('数据已清空', 'success');
                this.updateStatus();
            } else {
                this.showAlert('清空失败', 'danger');
            }
        } catch (error) {
            this.showAlert(`清空失败: ${error.message}`, 'danger');
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showAlert(message, type = 'info') {
        // 创建警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面顶部
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // 自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new HMDMApp();
});
