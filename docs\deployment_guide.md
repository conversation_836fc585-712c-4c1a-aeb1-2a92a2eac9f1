# HMDM 系统部署指南

## 1. 部署概述

本指南介绍如何在不同环境中部署人机功能分配模型系统（HMDM）。

### 1.1 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Web服务器     │    │   计算引擎      │
│   (Browser)     │◄──►│   (Flask)       │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   数据存储      │
                       │   (JSON/Excel)  │
                       └─────────────────┘
```

### 1.2 系统要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **操作系统**: Windows 10/Linux/macOS

#### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 20GB 可用空间
- **操作系统**: Windows Server/Ubuntu Server/CentOS

## 2. 开发环境部署

### 2.1 本地开发环境

#### 步骤1: 环境准备
```bash
# 检查Python版本
python --version  # 需要 >= 3.8

# 克隆项目
git clone <repository-url>
cd HMDM

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

#### 步骤2: 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements.txt[dev]
```

#### 步骤3: 验证安装
```bash
# 运行测试
python -m pytest tests/ -v

# 启动Web服务
python -m src.web.app

# 测试命令行工具
python -m src.cli.main --help
```

### 2.2 开发工具配置

#### VS Code配置
创建 `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"]
}
```

#### PyCharm配置
1. 打开项目目录
2. 设置Python解释器为 `venv/Scripts/python.exe`
3. 配置代码格式化工具为Black
4. 设置测试框架为pytest

## 3. 生产环境部署

### 3.1 Linux服务器部署

#### 步骤1: 系统准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和相关工具
sudo apt install python3.8 python3.8-venv python3-pip nginx supervisor -y

# 创建应用用户
sudo useradd -m -s /bin/bash hmdm
sudo su - hmdm
```

#### 步骤2: 应用部署
```bash
# 克隆项目
git clone <repository-url> /home/<USER>/HMDM
cd /home/<USER>/HMDM

# 创建虚拟环境
python3.8 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn
```

#### 步骤3: 配置Gunicorn
创建 `gunicorn.conf.py`:
```python
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

创建启动脚本 `start_gunicorn.sh`:
```bash
#!/bin/bash
cd /home/<USER>/HMDM
source venv/bin/activate
exec gunicorn -c gunicorn.conf.py src.web.app:app
```

#### 步骤4: 配置Supervisor
创建 `/etc/supervisor/conf.d/hmdm.conf`:
```ini
[program:hmdm]
command=/home/<USER>/HMDM/start_gunicorn.sh
directory=/home/<USER>/HMDM
user=hmdm
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/hmdm.log
```

启动服务:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start hmdm
```

#### 步骤5: 配置Nginx
创建 `/etc/nginx/sites-available/hmdm`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /home/<USER>/HMDM/src/web/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点:
```bash
sudo ln -s /etc/nginx/sites-available/hmdm /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3.2 Windows服务器部署

#### 步骤1: 安装Python
1. 下载Python 3.8+安装包
2. 安装时勾选"Add Python to PATH"
3. 验证安装: `python --version`

#### 步骤2: 部署应用
```cmd
# 克隆项目
git clone <repository-url> C:\HMDM
cd C:\HMDM

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
pip install waitress
```

#### 步骤3: 创建Windows服务
安装NSSM (Non-Sucking Service Manager):
```cmd
# 下载NSSM并解压到C:\nssm

# 创建服务
C:\nssm\nssm.exe install HMDM
```

配置服务参数:
- **Path**: `C:\HMDM\venv\Scripts\python.exe`
- **Startup directory**: `C:\HMDM`
- **Arguments**: `-m waitress --host=0.0.0.0 --port=5000 src.web.app:app`

启动服务:
```cmd
net start HMDM
```

### 3.3 Docker部署

#### Dockerfile
```dockerfile
FROM python:3.8-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 hmdm && chown -R hmdm:hmdm /app
USER hmdm

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "-m", "src.web.app"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  hmdm:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - hmdm
    restart: unless-stopped
```

#### 部署命令
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 4. 配置管理

### 4.1 环境变量配置

创建 `.env` 文件:
```env
# 应用配置
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 数据配置
DATA_DIR=data
UPLOAD_DIR=uploads
```

### 4.2 配置文件

创建 `config/production.py`:
```python
import os

class ProductionConfig:
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get('SECRET_KEY')
    LOG_LEVEL = 'INFO'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 数据库配置（如果使用）
    DATABASE_URL = os.environ.get('DATABASE_URL')
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
```

## 5. 监控和维护

### 5.1 日志管理

#### 日志配置
```python
# config/logging.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/hmdm.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'default',
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file']
    }
}
```

#### 日志轮转
```bash
# 使用logrotate管理日志
sudo vim /etc/logrotate.d/hmdm

/home/<USER>/HMDM/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 hmdm hmdm
}
```

### 5.2 性能监控

#### 系统监控脚本
```bash
#!/bin/bash
# monitor.sh

# 检查服务状态
if ! pgrep -f "gunicorn.*hmdm" > /dev/null; then
    echo "HMDM service is down!"
    sudo supervisorctl restart hmdm
fi

# 检查磁盘空间
DISK_USAGE=$(df /home/<USER>/HMDM | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is high: ${DISK_USAGE}%"
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
    echo "Memory usage is high: ${MEM_USAGE}%"
fi
```

### 5.3 备份策略

#### 数据备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/hmdm"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用数据
tar -czf $BACKUP_DIR/hmdm_data_$DATE.tar.gz /home/<USER>/HMDM/data

# 备份配置文件
tar -czf $BACKUP_DIR/hmdm_config_$DATE.tar.gz /home/<USER>/HMDM/config

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## 6. 故障排除

### 6.1 常见问题

#### 服务无法启动
```bash
# 检查日志
sudo tail -f /var/log/hmdm.log

# 检查端口占用
sudo netstat -tlnp | grep :5000

# 检查权限
ls -la /home/<USER>/HMDM/
```

#### 内存不足
```bash
# 检查内存使用
free -h

# 检查进程内存使用
ps aux --sort=-%mem | head

# 重启服务释放内存
sudo supervisorctl restart hmdm
```

#### 磁盘空间不足
```bash
# 检查磁盘使用
df -h

# 清理日志文件
sudo find /var/log -name "*.log" -mtime +30 -delete

# 清理临时文件
sudo rm -rf /tmp/*
```

### 6.2 性能优化

#### 应用优化
- 启用缓存机制
- 优化数据库查询
- 使用异步处理
- 启用压缩

#### 系统优化
- 调整内核参数
- 优化文件系统
- 配置swap分区
- 使用SSD存储

## 7. 安全配置

### 7.1 网络安全

#### 防火墙配置
```bash
# Ubuntu UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 7.2 应用安全

#### 安全配置
```python
# 安全头设置
from flask_talisman import Talisman

Talisman(app, {
    'force_https': True,
    'strict_transport_security': True,
    'content_security_policy': {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline'",
        'style-src': "'self' 'unsafe-inline'"
    }
})
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，包括监控、维护和安全配置，确保HMDM系统能够稳定可靠地运行。
