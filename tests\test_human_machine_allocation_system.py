"""
人机功能分配系统测试
"""

import pytest
from unittest.mock import Mock, patch

from src.hmdm.allocation.human_machine_allocation_system import (
    HumanMachineAllocationSystem,
    AllocationDecisionResult
)
from src.hmdm.allocation.allocation_scheme_generator import AllocationScheme, AllocationStrategy
from src.hmdm.models.task_models import TaskHierarchy, Task, TaskType, TaskAttribute, ExecutorType


class TestHumanMachineAllocationSystem:
    """人机功能分配系统测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.allocation_system = HumanMachineAllocationSystem()
        
        # 创建测试任务层次结构
        self.task_hierarchy = self._create_test_task_hierarchy()
    
    def _create_test_task_hierarchy(self) -> TaskHierarchy:
        """创建测试用的任务层次结构"""
        hierarchy = TaskHierarchy(root_task_id="root_task")
        
        # 创建根任务
        root_task = Task(
            id="root_task",
            name="综合军事任务",
            description="综合性军事指挥任务",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        root_task.attributes = TaskAttribute(
            complexity=0.8,
            importance=0.9,
            urgency=0.7,
            real_time_requirement=True
        )
        hierarchy.add_task(root_task)
        
        # 创建子任务
        tasks_data = [
            {
                "id": "intelligence_collection",
                "name": "情报收集任务",
                "description": "收集和分析情报信息",
                "complexity": 0.6,
                "importance": 0.8,
                "real_time_requirement": True
            },
            {
                "id": "situation_assessment",
                "name": "态势评估任务", 
                "description": "评估当前战场态势",
                "complexity": 0.9,
                "importance": 0.95,
                "real_time_requirement": False
            },
            {
                "id": "decision_making",
                "name": "决策制定任务",
                "description": "制定作战决策方案",
                "complexity": 0.95,
                "importance": 0.98,
                "real_time_requirement": False
            },
            {
                "id": "execution_monitoring",
                "name": "执行监控任务",
                "description": "监控任务执行情况",
                "complexity": 0.4,
                "importance": 0.7,
                "real_time_requirement": True
            }
        ]
        
        for task_data in tasks_data:
            task = Task(
                id=task_data["id"],
                name=task_data["name"],
                description=task_data["description"],
                task_type=TaskType.ZZ_TASK,
                parent_id="root_task",
                level=1,
                executor_type=ExecutorType.HUMAN_MACHINE
            )
            task.attributes = TaskAttribute(
                complexity=task_data["complexity"],
                importance=task_data["importance"],
                urgency=0.7,
                real_time_requirement=task_data["real_time_requirement"]
            )
            hierarchy.add_task(task)
        
        return hierarchy
    
    def test_system_initialization(self):
        """测试系统初始化"""
        system = HumanMachineAllocationSystem()
        
        # 验证组件初始化
        assert system.capability_analyzer is not None
        assert system.scheme_generator is not None
        assert system.effectiveness_evaluator is not None
        assert system.fuzzy_engine is not None
        
        # 验证配置
        assert isinstance(system.config, dict)
        assert 'default_scheme_count' in system.config
        assert 'decision_threshold' in system.config
        assert 'confidence_threshold' in system.config
        
        # 验证日志记录器
        assert system.logger is not None
    
    def test_allocation_decision_result_creation(self):
        """测试分配决策结果创建"""
        result = AllocationDecisionResult()
        
        # 测试默认值
        assert result.decision_id is not None
        assert len(result.decision_id) > 0
        assert result.recommended_scheme is None
        assert isinstance(result.alternative_schemes, list)
        assert result.decision_confidence == 0.0
        assert isinstance(result.decision_rationale, dict)
        assert isinstance(result.implementation_guidance, dict)
        
        # 测试转换为字典
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert 'decision_id' in result_dict
        assert 'recommended_scheme' in result_dict
        assert 'decision_confidence' in result_dict
    
    def test_allocate_functions_basic(self):
        """测试基本的功能分配"""
        result = self.allocation_system.allocate_functions(self.task_hierarchy)
        
        # 验证结果基本属性
        assert isinstance(result, AllocationDecisionResult)
        assert result.decision_id is not None
        assert result.recommended_scheme is not None
        assert isinstance(result.alternative_schemes, list)
        assert 0 <= result.decision_confidence <= 1
        
        # 验证推荐方案
        recommended_scheme = result.recommended_scheme
        assert isinstance(recommended_scheme, AllocationScheme)
        assert recommended_scheme.name is not None
        assert len(recommended_scheme.task_allocations) > 0
        
        # 验证任务分配的有效性
        for task_id, allocation_type in recommended_scheme.task_allocations.items():
            assert allocation_type in ["human", "machine", "collaboration"]
        
        # 验证效能指标
        assert result.effectiveness_metrics is not None
        assert 0 <= result.effectiveness_metrics.overall_effectiveness <= 1
        
        # 验证决策理由
        assert isinstance(result.decision_rationale, dict)
        assert 'selection_reason' in result.decision_rationale
        assert 'key_advantages' in result.decision_rationale
        
        # 验证实施指导
        assert isinstance(result.implementation_guidance, dict)
        assert 'deployment_steps' in result.implementation_guidance
        assert 'resource_requirements' in result.implementation_guidance
    
    def test_allocate_functions_with_constraints(self):
        """测试带约束条件的功能分配"""
        constraints = {
            'max_human_tasks': 3,
            'max_machine_tasks': 4,
            'min_collaboration_tasks': 1
        }
        
        result = self.allocation_system.allocate_functions(
            self.task_hierarchy, 
            constraints=constraints
        )
        
        assert isinstance(result, AllocationDecisionResult)
        assert result.recommended_scheme is not None
        
        # 验证约束条件的影响（注意：当前实现中约束检查是简化的）
        allocation_counts = {}
        for allocation in result.recommended_scheme.task_allocations.values():
            allocation_counts[allocation] = allocation_counts.get(allocation, 0) + 1
        
        # 验证分配结果合理性
        assert sum(allocation_counts.values()) > 0
    
    def test_allocate_functions_with_preferences(self):
        """测试带用户偏好的功能分配"""
        preferences = {
            'scheme_count': 3,
            'efficiency_priority': 0.8,
            'reliability_priority': 0.6,
            'prefer_human_decision': True
        }
        
        result = self.allocation_system.allocate_functions(
            self.task_hierarchy,
            preferences=preferences
        )
        
        assert isinstance(result, AllocationDecisionResult)
        assert result.recommended_scheme is not None
        
        # 验证生成的方案数量（包括推荐方案和备选方案）
        total_schemes = 1 + len(result.alternative_schemes)
        assert total_schemes >= preferences['scheme_count']
    
    def test_allocate_functions_with_optimization(self):
        """测试带优化准则的功能分配"""
        optimization_criteria = {
            'efficiency': 0.8,
            'reliability': 0.7
        }
        
        result = self.allocation_system.allocate_functions(
            self.task_hierarchy,
            optimization_criteria=optimization_criteria
        )
        
        assert isinstance(result, AllocationDecisionResult)
        assert result.recommended_scheme is not None
        
        # 验证优化的影响
        assert result.effectiveness_metrics is not None
        assert result.effectiveness_metrics.overall_effectiveness > 0
    
    def test_analyze_task_requirements(self):
        """测试任务需求分析"""
        task_analysis = self.allocation_system._analyze_task_requirements(self.task_hierarchy)
        
        assert isinstance(task_analysis, dict)
        assert 'total_tasks' in task_analysis
        assert 'complexity_distribution' in task_analysis
        assert 'capability_requirements' in task_analysis
        assert 'critical_tasks' in task_analysis
        
        # 验证任务统计
        assert task_analysis['total_tasks'] > 0
        
        # 验证复杂度分布
        complexity_dist = task_analysis['complexity_distribution']
        assert isinstance(complexity_dist, dict)
        assert 'low' in complexity_dist
        assert 'medium' in complexity_dist
        assert 'high' in complexity_dist
        
        # 验证能力需求
        capability_reqs = task_analysis['capability_requirements']
        assert isinstance(capability_reqs, dict)
        
        # 验证关键任务识别
        critical_tasks = task_analysis['critical_tasks']
        assert isinstance(critical_tasks, list)
    
    def test_build_scheme_comparison_matrix(self):
        """测试方案比较矩阵构建"""
        # 创建测试方案
        schemes = []
        effectiveness_results = {}
        
        for i in range(3):
            scheme = AllocationScheme(
                name=f"测试方案{i+1}",
                strategy=AllocationStrategy.CAPABILITY_BASED
            )
            scheme.task_allocations = {
                task.id: ["human", "machine", "collaboration"][i % 3]
                for task in self.task_hierarchy.get_all_tasks()
            }
            schemes.append(scheme)
            
            # 创建模拟的效能结果
            from src.hmdm.allocation.collaboration_effectiveness_evaluator import EffectivenessMetrics
            metrics = EffectivenessMetrics(
                overall_effectiveness=0.7 + i * 0.1,
                task_completion_rate=0.8 + i * 0.05,
                time_efficiency=0.75 + i * 0.05,
                resource_utilization=0.7 + i * 0.1,
                error_rate=0.1 - i * 0.02,
                coordination_overhead=0.2 - i * 0.05,
                adaptability=0.8 + i * 0.05
            )
            effectiveness_results[scheme.scheme_id] = metrics
        
        # 构建比较矩阵
        decision_matrix = self.allocation_system._build_scheme_comparison_matrix(
            schemes, effectiveness_results
        )
        
        # 验证决策矩阵
        assert decision_matrix is not None
        assert len(decision_matrix.alternative_ids) == len(schemes)
        assert len(decision_matrix.indicator_ids) > 0
        assert len(decision_matrix.matrix) == len(schemes)
        assert decision_matrix.evaluation_scheme is not None
        assert len(decision_matrix.evaluation_scheme.indicators) == len(decision_matrix.indicator_ids)

        # 验证评估方案存在且包含指标
        assert len(decision_matrix.evaluation_scheme.indicators) > 0
    
    def test_generate_decision_rationale(self):
        """测试决策理由生成"""
        # 创建测试方案和指标
        scheme = AllocationScheme(name="测试方案", strategy=AllocationStrategy.CAPABILITY_BASED)
        
        from src.hmdm.allocation.collaboration_effectiveness_evaluator import EffectivenessMetrics
        metrics = EffectivenessMetrics(
            overall_effectiveness=0.85,
            task_completion_rate=0.92,
            time_efficiency=0.88,
            error_rate=0.08,
            coordination_overhead=0.15,
            adaptability=0.82
        )
        
        # 模拟模糊决策结果
        fuzzy_result = Mock()
        fuzzy_result.confidence = 0.87
        
        rationale = self.allocation_system._generate_decision_rationale(
            scheme, metrics, fuzzy_result
        )
        
        # 验证决策理由
        assert isinstance(rationale, dict)
        assert 'selection_reason' in rationale
        assert 'key_advantages' in rationale
        assert 'performance_highlights' in rationale
        assert 'decision_factors' in rationale
        assert 'confidence_explanation' in rationale
        
        # 验证内容
        assert isinstance(rationale['key_advantages'], list)
        assert isinstance(rationale['performance_highlights'], dict)
        assert isinstance(rationale['decision_factors'], list)
        
        # 验证性能亮点包含关键指标
        highlights = rationale['performance_highlights']
        assert '任务完成率' in highlights
        assert '时间效率' in highlights
        assert '错误率' in highlights
    
    def test_generate_implementation_guidance(self):
        """测试实施指导生成"""
        scheme = AllocationScheme(name="测试方案")
        scheme.task_allocations = {
            task.id: ["human", "machine", "collaboration"][i % 3]
            for i, task in enumerate(self.task_hierarchy.get_all_tasks())
        }
        scheme.risk_assessment = {
            'human_overload_risk': 0.7,
            'coordination_risk': 0.6,
            'machine_failure_risk': 0.5
        }
        
        guidance = self.allocation_system._generate_implementation_guidance(
            scheme, self.task_hierarchy
        )
        
        # 验证实施指导
        assert isinstance(guidance, dict)
        assert 'deployment_steps' in guidance
        assert 'resource_requirements' in guidance
        assert 'risk_mitigation' in guidance
        assert 'success_metrics' in guidance
        assert 'timeline_estimate' in guidance
        
        # 验证内容
        assert isinstance(guidance['deployment_steps'], list)
        assert len(guidance['deployment_steps']) > 0
        
        assert isinstance(guidance['resource_requirements'], dict)
        assert len(guidance['resource_requirements']) > 0
        
        assert isinstance(guidance['risk_mitigation'], list)
        assert len(guidance['risk_mitigation']) > 0  # 应该有风险缓解措施
        
        assert isinstance(guidance['success_metrics'], dict)
        assert isinstance(guidance['timeline_estimate'], dict)
    
    def test_generate_capability_summary(self):
        """测试能力分析摘要生成"""
        summary = self.allocation_system._generate_capability_summary(self.task_hierarchy)
        
        assert isinstance(summary, dict)
        assert 'total_tasks' in summary
        assert 'human_suitable_tasks' in summary
        assert 'machine_suitable_tasks' in summary
        assert 'collaboration_suitable_tasks' in summary
        assert 'human_suitability_ratio' in summary
        assert 'machine_suitability_ratio' in summary
        assert 'collaboration_ratio' in summary
        
        # 验证数值合理性
        total_tasks = summary['total_tasks']
        assert total_tasks > 0
        
        human_tasks = summary['human_suitable_tasks']
        machine_tasks = summary['machine_suitable_tasks']
        collab_tasks = summary['collaboration_suitable_tasks']
        
        assert human_tasks + machine_tasks + collab_tasks == total_tasks
        
        # 验证比例
        assert 0 <= summary['human_suitability_ratio'] <= 1
        assert 0 <= summary['machine_suitability_ratio'] <= 1
        assert 0 <= summary['collaboration_ratio'] <= 1
        
        # 比例总和应该为1
        total_ratio = (summary['human_suitability_ratio'] + 
                      summary['machine_suitability_ratio'] + 
                      summary['collaboration_ratio'])
        assert abs(total_ratio - 1.0) < 0.01
    
    def test_generate_scheme_comparison(self):
        """测试方案比较信息生成"""
        # 创建测试方案和效能结果
        schemes = []
        effectiveness_results = {}
        
        for i in range(3):
            scheme = AllocationScheme(
                name=f"方案{i+1}",
                strategy=list(AllocationStrategy)[i % len(AllocationStrategy)]
            )
            schemes.append(scheme)
            
            from src.hmdm.allocation.collaboration_effectiveness_evaluator import EffectivenessMetrics
            metrics = EffectivenessMetrics(
                overall_effectiveness=0.6 + i * 0.15,
                task_completion_rate=0.8 + i * 0.1,
                time_efficiency=0.7 + i * 0.1
            )
            effectiveness_results[scheme.scheme_id] = metrics
        
        comparison = self.allocation_system._generate_scheme_comparison(
            schemes, effectiveness_results
        )
        
        # 验证比较信息
        assert isinstance(comparison, dict)
        assert 'total_schemes' in comparison
        assert 'scheme_rankings' in comparison
        assert 'performance_comparison' in comparison
        assert 'strategy_distribution' in comparison
        
        # 验证方案排名
        rankings = comparison['scheme_rankings']
        assert len(rankings) == len(schemes)
        
        for i, ranking in enumerate(rankings):
            assert 'rank' in ranking
            assert 'scheme_name' in ranking
            assert 'strategy' in ranking
            assert 'overall_effectiveness' in ranking
            assert 'key_strength' in ranking
            assert ranking['rank'] == i + 1
        
        # 验证性能比较
        perf_comp = comparison['performance_comparison']
        assert 'best_overall_effectiveness' in perf_comp
        assert 'worst_overall_effectiveness' in perf_comp
        assert 'performance_gap' in perf_comp
        assert 'average_effectiveness' in perf_comp
        
        # 验证策略分布
        strategy_dist = comparison['strategy_distribution']
        assert isinstance(strategy_dist, dict)
        assert sum(strategy_dist.values()) == len(schemes)
    
    def test_identify_key_strength(self):
        """测试关键优势识别"""
        from src.hmdm.allocation.collaboration_effectiveness_evaluator import EffectivenessMetrics
        
        # 测试时间效率最高的情况
        metrics = EffectivenessMetrics(
            task_completion_rate=0.8,
            time_efficiency=0.95,  # 最高
            resource_utilization=0.7,
            error_rate=0.1,
            coordination_overhead=0.2,
            adaptability=0.8
        )
        
        strength = self.allocation_system._identify_key_strength(metrics)
        assert strength == "时间效率"
        
        # 测试质量控制最好的情况
        metrics2 = EffectivenessMetrics(
            task_completion_rate=0.8,
            time_efficiency=0.7,
            resource_utilization=0.7,
            error_rate=0.02,  # 最低，转换后最高
            coordination_overhead=0.2,
            adaptability=0.8
        )
        
        strength2 = self.allocation_system._identify_key_strength(metrics2)
        assert strength2 == "质量控制"
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空任务层次结构
        empty_hierarchy = TaskHierarchy(root_task_id="empty")
        
        result = self.allocation_system.allocate_functions(empty_hierarchy)
        assert isinstance(result, AllocationDecisionResult)
        assert result.recommended_scheme is not None
        
        # 测试单个任务
        single_task_hierarchy = TaskHierarchy(root_task_id="single")
        single_task = Task(
            id="single",
            name="单个任务",
            task_type=TaskType.META_OPERATION
        )
        single_task.attributes = TaskAttribute()
        single_task_hierarchy.add_task(single_task)
        
        single_result = self.allocation_system.allocate_functions(single_task_hierarchy)
        assert isinstance(single_result, AllocationDecisionResult)
        assert single_result.recommended_scheme is not None
        assert len(single_result.recommended_scheme.task_allocations) >= 1
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的任务层次结构
        invalid_hierarchy = None
        
        with pytest.raises(Exception):
            self.allocation_system.allocate_functions(invalid_hierarchy)


if __name__ == '__main__':
    pytest.main([__file__])
