"""
决策工具函数

提供决策分析中常用的工具函数
"""

from typing import List, Dict, Optional, Any, Tuple
import numpy as np
from scipy.stats import rankdata
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
import seaborn as sns

from ..models.decision_models import DecisionResult, DecisionMatrix, FuzzyNumber
from ..models.evaluation_models import EvaluationScheme


def calculate_consistency_ratio(matrix: np.ndarray) -> float:
    """计算一致性比率（用于AHP方法）"""
    n = matrix.shape[0]
    if n < 3:
        return 0.0
    
    # 计算最大特征值
    eigenvalues = np.linalg.eigvals(matrix)
    lambda_max = np.max(np.real(eigenvalues))
    
    # 计算一致性指标CI
    ci = (lambda_max - n) / (n - 1)
    
    # 随机一致性指标RI
    ri_values = {3: 0.58, 4: 0.90, 5: 1.12, 6: 1.24, 7: 1.32, 8: 1.41, 9: 1.45, 10: 1.49}
    ri = ri_values.get(n, 1.49)
    
    # 一致性比率CR
    cr = ci / ri if ri > 0 else 0.0
    return cr


def normalize_fuzzy_weights(fuzzy_weights: List[FuzzyNumber]) -> List[FuzzyNumber]:
    """归一化模糊权重"""
    if not fuzzy_weights:
        return []
    
    # 计算总和的模糊数
    total_params = [0.0, 0.0, 0.0]
    for fw in fuzzy_weights:
        for i in range(3):
            total_params[i] += fw.parameters[i]
    
    # 归一化
    normalized_weights = []
    for fw in fuzzy_weights:
        normalized_params = []
        for i in range(3):
            if total_params[i] > 0:
                normalized_params.append(fw.parameters[i] / total_params[i])
            else:
                normalized_params.append(0.0)
        
        normalized_weights.append(FuzzyNumber(
            fuzzy_type=fw.fuzzy_type,
            parameters=normalized_params
        ))
    
    return normalized_weights


def calculate_rank_correlation(result1: DecisionResult, result2: DecisionResult) -> float:
    """计算两个决策结果的排序相关性（Spearman相关系数）"""
    if not result1.rankings or not result2.rankings:
        return 0.0
    
    # 获取共同的备选方案
    common_alternatives = set(alt_id for alt_id, _ in result1.rankings) & \
                         set(alt_id for alt_id, _ in result2.rankings)
    
    if len(common_alternatives) < 2:
        return 0.0
    
    # 构建排序向量
    rank1 = []
    rank2 = []
    
    # 创建排序字典
    rank_dict1 = {alt_id: i+1 for i, (alt_id, _) in enumerate(result1.rankings)}
    rank_dict2 = {alt_id: i+1 for i, (alt_id, _) in enumerate(result2.rankings)}
    
    for alt_id in common_alternatives:
        rank1.append(rank_dict1[alt_id])
        rank2.append(rank_dict2[alt_id])
    
    # 计算Spearman相关系数
    correlation = np.corrcoef(rankdata(rank1), rankdata(rank2))[0, 1]
    return correlation if not np.isnan(correlation) else 0.0


def calculate_decision_stability(results: List[DecisionResult]) -> Dict[str, float]:
    """计算决策稳定性指标"""
    if len(results) < 2:
        return {"stability": 1.0, "variance": 0.0}
    
    # 计算排序相关性矩阵
    n = len(results)
    correlations = []
    
    for i in range(n):
        for j in range(i+1, n):
            corr = calculate_rank_correlation(results[i], results[j])
            correlations.append(corr)
    
    # 计算稳定性指标
    mean_correlation = np.mean(correlations)
    variance_correlation = np.var(correlations)
    
    return {
        "stability": mean_correlation,
        "variance": variance_correlation,
        "min_correlation": np.min(correlations),
        "max_correlation": np.max(correlations)
    }


def detect_rank_reversal(original_result: DecisionResult, 
                        modified_result: DecisionResult) -> List[Dict[str, Any]]:
    """检测排序逆转现象"""
    reversals = []
    
    if not original_result.rankings or not modified_result.rankings:
        return reversals
    
    # 创建排序字典
    orig_ranks = {alt_id: i for i, (alt_id, _) in enumerate(original_result.rankings)}
    mod_ranks = {alt_id: i for i, (alt_id, _) in enumerate(modified_result.rankings)}
    
    # 检测逆转
    common_alts = set(orig_ranks.keys()) & set(mod_ranks.keys())
    
    for alt1 in common_alts:
        for alt2 in common_alts:
            if alt1 != alt2:
                orig_order = orig_ranks[alt1] < orig_ranks[alt2]  # alt1在原排序中是否优于alt2
                mod_order = mod_ranks[alt1] < mod_ranks[alt2]     # alt1在新排序中是否优于alt2
                
                if orig_order != mod_order:  # 发生逆转
                    reversals.append({
                        "alternative_1": alt1,
                        "alternative_2": alt2,
                        "original_order": "alt1 > alt2" if orig_order else "alt2 > alt1",
                        "modified_order": "alt1 > alt2" if mod_order else "alt2 > alt1",
                        "original_ranks": (orig_ranks[alt1], orig_ranks[alt2]),
                        "modified_ranks": (mod_ranks[alt1], mod_ranks[alt2])
                    })
    
    return reversals


def calculate_decision_confidence(result: DecisionResult, 
                                matrix: DecisionMatrix) -> float:
    """计算决策置信度"""
    if not result.rankings or len(result.rankings) < 2:
        return 1.0
    
    # 基于得分差异计算置信度
    scores = [score for _, score in result.rankings]
    
    if len(scores) < 2:
        return 1.0
    
    # 最高分与第二高分的差异
    score_gap = scores[0] - scores[1]
    
    # 得分的标准差
    score_std = np.std(scores)
    
    # 置信度计算（得分差异越大，标准差越小，置信度越高）
    if score_std > 0:
        confidence = min(1.0, score_gap / score_std)
    else:
        confidence = 1.0
    
    return max(0.0, confidence)


def generate_decision_report(result: DecisionResult, 
                           matrix: DecisionMatrix,
                           sensitivity_analysis: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """生成决策报告"""
    report = {
        "decision_summary": {
            "method": result.method.value,
            "recommended_alternative": result.recommended_alternative_id,
            "total_alternatives": len(result.rankings),
            "confidence": calculate_decision_confidence(result, matrix),
            "calculation_time": result.calculation_time.isoformat()
        },
        "rankings": [
            {
                "rank": i + 1,
                "alternative_id": alt_id,
                "alternative_name": matrix.alternatives[alt_id].name if alt_id in matrix.alternatives else alt_id,
                "score": score,
                "normalized_score": score / result.rankings[0][1] if result.rankings[0][1] > 0 else 0.0
            }
            for i, (alt_id, score) in enumerate(result.rankings)
        ],
        "method_parameters": result.parameters,
        "distances": result.distances
    }
    
    # 添加敏感性分析结果
    if sensitivity_analysis:
        report["sensitivity_analysis"] = {
            "most_sensitive_indicator": max(
                sensitivity_analysis.keys(),
                key=lambda k: sensitivity_analysis[k]["sensitivity_coefficient"]
            ),
            "least_sensitive_indicator": min(
                sensitivity_analysis.keys(),
                key=lambda k: sensitivity_analysis[k]["sensitivity_coefficient"]
            ),
            "average_sensitivity": np.mean([
                sa["sensitivity_coefficient"] for sa in sensitivity_analysis.values()
            ]),
            "detailed_results": sensitivity_analysis
        }
    
    return report


def visualize_decision_results(results: Dict[str, DecisionResult], 
                             matrix: DecisionMatrix,
                             save_path: Optional[str] = None) -> None:
    """可视化决策结果"""
    if not results:
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('多目标模糊决策结果分析', fontsize=16, fontweight='bold')
    
    # 1. 不同方法的排序比较
    ax1 = axes[0, 0]
    method_names = list(results.keys())
    alt_names = [matrix.alternatives[alt_id].name for alt_id in matrix.alternative_ids]
    
    rank_matrix = np.zeros((len(method_names), len(alt_names)))
    
    for i, method in enumerate(method_names):
        result = results[method]
        rank_dict = {alt_id: rank for rank, (alt_id, _) in enumerate(result.rankings)}
        for j, alt_id in enumerate(matrix.alternative_ids):
            rank_matrix[i, j] = rank_dict.get(alt_id, len(alt_names))
    
    sns.heatmap(rank_matrix, annot=True, fmt='.0f', cmap='RdYlBu_r',
                xticklabels=alt_names, yticklabels=method_names, ax=ax1)
    ax1.set_title('不同方法的排序结果')
    ax1.set_xlabel('备选方案')
    ax1.set_ylabel('决策方法')
    
    # 2. 得分分布
    ax2 = axes[0, 1]
    for method, result in results.items():
        scores = [score for _, score in result.rankings]
        ax2.plot(range(1, len(scores) + 1), sorted(scores, reverse=True), 
                marker='o', label=method)
    
    ax2.set_title('各方法得分分布')
    ax2.set_xlabel('排序')
    ax2.set_ylabel('得分')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 方法一致性分析
    ax3 = axes[1, 0]
    if len(results) > 1:
        correlations = []
        method_pairs = []
        
        method_list = list(results.keys())
        for i in range(len(method_list)):
            for j in range(i + 1, len(method_list)):
                corr = calculate_rank_correlation(results[method_list[i]], results[method_list[j]])
                correlations.append(corr)
                method_pairs.append(f"{method_list[i]}\nvs\n{method_list[j]}")
        
        bars = ax3.bar(range(len(correlations)), correlations)
        ax3.set_title('方法间排序相关性')
        ax3.set_xlabel('方法对比')
        ax3.set_ylabel('Spearman相关系数')
        ax3.set_xticks(range(len(method_pairs)))
        ax3.set_xticklabels(method_pairs, rotation=45, ha='right')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{correlations[i]:.3f}', ha='center', va='bottom')
    
    # 4. 推荐方案统计
    ax4 = axes[1, 1]
    recommended_counts = {}
    for result in results.values():
        rec_alt = result.recommended_alternative_id
        if rec_alt in matrix.alternatives:
            alt_name = matrix.alternatives[rec_alt].name
            recommended_counts[alt_name] = recommended_counts.get(alt_name, 0) + 1
    
    if recommended_counts:
        names = list(recommended_counts.keys())
        counts = list(recommended_counts.values())
        
        bars = ax4.bar(names, counts)
        ax4.set_title('各方法推荐方案统计')
        ax4.set_xlabel('备选方案')
        ax4.set_ylabel('推荐次数')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{int(height)}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def export_decision_results(results: Dict[str, DecisionResult],
                          matrix: DecisionMatrix,
                          file_path: str,
                          format: str = "json") -> None:
    """导出决策结果"""
    import json
    import pandas as pd
    
    if format.lower() == "json":
        export_data = {
            "matrix_info": {
                "id": matrix.id,
                "name": matrix.name,
                "alternatives": [alt.to_dict() for alt in matrix.alternatives.values()],
                "indicators": len(matrix.indicator_ids)
            },
            "results": {method: result.to_dict() for method, result in results.items()}
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    elif format.lower() == "excel":
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 导出排序结果
            for method, result in results.items():
                df_data = []
                for i, (alt_id, score) in enumerate(result.rankings):
                    alt_name = matrix.alternatives[alt_id].name if alt_id in matrix.alternatives else alt_id
                    df_data.append({
                        "排序": i + 1,
                        "方案ID": alt_id,
                        "方案名称": alt_name,
                        "得分": score
                    })
                
                df = pd.DataFrame(df_data)
                df.to_excel(writer, sheet_name=f"{method}_排序结果", index=False)
            
            # 导出汇总比较
            summary_data = []
            for alt_id in matrix.alternative_ids:
                alt_name = matrix.alternatives[alt_id].name if alt_id in matrix.alternatives else alt_id
                row = {"方案ID": alt_id, "方案名称": alt_name}
                
                for method, result in results.items():
                    rank_dict = {aid: i+1 for i, (aid, _) in enumerate(result.rankings)}
                    row[f"{method}_排序"] = rank_dict.get(alt_id, len(matrix.alternative_ids))
                    row[f"{method}_得分"] = result.scores.get(alt_id, 0.0)
                
                summary_data.append(row)
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name="汇总比较", index=False)
