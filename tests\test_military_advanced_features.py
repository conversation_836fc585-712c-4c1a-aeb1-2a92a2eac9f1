"""
军事高级功能测试

测试安全管理、动态任务管理、实时数据处理等高级军事功能
"""

import pytest
import time
import asyncio
from datetime import datetime, timedelta
from src.hmdm.security.military_security import (
    MilitarySecurityManager, SecurityLevel, UserRole, OperationType
)
from src.hmdm.task_analysis.dynamic_task_manager import (
    DynamicTaskManager, TaskResource, ResourceType, TaskPriority, TaskStatus
)
from src.hmdm.utils.realtime_processor import (
    RealTimeProcessor, RealTimeData, DataType, ProcessingPriority
)
from src.hmdm.models.task_models import Task, TaskType, ExecutorType


class TestMilitarySecurityManager:
    """军事安全管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.security_manager = MilitarySecurityManager()
    
    def test_user_authentication(self):
        """测试用户认证"""
        # 测试成功认证
        credential = self.security_manager.authenticate_user(
            username="admin",
            password="admin123",
            role=UserRole.ADMIN
        )
        
        assert credential is not None
        assert credential.username == "admin"
        assert credential.role == UserRole.ADMIN
        assert credential.security_clearance == SecurityLevel.TOP_SECRET
        assert "admin" in credential.permissions
        assert credential.is_valid()
        
        # 测试失败认证
        failed_credential = self.security_manager.authenticate_user(
            username="admin",
            password="wrong_password",
            role=UserRole.ADMIN
        )
        
        assert failed_credential is None
    
    def test_session_validation(self):
        """测试会话验证"""
        # 创建会话
        credential = self.security_manager.authenticate_user(
            username="commander",
            password="cmd123",
            role=UserRole.COMMANDER
        )
        
        assert credential is not None
        session_token = credential.session_token
        
        # 验证有效会话
        validated_credential = self.security_manager.validate_session(session_token)
        assert validated_credential is not None
        assert validated_credential.username == "commander"
        
        # 验证无效会话
        invalid_credential = self.security_manager.validate_session("invalid_token")
        assert invalid_credential is None
    
    def test_access_control(self):
        """测试访问控制"""
        # 创建不同角色的用户
        admin_credential = self.security_manager.authenticate_user(
            "admin", "admin123", UserRole.ADMIN
        )
        operator_credential = self.security_manager.authenticate_user(
            "operator", "op123", UserRole.OPERATOR
        )
        
        # 测试管理员访问绝密资源
        admin_access = self.security_manager.check_access_permission(
            admin_credential.session_token,
            "top_secret_data",
            SecurityLevel.TOP_SECRET,
            "read"
        )
        assert admin_access is True
        
        # 测试操作员访问绝密资源（应该失败）
        operator_access = self.security_manager.check_access_permission(
            operator_credential.session_token,
            "top_secret_data", 
            SecurityLevel.TOP_SECRET,
            "read"
        )
        assert operator_access is False
        
        # 测试操作员访问秘密资源（应该成功）
        operator_secret_access = self.security_manager.check_access_permission(
            operator_credential.session_token,
            "secret_data",
            SecurityLevel.SECRET,
            "read"
        )
        assert operator_secret_access is True
    
    def test_data_encryption(self):
        """测试数据加密"""
        test_data = "这是机密军事数据"
        
        # 加密机密数据
        encrypted = self.security_manager.encrypt_sensitive_data(
            test_data, SecurityLevel.CONFIDENTIAL
        )
        assert encrypted != test_data
        assert ":" in encrypted  # 包含时间戳和签名
        
        # 解密数据
        decrypted = self.security_manager.decrypt_sensitive_data(
            encrypted, SecurityLevel.CONFIDENTIAL
        )
        assert decrypted == test_data
        
        # 测试公开数据不加密
        public_data = "公开信息"
        public_encrypted = self.security_manager.encrypt_sensitive_data(
            public_data, SecurityLevel.PUBLIC
        )
        assert public_encrypted == public_data
    
    def test_audit_logging(self):
        """测试审计日志"""
        # 使用管理员账户执行操作
        admin_credential = self.security_manager.authenticate_user(
            "admin", "admin123", UserRole.ADMIN
        )

        self.security_manager.check_access_permission(
            admin_credential.session_token,
            "analysis_data",
            SecurityLevel.CONFIDENTIAL,
            "analysis"
        )

        # 获取审计日志（需要管理员权限）
        logs = self.security_manager.get_audit_logs(admin_credential.session_token)
        
        # 验证日志记录
        assert len(logs) >= 2  # 至少有登录和访问日志
        
        # 检查登录日志
        login_logs = [log for log in logs if log["operation"] == "登录"]
        assert len(login_logs) >= 1
        assert login_logs[0]["username"] == "admin"
        assert login_logs[0]["success"] is True


class TestDynamicTaskManager:
    """动态任务管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.task_manager = DynamicTaskManager()
        
        # 添加测试资源
        human_resource = TaskResource(
            id="human_1",
            name="分析员A",
            type=ResourceType.HUMAN,
            capacity=8.0,  # 8小时工作时间
            capabilities=["analysis", "decision"],
            security_clearance=SecurityLevel.CONFIDENTIAL
        )
        
        machine_resource = TaskResource(
            id="machine_1", 
            name="分析系统A",
            type=ResourceType.MACHINE,
            capacity=24.0,  # 24小时运行
            capabilities=["data_processing", "calculation"],
            security_clearance=SecurityLevel.SECRET
        )
        
        self.task_manager.add_resource(human_resource)
        self.task_manager.add_resource(machine_resource)
    
    def test_task_submission_and_assignment(self):
        """测试任务提交和分配"""
        # 创建测试任务
        task = Task(
            name="态势分析任务",
            description="分析当前战场态势",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN
        )
        task.required_capabilities = ["analysis"]
        
        # 提交任务
        task_id = self.task_manager.submit_task(
            task=task,
            priority=TaskPriority.HIGH,
            deadline=datetime.now() + timedelta(hours=2),
            estimated_duration=60.0
        )
        
        assert task_id == task.id
        
        # 检查任务状态
        status = self.task_manager.get_task_status(task_id)
        assert status is not None
        assert status["status"] == TaskStatus.ASSIGNED.value
        assert len(status["assigned_resources"]) > 0
    
    def test_task_reassignment(self):
        """测试任务重分配"""
        # 创建任务
        task = Task(
            name="数据处理任务",
            description="处理传感器数据",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.MACHINE
        )
        task.required_capabilities = ["data_processing"]
        
        task_id = self.task_manager.submit_task(task, TaskPriority.NORMAL)
        
        # 重新分配任务
        success = self.task_manager.reassign_task(task_id, "负载均衡")
        assert success is True
        
        # 检查重分配计数
        status = self.task_manager.get_task_status(task_id)
        assert status["reassignment_count"] == 1
    
    def test_task_progress_update(self):
        """测试任务进度更新"""
        # 创建任务
        task = Task(
            name="进度测试任务",
            description="测试进度更新",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN
        )
        
        task_id = self.task_manager.submit_task(task, TaskPriority.NORMAL)
        
        # 更新进度
        self.task_manager.update_task_progress(task_id, 0.5)
        status = self.task_manager.get_task_status(task_id)
        assert status["progress"] == 0.5
        
        # 完成任务
        self.task_manager.update_task_progress(task_id, 1.0)
        status = self.task_manager.get_task_status(task_id)
        assert status["status"] == TaskStatus.COMPLETED.value
    
    def test_resource_status(self):
        """测试资源状态"""
        resource_status = self.task_manager.get_resource_status()
        
        assert len(resource_status) == 2
        
        # 检查人员资源
        human_status = next(r for r in resource_status if r["type"] == ResourceType.HUMAN.value)
        assert human_status["name"] == "分析员A"
        assert human_status["capacity"] == 8.0
        assert human_status["is_available"] is True
        
        # 检查机器资源
        machine_status = next(r for r in resource_status if r["type"] == ResourceType.MACHINE.value)
        assert machine_status["name"] == "分析系统A"
        assert machine_status["capacity"] == 24.0
    
    def test_performance_metrics(self):
        """测试性能指标"""
        # 提交几个任务
        for i in range(3):
            task = Task(
                name=f"测试任务{i}",
                description=f"测试任务{i}",
                task_type=TaskType.MISSION_TASK,
                executor_type=ExecutorType.HUMAN
            )
            self.task_manager.submit_task(task, TaskPriority.NORMAL)
        
        metrics = self.task_manager.get_performance_metrics()
        
        assert metrics["total_tasks"] == 3
        assert metrics["resource_utilization"] >= 0
        assert "reassignment_rate" in metrics
    
    def test_task_optimization(self):
        """测试任务优化"""
        # 创建一个超期任务
        overdue_task = Task(
            name="超期任务",
            description="测试超期处理",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN
        )
        
        task_id = self.task_manager.submit_task(
            overdue_task,
            TaskPriority.NORMAL,
            deadline=datetime.now() - timedelta(hours=1)  # 已经超期
        )
        
        # 执行优化
        optimization_results = self.task_manager.optimize_task_allocation()
        
        assert "overdue_handled" in optimization_results
        assert "recommendations" in optimization_results


class TestRealTimeProcessor:
    """实时数据处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.processor = RealTimeProcessor(max_workers=2, buffer_size=100)
    
    def teardown_method(self):
        """测试后清理"""
        self.processor.shutdown()
    
    def test_data_ingestion(self):
        """测试数据接收"""
        # 创建测试数据
        data = RealTimeData(
            id="test_data_1",
            data_type=DataType.SENSOR,
            content={"temperature": 25.5, "humidity": 60.0},
            source="sensor_1",
            priority=ProcessingPriority.NORMAL
        )
        
        # 接收数据
        success = self.processor.ingest_data(data)
        assert success is True
        
        # 检查统计信息
        time.sleep(0.1)  # 等待处理
        stats = self.processor.get_processing_stats()
        assert stats["total_received"] >= 1
    
    def test_processing_rules(self):
        """测试处理规则"""
        # 创建告警规则
        alert_rule_id = self.processor.create_alert_rule(
            name="高温告警",
            data_type=DataType.SENSOR,
            threshold_field="temperature",
            threshold_value=30.0,
            comparison="greater"
        )
        
        assert alert_rule_id is not None
        
        # 创建触发告警的数据
        high_temp_data = RealTimeData(
            id="high_temp_1",
            data_type=DataType.SENSOR,
            content={"temperature": 35.0, "humidity": 70.0},
            source="sensor_1",
            priority=ProcessingPriority.HIGH
        )
        
        # 处理数据
        result = self.processor.process_data_sync(high_temp_data)
        
        assert "results" in result
        assert alert_rule_id in result["results"]
        assert result["results"][alert_rule_id]["alert_type"] == "threshold_exceeded"
    
    def test_aggregation_rules(self):
        """测试聚合规则"""
        # 创建聚合规则
        agg_rule_id = self.processor.create_aggregation_rule(
            name="温度平均值",
            data_type=DataType.SENSOR,
            field_name="temperature",
            window_size=60,
            aggregation_type="average"
        )
        
        assert agg_rule_id is not None
        
        # 发送多个数据点
        temperatures = [20.0, 22.0, 24.0, 26.0, 28.0]
        for i, temp in enumerate(temperatures):
            data = RealTimeData(
                id=f"temp_data_{i}",
                data_type=DataType.SENSOR,
                content={"temperature": temp},
                source="sensor_1"
            )
            
            result = self.processor.process_data_sync(data)
            
            if agg_rule_id in result["results"]:
                agg_result = result["results"][agg_rule_id]
                assert "result" in agg_result
                assert agg_result["aggregation_type"] == "average"
    
    def test_priority_processing(self):
        """测试优先级处理"""
        # 创建不同优先级的数据
        normal_data = RealTimeData(
            id="normal_1",
            data_type=DataType.STATUS,
            content={"status": "normal"},
            priority=ProcessingPriority.NORMAL
        )
        
        critical_data = RealTimeData(
            id="critical_1",
            data_type=DataType.ALERT,
            content={"alert": "system_failure"},
            priority=ProcessingPriority.CRITICAL
        )
        
        # 接收数据
        self.processor.ingest_data(normal_data)
        self.processor.ingest_data(critical_data)
        
        # 等待处理
        time.sleep(0.5)
        
        # 检查处理统计
        stats = self.processor.get_processing_stats()
        assert stats["total_received"] >= 2
    
    @pytest.mark.asyncio
    async def test_async_processing(self):
        """测试异步处理"""
        data = RealTimeData(
            id="async_test_1",
            data_type=DataType.INTELLIGENCE,
            content={"intel_type": "enemy_movement", "confidence": 0.8},
            source="intel_system"
        )
        
        # 异步处理
        result = await self.processor.process_data_async(data)
        
        assert "data_id" in result
        assert result["data_id"] == "async_test_1"
        assert "processing_time" in result
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        # 发送一些数据
        for i in range(10):
            data = RealTimeData(
                id=f"perf_test_{i}",
                data_type=DataType.SENSOR,
                content={"value": i * 10},
                source="test_source"
            )
            self.processor.ingest_data(data)
        
        # 等待处理
        time.sleep(1)
        
        # 获取性能统计
        stats = self.processor.get_processing_stats()
        
        assert "total_received" in stats
        assert "total_processed" in stats
        assert "average_processing_time" in stats
        assert "throughput" in stats
        assert "buffer_utilization" in stats
        
        # 获取规则统计
        rule_stats = self.processor.get_rule_stats()
        assert isinstance(rule_stats, list)
        
        # 获取最近数据
        recent_data = self.processor.get_recent_data(limit=5)
        assert len(recent_data) <= 5
        assert all("id" in data for data in recent_data)


class TestIntegratedMilitaryFeatures:
    """军事功能集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.security_manager = MilitarySecurityManager()
        self.task_manager = DynamicTaskManager(self.security_manager)
        self.processor = RealTimeProcessor()
    
    def teardown_method(self):
        """测试后清理"""
        self.task_manager.shutdown()
        self.processor.shutdown()
    
    def test_secure_task_management(self):
        """测试安全的任务管理"""
        # 创建用户会话
        credential = self.security_manager.authenticate_user(
            "commander", "cmd123", UserRole.COMMANDER
        )
        
        assert credential is not None
        
        # 检查访问权限
        can_access = self.security_manager.check_access_permission(
            credential.session_token,
            "task_management",
            SecurityLevel.CONFIDENTIAL,
            "command"
        )
        
        assert can_access is True
        
        # 创建任务（需要权限验证）
        task = Task(
            name="机密作战任务",
            description="执行机密作战计划",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        
        # 添加资源
        secure_resource = TaskResource(
            id="secure_unit_1",
            name="特种作战单元",
            type=ResourceType.HYBRID,
            security_clearance=SecurityLevel.TOP_SECRET,
            capabilities=["combat", "intelligence"]
        )
        
        self.task_manager.add_resource(secure_resource)
        
        # 提交任务
        task_id = self.task_manager.submit_task(
            task, TaskPriority.CRITICAL,
            deadline=datetime.now() + timedelta(hours=4)
        )
        
        assert task_id is not None
        
        # 检查任务状态
        status = self.task_manager.get_task_status(task_id)
        assert status is not None
        assert status["priority"] == TaskPriority.CRITICAL.value
    
    def test_realtime_intelligence_processing(self):
        """测试实时情报处理"""
        # 创建情报处理规则
        intel_rule_id = self.processor.create_alert_rule(
            name="敌军活动告警",
            data_type=DataType.INTELLIGENCE,
            threshold_field="threat_level",
            threshold_value=0.7,
            comparison="greater"
        )
        
        # 模拟接收情报数据
        intel_data = RealTimeData(
            id="intel_001",
            data_type=DataType.INTELLIGENCE,
            content={
                "source": "satellite_imagery",
                "threat_level": 0.85,
                "enemy_units": 3,
                "location": "sector_7",
                "confidence": 0.9
            },
            source="intel_system",
            priority=ProcessingPriority.URGENT,
            security_level="CONFIDENTIAL"
        )
        
        # 处理情报数据
        result = self.processor.process_data_sync(intel_data)
        
        assert "results" in result
        assert intel_rule_id in result["results"]
        
        # 验证告警信息
        alert_info = result["results"][intel_rule_id]
        assert alert_info["alert_type"] == "threshold_exceeded"
        assert alert_info["actual_value"] == 0.85
    
    def test_end_to_end_military_workflow(self):
        """测试端到端军事工作流程"""
        # 1. 用户认证
        credential = self.security_manager.authenticate_user(
            "analyst", "ana123", UserRole.ANALYST
        )
        assert credential is not None
        
        # 2. 接收实时数据
        sensor_data = RealTimeData(
            id="sensor_001",
            data_type=DataType.SENSOR,
            content={
                "radar_contacts": 5,
                "signal_strength": 0.8,
                "bearing": 45,
                "range": 15.2
            },
            source="radar_system",
            priority=ProcessingPriority.HIGH
        )
        
        # 3. 处理数据并生成任务
        processing_result = self.processor.process_data_sync(sensor_data)
        assert processing_result is not None
        
        # 4. 基于处理结果创建分析任务
        analysis_task = Task(
            name="雷达数据分析",
            description="分析雷达探测到的目标",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        
        # 添加分析资源
        analysis_resource = TaskResource(
            id="analysis_team_1",
            name="雷达分析小组",
            type=ResourceType.HYBRID,
            security_clearance=SecurityLevel.CONFIDENTIAL,
            capabilities=["radar_analysis", "target_identification"]
        )
        
        self.task_manager.add_resource(analysis_resource)
        
        # 5. 提交分析任务
        task_id = self.task_manager.submit_task(
            analysis_task,
            TaskPriority.HIGH,
            estimated_duration=30.0
        )
        
        assert task_id is not None
        
        # 6. 检查任务分配
        task_status = self.task_manager.get_task_status(task_id)
        assert task_status["status"] == TaskStatus.ASSIGNED.value
        
        # 7. 模拟任务执行进度
        self.task_manager.update_task_progress(task_id, 0.5)
        self.task_manager.update_task_progress(task_id, 1.0)
        
        # 8. 验证任务完成
        final_status = self.task_manager.get_task_status(task_id)
        assert final_status["status"] == TaskStatus.COMPLETED.value
        
        # 9. 检查审计日志（需要管理员权限）
        admin_credential = self.security_manager.authenticate_user(
            "admin", "admin123", UserRole.ADMIN
        )
        audit_logs = self.security_manager.get_audit_logs(admin_credential.session_token)
        assert len(audit_logs) > 0
        
        # 验证整个工作流程的性能指标
        task_metrics = self.task_manager.get_performance_metrics()
        processing_stats = self.processor.get_processing_stats()
        
        assert task_metrics["completed_tasks"] >= 1
        assert processing_stats["total_processed"] >= 1
