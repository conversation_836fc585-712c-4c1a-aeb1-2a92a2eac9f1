"""
安全配置管理器

管理系统的安全配置参数和策略。
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from .military_security import SecurityLevel


@dataclass
class SecurityConfig:
    """安全配置"""
    
    # 认证配置
    session_timeout: int = 3600  # 会话超时时间（秒）
    max_login_attempts: int = 3  # 最大登录尝试次数
    account_lockout_duration: int = 1800  # 账户锁定时间（秒）
    password_min_length: int = 8  # 密码最小长度
    password_complexity_required: bool = True  # 是否要求密码复杂性
    
    # 加密配置
    enable_encryption: bool = True  # 是否启用数据加密
    encryption_algorithm: str = 'AES-256'  # 加密算法
    key_rotation_interval: int = 86400 * 30  # 密钥轮换间隔（秒）
    
    # 审计配置
    enable_audit_logging: bool = True  # 是否启用审计日志
    audit_log_retention_days: int = 90  # 审计日志保留天数
    audit_log_max_size: int = 100 * 1024 * 1024  # 审计日志最大大小（字节）
    
    # 访问控制配置
    default_security_level: SecurityLevel = SecurityLevel.INTERNAL
    enable_role_based_access: bool = True  # 是否启用基于角色的访问控制
    enable_attribute_based_access: bool = False  # 是否启用基于属性的访问控制
    
    # 网络安全配置
    enable_https_only: bool = True  # 是否仅允许HTTPS
    enable_csrf_protection: bool = True  # 是否启用CSRF保护
    enable_xss_protection: bool = True  # 是否启用XSS保护
    allowed_origins: list = field(default_factory=lambda: ['localhost', '127.0.0.1'])
    
    # 监控配置
    enable_intrusion_detection: bool = True  # 是否启用入侵检测
    max_requests_per_minute: int = 100  # 每分钟最大请求数
    suspicious_activity_threshold: int = 10  # 可疑活动阈值
    
    # 备份和恢复配置
    enable_automatic_backup: bool = True  # 是否启用自动备份
    backup_interval_hours: int = 24  # 备份间隔（小时）
    backup_retention_days: int = 30  # 备份保留天数
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, SecurityLevel):
                result[key] = value.value
            elif isinstance(value, list):
                result[key] = value.copy()
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SecurityConfig':
        """从字典创建配置"""
        # 处理SecurityLevel枚举
        if 'default_security_level' in data:
            if isinstance(data['default_security_level'], str):
                # 支持中文和英文的安全等级
                level_mapping = {
                    'PUBLIC': SecurityLevel.PUBLIC,
                    'INTERNAL': SecurityLevel.INTERNAL,
                    'SECRET': SecurityLevel.SECRET,
                    'CONFIDENTIAL': SecurityLevel.CONFIDENTIAL,
                    'TOP_SECRET': SecurityLevel.TOP_SECRET,
                    '公开': SecurityLevel.PUBLIC,
                    '内部': SecurityLevel.INTERNAL,
                    '秘密': SecurityLevel.SECRET,
                    '机密': SecurityLevel.CONFIDENTIAL,
                    '绝密': SecurityLevel.TOP_SECRET
                }
                level_str = data['default_security_level']
                data['default_security_level'] = level_mapping.get(level_str, SecurityLevel.INTERNAL)
        
        return cls(**data)


class SecurityConfigManager:
    """安全配置管理器"""
    
    def __init__(self, config_file: str = 'config/security_config.json'):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        
        # 配置变更监听器
        self.change_listeners = []
    
    def _load_config(self) -> SecurityConfig:
        """加载安全配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return SecurityConfig.from_dict(config_data)
            else:
                # 使用默认配置
                config = SecurityConfig()
                self._save_config(config)
                return config
        except Exception as e:
            self.logger.error(f"加载安全配置失败: {e}")
            return SecurityConfig()
    
    def _save_config(self, config: SecurityConfig):
        """保存安全配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            self.logger.info("安全配置已保存")
        except Exception as e:
            self.logger.error(f"保存安全配置失败: {e}")
    
    def get_config(self) -> SecurityConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            # 验证配置更新
            if not self._validate_config_updates(updates):
                return False
            
            # 应用更新
            config_dict = self.config.to_dict()
            config_dict.update(updates)
            
            new_config = SecurityConfig.from_dict(config_dict)
            
            # 保存配置
            self._save_config(new_config)
            
            # 更新内存中的配置
            old_config = self.config
            self.config = new_config
            
            # 通知监听器
            self._notify_config_change(old_config, new_config)
            
            self.logger.info("安全配置已更新")
            return True
            
        except Exception as e:
            self.logger.error(f"更新安全配置失败: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            old_config = self.config
            self.config = SecurityConfig()
            self._save_config(self.config)
            
            # 通知监听器
            self._notify_config_change(old_config, self.config)
            
            self.logger.info("安全配置已重置为默认值")
            return True
            
        except Exception as e:
            self.logger.error(f"重置安全配置失败: {e}")
            return False
    
    def add_change_listener(self, listener):
        """添加配置变更监听器"""
        self.change_listeners.append(listener)
    
    def remove_change_listener(self, listener):
        """移除配置变更监听器"""
        if listener in self.change_listeners:
            self.change_listeners.remove(listener)
    
    def get_security_policy(self) -> Dict[str, Any]:
        """获取安全策略摘要"""
        config = self.config
        
        return {
            'authentication': {
                'session_timeout_minutes': config.session_timeout // 60,
                'max_login_attempts': config.max_login_attempts,
                'password_policy': {
                    'min_length': config.password_min_length,
                    'complexity_required': config.password_complexity_required
                }
            },
            'encryption': {
                'enabled': config.enable_encryption,
                'algorithm': config.encryption_algorithm
            },
            'audit': {
                'enabled': config.enable_audit_logging,
                'retention_days': config.audit_log_retention_days
            },
            'access_control': {
                'default_level': config.default_security_level.value,
                'role_based': config.enable_role_based_access
            },
            'network_security': {
                'https_only': config.enable_https_only,
                'csrf_protection': config.enable_csrf_protection,
                'xss_protection': config.enable_xss_protection
            },
            'monitoring': {
                'intrusion_detection': config.enable_intrusion_detection,
                'rate_limiting': config.max_requests_per_minute
            }
        }
    
    def validate_security_compliance(self) -> Dict[str, Any]:
        """验证安全合规性"""
        config = self.config
        compliance_issues = []
        recommendations = []
        
        # 检查密码策略
        if config.password_min_length < 8:
            compliance_issues.append("密码最小长度应不少于8位")
        
        if not config.password_complexity_required:
            recommendations.append("建议启用密码复杂性要求")
        
        # 检查会话安全
        if config.session_timeout > 7200:  # 2小时
            recommendations.append("建议将会话超时时间设置为2小时以内")
        
        # 检查加密设置
        if not config.enable_encryption:
            compliance_issues.append("必须启用数据加密")
        
        # 检查审计设置
        if not config.enable_audit_logging:
            compliance_issues.append("必须启用审计日志")
        
        if config.audit_log_retention_days < 90:
            recommendations.append("建议审计日志保留期不少于90天")
        
        # 检查网络安全
        if not config.enable_https_only:
            compliance_issues.append("必须启用HTTPS强制")
        
        if not config.enable_csrf_protection:
            compliance_issues.append("必须启用CSRF保护")
        
        # 计算合规分数
        total_checks = 8
        issues_count = len(compliance_issues)
        compliance_score = max(0, (total_checks - issues_count) / total_checks * 100)
        
        return {
            'compliance_score': compliance_score,
            'status': 'compliant' if issues_count == 0 else 'non_compliant',
            'issues': compliance_issues,
            'recommendations': recommendations,
            'last_check': datetime.now().isoformat()
        }
    
    def _validate_config_updates(self, updates: Dict[str, Any]) -> bool:
        """验证配置更新"""
        try:
            # 验证数值范围
            if 'session_timeout' in updates:
                if not (300 <= updates['session_timeout'] <= 86400):  # 5分钟到24小时
                    self.logger.error("会话超时时间必须在5分钟到24小时之间")
                    return False
            
            if 'max_login_attempts' in updates:
                if not (1 <= updates['max_login_attempts'] <= 10):
                    self.logger.error("最大登录尝试次数必须在1到10之间")
                    return False
            
            if 'password_min_length' in updates:
                if not (6 <= updates['password_min_length'] <= 32):
                    self.logger.error("密码最小长度必须在6到32之间")
                    return False
            
            if 'audit_log_retention_days' in updates:
                if not (1 <= updates['audit_log_retention_days'] <= 365):
                    self.logger.error("审计日志保留天数必须在1到365之间")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def _notify_config_change(self, old_config: SecurityConfig, new_config: SecurityConfig):
        """通知配置变更"""
        for listener in self.change_listeners:
            try:
                listener(old_config, new_config)
            except Exception as e:
                self.logger.error(f"配置变更通知失败: {e}")


# 全局安全配置管理器实例
security_config_manager = SecurityConfigManager()
