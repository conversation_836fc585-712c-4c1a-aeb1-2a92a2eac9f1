"""
HMDM系统配置定义

定义系统配置相关的数据结构和枚举
"""

import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

from ..security.military_security import SecurityLevel


class SystemStatus(Enum):
    """系统状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class ModuleStatus(Enum):
    """模块状态枚举"""
    NOT_LOADED = "not_loaded"
    LOADING = "loading"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class SystemConfig:
    """系统配置类"""
    
    # 基本信息
    system_name: str = "HMDM军事综合决策支持系统"
    version: str = "1.0.0"
    description: str = "基于人机协同的军事综合决策支持系统"
    
    # 环境配置
    environment: str = "production"  # development, testing, production
    debug_mode: bool = False
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/hmdm.log"
    log_max_size: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5
    enable_console_log: bool = True
    
    # 模块配置
    enabled_modules: List[str] = field(default_factory=lambda: [
        "situation_awareness",
        "communication", 
        "decision_support",
        "training",
        "knowledge_base",
        "simulation",
        "scenarios"
    ])
    
    # 网络配置
    host: str = "127.0.0.1"
    port: int = 5000
    max_connections: int = 100
    
    # 数据配置
    data_directory: str = "data"
    backup_directory: str = "backups"
    temp_directory: str = "temp"
    
    # 人机分配配置
    allocation_config_file: Optional[str] = None
    allocation_config: Optional[Any] = None  # 避免循环导入，使用Any
    
    # 安全配置
    security_level: SecurityLevel = SecurityLevel.SECRET
    enable_encryption: bool = True
    enable_audit_log: bool = True
    
    # 性能配置
    max_concurrent_tasks: int = 10
    task_timeout: int = 300  # 秒
    memory_limit: int = 1024  # MB
    
    # 监控配置
    enable_monitoring: bool = True
    monitoring_interval: int = 60  # 秒
    health_check_interval: int = 30  # 秒
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证基本配置
            if not self.system_name or not self.version:
                return False
            
            # 验证端口范围
            if not (1 <= self.port <= 65535):
                return False
            
            # 验证日志级别
            valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            if self.log_level not in valid_log_levels:
                return False
            
            # 验证环境
            valid_environments = ["development", "testing", "production"]
            if self.environment not in valid_environments:
                return False
            
            # 验证数值范围
            if self.max_connections <= 0 or self.max_concurrent_tasks <= 0:
                return False
            
            if self.task_timeout <= 0 or self.memory_limit <= 0:
                return False
            
            return True
            
        except Exception:
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                result[key] = value.value
            elif hasattr(value, 'to_dict'):
                result[key] = value.to_dict()
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemConfig':
        """从字典创建配置"""
        config = cls()
        
        for key, value in data.items():
            if hasattr(config, key):
                # 处理枚举类型
                if key == 'security_level' and isinstance(value, str):
                    try:
                        setattr(config, key, SecurityLevel(value))
                    except ValueError:
                        pass  # 使用默认值
                else:
                    setattr(config, key, value)
        
        return config
    
    def create_directories(self) -> bool:
        """创建必要的目录"""
        try:
            directories = [
                self.data_directory,
                self.backup_directory,
                self.temp_directory,
                os.path.dirname(self.log_file) if self.log_file else "logs"
            ]
            
            for directory in directories:
                if directory and not os.path.exists(directory):
                    os.makedirs(directory, exist_ok=True)
            
            return True
            
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'level': self.log_level,
            'file': self.log_file,
            'max_size': self.log_max_size,
            'backup_count': self.log_backup_count,
            'enable_console': self.enable_console_log
        }
    
    def get_network_config(self) -> Dict[str, Any]:
        """获取网络配置"""
        return {
            'host': self.host,
            'port': self.port,
            'max_connections': self.max_connections
        }
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return {
            'security_level': self.security_level.value,
            'enable_encryption': self.enable_encryption,
            'enable_audit_log': self.enable_audit_log
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return {
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'task_timeout': self.task_timeout,
            'memory_limit': self.memory_limit
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return {
            'enable_monitoring': self.enable_monitoring,
            'monitoring_interval': self.monitoring_interval,
            'health_check_interval': self.health_check_interval
        }
    
    def update_from_dict(self, updates: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for key, value in updates.items():
            if hasattr(self, key):
                # 处理枚举类型
                if key == 'security_level' and isinstance(value, str):
                    try:
                        setattr(self, key, SecurityLevel(value))
                    except ValueError:
                        pass  # 保持原值
                else:
                    setattr(self, key, value)
    
    def copy(self) -> 'SystemConfig':
        """创建配置副本"""
        return SystemConfig.from_dict(self.to_dict())
    
    def merge(self, other: 'SystemConfig') -> 'SystemConfig':
        """合并两个配置"""
        merged_dict = self.to_dict()
        other_dict = other.to_dict()
        
        # 合并字典
        for key, value in other_dict.items():
            if value is not None:  # 只合并非None值
                merged_dict[key] = value
        
        return SystemConfig.from_dict(merged_dict)
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'system_name': self.system_name,
            'version': self.version,
            'environment': self.environment,
            'enabled_modules_count': len(self.enabled_modules),
            'security_level': self.security_level.value,
            'debug_mode': self.debug_mode,
            'monitoring_enabled': self.enable_monitoring
        }


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    status: ModuleStatus = ModuleStatus.NOT_LOADED
    instance: Optional[Any] = None
    dependencies: List[str] = field(default_factory=list)
    config: Dict[str, Any] = field(default_factory=dict)
    last_error: Optional[str] = None
    start_time: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'status': self.status.value,
            'dependencies': self.dependencies,
            'config': self.config,
            'last_error': self.last_error,
            'start_time': self.start_time,
            'has_instance': self.instance is not None
        }


# 默认配置实例
DEFAULT_SYSTEM_CONFIG = SystemConfig()


def load_system_config(config_file: str) -> SystemConfig:
    """加载系统配置"""
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return SystemConfig.from_dict(config_data)
        else:
            return SystemConfig()
    except Exception as e:
        print(f"加载系统配置失败: {e}")
        return SystemConfig()


def save_system_config(config: SystemConfig, config_file: str) -> bool:
    """保存系统配置"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config.to_dict(), f, indent=2, ensure_ascii=False, default=str)
        return True
    except Exception as e:
        print(f"保存系统配置失败: {e}")
        return False
