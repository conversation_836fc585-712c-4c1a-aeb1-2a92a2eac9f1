"""
方案评估模块

实现多方案的综合评估和最优方案推荐：
- 多指标多因素的计算和综合评估
- 基于量化评估结果的方案推荐
- 支持负荷、效率等多维度评估指标
"""

from .scheme_evaluator import SchemeEvaluator
from .evaluation_utils import (
    calculate_indicator_importance,
    detect_evaluation_outliers,
    generate_evaluation_summary,
    calculate_skewness,
    calculate_kurtosis,
    visualize_evaluation_results,
    export_evaluation_report
)

__all__ = [
    "SchemeEvaluator",
    "calculate_indicator_importance",
    "detect_evaluation_outliers",
    "generate_evaluation_summary",
    "calculate_skewness",
    "calculate_kurtosis",
    "visualize_evaluation_results",
    "export_evaluation_report"
]
