"""
缓存管理系统

提供高性能的缓存管理功能：
- 多级缓存策略
- LRU/LFU缓存算法
- 缓存预热和失效
- 分布式缓存支持
- 缓存性能监控
"""

import time
import threading
import pickle
import hashlib
import logging
from typing import Any, Optional, Dict, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import OrderedDict, defaultdict
from enum import Enum
import json
import os

from ..monitoring.performance_monitor import PerformanceMonitor


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "最近最少使用"
    LFU = "最少使用频率"
    FIFO = "先进先出"
    TTL = "生存时间"


class CacheLevel(Enum):
    """缓存级别"""
    L1_MEMORY = "一级内存缓存"
    L2_DISK = "二级磁盘缓存"
    L3_DISTRIBUTED = "三级分布式缓存"


@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    created_time: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    ttl: Optional[int] = None  # 生存时间（秒）
    size: int = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_time).total_seconds() > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: OrderedDict[str, CacheItem] = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                item = self.cache[key]
                if item.is_expired():
                    del self.cache[key]
                    return None
                
                # 移动到末尾（最近使用）
                self.cache.move_to_end(key)
                item.touch()
                return item.value
            return None
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """存储缓存项"""
        with self.lock:
            try:
                # 计算值的大小
                size = len(pickle.dumps(value))
                
                if key in self.cache:
                    # 更新现有项
                    item = self.cache[key]
                    item.value = value
                    item.created_time = datetime.now()
                    item.ttl = ttl
                    item.size = size
                    self.cache.move_to_end(key)
                else:
                    # 添加新项
                    if len(self.cache) >= self.max_size:
                        # 移除最久未使用的项
                        self.cache.popitem(last=False)
                    
                    item = CacheItem(key=key, value=value, ttl=ttl, size=size)
                    self.cache[key] = item
                
                return True
                
            except Exception:
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            total_size = sum(item.size for item in self.cache.values())
            return {
                'item_count': len(self.cache),
                'max_size': self.max_size,
                'total_memory_bytes': total_size,
                'utilization': len(self.cache) / self.max_size if self.max_size > 0 else 0
            }


class LFUCache:
    """LFU缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, CacheItem] = {}
        self.frequencies: defaultdict = defaultdict(set)
        self.min_frequency = 0
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key not in self.cache:
                return None
            
            item = self.cache[key]
            if item.is_expired():
                self._remove_item(key)
                return None
            
            # 更新频率
            self._update_frequency(key)
            item.touch()
            return item.value
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """存储缓存项"""
        with self.lock:
            try:
                size = len(pickle.dumps(value))
                
                if key in self.cache:
                    # 更新现有项
                    item = self.cache[key]
                    item.value = value
                    item.created_time = datetime.now()
                    item.ttl = ttl
                    item.size = size
                    self._update_frequency(key)
                else:
                    # 添加新项
                    if len(self.cache) >= self.max_size:
                        self._evict_lfu()

                    item = CacheItem(key=key, value=value, ttl=ttl, size=size)
                    item.access_count = 1  # 初始化访问计数
                    self.cache[key] = item
                    self.frequencies[1].add(key)
                    self.min_frequency = 1
                
                return True
                
            except Exception:
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                self._remove_item(key)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.frequencies.clear()
            self.min_frequency = 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def _update_frequency(self, key: str):
        """更新访问频率"""
        item = self.cache[key]
        old_freq = item.access_count
        new_freq = old_freq + 1

        # 从旧频率集合中移除
        if old_freq > 0:
            self.frequencies[old_freq].discard(key)
            if old_freq == self.min_frequency and not self.frequencies[old_freq]:
                self.min_frequency += 1

        # 添加到新频率集合
        self.frequencies[new_freq].add(key)
        item.access_count = new_freq
    
    def _evict_lfu(self):
        """淘汰最少使用的项"""
        if self.frequencies[self.min_frequency]:
            key_to_remove = self.frequencies[self.min_frequency].pop()
            self._remove_item(key_to_remove)
    
    def _remove_item(self, key: str):
        """移除缓存项"""
        if key in self.cache:
            item = self.cache[key]
            self.frequencies[item.access_count].discard(key)
            del self.cache[key]


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, 
                 performance_monitor: Optional[PerformanceMonitor] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        self.performance_monitor = performance_monitor
        
        # 缓存配置
        self.default_strategy = CacheStrategy(self.config.get('default_strategy', CacheStrategy.LRU.value))
        self.default_ttl = self.config.get('default_ttl', 3600)  # 1小时
        self.max_memory_size = self.config.get('max_memory_size', 1000)
        self.enable_disk_cache = self.config.get('enable_disk_cache', False)
        self.disk_cache_dir = self.config.get('disk_cache_dir', 'cache')
        
        # 多级缓存
        self.l1_cache = self._create_cache(CacheLevel.L1_MEMORY)
        self.l2_cache = None
        if self.enable_disk_cache:
            self.l2_cache = self._create_disk_cache()
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'puts': 0,
            'deletes': 0,
            'evictions': 0
        }
        
        # 预热任务
        self.warmup_tasks: List[Callable] = []
        
        self.logger.info("缓存管理器初始化完成")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        try:
            # 尝试L1缓存
            value = self.l1_cache.get(key)
            if value is not None:
                self.stats['hits'] += 1
                if self.performance_monitor:
                    self.performance_monitor.record_cache_hit('L1')
                return value
            
            # 尝试L2缓存
            if self.l2_cache:
                value = self.l2_cache.get(key)
                if value is not None:
                    # 回填到L1缓存
                    self.l1_cache.put(key, value)
                    self.stats['hits'] += 1
                    if self.performance_monitor:
                        self.performance_monitor.record_cache_hit('L2')
                    return value
            
            # 缓存未命中
            self.stats['misses'] += 1
            if self.performance_monitor:
                self.performance_monitor.record_cache_miss()
            
            return default
            
        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            return default
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """存储缓存值"""
        try:
            ttl = ttl or self.default_ttl
            
            # 存储到L1缓存
            success = self.l1_cache.put(key, value, ttl)
            
            # 存储到L2缓存
            if self.l2_cache and success:
                self.l2_cache.put(key, value, ttl)
            
            if success:
                self.stats['puts'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"存储缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            success1 = self.l1_cache.delete(key)
            success2 = True
            
            if self.l2_cache:
                success2 = self.l2_cache.delete(key)
            
            if success1 or success2:
                self.stats['deletes'] += 1
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False

    def clear(self):
        """清空所有缓存"""
        try:
            self.l1_cache.clear()
            if self.l2_cache:
                self.l2_cache.clear()

            # 重置统计
            self.stats = {
                'hits': 0,
                'misses': 0,
                'puts': 0,
                'deletes': 0,
                'evictions': 0
            }

            self.logger.info("缓存已清空")

        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            l1_stats = self.l1_cache.get_stats()
            l2_stats = {}
            if self.l2_cache:
                l2_stats = self.l2_cache.get_stats()

            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0

            return {
                'hit_rate': hit_rate,
                'total_requests': total_requests,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'puts': self.stats['puts'],
                'deletes': self.stats['deletes'],
                'evictions': self.stats['evictions'],
                'l1_cache': l1_stats,
                'l2_cache': l2_stats,
                'strategy': self.default_strategy.value
            }

        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}

    def add_warmup_task(self, task: Callable):
        """添加预热任务"""
        self.warmup_tasks.append(task)

    def warmup(self):
        """执行缓存预热"""
        try:
            self.logger.info("开始缓存预热")

            for task in self.warmup_tasks:
                try:
                    task()
                except Exception as e:
                    self.logger.error(f"预热任务执行失败: {e}")

            self.logger.info("缓存预热完成")

        except Exception as e:
            self.logger.error(f"缓存预热失败: {e}")

    def _create_cache(self, level: CacheLevel):
        """创建缓存实例"""
        if self.default_strategy == CacheStrategy.LRU:
            return LRUCache(self.max_memory_size)
        elif self.default_strategy == CacheStrategy.LFU:
            return LFUCache(self.max_memory_size)
        else:
            return LRUCache(self.max_memory_size)  # 默认使用LRU

    def _create_disk_cache(self):
        """创建磁盘缓存"""
        # 简化的磁盘缓存实现
        return DiskCache(self.disk_cache_dir)

    def cache_decorator(self, key_func: Optional[Callable] = None, ttl: Optional[int] = None):
        """缓存装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

                # 尝试从缓存获取
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result

                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.put(cache_key, result, ttl)

                return result

            return wrapper
        return decorator


class DiskCache:
    """磁盘缓存实现"""

    def __init__(self, cache_dir: str):
        self.cache_dir = cache_dir
        self.lock = threading.RLock()

        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)

    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            try:
                file_path = self._get_file_path(key)
                if not os.path.exists(file_path):
                    return None

                with open(file_path, 'rb') as f:
                    data = pickle.load(f)

                # 检查是否过期
                if 'ttl' in data and data['ttl']:
                    created_time = datetime.fromisoformat(data['created_time'])
                    if (datetime.now() - created_time).total_seconds() > data['ttl']:
                        os.remove(file_path)
                        return None

                return data['value']

            except Exception:
                return None

    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """存储缓存项"""
        with self.lock:
            try:
                file_path = self._get_file_path(key)

                data = {
                    'value': value,
                    'created_time': datetime.now().isoformat(),
                    'ttl': ttl
                }

                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)

                return True

            except Exception:
                return False

    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            try:
                file_path = self._get_file_path(key)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    return True
                return False

            except Exception:
                return False

    def clear(self):
        """清空缓存"""
        with self.lock:
            try:
                for filename in os.listdir(self.cache_dir):
                    file_path = os.path.join(self.cache_dir, filename)
                    if os.path.isfile(file_path):
                        os.remove(file_path)

            except Exception:
                pass

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            file_count = 0
            total_size = 0

            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path):
                    file_count += 1
                    total_size += os.path.getsize(file_path)

            return {
                'item_count': file_count,
                'total_size_bytes': total_size,
                'cache_dir': self.cache_dir
            }

        except Exception:
            return {}

    def _get_file_path(self, key: str) -> str:
        """获取缓存文件路径"""
        # 使用MD5哈希作为文件名
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{hash_key}.cache")
