人机功能分配模型应用于支持联合ZZ指挥系统人机功能分配方案的优选，模型研究信息系统的人机功能分配准则，充分挖掘人与机各自的优势，拟基于多个人机功能分配方案，结合任务层次化分析及流程分解，通过构建多目标模糊决策模型，实现多影响因素的静态人机功能分配。模型的输入为多个人机功能分配方案，输出为推荐的人机功能分配方案。
通过调用该模型，首先，可以实现对多个人机功能分配方案进行多指标多因素的计算及综合评估；其次，可以根据多个方案的量化评估结果，给出推荐方案。
本模型可应用于对联合ZZ指挥系统中典型任务场景（如态势分析、威胁计算、辅助决策等）的人机功能分配方案进行多因素计算（如负荷、效率）等，为人机功能分配方案的设计、对比提供量化支撑。
本模型通过重点突破三层次任务分解、多目标模糊决策模型等关键技术，实现对人机功能分配模型的开发建设，具体建设路线如图34所示。人机功能分配模型分为任务分析及流程分解、基于多目标模糊决策的功能分配两部分。
 
图34.png 人机功能分配模型技术路线图
层次任务分析法是一种结构化描述任务与其子任务层次体系的任务分析方法。Annett等人分析了复杂任务中不同行为的层次特征，如计划、判断和决策等，并将任务具体划分为子任务、操作，通过层级分析将任务进行不断的分解，逐级将用户行为进行细化，直到具体操作动作；GOMS模型是一种用户认知模型，主要用来描述任务在实践过程中是如何执行的。它的基本思想是：首先确定目标，将目标进行分解；当完成同一目标的方法多于一种时，根据使用情境，通过选择规则来选择合适的方法；每种方法是通过一系列的操作序列集合来实现的。
基于层次分析法和GOMS模型，建立三层次的任务分解技术，实现“使命任务-ZZ任务-典型功能-人机交互流程-操作序列-元操作”的分解逻辑；任务流程分解的目的是在功能分解的基础上建立操作流程，即为完成功能而需要的各项有序操作。将功能分解形成标准化的人机交互操作流程，且操作具有先后顺序的固化程序，操作流程的步骤之间具有关联关系。为了确保建立的规则符合ZZ使用要求，并且准确、流畅，采用“input→action→output”的模式对任务进行分解；元操作分解是在任务流程分解到操作层的基础上，进一步细分为元操作层，这里的元操作指构成操作的更小的基本操作单位。根据GOMS法对于元操作的定义与分解，操作的类型有感知操作、认知操作、动机行为或者这几种操作的结合，每个操作都有一个预定的执行时间，该基本操作单位为不可再被分解的单位，如输入文字操作，可分解为心理准备、归位（为手从别的部位移动到/离开鼠标的操作）、移动鼠标、点击按键（次数由输入文字的数量和拼音组成决定）、归位；选取典型任务，采用层次任务分析法和GOMS模型对所任务进行自上而下的逐层分解，形成任务分解结构图。
最后，采用多目标模糊决策进行功能分配。拟采用加权相对偏差距离最小法建立多目标模糊决策模型，充分考虑使用环境、技术水平、目标及任务，通过定性计算与定量分析相结合的方法，实现多影响因素的静态人机功能分配。使用加权相对偏差距离最小法建立多目标模糊决策模型，具体步骤为1）建立决策论域，针对决策对象选取决策因素，即确定决策的指标体系2）确定因素指标集合，对决策对象进行决策，要选取合适的因素指标集；3）确定因素指标值矩阵，由决策论域和因素指标集合建立因素指标值矩阵，相关数据由有经验的专家打分综合而成，得到各功能分配方案因素指标的相对值，由各约束函数值组成的因素指标值矩阵；4）计算模糊关系矩阵；5）确定各因素指标的权值向量；6）根据目标函数决策结果。
