"""
军事综合功能测试

测试态势感知、通信协同等综合军事功能
"""

import pytest
import time
import asyncio
from datetime import datetime, timedelta
from src.hmdm.analysis.situation_awareness import (
    SituationAwarenessEngine, SituationEntity, ThreatLevel, SituationType
)
from src.hmdm.communication.military_comms import (
    MilitaryCommunicationSystem, MilitaryUnit, MilitaryMessage, 
    MessageType, MessagePriority, UnitType
)
from src.hmdm.utils.realtime_processor import RealTimeData, DataType, ProcessingPriority
from src.hmdm.security.military_security import MilitarySecurityManager, SecurityLevel, UserRole


class TestSituationAwareness:
    """态势感知测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.sa_engine = SituationAwarenessEngine()
    
    def test_sensor_data_ingestion(self):
        """测试传感器数据接收"""
        # 创建传感器数据
        sensor_data = RealTimeData(
            id="sensor_001",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "target_001",
                "name": "敌军坦克",
                "type": "tank",
                "position": [116.3974, 39.9093, 0],  # 北京坐标
                "velocity": [10.0, 5.0, 0],
                "heading": 45.0,
                "allegiance": "enemy"
            },
            source="radar_system"
        )
        
        # 接收数据
        success = self.sa_engine.ingest_sensor_data(sensor_data)
        assert success is True
        
        # 验证实体创建
        assert "target_001" in self.sa_engine.entities
        entity = self.sa_engine.entities["target_001"]
        assert entity.name == "敌军坦克"
        assert entity.entity_type == "tank"
        assert entity.position == (116.3974, 39.9093, 0)
        assert entity.attributes["allegiance"] == "enemy"
    
    def test_intelligence_data_ingestion(self):
        """测试情报数据接收"""
        # 创建情报数据
        intel_data = RealTimeData(
            id="intel_001",
            data_type=DataType.INTELLIGENCE,
            content={
                "target_id": "target_002",
                "target_name": "敌军指挥所",
                "target_type": "command_post",
                "threat_level": 0.8,
                "capabilities": ["command", "communication"],
                "allegiance": "enemy",
                "intel_confidence": 0.7
            },
            source="intelligence_system"
        )
        
        # 接收数据
        success = self.sa_engine.ingest_intelligence_data(intel_data)
        assert success is True
        
        # 验证实体创建
        assert "target_002" in self.sa_engine.entities
        entity = self.sa_engine.entities["target_002"]
        assert entity.name == "敌军指挥所"
        assert entity.attributes["threat_level"] == 0.8
        assert "command" in entity.attributes["capabilities"]
    
    def test_situation_assessment(self):
        """测试态势评估"""
        # 添加友军实体
        friendly_data = RealTimeData(
            id="friendly_001",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "friendly_001",
                "name": "友军步兵",
                "type": "infantry",
                "position": [116.4074, 39.9193, 0],
                "allegiance": "friendly"
            },
            source="friendly_system"
        )
        
        # 添加敌军实体
        enemy_data = RealTimeData(
            id="enemy_001",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "enemy_001",
                "name": "敌军装甲车",
                "type": "armored_vehicle",
                "position": [116.3874, 39.9093, 0],
                "allegiance": "enemy",
                "capabilities": ["mobility", "firepower"]
            },
            source="radar_system"
        )
        
        # 接收数据
        self.sa_engine.ingest_sensor_data(friendly_data)
        self.sa_engine.ingest_sensor_data(enemy_data)
        
        # 执行态势评估
        assessment = self.sa_engine.perform_situation_assessment()
        
        # 验证评估结果
        assert assessment is not None
        assert assessment.threat_level in list(ThreatLevel)
        assert assessment.situation_type in list(SituationType)
        assert assessment.confidence > 0
        assert len(assessment.summary) > 0
        assert len(assessment.entities) == 2
        assert len(assessment.recommendations) > 0
    
    def test_situation_prediction(self):
        """测试态势预测"""
        # 添加移动实体
        moving_entity_data = RealTimeData(
            id="moving_001",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "moving_001",
                "name": "移动目标",
                "type": "vehicle",
                "position": [116.3974, 39.9093, 0],
                "velocity": [20.0, 10.0, 0],  # 较快速度
                "allegiance": "enemy"
            },
            source="tracking_system"
        )
        
        self.sa_engine.ingest_sensor_data(moving_entity_data)
        
        # 执行预测
        predictions = self.sa_engine.predict_situation(time_horizon=1800)  # 30分钟
        
        # 验证预测结果
        assert "predictions" in predictions
        assert "confidence" in predictions
        assert "key_events" in predictions
        assert len(predictions["predictions"]) > 0
        
        # 检查预测数据结构
        first_prediction = predictions["predictions"][0]
        assert "time_offset" in first_prediction
        assert "predicted_entities" in first_prediction
        assert "potential_conflicts" in first_prediction
    
    def test_situation_summary(self):
        """测试态势摘要"""
        # 添加一些实体
        for i in range(3):
            entity_data = RealTimeData(
                id=f"entity_{i}",
                data_type=DataType.SENSOR,
                content={
                    "entity_id": f"entity_{i}",
                    "name": f"实体{i}",
                    "type": "unit",
                    "position": [116.3974 + i*0.01, 39.9093 + i*0.01, 0],
                    "allegiance": "friendly" if i % 2 == 0 else "enemy"
                },
                source="test_system"
            )
            self.sa_engine.ingest_sensor_data(entity_data)
        
        # 执行态势评估
        self.sa_engine.perform_situation_assessment()
        
        # 获取态势摘要
        summary = self.sa_engine.get_situation_summary()
        
        # 验证摘要内容
        assert "current_situation" in summary
        assert "entity_statistics" in summary
        assert "system_status" in summary
        
        stats = summary["entity_statistics"]
        assert stats["total"] == 3
        assert "by_allegiance" in stats
        assert stats["by_allegiance"]["friendly"] == 2
        assert stats["by_allegiance"]["enemy"] == 1


class TestMilitaryCommunication:
    """军事通信测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.security_manager = MilitarySecurityManager()
        self.comm_system = MilitaryCommunicationSystem(self.security_manager)
        
        # 创建测试单位
        self.command_post = MilitaryUnit(
            id="cmd_001",
            name="第1指挥所",
            unit_type=UnitType.COMMAND_POST,
            commander="张司令",
            security_clearance=SecurityLevel.TOP_SECRET
        )
        
        self.combat_unit = MilitaryUnit(
            id="combat_001",
            name="第1作战单位",
            unit_type=UnitType.COMBAT_UNIT,
            commander="李营长",
            security_clearance=SecurityLevel.CONFIDENTIAL
        )
        
        # 注册单位
        self.comm_system.register_unit(self.command_post)
        self.comm_system.register_unit(self.combat_unit)

    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'comm_system'):
            self.comm_system.shutdown()
    
    def test_unit_registration(self):
        """测试单位注册"""
        # 验证单位注册
        assert "cmd_001" in self.comm_system.units
        assert "combat_001" in self.comm_system.units
        
        # 验证消息队列创建
        assert "cmd_001" in self.comm_system.message_queues
        assert "combat_001" in self.comm_system.message_queues
        
        # 获取单位状态
        cmd_status = self.comm_system.get_unit_status("cmd_001")
        assert cmd_status is not None
        assert cmd_status["name"] == "第1指挥所"
        assert cmd_status["type"] == UnitType.COMMAND_POST.value
        assert cmd_status["is_online"] is True
    
    def test_message_sending(self):
        """测试消息发送"""
        # 创建测试消息
        message = MilitaryMessage(
            message_type=MessageType.COMMAND,
            priority=MessagePriority.IMMEDIATE,
            sender_id="cmd_001",
            sender_name="第1指挥所",
            recipient_id="combat_001",
            recipient_name="第1作战单位",
            subject="作战指令",
            content={
                "command": "advance",
                "target_position": [116.4074, 39.9193],
                "execution_time": "immediate"
            },
            security_level=SecurityLevel.CONFIDENTIAL,
            acknowledgment_required=True
        )
        
        # 发送消息
        success = self.comm_system.send_message(message)
        assert success is True
        
        # 验证消息存储
        assert message.id in self.comm_system.messages
        assert message.delivery_status == "delivered"
        
        # 验证消息队列
        combat_queue = self.comm_system.message_queues["combat_001"]
        assert message.id in combat_queue
    
    def test_message_receiving(self):
        """测试消息接收"""
        # 发送测试消息
        message = self.comm_system.create_command_message(
            sender_id="cmd_001",
            recipient_id="combat_001",
            command="patrol",
            parameters={"area": "sector_A", "duration": 120}
        )
        
        self.comm_system.send_message(message)
        
        # 接收消息
        received_messages = self.comm_system.receive_messages("combat_001")
        
        # 验证接收结果
        assert len(received_messages) == 1
        assert received_messages[0].id == message.id
        assert received_messages[0].content["command"] == "patrol"
        assert received_messages[0].content["parameters"]["area"] == "sector_A"
    
    def test_message_acknowledgment(self):
        """测试消息确认"""
        # 创建需要确认的消息
        message = MilitaryMessage(
            message_type=MessageType.COMMAND,
            sender_id="cmd_001",
            recipient_id="combat_001",
            subject="测试确认",
            acknowledgment_required=True
        )
        
        self.comm_system.send_message(message)
        
        # 确认消息
        ack_success = self.comm_system.acknowledge_message(message.id, "combat_001")
        assert ack_success is True
        
        # 验证确认状态
        stored_message = self.comm_system.messages[message.id]
        assert stored_message.acknowledged is True
        assert stored_message.acknowledged_by == "combat_001"
        assert stored_message.acknowledged_at is not None
        
        # 验证消息从队列中移除
        combat_queue = self.comm_system.message_queues["combat_001"]
        assert message.id not in combat_queue
    
    def test_broadcast_message(self):
        """测试广播消息"""
        # 添加更多单位
        support_unit = MilitaryUnit(
            id="support_001",
            name="支援单位",
            unit_type=UnitType.SUPPORT_UNIT,
            security_clearance=SecurityLevel.SECRET
        )
        self.comm_system.register_unit(support_unit)
        
        # 创建广播消息
        broadcast_msg = MilitaryMessage(
            message_type=MessageType.ALERT,
            priority=MessagePriority.FLASH,
            sender_id="cmd_001",
            sender_name="第1指挥所",
            subject="紧急警报",
            content={
                "alert_type": "enemy_contact",
                "location": [116.3974, 39.9093],
                "severity": "high"
            },
            security_level=SecurityLevel.SECRET
        )
        
        # 广播到多个单位
        target_units = ["combat_001", "support_001"]
        results = self.comm_system.broadcast_message(broadcast_msg, target_units)
        
        # 验证广播结果
        assert len(results) == 2
        assert results["combat_001"] is True
        assert results["support_001"] is True
        
        # 验证各单位都收到消息
        combat_messages = self.comm_system.receive_messages("combat_001")
        support_messages = self.comm_system.receive_messages("support_001")
        
        assert len(combat_messages) >= 1
        assert len(support_messages) >= 1
        
        # 验证消息内容
        combat_alert = next(m for m in combat_messages if m.subject == "紧急警报")
        assert combat_alert.content["alert_type"] == "enemy_contact"
    
    def test_status_report_creation(self):
        """测试状态报告创建"""
        # 创建状态报告
        status_data = {
            "unit_status": "operational",
            "personnel_count": 50,
            "equipment_status": "good",
            "ammunition_level": 0.8,
            "fuel_level": 0.6,
            "morale": "high"
        }
        
        status_report = self.comm_system.create_status_report(
            sender_id="combat_001",
            recipient_id="cmd_001",
            status_data=status_data
        )
        
        # 验证报告内容
        assert status_report.message_type == MessageType.REPORT
        assert status_report.priority == MessagePriority.ROUTINE
        assert status_report.subject == "状态报告"
        assert status_report.content["status"] == status_data
        assert "report_time" in status_report.content
    
    def test_communication_statistics(self):
        """测试通信统计"""
        # 发送几条消息
        for i in range(3):
            message = MilitaryMessage(
                sender_id="cmd_001",
                recipient_id="combat_001",
                subject=f"测试消息{i}",
                content={"test": f"data_{i}"}
            )
            self.comm_system.send_message(message)
        
        # 获取统计信息
        stats = self.comm_system.get_communication_statistics()
        
        # 验证统计数据
        assert stats["total_messages"] >= 3
        assert stats["delivered_messages"] >= 0
        assert stats["active_units"] == 2
        assert stats["total_units"] == 2
        assert "success_rate" in stats
        assert "message_queues" in stats


class TestIntegratedMilitaryOperations:
    """综合军事作业测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.security_manager = MilitarySecurityManager()
        self.sa_engine = SituationAwarenessEngine()
        self.comm_system = MilitaryCommunicationSystem(self.security_manager)
        
        # 设置军事单位
        self.setup_military_units()
    
    def setup_military_units(self):
        """设置军事单位"""
        # 指挥所
        command_post = MilitaryUnit(
            id="hq_001",
            name="联合指挥部",
            unit_type=UnitType.COMMAND_POST,
            commander="王将军",
            security_clearance=SecurityLevel.TOP_SECRET
        )
        
        # 侦察单位
        recon_unit = MilitaryUnit(
            id="recon_001",
            name="侦察分队",
            unit_type=UnitType.INTELLIGENCE_UNIT,
            commander="赵队长",
            security_clearance=SecurityLevel.CONFIDENTIAL
        )
        
        # 作战单位
        combat_unit = MilitaryUnit(
            id="combat_001",
            name="突击营",
            unit_type=UnitType.COMBAT_UNIT,
            commander="钱营长",
            security_clearance=SecurityLevel.CONFIDENTIAL  # 提升安全等级以接收机密消息
        )
        
        # 注册单位
        self.comm_system.register_unit(command_post)
        self.comm_system.register_unit(recon_unit)
        self.comm_system.register_unit(combat_unit)
    
    def test_reconnaissance_to_assessment_workflow(self):
        """测试侦察-评估工作流程"""
        # 1. 侦察单位发现目标
        target_data = RealTimeData(
            id="recon_target_001",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "enemy_tank_001",
                "name": "敌军主战坦克",
                "type": "main_battle_tank",
                "position": [116.3974, 39.9093, 0],
                "velocity": [15.0, 8.0, 0],
                "heading": 90.0,
                "allegiance": "enemy",
                "capabilities": ["heavy_armor", "main_gun", "mobility"]
            },
            source="recon_001"
        )
        
        # 2. 态势感知系统处理数据
        ingestion_success = self.sa_engine.ingest_sensor_data(target_data)
        assert ingestion_success is True
        
        # 3. 执行态势评估
        assessment = self.sa_engine.perform_situation_assessment()
        assert assessment is not None
        assert assessment.threat_level != ThreatLevel.MINIMAL
        
        # 4. 侦察单位向指挥部报告
        intel_report = MilitaryMessage(
            message_type=MessageType.INTELLIGENCE,
            priority=MessagePriority.IMMEDIATE,
            sender_id="recon_001",
            sender_name="侦察分队",
            recipient_id="hq_001",
            recipient_name="联合指挥部",
            subject="敌军目标发现报告",
            content={
                "target_id": "enemy_tank_001",
                "target_type": "main_battle_tank",
                "position": [116.3974, 39.9093, 0],
                "threat_assessment": assessment.threat_level.value,
                "recommendations": assessment.recommendations
            },
            security_level=SecurityLevel.CONFIDENTIAL,
            acknowledgment_required=True
        )
        
        send_success = self.comm_system.send_message(intel_report)
        assert send_success is True
        
        # 5. 指挥部接收报告
        hq_messages = self.comm_system.receive_messages("hq_001")
        assert len(hq_messages) >= 1
        
        intel_msg = next(m for m in hq_messages if m.subject == "敌军目标发现报告")
        assert intel_msg.content["target_type"] == "main_battle_tank"
        assert intel_msg.content["threat_assessment"] == assessment.threat_level.value
    
    def test_command_and_control_workflow(self):
        """测试指挥控制工作流程"""
        # 1. 指挥部下达作战指令
        combat_order = self.comm_system.create_command_message(
            sender_id="hq_001",
            recipient_id="combat_001",
            command="engage_target",
            parameters={
                "target_id": "enemy_tank_001",
                "engagement_type": "direct_fire",
                "rules_of_engagement": "weapons_free",
                "coordination_required": True
            },
            priority=MessagePriority.FLASH
        )
        
        send_success = self.comm_system.send_message(combat_order)
        assert send_success is True, f"Message send failed. Units: {list(self.comm_system.units.keys())}"
        
        # 2. 作战单位接收指令
        combat_messages = self.comm_system.receive_messages("combat_001")
        assert len(combat_messages) >= 1, f"Expected messages but got {len(combat_messages)}"

        # 查找指令消息
        order_msg = None
        for msg in combat_messages:
            if msg.content.get("command") == "engage_target":
                order_msg = msg
                break

        assert order_msg is not None, f"Command message not found in {[m.content for m in combat_messages]}"
        assert order_msg.content["parameters"]["target_id"] == "enemy_tank_001"
        assert order_msg.priority == MessagePriority.FLASH
        
        # 3. 作战单位确认指令
        ack_success = self.comm_system.acknowledge_message(order_msg.id, "combat_001")
        assert ack_success is True
        
        # 4. 作战单位报告执行状态
        execution_report = self.comm_system.create_status_report(
            sender_id="combat_001",
            recipient_id="hq_001",
            status_data={
                "mission_status": "in_progress",
                "target_engaged": True,
                "ammunition_expended": 0.2,
                "casualties": 0,
                "estimated_completion": "5_minutes"
            }
        )
        
        report_success = self.comm_system.send_message(execution_report)
        assert report_success is True
        
        # 5. 指挥部接收执行报告
        hq_messages = self.comm_system.receive_messages("hq_001")
        status_reports = [m for m in hq_messages if m.message_type == MessageType.REPORT]
        assert len(status_reports) >= 1
        
        latest_report = status_reports[-1]
        assert latest_report.content["status"]["mission_status"] == "in_progress"
        assert latest_report.content["status"]["target_engaged"] is True
    
    def test_multi_unit_coordination(self):
        """测试多单位协同"""
        # 1. 指挥部向多个单位广播协同指令
        coordination_msg = MilitaryMessage(
            message_type=MessageType.COORDINATION,
            priority=MessagePriority.IMMEDIATE,
            sender_id="hq_001",
            sender_name="联合指挥部",
            subject="协同作战指令",
            content={
                "operation_name": "联合突击",
                "phase": "preparation",
                "coordination_time": (datetime.now() + timedelta(minutes=10)).isoformat(),
                "roles": {
                    "recon_001": "target_designation",
                    "combat_001": "main_assault"
                }
            },
            security_level=SecurityLevel.CONFIDENTIAL,
            acknowledgment_required=True
        )
        
        # 广播到参与单位
        target_units = ["recon_001", "combat_001"]
        broadcast_results = self.comm_system.broadcast_message(coordination_msg, target_units)
        
        # 验证广播成功
        assert all(broadcast_results.values())
        
        # 2. 各单位确认协同指令
        for unit_id in target_units:
            unit_messages = self.comm_system.receive_messages(unit_id)

            # 查找协同指令消息
            coord_msg = None
            for msg in unit_messages:
                if msg.subject == "协同作战指令":
                    coord_msg = msg
                    break

            assert coord_msg is not None, f"Coordination message not found for {unit_id}"

            # 确认消息
            ack_success = self.comm_system.acknowledge_message(coord_msg.id, unit_id)
            assert ack_success is True
        
        # 3. 验证协同信息
        recon_messages = self.comm_system.receive_messages("recon_001")
        combat_messages = self.comm_system.receive_messages("combat_001")
        
        # 由于消息已确认，队列应该为空或只包含其他消息
        # 但我们可以从存储的消息中验证内容
        coord_messages = [m for m in self.comm_system.messages.values() 
                         if m.subject == "协同作战指令"]
        assert len(coord_messages) == 2  # 广播给两个单位
        
        for msg in coord_messages:
            assert msg.content["operation_name"] == "联合突击"
            assert msg.acknowledged is True
    
    def test_emergency_response_workflow(self):
        """测试应急响应工作流程"""
        # 1. 模拟紧急威胁出现
        emergency_data = RealTimeData(
            id="emergency_001",
            data_type=DataType.ALERT,
            content={
                "alert_type": "incoming_missile",
                "threat_level": 0.95,
                "estimated_impact_time": 120,  # 2分钟
                "impact_area": [116.4074, 39.9193],
                "recommended_action": "immediate_evacuation"
            },
            source="early_warning_system",
            priority=ProcessingPriority.CRITICAL
        )
        
        # 2. 系统自动生成紧急警报
        emergency_alert = MilitaryMessage(
            message_type=MessageType.ALERT,
            priority=MessagePriority.FLASH_OVERRIDE,
            sender_id="hq_001",
            sender_name="联合指挥部",
            subject="紧急威胁警报",
            content=emergency_data.content,
            security_level=SecurityLevel.SECRET,
            expires_at=datetime.now() + timedelta(minutes=2)
        )
        
        # 3. 向所有单位广播紧急警报
        all_units = list(self.comm_system.units.keys())
        broadcast_results = self.comm_system.broadcast_message(emergency_alert, all_units)
        
        # 验证广播成功
        assert all(broadcast_results.values())
        
        # 4. 验证所有单位都收到警报
        for unit_id in all_units:
            unit_messages = self.comm_system.receive_messages(unit_id)
            alert_messages = [m for m in unit_messages if m.message_type == MessageType.ALERT]
            assert len(alert_messages) >= 1
            
            emergency_msg = next(m for m in alert_messages if m.subject == "紧急威胁警报")
            assert emergency_msg.priority == MessagePriority.FLASH_OVERRIDE
            assert emergency_msg.content["alert_type"] == "incoming_missile"
        
        # 5. 验证通信统计反映紧急情况
        comm_stats = self.comm_system.get_communication_statistics()
        assert comm_stats["total_messages"] >= len(all_units)  # 至少广播给所有单位
