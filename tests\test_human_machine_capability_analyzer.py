"""
人机能力分析引擎测试
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.hmdm.allocation.human_machine_capability_analyzer import (
    HumanMachineCapabilityAnalyzer,
    HumanCapability,
    MachineCapability,
    CapabilityType
)
from src.hmdm.models.task_models import Task, TaskType, TaskAttribute, ExecutorType


class TestHumanMachineCapabilityAnalyzer:
    """人机能力分析引擎测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.analyzer = HumanMachineCapabilityAnalyzer()
        
        # 创建测试任务
        self.test_task_complex = Task(
            name="复杂态势分析任务",
            description="需要创新思维和复杂决策的态势分析",
            task_type=TaskType.TYPICAL_FUNCTION,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        
        # 设置复杂任务属性
        self.test_task_complex.attributes = TaskAttribute(
            complexity=0.9,
            importance=0.8,
            urgency=0.7,
            real_time_requirement=False,
            combat_effectiveness=0.8
        )
        
        self.test_task_routine = Task(
            name="数据处理任务",
            description="大量数据的计算和统计分析",
            task_type=TaskType.OPERATION_SEQUENCE,
            executor_type=ExecutorType.MACHINE
        )
        
        # 设置常规任务属性
        self.test_task_routine.attributes = TaskAttribute(
            complexity=0.3,
            importance=0.6,
            urgency=0.9,
            real_time_requirement=True,
            combat_effectiveness=0.5
        )
    
    def test_human_capability_model(self):
        """测试人类能力模型"""
        human_cap = HumanCapability()
        
        # 测试默认值
        assert 0 <= human_cap.cognitive_load_capacity <= 1
        assert 0 <= human_cap.decision_flexibility <= 1
        assert 0 <= human_cap.creative_thinking <= 1
        
        # 测试转换为字典
        cap_dict = human_cap.to_dict()
        assert isinstance(cap_dict, dict)
        assert 'cognitive_load_capacity' in cap_dict
        assert 'creative_thinking' in cap_dict
        assert len(cap_dict) == 8  # 8个能力维度
    
    def test_machine_capability_model(self):
        """测试机器能力模型"""
        machine_cap = MachineCapability()
        
        # 测试默认值
        assert 0 <= machine_cap.processing_speed <= 1
        assert 0 <= machine_cap.computational_accuracy <= 1
        assert 0 <= machine_cap.continuous_operation <= 1
        
        # 测试转换为字典
        cap_dict = machine_cap.to_dict()
        assert isinstance(cap_dict, dict)
        assert 'processing_speed' in cap_dict
        assert 'computational_accuracy' in cap_dict
        assert len(cap_dict) == 8  # 8个能力维度
    
    def test_analyze_task_requirements(self):
        """测试任务需求分析"""
        # 测试复杂任务
        requirements_complex = self.analyzer.analyze_task_requirements(self.test_task_complex)
        
        assert isinstance(requirements_complex, dict)
        assert len(requirements_complex) == len(CapabilityType)
        
        # 复杂任务应该对认知和创造能力需求较高
        assert requirements_complex[CapabilityType.COGNITIVE] > 0.5
        assert requirements_complex[CapabilityType.CREATIVE] > 0.3
        
        # 测试常规任务
        requirements_routine = self.analyzer.analyze_task_requirements(self.test_task_routine)
        
        # 常规任务应该对处理能力需求较高
        assert requirements_routine[CapabilityType.PROCESSING] > 0.3
        
        # 所有需求值应该在0-1范围内
        for req_value in requirements_complex.values():
            assert 0 <= req_value <= 1
        
        for req_value in requirements_routine.values():
            assert 0 <= req_value <= 1
    
    def test_evaluate_human_suitability(self):
        """测试人类适合度评估"""
        # 测试复杂任务的人类适合度
        human_score_complex = self.analyzer.evaluate_human_suitability(self.test_task_complex)
        
        assert isinstance(human_score_complex, float)
        assert 0 <= human_score_complex <= 1
        
        # 测试常规任务的人类适合度
        human_score_routine = self.analyzer.evaluate_human_suitability(self.test_task_routine)
        
        assert isinstance(human_score_routine, float)
        assert 0 <= human_score_routine <= 1
        
        # 复杂任务的人类适合度应该高于常规任务
        assert human_score_complex >= human_score_routine
    
    def test_evaluate_machine_suitability(self):
        """测试机器适合度评估"""
        # 测试复杂任务的机器适合度
        machine_score_complex = self.analyzer.evaluate_machine_suitability(self.test_task_complex)
        
        assert isinstance(machine_score_complex, float)
        assert 0 <= machine_score_complex <= 1
        
        # 测试常规任务的机器适合度
        machine_score_routine = self.analyzer.evaluate_machine_suitability(self.test_task_routine)
        
        assert isinstance(machine_score_routine, float)
        assert 0 <= machine_score_routine <= 1
        
        # 常规任务的机器适合度应该高于复杂任务
        assert machine_score_routine >= machine_score_complex
    
    def test_recommend_allocation(self):
        """测试分配推荐"""
        # 测试复杂任务分配推荐
        allocation_type, confidence, details = self.analyzer.recommend_allocation(self.test_task_complex)
        
        assert allocation_type in ["human", "machine", "collaboration"]
        assert isinstance(confidence, float)
        assert 0 <= confidence <= 1
        assert isinstance(details, dict)
        
        # 验证详细信息包含必要字段
        assert "reason" in details
        assert "confidence" in details
        assert "human_score" in details
        assert "machine_score" in details
        
        # 测试常规任务分配推荐
        allocation_type_routine, confidence_routine, details_routine = self.analyzer.recommend_allocation(self.test_task_routine)
        
        assert allocation_type_routine in ["human", "machine", "collaboration"]
        assert isinstance(confidence_routine, float)
        assert 0 <= confidence_routine <= 1
        
        # 验证不同任务类型可能得到不同的分配建议
        # 这里不强制要求特定结果，因为算法可能会根据具体实现调整
    
    def test_get_human_advantages(self):
        """测试获取人类优势"""
        advantages = self.analyzer._get_human_advantages(self.test_task_complex)
        
        assert isinstance(advantages, list)
        assert len(advantages) > 0
        
        # 验证优势描述是字符串
        for advantage in advantages:
            assert isinstance(advantage, str)
            assert len(advantage) > 0
    
    def test_get_machine_advantages(self):
        """测试获取机器优势"""
        advantages = self.analyzer._get_machine_advantages(self.test_task_routine)
        
        assert isinstance(advantages, list)
        assert len(advantages) > 0
        
        # 验证优势描述是字符串
        for advantage in advantages:
            assert isinstance(advantage, str)
            assert len(advantage) > 0
    
    def test_suggest_roles(self):
        """测试角色建议"""
        human_role = self.analyzer._suggest_human_role(self.test_task_complex)
        machine_role = self.analyzer._suggest_machine_role(self.test_task_routine)
        
        assert isinstance(human_role, str)
        assert isinstance(machine_role, str)
        assert len(human_role) > 0
        assert len(machine_role) > 0
    
    def test_capability_weights_initialization(self):
        """测试能力权重初始化"""
        weights = self.analyzer.task_capability_weights
        
        assert isinstance(weights, dict)
        assert 'high_complexity' in weights
        assert 'high_accuracy' in weights
        assert 'time_critical' in weights
        assert 'creative_task' in weights
        assert 'routine_task' in weights
        
        # 验证每个权重配置包含所有能力类型
        for task_type, capability_weights in weights.items():
            assert len(capability_weights) == len(CapabilityType)
            for cap_type, weight in capability_weights.items():
                assert isinstance(cap_type, CapabilityType)
                assert 0 <= weight <= 1
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空任务
        empty_task = Task(name="空任务", description="", task_type=TaskType.META_OPERATION)
        empty_task.attributes = TaskAttribute()
        
        # 应该能正常处理空任务
        requirements = self.analyzer.analyze_task_requirements(empty_task)
        assert isinstance(requirements, dict)
        
        human_score = self.analyzer.evaluate_human_suitability(empty_task)
        assert 0 <= human_score <= 1
        
        machine_score = self.analyzer.evaluate_machine_suitability(empty_task)
        assert 0 <= machine_score <= 1
        
        allocation_type, confidence, details = self.analyzer.recommend_allocation(empty_task)
        assert allocation_type in ["human", "machine", "collaboration"]
    
    def test_threshold_sensitivity(self):
        """测试阈值敏感性"""
        # 测试不同阈值对分配决策的影响
        thresholds = [0.05, 0.1, 0.15, 0.2, 0.3]
        
        for threshold in thresholds:
            allocation_type, confidence, details = self.analyzer.recommend_allocation(
                self.test_task_complex, threshold=threshold
            )
            assert allocation_type in ["human", "machine", "collaboration"]
            assert 0 <= confidence <= 1
    
    def test_logging(self):
        """测试日志记录"""
        # 验证分析器有日志记录器
        assert hasattr(self.analyzer, 'logger')
        assert self.analyzer.logger is not None

        # 执行分配推荐，验证不会出错
        allocation_type, confidence, details = self.analyzer.recommend_allocation(self.test_task_complex)
        assert allocation_type in ["human", "machine", "collaboration"]


if __name__ == '__main__':
    pytest.main([__file__])
