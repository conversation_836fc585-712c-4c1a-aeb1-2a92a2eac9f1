"""
任务分解相关的数据模型

基于层次任务分析法和GOMS模型，实现三层次任务分解：
使命任务 → ZZ任务 → 典型功能 → 人机交互流程 → 操作序列 → 元操作
"""

from typing import List, Optional, Dict, Any, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid


class TaskType(Enum):
    """任务类型枚举"""
    MISSION_TASK = "使命任务"
    ZZ_TASK = "ZZ任务"
    TYPICAL_FUNCTION = "典型功能"
    INTERACTION_PROCESS = "人机交互流程"
    OPERATION_SEQUENCE = "操作序列"
    META_OPERATION = "元操作"


class OperationType(Enum):
    """操作类型枚举（基于GOMS模型）"""
    PERCEPTION = "感知操作"
    COGNITION = "认知操作"
    MOTOR = "动机行为"
    COMBINED = "组合操作"


class ExecutorType(Enum):
    """执行者类型"""
    HUMAN = "人"
    MACHINE = "机"
    HUMAN_MACHINE = "人机协作"


@dataclass
class TaskAttribute:
    """任务属性"""
    complexity: float = 0.0  # 复杂度 (0-1)
    importance: float = 0.0  # 重要性 (0-1)
    urgency: float = 0.0     # 紧急性 (0-1)
    frequency: float = 0.0   # 频率 (0-1)
    duration: float = 0.0    # 预计执行时间(秒)
    error_rate: float = 0.0  # 错误率 (0-1)
    workload: float = 0.0    # 工作负荷 (0-1)

    # 军事专用属性
    military_priority: str = ""      # 军事优先级
    security_level: str = ""         # 安全等级
    real_time_requirement: bool = False  # 实时性要求
    combat_effectiveness: float = 0.0    # 作战效能

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "complexity": self.complexity,
            "importance": self.importance,
            "urgency": self.urgency,
            "frequency": self.frequency,
            "duration": self.duration,
            "error_rate": self.error_rate,
            "workload": self.workload,
            "military_priority": self.military_priority,
            "security_level": self.security_level,
            "real_time_requirement": self.real_time_requirement,
            "combat_effectiveness": self.combat_effectiveness
        }

    def get(self, key: str, default=None):
        """获取属性值，支持字典式访问"""
        return getattr(self, key, default)

    def __contains__(self, key: str) -> bool:
        """支持 'in' 操作符"""
        return hasattr(self, key)


@dataclass
class IOPattern:
    """Input-Action-Output模式"""
    input_description: str = ""      # 输入描述
    action_description: str = ""     # 动作描述
    output_description: str = ""     # 输出描述
    input_type: str = ""            # 输入类型
    output_type: str = ""           # 输出类型
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式"""
        return {
            "input_description": self.input_description,
            "action_description": self.action_description,
            "output_description": self.output_description,
            "input_type": self.input_type,
            "output_type": self.output_type
        }


@dataclass
class Task:
    """任务基础类"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    task_type: TaskType = TaskType.MISSION_TASK
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    level: int = 0  # 层级深度
    
    # 任务属性
    attributes: TaskAttribute = field(default_factory=TaskAttribute)
    
    # IO模式
    io_pattern: IOPattern = field(default_factory=IOPattern)
    
    # 执行相关
    executor_type: ExecutorType = ExecutorType.HUMAN_MACHINE
    prerequisites: List[str] = field(default_factory=list)  # 前置条件
    constraints: List[str] = field(default_factory=list)    # 约束条件
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_child(self, child_id: str) -> None:
        """添加子任务"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
            self.updated_at = datetime.now()
    
    def remove_child(self, child_id: str) -> None:
        """移除子任务"""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type.value,
            "parent_id": self.parent_id,
            "children_ids": self.children_ids,
            "level": self.level,
            "attributes": self.attributes.to_dict(),
            "io_pattern": self.io_pattern.to_dict(),
            "executor_type": self.executor_type.value,
            "prerequisites": self.prerequisites,
            "constraints": self.constraints,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata
        }


@dataclass
class MetaOperation(Task):
    """元操作类（最小的不可分解操作单位）"""
    operation_type: OperationType = OperationType.PERCEPTION
    execution_time: float = 0.0  # 预定执行时间(秒)
    mental_preparation_time: float = 0.0  # 心理准备时间
    positioning_time: float = 0.0  # 归位时间
    action_time: float = 0.0  # 实际动作时间
    
    def __post_init__(self):
        """初始化后处理"""
        self.task_type = TaskType.META_OPERATION
        # 计算总执行时间
        self.execution_time = (
            self.mental_preparation_time + 
            self.positioning_time + 
            self.action_time
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            "operation_type": self.operation_type.value,
            "execution_time": self.execution_time,
            "mental_preparation_time": self.mental_preparation_time,
            "positioning_time": self.positioning_time,
            "action_time": self.action_time
        })
        return base_dict


@dataclass
class TaskHierarchy:
    """任务层次结构"""
    root_task_id: str
    tasks: Dict[str, Task] = field(default_factory=dict)
    
    def add_task(self, task: Task) -> None:
        """添加任务到层次结构"""
        self.tasks[task.id] = task
        
        # 如果有父任务，将此任务添加为子任务
        if task.parent_id and task.parent_id in self.tasks:
            parent_task = self.tasks[task.parent_id]
            parent_task.add_child(task.id)
            # 设置层级
            task.level = parent_task.level + 1
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def get_children(self, task_id: str) -> List[Task]:
        """获取子任务列表"""
        task = self.get_task(task_id)
        if not task:
            return []
        return [self.tasks[child_id] for child_id in task.children_ids 
                if child_id in self.tasks]
    
    def get_parent(self, task_id: str) -> Optional[Task]:
        """获取父任务"""
        task = self.get_task(task_id)
        if not task or not task.parent_id:
            return None
        return self.get_task(task.parent_id)
    
    def get_path_to_root(self, task_id: str) -> List[Task]:
        """获取到根任务的路径"""
        path = []
        current_task = self.get_task(task_id)
        
        while current_task:
            path.append(current_task)
            if current_task.parent_id:
                current_task = self.get_task(current_task.parent_id)
            else:
                break
        
        return list(reversed(path))
    
    def get_all_descendants(self, task_id: str) -> List[Task]:
        """获取所有后代任务"""
        descendants = []
        children = self.get_children(task_id)

        for child in children:
            descendants.append(child)
            descendants.extend(self.get_all_descendants(child.id))

        return descendants

    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_tasks_by_level(self, level: int) -> List[Task]:
        """获取指定层级的任务"""
        return [task for task in self.tasks.values() if task.level == level]

    def get_leaf_tasks(self) -> List[Task]:
        """获取叶子任务（没有子任务的任务）"""
        return [task for task in self.tasks.values() if not task.children_ids]

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "root_task_id": self.root_task_id,
            "tasks": {task_id: task.to_dict() for task_id, task in self.tasks.items()}
        }
