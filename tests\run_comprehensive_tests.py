#!/usr/bin/env python3
"""
HMDM系统综合测试执行脚本

该脚本执行完整的测试套件，包括：
- 功能测试
- 性能测试
- 集成测试
- API测试
- 边界条件测试

使用方法:
    python tests/run_comprehensive_tests.py [--test-type TYPE] [--output-dir DIR]
"""

import os
import sys
import json
import time
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from hmdm.utils.logger import get_logger, PerformanceTimer


class ComprehensiveTestRunner:
    """综合测试执行器"""
    
    def __init__(self, output_dir: str = "test_results"):
        self.logger = get_logger("test_runner")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "test_suites": {},
            "summary": {},
            "errors": []
        }
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试套件"""
        self.logger.info("开始执行综合测试套件")
        
        test_suites = [
            ("unit_tests", self.run_unit_tests),
            ("integration_tests", self.run_integration_tests),
            ("performance_tests", self.run_performance_tests),
            ("api_tests", self.run_api_tests),
            ("boundary_tests", self.run_boundary_tests),
            ("scenario_tests", self.run_scenario_tests)
        ]
        
        for suite_name, test_func in test_suites:
            self.logger.info(f"执行测试套件: {suite_name}")
            try:
                with PerformanceTimer(f"{suite_name}_execution") as timer:
                    result = test_func()
                    result["execution_time"] = timer.duration
                    self.test_results["test_suites"][suite_name] = result
            except Exception as e:
                self.logger.error(f"测试套件 {suite_name} 执行失败: {e}")
                self.test_results["errors"].append({
                    "suite": suite_name,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        self.test_results["end_time"] = datetime.now().isoformat()
        self.generate_summary()
        self.save_results()
        
        return self.test_results
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        self.logger.info("执行单元测试")
        
        # 运行pytest
        cmd = [
            sys.executable, "-m", "pytest", 
            "tests/test_task_analysis.py",
            "tests/test_decision.py", 
            "-v", "--tb=short",
            "--cov=src",
            "--cov-report=json",
            f"--cov-report=html:{self.output_dir}/coverage_html"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # 解析覆盖率报告
        coverage_data = {}
        coverage_file = Path("coverage.json")
        if coverage_file.exists():
            with open(coverage_file) as f:
                coverage_data = json.load(f)
        
        return {
            "status": "passed" if result.returncode == 0 else "failed",
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "coverage": coverage_data.get("totals", {}) if coverage_data else {}
        }
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        self.logger.info("执行集成测试")
        
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_integration.py",
            "-v", "--tb=short"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        return {
            "status": "passed" if result.returncode == 0 else "failed",
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        self.logger.info("执行性能测试")
        
        from tests.test_performance import PerformanceTestSuite
        
        perf_suite = PerformanceTestSuite()
        results = {}
        
        # 任务分解性能测试
        results["task_decomposition"] = perf_suite.test_task_decomposition_performance()
        
        # 决策算法性能测试
        results["decision_algorithms"] = perf_suite.test_decision_algorithms_performance()
        
        # 数据处理性能测试
        results["data_processing"] = perf_suite.test_data_processing_performance()
        
        # 内存使用测试
        results["memory_usage"] = perf_suite.test_memory_usage()
        
        return {
            "status": "completed",
            "results": results,
            "benchmarks_met": self.check_performance_benchmarks(results)
        }
    
    def run_api_tests(self) -> Dict[str, Any]:
        """运行API测试"""
        self.logger.info("执行API测试")
        
        # 启动Web服务器
        server_process = self.start_web_server()
        
        try:
            # 等待服务器启动
            time.sleep(3)
            
            # 运行API测试
            from tests.test_api import APITestSuite
            api_suite = APITestSuite("http://localhost:5000")
            results = api_suite.run_all_tests()
            
            return {
                "status": "completed",
                "results": results,
                "server_started": server_process is not None
            }
            
        finally:
            # 停止Web服务器
            if server_process:
                server_process.terminate()
                server_process.wait()
    
    def run_boundary_tests(self) -> Dict[str, Any]:
        """运行边界条件测试"""
        self.logger.info("执行边界条件测试")
        
        from tests.test_boundary import BoundaryTestSuite
        
        boundary_suite = BoundaryTestSuite()
        results = {}
        
        # 空输入测试
        results["empty_input"] = boundary_suite.test_empty_input_handling()
        
        # 极值测试
        results["extreme_values"] = boundary_suite.test_extreme_values()
        
        # 大数据量测试
        results["large_dataset"] = boundary_suite.test_large_dataset_handling()
        
        # 异常数据测试
        results["invalid_data"] = boundary_suite.test_invalid_data_handling()
        
        return {
            "status": "completed",
            "results": results
        }
    
    def run_scenario_tests(self) -> Dict[str, Any]:
        """运行场景测试"""
        self.logger.info("执行场景测试")
        
        from tests.test_scenarios import ScenarioTestSuite
        
        scenario_suite = ScenarioTestSuite()
        results = {}
        
        # 态势分析场景
        results["situation_analysis"] = scenario_suite.test_situation_analysis_scenario()
        
        # 威胁计算场景
        results["threat_calculation"] = scenario_suite.test_threat_calculation_scenario()
        
        # 辅助决策场景
        results["decision_support"] = scenario_suite.test_decision_support_scenario()
        
        return {
            "status": "completed",
            "results": results
        }
    
    def start_web_server(self) -> Optional[subprocess.Popen]:
        """启动Web服务器"""
        try:
            cmd = [sys.executable, "-m", "src.web.app"]
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )
            return process
        except Exception as e:
            self.logger.error(f"启动Web服务器失败: {e}")
            return None
    
    def check_performance_benchmarks(self, results: Dict[str, Any]) -> Dict[str, bool]:
        """检查性能基准"""
        benchmarks = {}
        
        # 定义性能基准
        performance_targets = {
            "task_decomposition_time": 5.0,  # 秒
            "decision_algorithm_time": 10.0,  # 秒
            "memory_usage": 500,  # MB
            "data_processing_time": 3.0  # 秒
        }
        
        for metric, target in performance_targets.items():
            actual = self.extract_performance_metric(results, metric)
            benchmarks[metric] = actual <= target if actual is not None else False
        
        return benchmarks
    
    def extract_performance_metric(self, results: Dict[str, Any], metric: str) -> Optional[float]:
        """从结果中提取性能指标"""
        # 这里需要根据实际的结果结构来提取指标
        # 简化实现
        return None
    
    def generate_summary(self):
        """生成测试摘要"""
        summary = {
            "total_suites": len(self.test_results["test_suites"]),
            "passed_suites": 0,
            "failed_suites": 0,
            "total_errors": len(self.test_results["errors"]),
            "execution_time": 0
        }
        
        for suite_name, suite_result in self.test_results["test_suites"].items():
            if suite_result.get("status") in ["passed", "completed"]:
                summary["passed_suites"] += 1
            else:
                summary["failed_suites"] += 1
            
            summary["execution_time"] += suite_result.get("execution_time", 0)
        
        summary["success_rate"] = (
            summary["passed_suites"] / summary["total_suites"] 
            if summary["total_suites"] > 0 else 0
        )
        
        self.test_results["summary"] = summary
    
    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_file = self.output_dir / f"test_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        # 保存摘要报告
        summary_file = self.output_dir / f"test_summary_{timestamp}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            self.write_summary_report(f)
        
        self.logger.info(f"测试结果已保存到: {results_file}")
        self.logger.info(f"测试摘要已保存到: {summary_file}")
    
    def write_summary_report(self, file):
        """写入摘要报告"""
        summary = self.test_results["summary"]
        
        file.write("HMDM系统综合测试报告\n")
        file.write("=" * 50 + "\n\n")
        
        file.write(f"测试开始时间: {self.test_results['start_time']}\n")
        file.write(f"测试结束时间: {self.test_results['end_time']}\n")
        file.write(f"总执行时间: {summary['execution_time']:.2f} 秒\n\n")
        
        file.write("测试套件统计:\n")
        file.write(f"  总套件数: {summary['total_suites']}\n")
        file.write(f"  通过套件: {summary['passed_suites']}\n")
        file.write(f"  失败套件: {summary['failed_suites']}\n")
        file.write(f"  成功率: {summary['success_rate']:.2%}\n\n")
        
        if self.test_results["errors"]:
            file.write("错误信息:\n")
            for error in self.test_results["errors"]:
                file.write(f"  [{error['suite']}] {error['error']}\n")
            file.write("\n")
        
        file.write("各测试套件详情:\n")
        for suite_name, suite_result in self.test_results["test_suites"].items():
            status = suite_result.get("status", "unknown")
            exec_time = suite_result.get("execution_time", 0)
            file.write(f"  {suite_name}: {status} ({exec_time:.2f}s)\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HMDM系统综合测试")
    parser.add_argument(
        "--test-type", 
        choices=["all", "unit", "integration", "performance", "api", "boundary", "scenario"],
        default="all",
        help="指定要运行的测试类型"
    )
    parser.add_argument(
        "--output-dir",
        default="test_results",
        help="测试结果输出目录"
    )
    
    args = parser.parse_args()
    
    runner = ComprehensiveTestRunner(args.output_dir)
    
    if args.test_type == "all":
        results = runner.run_all_tests()
    else:
        # 运行特定类型的测试
        test_methods = {
            "unit": runner.run_unit_tests,
            "integration": runner.run_integration_tests,
            "performance": runner.run_performance_tests,
            "api": runner.run_api_tests,
            "boundary": runner.run_boundary_tests,
            "scenario": runner.run_scenario_tests
        }
        
        if args.test_type in test_methods:
            results = test_methods[args.test_type]()
            runner.test_results["test_suites"][args.test_type] = results
            runner.generate_summary()
            runner.save_results()
    
    # 打印摘要
    summary = runner.test_results["summary"]
    print(f"\n测试完成!")
    print(f"成功率: {summary.get('success_rate', 0):.2%}")
    print(f"执行时间: {summary.get('execution_time', 0):.2f} 秒")
    
    # 返回适当的退出码
    return 0 if summary.get("failed_suites", 0) == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
