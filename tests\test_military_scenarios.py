"""
军事场景测试用例

测试军事场景任务分解和评估功能
"""

import pytest
import time
from src.hmdm.task_analysis.hierarchical_task_analyzer import HierarchicalTaskAnalyzer
from src.hmdm.task_analysis.military_scenarios import MilitaryScenarioTemplates
from src.hmdm.evaluation.scheme_evaluator import SchemeEvaluator
from src.hmdm.evaluation.military_indicators import MilitaryIndicatorSystem
from src.hmdm.decision.rapid_decision_engine import RapidDecisionEngine
from src.hmdm.models.task_models import Task, TaskType, ExecutorType
from src.hmdm.models.decision_models import Alternative, DecisionMatrix


class TestMilitaryScenarios:
    """军事场景测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = HierarchicalTaskAnalyzer()
        self.evaluator = SchemeEvaluator()
        self.rapid_engine = RapidDecisionEngine()
    
    def test_military_scenario_templates(self):
        """测试军事场景模板"""
        templates = MilitaryScenarioTemplates.get_military_scenario_templates()
        
        # 验证场景数量
        assert len(templates) >= 3, "应至少包含3个军事场景"
        
        # 验证必要场景
        required_scenarios = ["态势分析", "威胁计算", "辅助决策"]
        for scenario in required_scenarios:
            assert scenario in templates, f"缺少必要场景: {scenario}"
        
        # 验证场景结构
        for scenario_name, scenario_tasks in templates.items():
            assert isinstance(scenario_tasks, list), f"场景{scenario_name}应为任务列表"
            assert len(scenario_tasks) > 0, f"场景{scenario_name}不能为空"
            
            for task in scenario_tasks:
                assert "name" in task, f"场景{scenario_name}的任务缺少名称"
                assert "description" in task, f"场景{scenario_name}的任务缺少描述"
                assert "executor_type" in task, f"场景{scenario_name}的任务缺少执行者类型"
                assert "attributes" in task, f"场景{scenario_name}的任务缺少属性"
    
    def test_situation_analysis_scenario(self):
        """测试态势分析场景"""
        # 创建根任务
        root_task = Task(
            name="联合作战态势分析",
            description="联合作战环境下的态势分析任务",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        
        # 创建任务层次结构
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 使用军事场景分解
        subtasks = self.analyzer.auto_decompose_by_military_scenario(
            root_task.id, "态势分析"
        )
        
        # 验证分解结果
        assert len(subtasks) >= 3, "态势分析应至少包含3个子任务"
        
        # 验证关键子任务
        task_names = [task.name for task in subtasks]
        assert "情报收集与处理" in task_names, "应包含情报收集与处理任务"
        assert "态势感知与理解" in task_names, "应包含态势感知与理解任务"
        assert "态势预测与预警" in task_names, "应包含态势预测与预警任务"
        
        # 验证军事属性
        for task in subtasks:
            assert hasattr(task, 'attributes'), "任务应包含属性"
            attrs = task.attributes
            assert "military_priority" in attrs, "应包含军事优先级"
            assert "security_level" in attrs, "应包含安全等级"
            assert "combat_effectiveness" in attrs, "应包含作战效能"
    
    def test_threat_calculation_scenario(self):
        """测试威胁计算场景"""
        # 创建根任务
        root_task = Task(
            name="威胁计算与评估",
            description="敌方威胁的识别、计算和评估",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        
        # 创建任务层次结构
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 使用军事场景分解
        subtasks = self.analyzer.auto_decompose_by_military_scenario(
            root_task.id, "威胁计算"
        )
        
        # 验证分解结果
        assert len(subtasks) >= 3, "威胁计算应至少包含3个子任务"
        
        # 验证关键子任务
        task_names = [task.name for task in subtasks]
        assert "威胁识别" in task_names, "应包含威胁识别任务"
        assert "威胁评估" in task_names, "应包含威胁评估任务"
        assert "对策生成" in task_names, "应包含对策生成任务"
        
        # 验证实时性要求
        for task in subtasks:
            attrs = task.attributes
            if task.name in ["威胁识别", "威胁评估"]:
                assert attrs.get("real_time_requirement", False), f"{task.name}应要求实时处理"
    
    def test_military_indicators(self):
        """测试军事指标体系"""
        indicators = MilitaryIndicatorSystem.get_military_indicators()
        
        # 验证指标类别
        expected_categories = [
            "作战效能指标", "指挥控制指标"
        ]
        
        for category in expected_categories:
            assert category in indicators, f"缺少指标类别: {category}"
            assert len(indicators[category]) > 0, f"指标类别{category}不能为空"
        
        # 验证作战效能指标
        combat_indicators = indicators["作战效能指标"]
        indicator_names = [ind.name for ind in combat_indicators]
        assert "火力打击效能" in indicator_names, "应包含火力打击效能指标"
        assert "机动能力" in indicator_names, "应包含机动能力指标"
        assert "防护能力" in indicator_names, "应包含防护能力指标"
        
        # 验证指标属性
        for indicator in combat_indicators:
            assert hasattr(indicator, 'military_attributes'), "指标应包含军事属性"
            mil_attrs = indicator.military_attributes
            assert "domain" in mil_attrs, "应包含作战域"
            assert "security_level" in mil_attrs, "应包含安全等级"
    
    def test_military_evaluation_scheme(self):
        """测试军事评估方案"""
        # 测试综合评估方案
        comprehensive_scheme = self.evaluator.create_military_evaluation_scheme(
            scheme_name="综合军事评估",
            focus_area="综合"
        )
        
        assert comprehensive_scheme.name == "综合军事评估"
        assert len(comprehensive_scheme.indicators) > 0, "评估方案应包含指标"
        
        # 验证权重归一化
        total_weight = sum(weight.weight for weight in comprehensive_scheme.weights.values())
        assert abs(total_weight - 1.0) < 0.01, "权重总和应接近1.0"
        
        # 测试专项评估方案
        combat_scheme = self.evaluator.create_military_evaluation_scheme(
            focus_area="作战效能"
        )
        
        assert len(combat_scheme.indicators) > 0, "作战效能评估方案应包含指标"
        
        # 测试指挥控制评估方案
        command_scheme = self.evaluator.create_military_evaluation_scheme(
            focus_area="指挥控制"
        )
        
        assert len(command_scheme.indicators) > 0, "指挥控制评估方案应包含指标"
    
    def test_rapid_decision_engine(self):
        """测试快速决策引擎"""
        # 创建测试备选方案
        alternatives = [
            Alternative(
                id="alt1",
                name="人工主导方案",
                attributes={
                    "combat_strike_effectiveness": 0.8,
                    "command_efficiency": 0.7,
                    "system_response_time": 2.0,
                    "cost_effectiveness": 0.6
                }
            ),
            Alternative(
                id="alt2", 
                name="机器主导方案",
                attributes={
                    "combat_strike_effectiveness": 0.9,
                    "command_efficiency": 0.9,
                    "system_response_time": 0.5,
                    "cost_effectiveness": 0.8
                }
            ),
            Alternative(
                id="alt3",
                name="人机协同方案",
                attributes={
                    "combat_strike_effectiveness": 0.85,
                    "command_efficiency": 0.85,
                    "system_response_time": 1.0,
                    "cost_effectiveness": 0.7
                }
            )
        ]
        
        # 创建军事评估方案
        military_scheme = self.evaluator.create_military_evaluation_scheme(focus_area="综合")
        
        # 创建决策矩阵
        matrix = DecisionMatrix(
            alternatives=alternatives,
            evaluation_scheme=military_scheme
        )
        
        # 测试快速决策
        start_time = time.time()
        result = self.rapid_engine.rapid_wrdm_decision(matrix, time_limit=0.1)
        decision_time = time.time() - start_time
        
        # 验证决策时间
        assert decision_time < 0.15, f"决策时间{decision_time:.3f}s超过限制"
        
        # 验证决策结果
        assert result.recommended_alternative_id in [alt.id for alt in alternatives]
        assert len(result.scores) == len(alternatives)
        assert len(result.rankings) == len(alternatives)
        assert "calculation_time" in result.parameters
    
    def test_parallel_rapid_decision(self):
        """测试并行快速决策"""
        # 创建多个决策矩阵
        matrices = []
        for i in range(3):
            alternatives = [
                Alternative(
                    id=f"alt{i}_1",
                    name=f"方案{i}_1",
                    attributes={"efficiency": 0.7 + i * 0.1, "cost": 0.5}
                ),
                Alternative(
                    id=f"alt{i}_2", 
                    name=f"方案{i}_2",
                    attributes={"efficiency": 0.6 + i * 0.1, "cost": 0.8}
                )
            ]
            
            scheme = self.evaluator.create_default_evaluation_scheme()
            matrix = DecisionMatrix(alternatives=alternatives, evaluation_scheme=scheme)
            matrices.append(matrix)
        
        # 测试并行决策
        start_time = time.time()
        results = self.rapid_engine.parallel_rapid_decision(matrices, time_limit=0.5)
        total_time = time.time() - start_time
        
        # 验证结果
        assert len(results) == len(matrices), "结果数量应与输入矩阵数量相同"
        assert total_time < 0.6, f"并行决策时间{total_time:.3f}s超过限制"
        
        for result in results:
            assert result.recommended_alternative_id is not None
            assert len(result.scores) > 0
    
    def test_military_performance_gap_analysis(self):
        """测试军事性能差距分析"""
        # 创建当前方案
        current_alternative = Alternative(
            id="current",
            name="当前方案",
            attributes={
                "combat_strike_effectiveness": 0.6,
                "command_efficiency": 0.7,
                "logistics_efficiency": 0.5,
                "system_response_time": 3.0
            }
        )
        
        # 设定目标分数
        target_scores = {
            "combat_strike_effectiveness": 0.9,
            "command_efficiency": 0.85,
            "logistics_efficiency": 0.8,
            "system_response_time": 0.8  # 注意：响应时间是越小越好
        }
        
        # 执行差距分析
        gap_analysis = self.evaluator.analyze_military_performance_gap(
            current_alternative=current_alternative,
            target_scores=target_scores,
            focus_area="综合"
        )
        
        # 验证分析结果
        assert "current_total_score" in gap_analysis
        assert "target_total_score" in gap_analysis
        assert "overall_gap" in gap_analysis
        assert "indicator_gaps" in gap_analysis
        assert "improvement_priorities" in gap_analysis
        
        # 验证改进优先级
        priorities = gap_analysis["improvement_priorities"]
        assert len(priorities) > 0, "应识别出需要改进的指标"
        
        # 验证优先级排序（按差距大小降序）
        for i in range(len(priorities) - 1):
            assert priorities[i]["gap"] >= priorities[i + 1]["gap"], "优先级应按差距大小排序"
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        # 创建测试数据
        alternatives = [
            Alternative(id="test1", name="测试1", attributes={"efficiency": 0.8}),
            Alternative(id="test2", name="测试2", attributes={"efficiency": 0.6})
        ]
        
        scheme = self.evaluator.create_default_evaluation_scheme()
        matrix = DecisionMatrix(alternatives=alternatives, evaluation_scheme=scheme)
        
        # 清空缓存
        self.rapid_engine.clear_cache()
        
        # 第一次决策
        result1 = self.rapid_engine.rapid_wrdm_decision(matrix)
        
        # 第二次决策（应使用缓存）
        start_time = time.time()
        result2 = self.rapid_engine.rapid_wrdm_decision(matrix)
        cached_time = time.time() - start_time
        
        # 验证缓存效果
        assert result1.recommended_alternative_id == result2.recommended_alternative_id
        assert cached_time < 0.01, "缓存决策应非常快速"
        
        # 验证缓存统计
        stats = self.rapid_engine.get_cache_stats()
        assert stats["decision_cache_size"] > 0, "决策缓存应有内容"
