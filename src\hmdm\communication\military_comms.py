"""
军事通信与协同模块

实现军事单位间的安全通信、指令传递和协同作战功能。
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import hashlib

from ..security.military_security import SecurityLevel, MilitarySecurityManager


class MessageType(Enum):
    """消息类型"""
    COMMAND = "指令"
    REPORT = "报告"
    REQUEST = "请求"
    RESPONSE = "响应"
    ALERT = "告警"
    INTELLIGENCE = "情报"
    STATUS = "状态"
    COORDINATION = "协同"


class MessagePriority(Enum):
    """消息优先级"""
    ROUTINE = 1
    PRIORITY = 2
    IMMEDIATE = 3
    FLASH = 4
    FLASH_OVERRIDE = 5


class UnitType(Enum):
    """单位类型"""
    COMMAND_POST = "指挥所"
    COMBAT_UNIT = "作战单位"
    SUPPORT_UNIT = "支援单位"
    INTELLIGENCE_UNIT = "情报单位"
    LOGISTICS_UNIT = "后勤单位"
    COMMUNICATION_UNIT = "通信单位"


@dataclass
class MilitaryMessage:
    """军事消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    message_type: MessageType = MessageType.REPORT
    priority: MessagePriority = MessagePriority.ROUTINE
    sender_id: str = ""
    sender_name: str = ""
    recipient_id: str = ""
    recipient_name: str = ""
    subject: str = ""
    content: Dict[str, Any] = field(default_factory=dict)
    security_level: SecurityLevel = SecurityLevel.SECRET
    timestamp: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    acknowledgment_required: bool = False
    acknowledged: bool = False
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: str = ""
    delivery_status: str = "pending"  # pending, delivered, failed, expired
    retry_count: int = 0
    max_retries: int = 3
    
    def is_expired(self) -> bool:
        """检查消息是否过期"""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "message_type": self.message_type.value,
            "priority": self.priority.value,
            "sender_id": self.sender_id,
            "sender_name": self.sender_name,
            "recipient_id": self.recipient_id,
            "recipient_name": self.recipient_name,
            "subject": self.subject,
            "content": self.content,
            "security_level": self.security_level.value,
            "timestamp": self.timestamp.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "acknowledgment_required": self.acknowledgment_required,
            "acknowledged": self.acknowledged,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "acknowledged_by": self.acknowledged_by,
            "delivery_status": self.delivery_status,
            "retry_count": self.retry_count
        }


@dataclass
class MilitaryUnit:
    """军事单位"""
    id: str
    name: str
    unit_type: UnitType
    commander: str = ""
    location: str = ""
    status: str = "active"  # active, inactive, offline
    capabilities: List[str] = field(default_factory=list)
    security_clearance: SecurityLevel = SecurityLevel.SECRET
    communication_channels: List[str] = field(default_factory=list)
    subordinate_units: List[str] = field(default_factory=list)
    superior_unit: str = ""
    last_contact: datetime = field(default_factory=datetime.now)
    
    def is_online(self) -> bool:
        """检查单位是否在线"""
        if self.status != "active":
            return False
        
        # 如果超过5分钟没有联系，认为离线
        return (datetime.now() - self.last_contact).total_seconds() < 300


class MilitaryCommunicationSystem:
    """军事通信系统"""
    
    def __init__(self, security_manager: Optional[MilitarySecurityManager] = None):
        self.logger = logging.getLogger(__name__)
        self.security_manager = security_manager
        
        # 单位管理
        self.units: Dict[str, MilitaryUnit] = {}
        
        # 消息管理
        self.messages: Dict[str, MilitaryMessage] = {}
        self.message_queues: Dict[str, List[str]] = {}  # unit_id -> message_ids
        
        # 通信统计
        self.communication_stats = {
            "total_messages": 0,
            "delivered_messages": 0,
            "failed_messages": 0,
            "pending_messages": 0,
            "average_delivery_time": 0.0
        }
        
        # 消息处理器
        self.message_handlers: Dict[MessageType, List[Callable]] = {}
        
        # 启动后台任务
        self.running = True
        self.background_tasks = []
        # 不在初始化时启动异步任务，而是在需要时启动
    
    def register_unit(self, unit: MilitaryUnit) -> bool:
        """注册军事单位"""
        try:
            if unit.id in self.units:
                self.logger.warning(f"单位 {unit.id} 已存在")
                return False
            
            self.units[unit.id] = unit
            self.message_queues[unit.id] = []
            
            self.logger.info(f"注册单位: {unit.name} ({unit.unit_type.value})")
            return True
            
        except Exception as e:
            self.logger.error(f"单位注册失败: {e}")
            return False
    
    def send_message(self, message: MilitaryMessage, session_token: str = "") -> bool:
        """发送消息"""
        try:
            # 安全检查
            if self.security_manager and session_token:
                if not self.security_manager.check_access_permission(
                    session_token, "communication", message.security_level, "send"
                ):
                    self.logger.warning(f"消息发送权限不足: {message.id}")
                    return False
            
            # 验证收发单位
            if message.sender_id not in self.units:
                self.logger.error(f"发送单位不存在: {message.sender_id}")
                return False
            
            if message.recipient_id not in self.units:
                self.logger.error(f"接收单位不存在: {message.recipient_id}")
                return False
            
            # 检查接收单位状态
            recipient_unit = self.units[message.recipient_id]
            if not recipient_unit.is_online():
                self.logger.warning(f"接收单位离线: {message.recipient_id}")
                message.delivery_status = "failed"
                return False
            
            # 存储消息
            self.messages[message.id] = message
            
            # 添加到接收单位的消息队列
            self.message_queues[message.recipient_id].append(message.id)
            
            # 更新统计
            self.communication_stats["total_messages"] += 1
            self.communication_stats["pending_messages"] += 1
            
            # 标记为已投递
            message.delivery_status = "delivered"
            
            # 触发消息处理器
            self._trigger_message_handlers(message)
            
            self.logger.info(f"消息发送成功: {message.id} from {message.sender_name} to {message.recipient_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"消息发送失败: {e}")
            return False
    
    def receive_messages(self, unit_id: str, session_token: str = "") -> List[MilitaryMessage]:
        """接收消息"""
        try:
            # 安全检查
            if self.security_manager and session_token:
                credential = self.security_manager.validate_session(session_token)
                if not credential:
                    return []
            
            if unit_id not in self.units:
                self.logger.error(f"单位不存在: {unit_id}")
                return []
            
            # 更新单位最后联系时间
            self.units[unit_id].last_contact = datetime.now()
            
            # 获取消息队列
            message_ids = self.message_queues.get(unit_id, [])
            messages = []
            
            for msg_id in message_ids:
                if msg_id in self.messages:
                    message = self.messages[msg_id]
                    
                    # 检查消息是否过期
                    if message.is_expired():
                        message.delivery_status = "expired"
                        continue
                    
                    # 安全等级检查
                    unit = self.units[unit_id]
                    if not self._check_security_clearance(unit, message):
                        continue
                    
                    messages.append(message)
            
            # 按优先级和时间排序
            messages.sort(key=lambda m: (m.priority.value, m.timestamp), reverse=True)
            
            return messages
            
        except Exception as e:
            self.logger.error(f"消息接收失败: {e}")
            return []
    
    def acknowledge_message(self, message_id: str, unit_id: str, session_token: str = "") -> bool:
        """确认消息"""
        try:
            if message_id not in self.messages:
                return False
            
            message = self.messages[message_id]
            
            # 验证接收单位
            if message.recipient_id != unit_id:
                return False
            
            # 标记为已确认
            message.acknowledged = True
            message.acknowledged_at = datetime.now()
            message.acknowledged_by = unit_id
            
            # 从待处理队列中移除
            if unit_id in self.message_queues and message_id in self.message_queues[unit_id]:
                self.message_queues[unit_id].remove(message_id)
                self.communication_stats["pending_messages"] -= 1
                self.communication_stats["delivered_messages"] += 1
            
            self.logger.info(f"消息已确认: {message_id} by {unit_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"消息确认失败: {e}")
            return False
    
    def broadcast_message(self, message: MilitaryMessage, target_units: List[str], 
                         session_token: str = "") -> Dict[str, bool]:
        """广播消息"""
        results = {}
        
        for unit_id in target_units:
            # 创建消息副本
            broadcast_msg = MilitaryMessage(
                message_type=message.message_type,
                priority=message.priority,
                sender_id=message.sender_id,
                sender_name=message.sender_name,
                recipient_id=unit_id,
                recipient_name=self.units.get(unit_id, MilitaryUnit(id=unit_id, name="Unknown", unit_type=UnitType.COMBAT_UNIT)).name,
                subject=message.subject,
                content=message.content.copy(),
                security_level=message.security_level,
                acknowledgment_required=message.acknowledgment_required,
                expires_at=message.expires_at
            )
            
            results[unit_id] = self.send_message(broadcast_msg, session_token)
        
        return results
    
    def create_command_message(self, sender_id: str, recipient_id: str, 
                             command: str, parameters: Dict[str, Any] = None,
                             priority: MessagePriority = MessagePriority.IMMEDIATE) -> MilitaryMessage:
        """创建指令消息"""
        return MilitaryMessage(
            message_type=MessageType.COMMAND,
            priority=priority,
            sender_id=sender_id,
            sender_name=self.units.get(sender_id, MilitaryUnit(id=sender_id, name="Unknown", unit_type=UnitType.COMMAND_POST)).name,
            recipient_id=recipient_id,
            recipient_name=self.units.get(recipient_id, MilitaryUnit(id=recipient_id, name="Unknown", unit_type=UnitType.COMBAT_UNIT)).name,
            subject=f"指令: {command}",
            content={
                "command": command,
                "parameters": parameters or {},
                "execution_time": datetime.now().isoformat()
            },
            security_level=SecurityLevel.CONFIDENTIAL,
            acknowledgment_required=True,
            expires_at=datetime.now() + timedelta(hours=1)
        )
    
    def create_status_report(self, sender_id: str, recipient_id: str,
                           status_data: Dict[str, Any]) -> MilitaryMessage:
        """创建状态报告"""
        return MilitaryMessage(
            message_type=MessageType.REPORT,
            priority=MessagePriority.ROUTINE,
            sender_id=sender_id,
            sender_name=self.units.get(sender_id, MilitaryUnit(id=sender_id, name="Unknown", unit_type=UnitType.COMBAT_UNIT)).name,
            recipient_id=recipient_id,
            recipient_name=self.units.get(recipient_id, MilitaryUnit(id=recipient_id, name="Unknown", unit_type=UnitType.COMMAND_POST)).name,
            subject="状态报告",
            content={
                "status": status_data,
                "report_time": datetime.now().isoformat()
            },
            security_level=SecurityLevel.SECRET
        )
    
    def register_message_handler(self, message_type: MessageType, handler: Callable) -> None:
        """注册消息处理器"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        
        self.message_handlers[message_type].append(handler)
        self.logger.info(f"注册消息处理器: {message_type.value}")
    
    def get_unit_status(self, unit_id: str) -> Optional[Dict[str, Any]]:
        """获取单位状态"""
        if unit_id not in self.units:
            return None
        
        unit = self.units[unit_id]
        pending_messages = len(self.message_queues.get(unit_id, []))
        
        return {
            "id": unit.id,
            "name": unit.name,
            "type": unit.unit_type.value,
            "commander": unit.commander,
            "location": unit.location,
            "status": unit.status,
            "is_online": unit.is_online(),
            "last_contact": unit.last_contact.isoformat(),
            "pending_messages": pending_messages,
            "capabilities": unit.capabilities,
            "security_clearance": unit.security_clearance.value
        }
    
    def get_communication_statistics(self) -> Dict[str, Any]:
        """获取通信统计"""
        stats = self.communication_stats.copy()
        
        # 计算成功率
        total = stats["total_messages"]
        if total > 0:
            stats["success_rate"] = stats["delivered_messages"] / total
            stats["failure_rate"] = stats["failed_messages"] / total
        else:
            stats["success_rate"] = 0.0
            stats["failure_rate"] = 0.0
        
        # 添加实时统计
        stats["active_units"] = len([u for u in self.units.values() if u.is_online()])
        stats["total_units"] = len(self.units)
        stats["message_queues"] = {uid: len(queue) for uid, queue in self.message_queues.items()}
        
        return stats
    
    def _check_security_clearance(self, unit: MilitaryUnit, message: MilitaryMessage) -> bool:
        """检查安全等级权限"""
        unit_level = list(SecurityLevel).index(unit.security_clearance)
        message_level = list(SecurityLevel).index(message.security_level)
        
        return unit_level >= message_level
    
    def _trigger_message_handlers(self, message: MilitaryMessage) -> None:
        """触发消息处理器"""
        handlers = self.message_handlers.get(message.message_type, [])
        
        for handler in handlers:
            try:
                handler(message)
            except Exception as e:
                self.logger.error(f"消息处理器执行失败: {e}")
    
    def start_background_tasks(self) -> None:
        """启动后台任务（需要在事件循环中调用）"""
        try:
            # 消息清理任务
            cleanup_task = asyncio.create_task(self._cleanup_expired_messages())
            self.background_tasks.append(cleanup_task)

            # 统计更新任务
            stats_task = asyncio.create_task(self._update_statistics())
            self.background_tasks.append(stats_task)
        except RuntimeError:
            # 如果没有运行的事件循环，跳过异步任务
            self.logger.info("没有运行的事件循环，跳过后台任务启动")
    
    def cleanup_expired_messages(self) -> None:
        """清理过期消息（同步版本）"""
        try:
            expired_messages = []

            for msg_id, message in self.messages.items():
                if message.is_expired():
                    expired_messages.append(msg_id)

            for msg_id in expired_messages:
                message = self.messages[msg_id]
                message.delivery_status = "expired"

                # 从队列中移除
                for unit_id, queue in self.message_queues.items():
                    if msg_id in queue:
                        queue.remove(msg_id)
                        break

                self.logger.info(f"清理过期消息: {msg_id}")

        except Exception as e:
            self.logger.error(f"消息清理异常: {e}")

    async def _cleanup_expired_messages(self) -> None:
        """清理过期消息（异步版本）"""
        while self.running:
            try:
                self.cleanup_expired_messages()
                await asyncio.sleep(60)  # 每分钟清理一次

            except Exception as e:
                self.logger.error(f"消息清理任务异常: {e}")
                await asyncio.sleep(10)
    
    def update_statistics(self) -> None:
        """更新统计信息（同步版本）"""
        try:
            # 更新待处理消息数量
            pending_count = sum(len(queue) for queue in self.message_queues.values())
            self.communication_stats["pending_messages"] = pending_count

        except Exception as e:
            self.logger.error(f"统计更新异常: {e}")

    async def _update_statistics(self) -> None:
        """更新统计信息（异步版本）"""
        while self.running:
            try:
                self.update_statistics()
                await asyncio.sleep(30)  # 每30秒更新一次

            except Exception as e:
                self.logger.error(f"统计更新任务异常: {e}")
                await asyncio.sleep(10)
    
    def shutdown(self) -> None:
        """关闭通信系统"""
        self.running = False
        
        # 取消后台任务
        for task in self.background_tasks:
            task.cancel()
        
        self.logger.info("军事通信系统已关闭")
