"""
人机能力分析引擎

分析人与机器在不同任务中的能力优势和适用性
基于任务特征评估人机分工的最优方案
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging
import numpy as np

from ..models.task_models import Task, ExecutorType


class CapabilityType(Enum):
    """能力类型枚举"""
    COGNITIVE = "cognitive"          # 认知能力
    PHYSICAL = "physical"            # 物理能力  
    PROCESSING = "processing"        # 处理能力
    DECISION = "decision"            # 决策能力
    CREATIVE = "creative"            # 创造能力
    ADAPTIVE = "adaptive"            # 适应能力


@dataclass
class HumanCapability:
    """人的能力模型"""
    cognitive_load_capacity: float = 0.7      # 认知负荷容量 (0-1)
    decision_flexibility: float = 0.8         # 决策灵活性 (0-1)
    creative_thinking: float = 0.9            # 创造性思维 (0-1)
    experience_learning: float = 0.8          # 经验学习能力 (0-1)
    stress_tolerance: float = 0.6             # 压力承受能力 (0-1)
    intuitive_judgment: float = 0.7           # 直觉判断能力 (0-1)
    pattern_recognition: float = 0.6          # 模式识别能力 (0-1)
    communication_ability: float = 0.9        # 沟通协调能力 (0-1)
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'cognitive_load_capacity': self.cognitive_load_capacity,
            'decision_flexibility': self.decision_flexibility,
            'creative_thinking': self.creative_thinking,
            'experience_learning': self.experience_learning,
            'stress_tolerance': self.stress_tolerance,
            'intuitive_judgment': self.intuitive_judgment,
            'pattern_recognition': self.pattern_recognition,
            'communication_ability': self.communication_ability
        }


@dataclass
class MachineCapability:
    """机器能力模型"""
    processing_speed: float = 0.95            # 处理速度 (0-1)
    computational_accuracy: float = 0.99      # 计算精确度 (0-1)
    data_storage_capacity: float = 0.98       # 数据存储容量 (0-1)
    continuous_operation: float = 0.95        # 持续操作能力 (0-1)
    pattern_recognition: float = 0.85         # 模式识别能力 (0-1)
    rule_execution: float = 0.99              # 规则执行能力 (0-1)
    parallel_processing: float = 0.9          # 并行处理能力 (0-1)
    consistency: float = 0.99                 # 一致性 (0-1)
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'processing_speed': self.processing_speed,
            'computational_accuracy': self.computational_accuracy,
            'data_storage_capacity': self.data_storage_capacity,
            'continuous_operation': self.continuous_operation,
            'pattern_recognition': self.pattern_recognition,
            'rule_execution': self.rule_execution,
            'parallel_processing': self.parallel_processing,
            'consistency': self.consistency
        }


class HumanMachineCapabilityAnalyzer:
    """人机能力分析引擎"""
    
    def __init__(self):
        """初始化分析引擎"""
        self.logger = logging.getLogger(__name__)
        self.human_capability_model = HumanCapability()
        self.machine_capability_model = MachineCapability()
        
        # 任务特征与能力需求的映射权重
        self.task_capability_weights = self._init_capability_weights()
    
    def _init_capability_weights(self) -> Dict[str, Dict[CapabilityType, float]]:
        """初始化任务特征与能力需求的映射权重"""
        return {
            'high_complexity': {
                CapabilityType.COGNITIVE: 0.9,
                CapabilityType.CREATIVE: 0.8,
                CapabilityType.DECISION: 0.7,
                CapabilityType.ADAPTIVE: 0.6,
                CapabilityType.PROCESSING: 0.4,
                CapabilityType.PHYSICAL: 0.2
            },
            'high_accuracy': {
                CapabilityType.PROCESSING: 0.95,
                CapabilityType.PHYSICAL: 0.8,
                CapabilityType.COGNITIVE: 0.6,
                CapabilityType.DECISION: 0.5,
                CapabilityType.CREATIVE: 0.2,
                CapabilityType.ADAPTIVE: 0.3
            },
            'time_critical': {
                CapabilityType.PROCESSING: 0.9,
                CapabilityType.PHYSICAL: 0.7,
                CapabilityType.DECISION: 0.6,
                CapabilityType.COGNITIVE: 0.5,
                CapabilityType.CREATIVE: 0.2,
                CapabilityType.ADAPTIVE: 0.4
            },
            'creative_task': {
                CapabilityType.CREATIVE: 0.95,
                CapabilityType.COGNITIVE: 0.8,
                CapabilityType.DECISION: 0.7,
                CapabilityType.ADAPTIVE: 0.6,
                CapabilityType.PROCESSING: 0.3,
                CapabilityType.PHYSICAL: 0.2
            },
            'routine_task': {
                CapabilityType.PROCESSING: 0.9,
                CapabilityType.PHYSICAL: 0.8,
                CapabilityType.COGNITIVE: 0.4,
                CapabilityType.DECISION: 0.3,
                CapabilityType.CREATIVE: 0.1,
                CapabilityType.ADAPTIVE: 0.2
            }
        }
    
    def analyze_task_requirements(self, task: Task) -> Dict[CapabilityType, float]:
        """
        分析任务对各种能力的需求程度
        
        Args:
            task: 待分析的任务对象
            
        Returns:
            Dict[CapabilityType, float]: 各能力类型的需求程度 (0-1)
        """
        requirements = {capability: 0.0 for capability in CapabilityType}
        
        # 基于任务属性分析能力需求
        if hasattr(task.attributes, 'complexity') and task.attributes.complexity > 0.7:
            for cap_type, weight in self.task_capability_weights['high_complexity'].items():
                requirements[cap_type] += weight * task.attributes.complexity
        
        if hasattr(task.attributes, 'real_time_requirement') and task.attributes.real_time_requirement:
            for cap_type, weight in self.task_capability_weights['time_critical'].items():
                requirements[cap_type] += weight * 0.8
        
        # 基于任务描述分析（简化的关键词匹配）
        task_desc = (task.description or "").lower()
        if any(keyword in task_desc for keyword in ['创新', '设计', '策划', '创造']):
            for cap_type, weight in self.task_capability_weights['creative_task'].items():
                requirements[cap_type] += weight * 0.7
        
        if any(keyword in task_desc for keyword in ['计算', '处理', '统计', '分析']):
            for cap_type, weight in self.task_capability_weights['routine_task'].items():
                requirements[cap_type] += weight * 0.6
        
        # 归一化处理
        max_requirement = max(requirements.values()) if requirements.values() else 1.0
        if max_requirement > 0:
            for cap_type in requirements:
                requirements[cap_type] = min(1.0, requirements[cap_type] / max_requirement)
        
        return requirements
    
    def evaluate_human_suitability(self, task: Task) -> float:
        """
        评估人类执行该任务的适合度
        
        Args:
            task: 待评估的任务
            
        Returns:
            float: 人类适合度评分 (0-1)
        """
        task_requirements = self.analyze_task_requirements(task)
        human_capabilities = self.human_capability_model.to_dict()
        
        # 能力匹配度计算
        total_score = 0.0
        total_weight = 0.0
        
        capability_mapping = {
            CapabilityType.COGNITIVE: 'cognitive_load_capacity',
            CapabilityType.DECISION: 'decision_flexibility', 
            CapabilityType.CREATIVE: 'creative_thinking',
            CapabilityType.ADAPTIVE: 'experience_learning',
            CapabilityType.PROCESSING: 'pattern_recognition',
            CapabilityType.PHYSICAL: 'communication_ability'
        }
        
        for cap_type, requirement in task_requirements.items():
            if cap_type in capability_mapping:
                human_capability = human_capabilities[capability_mapping[cap_type]]
                # 使用高斯函数计算匹配度，避免过度惩罚
                match_score = np.exp(-((requirement - human_capability) ** 2) / (2 * 0.2 ** 2))
                total_score += requirement * match_score
                total_weight += requirement
        
        return total_score / total_weight if total_weight > 0 else 0.5
    
    def evaluate_machine_suitability(self, task: Task) -> float:
        """
        评估机器执行该任务的适合度
        
        Args:
            task: 待评估的任务
            
        Returns:
            float: 机器适合度评分 (0-1)
        """
        task_requirements = self.analyze_task_requirements(task)
        machine_capabilities = self.machine_capability_model.to_dict()
        
        # 能力匹配度计算
        total_score = 0.0
        total_weight = 0.0
        
        capability_mapping = {
            CapabilityType.PROCESSING: 'processing_speed',
            CapabilityType.PHYSICAL: 'computational_accuracy',
            CapabilityType.COGNITIVE: 'pattern_recognition',
            CapabilityType.DECISION: 'rule_execution',
            CapabilityType.CREATIVE: 'parallel_processing',  # 机器创造性较低
            CapabilityType.ADAPTIVE: 'consistency'
        }
        
        for cap_type, requirement in task_requirements.items():
            if cap_type in capability_mapping:
                machine_capability = machine_capabilities[capability_mapping[cap_type]]
                # 对于创造性任务，机器能力打折
                if cap_type == CapabilityType.CREATIVE:
                    machine_capability *= 0.3
                elif cap_type == CapabilityType.ADAPTIVE:
                    machine_capability *= 0.4
                
                match_score = np.exp(-((requirement - machine_capability) ** 2) / (2 * 0.2 ** 2))
                total_score += requirement * match_score
                total_weight += requirement
        
        return total_score / total_weight if total_weight > 0 else 0.5
    
    def recommend_allocation(self, task: Task, threshold: float = 0.15) -> Tuple[str, float, Dict]:
        """
        推荐任务分配方案
        
        Args:
            task: 待分配的任务
            threshold: 分配决策阈值
            
        Returns:
            Tuple[str, float, Dict]: (分配类型, 置信度, 详细信息)
        """
        human_score = self.evaluate_human_suitability(task)
        machine_score = self.evaluate_machine_suitability(task)
        
        self.logger.info(f"任务 {task.name} 人机适合度评估: 人类={human_score:.3f}, 机器={machine_score:.3f}")
        
        if human_score > machine_score + threshold:
            return "human", human_score, {
                "reason": "人类在该任务中具有明显优势",
                "advantages": self._get_human_advantages(task),
                "confidence": human_score,
                "human_score": human_score,
                "machine_score": machine_score
            }
        elif machine_score > human_score + threshold:
            return "machine", machine_score, {
                "reason": "机器在该任务中具有明显优势", 
                "advantages": self._get_machine_advantages(task),
                "confidence": machine_score,
                "human_score": human_score,
                "machine_score": machine_score
            }
        else:
            collaboration_score = (human_score + machine_score) / 2 + 0.1  # 协同加成
            return "collaboration", collaboration_score, {
                "reason": "人机协同执行效果最佳",
                "human_role": self._suggest_human_role(task),
                "machine_role": self._suggest_machine_role(task),
                "confidence": collaboration_score,
                "human_score": human_score,
                "machine_score": machine_score
            }
    
    def _get_human_advantages(self, task: Task) -> List[str]:
        """获取人类在该任务中的优势"""
        advantages = []
        task_requirements = self.analyze_task_requirements(task)
        
        if task_requirements.get(CapabilityType.CREATIVE, 0) > 0.6:
            advantages.append("创造性思维和创新能力")
        if task_requirements.get(CapabilityType.DECISION, 0) > 0.6:
            advantages.append("灵活决策和判断能力")
        if task_requirements.get(CapabilityType.ADAPTIVE, 0) > 0.6:
            advantages.append("环境适应和学习能力")
        if task_requirements.get(CapabilityType.COGNITIVE, 0) > 0.6:
            advantages.append("复杂认知和理解能力")
        
        return advantages or ["综合分析和判断能力"]
    
    def _get_machine_advantages(self, task: Task) -> List[str]:
        """获取机器在该任务中的优势"""
        advantages = []
        task_requirements = self.analyze_task_requirements(task)
        
        if task_requirements.get(CapabilityType.PROCESSING, 0) > 0.6:
            advantages.append("高速数据处理能力")
        if task_requirements.get(CapabilityType.PHYSICAL, 0) > 0.6:
            advantages.append("精确计算和执行能力")
        if hasattr(task.attributes, 'real_time_requirement') and task.attributes.real_time_requirement:
            advantages.append("实时响应和持续工作能力")
        
        return advantages or ["稳定可靠的执行能力"]
    
    def _suggest_human_role(self, task: Task) -> str:
        """建议人类在协同中的角色"""
        task_requirements = self.analyze_task_requirements(task)
        
        if task_requirements.get(CapabilityType.DECISION, 0) > 0.6:
            return "决策制定和方案选择"
        elif task_requirements.get(CapabilityType.CREATIVE, 0) > 0.6:
            return "创意设计和策略规划"
        else:
            return "监督控制和异常处理"
    
    def _suggest_machine_role(self, task: Task) -> str:
        """建议机器在协同中的角色"""
        task_requirements = self.analyze_task_requirements(task)
        
        if task_requirements.get(CapabilityType.PROCESSING, 0) > 0.6:
            return "数据处理和计算分析"
        elif task_requirements.get(CapabilityType.PHYSICAL, 0) > 0.6:
            return "精确执行和结果输出"
        else:
            return "信息收集和基础处理"
