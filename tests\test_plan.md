# HMDM系统测试方案

## 1. 测试概述

### 1.1 测试目标
- 验证HMDM系统的功能完整性和正确性
- 确保系统满足需求规格说明书的所有要求
- 验证系统的性能、稳定性和可用性
- 确保系统在各种场景下的鲁棒性

### 1.2 测试范围
- 任务分解模块（HTA和GOMS模型）
- 多目标决策模块（WRDM、TOPSIS、Fuzzy AHP）
- 方案评估模块
- Web界面和API接口
- 命令行工具
- 数据导入导出功能

### 1.3 测试策略
- **单元测试**：测试各个模块的基本功能
- **集成测试**：测试模块间的协作
- **系统测试**：测试完整的业务流程
- **性能测试**：测试系统的性能指标
- **用户验收测试**：验证用户需求的满足程度

### 1.4 测试环境
- **操作系统**：Windows 10/11, Ubuntu 20.04, macOS 12+
- **Python版本**：3.8, 3.9, 3.10, 3.11
- **浏览器**：Chrome 90+, Firefox 88+, Edge 90+
- **硬件配置**：最低2GB RAM，推荐4GB RAM

## 2. 功能测试用例

### 2.1 任务分解模块测试

#### 2.1.1 HTA层次任务分析测试

**测试用例TC-HTA-001：基本任务创建**
- **测试目的**：验证任务创建功能
- **前置条件**：系统正常启动
- **测试步骤**：
  1. 创建HierarchicalTaskAnalyzer实例
  2. 创建根任务（使命任务类型）
  3. 调用create_task_hierarchy方法
- **预期结果**：成功创建任务层次结构，根任务ID正确设置
- **优先级**：高

**测试用例TC-HTA-002：任务分解功能**
- **测试目的**：验证任务分解为子任务的功能
- **前置条件**：已创建根任务
- **测试步骤**：
  1. 定义子任务规格列表
  2. 调用decompose_task方法
  3. 验证子任务属性设置
- **预期结果**：成功创建子任务，父子关系正确建立
- **优先级**：高

**测试用例TC-HTA-003：场景化自动分解**
- **测试目的**：验证按场景自动分解功能
- **前置条件**：已创建根任务
- **测试数据**：态势分析、威胁计算、辅助决策场景
- **测试步骤**：
  1. 调用auto_decompose_by_scenario方法
  2. 验证生成的子任务数量和类型
  3. 检查任务属性的合理性
- **预期结果**：根据场景生成合适的子任务结构
- **优先级**：高

**测试用例TC-HTA-004：任务指标计算**
- **测试目的**：验证任务指标聚合计算功能
- **前置条件**：已创建包含子任务的层次结构
- **测试步骤**：
  1. 调用calculate_task_metrics方法
  2. 验证指标计算逻辑（累加、平均、最大值）
  3. 检查计算结果的合理性
- **预期结果**：正确计算任务的综合指标
- **优先级**：中

**测试用例TC-HTA-005：层次结构验证**
- **测试目的**：验证任务层次结构的完整性检查
- **前置条件**：已创建任务层次结构
- **测试步骤**：
  1. 调用validate_hierarchy方法
  2. 人为制造错误（如不存在的子任务ID）
  3. 再次验证并检查错误报告
- **预期结果**：正确识别层次结构中的错误
- **优先级**：中

#### 2.1.2 GOMS模型测试

**测试用例TC-GOMS-001：操作符管理**
- **测试目的**：验证GOMS操作符的创建和管理
- **测试步骤**：
  1. 检查标准操作符库的完整性
  2. 添加自定义操作符
  3. 验证操作符属性设置
- **预期结果**：操作符正确创建和存储
- **优先级**：高

**测试用例TC-GOMS-002：方法创建和管理**
- **测试目的**：验证GOMS方法的创建功能
- **测试步骤**：
  1. 创建包含多个操作符的方法
  2. 验证方法属性设置
  3. 检查操作符序列的正确性
- **预期结果**：方法正确创建，操作符序列有效
- **优先级**：高

**测试用例TC-GOMS-003：元操作分解**
- **测试目的**：验证任务到元操作的分解功能
- **前置条件**：已创建操作序列类型的任务
- **测试步骤**：
  1. 调用decompose_to_meta_operations方法
  2. 验证生成的元操作类型和数量
  3. 检查元操作的时间参数
- **预期结果**：正确生成元操作序列
- **优先级**：高

**测试用例TC-GOMS-004：性能指标计算**
- **测试目的**：验证GOMS方法的性能计算
- **测试步骤**：
  1. 计算方法执行时间
  2. 计算错误概率
  3. 计算心理负荷
- **预期结果**：性能指标计算正确
- **优先级**：中

### 2.2 多目标决策模块测试

#### 2.2.1 WRDM算法测试

**测试用例TC-WRDM-001：基本决策功能**
- **测试目的**：验证WRDM算法的基本决策功能
- **测试数据**：3个备选方案，3个评估指标
- **测试步骤**：
  1. 构建决策矩阵
  2. 执行WRDM算法
  3. 验证排序结果
- **预期结果**：生成合理的方案排序
- **优先级**：高

**测试用例TC-WRDM-002：理想解计算**
- **测试目的**：验证正理想解和负理想解的计算
- **测试步骤**：
  1. 构建包含效益型和成本型指标的矩阵
  2. 计算理想解
  3. 验证理想解的正确性
- **预期结果**：理想解计算符合指标类型
- **优先级**：高

**测试用例TC-WRDM-003：权重应用**
- **测试目的**：验证指标权重的正确应用
- **测试步骤**：
  1. 设置不同的指标权重
  2. 执行决策算法
  3. 比较权重变化对结果的影响
- **预期结果**：权重变化合理影响决策结果
- **优先级**：中

#### 2.2.2 TOPSIS算法测试

**测试用例TC-TOPSIS-001：TOPSIS决策**
- **测试目的**：验证TOPSIS算法的正确性
- **测试数据**：标准TOPSIS测试数据集
- **测试步骤**：
  1. 执行TOPSIS算法
  2. 计算相对接近度
  3. 验证排序结果
- **预期结果**：TOPSIS结果与理论计算一致
- **优先级**：高

**测试用例TC-TOPSIS-002：距离计算验证**
- **测试目的**：验证到理想解的距离计算
- **测试步骤**：
  1. 手工计算欧几里得距离
  2. 对比系统计算结果
  3. 验证相对接近度公式
- **预期结果**：距离计算准确无误
- **优先级**：中

#### 2.2.3 Fuzzy AHP算法测试

**测试用例TC-FAHP-001：模糊判断矩阵构建**
- **测试目的**：验证模糊判断矩阵的构建
- **测试步骤**：
  1. 构建模糊判断矩阵
  2. 验证矩阵的一致性
  3. 检查模糊数的合理性
- **预期结果**：模糊判断矩阵构建正确
- **优先级**：中

**测试用例TC-FAHP-002：模糊权重计算**
- **测试目的**：验证模糊权重的计算和去模糊化
- **测试步骤**：
  1. 计算模糊权重
  2. 执行去模糊化
  3. 验证权重归一化
- **预期结果**：模糊权重计算正确
- **优先级**：中

### 2.3 方案评估模块测试

#### 2.3.1 评估方案管理测试

**测试用例TC-EVAL-001：默认评估方案创建**
- **测试目的**：验证默认评估方案的创建
- **测试步骤**：
  1. 调用create_default_evaluation_scheme方法
  2. 验证指标数量和类型
  3. 检查权重分配的合理性
- **预期结果**：创建包含7类指标的默认方案
- **优先级**：高

**测试用例TC-EVAL-002：自定义评估方案**
- **测试目的**：验证自定义评估方案的创建
- **测试步骤**：
  1. 创建自定义指标
  2. 设置指标权重
  3. 验证方案的完整性
- **预期结果**：自定义方案创建成功
- **优先级**：中

#### 2.3.2 方案评估测试

**测试用例TC-EVAL-003：单方案评估**
- **测试目的**：验证单个方案的评估功能
- **测试数据**：包含完整属性的备选方案
- **测试步骤**：
  1. 调用evaluate_single_scheme方法
  2. 验证指标值计算
  3. 检查总分计算
- **预期结果**：正确计算方案的各项得分
- **优先级**：高

**测试用例TC-EVAL-004：多方案比较**
- **测试目的**：验证多方案比较功能
- **测试数据**：5个不同类型的备选方案
- **测试步骤**：
  1. 调用compare_schemes方法
  2. 验证排序结果
  3. 检查统计分析结果
- **预期结果**：生成完整的比较报告
- **优先级**：高

**测试用例TC-EVAL-005：最优方案推荐**
- **测试目的**：验证最优方案推荐功能
- **测试步骤**：
  1. 调用recommend_best_scheme方法
  2. 验证推荐结果的合理性
  3. 检查置信度计算
- **预期结果**：推荐合理的最优方案
- **优先级**：高

### 2.4 用户界面测试

#### 2.4.1 Web界面测试

**测试用例TC-WEB-001：页面加载测试**
- **测试目的**：验证Web页面的正常加载
- **测试步骤**：
  1. 启动Web服务
  2. 访问主页面
  3. 检查页面元素完整性
- **预期结果**：页面正常加载，所有元素显示正确
- **优先级**：高

**测试用例TC-WEB-002：任务创建界面**
- **测试目的**：验证任务创建的Web界面
- **测试步骤**：
  1. 选择任务场景
  2. 点击创建任务按钮
  3. 验证任务结构显示
- **预期结果**：任务结构正确显示在界面上
- **优先级**：高

**测试用例TC-WEB-003：方案管理界面**
- **测试目的**：验证备选方案管理界面
- **测试步骤**：
  1. 添加备选方案
  2. 编辑方案属性
  3. 删除方案
- **预期结果**：方案管理功能正常工作
- **优先级**：高

**测试用例TC-WEB-004：评估结果展示**
- **测试目的**：验证评估结果的展示
- **测试步骤**：
  1. 执行方案评估
  2. 查看结果页面
  3. 验证图表和表格显示
- **预期结果**：结果清晰直观地展示
- **优先级**：高

#### 2.4.2 API接口测试

**测试用例TC-API-001：会话状态API**
- **测试目的**：验证会话状态查询API
- **测试步骤**：
  1. 发送GET请求到/api/session/status
  2. 验证响应格式
  3. 检查状态信息的准确性
- **预期结果**：返回正确的会话状态信息
- **优先级**：高

**测试用例TC-API-002：任务创建API**
- **测试目的**：验证任务创建API
- **测试步骤**：
  1. 发送POST请求到/api/task/create
  2. 传入场景参数
  3. 验证返回的任务结构
- **预期结果**：成功创建任务并返回结构数据
- **优先级**：高

**测试用例TC-API-003：方案评估API**
- **测试目的**：验证方案评估API
- **测试步骤**：
  1. 先添加备选方案和评估方案
  2. 发送POST请求到/api/evaluation/run
  3. 验证评估结果
- **预期结果**：返回完整的评估结果
- **优先级**：高

#### 2.4.3 命令行工具测试

**测试用例TC-CLI-001：帮助信息显示**
- **测试目的**：验证命令行帮助信息
- **测试步骤**：
  1. 执行python -m src.cli.main --help
  2. 检查帮助信息的完整性
  3. 验证子命令列表
- **预期结果**：显示完整的帮助信息
- **优先级**：中

**测试用例TC-CLI-002：示例数据创建**
- **测试目的**：验证示例数据创建功能
- **测试步骤**：
  1. 执行create-sample命令
  2. 检查生成的文件
  3. 验证文件内容的正确性
- **预期结果**：成功创建示例数据文件
- **优先级**：高

**测试用例TC-CLI-003：任务创建命令**
- **测试目的**：验证任务创建命令
- **测试步骤**：
  1. 执行create-task命令
  2. 指定场景和输出文件
  3. 验证输出文件内容
- **预期结果**：成功创建任务结构文件
- **优先级**：高

**测试用例TC-CLI-004：方案评估命令**
- **测试目的**：验证方案评估命令
- **测试步骤**：
  1. 准备Excel格式的备选方案文件
  2. 执行evaluate命令
  3. 验证评估结果文件
- **预期结果**：成功完成方案评估
- **优先级**：高

## 3. 性能测试用例

### 3.1 响应时间测试

**测试用例TC-PERF-001：任务分解性能**
- **测试目的**：测试大规模任务分解的性能
- **测试数据**：包含100个子任务的复杂任务结构
- **性能指标**：响应时间 < 5秒
- **测试步骤**：
  1. 创建大规模任务结构
  2. 记录分解时间
  3. 验证内存使用情况
- **预期结果**：在性能指标范围内完成

**测试用例TC-PERF-002：决策算法性能**
- **测试目的**：测试多目标决策算法的性能
- **测试数据**：50个备选方案，20个评估指标
- **性能指标**：响应时间 < 10秒
- **测试步骤**：
  1. 构建大规模决策矩阵
  2. 执行三种决策算法
  3. 记录执行时间和资源使用
- **预期结果**：算法在可接受时间内完成

### 3.2 并发测试

**测试用例TC-PERF-003：Web服务并发**
- **测试目的**：测试Web服务的并发处理能力
- **测试条件**：10个并发用户
- **测试步骤**：
  1. 模拟多用户同时访问
  2. 执行各种操作
  3. 监控服务器性能
- **预期结果**：服务稳定，响应时间合理

### 3.3 内存使用测试

**测试用例TC-PERF-004：内存泄漏测试**
- **测试目的**：检测系统是否存在内存泄漏
- **测试步骤**：
  1. 长时间运行系统
  2. 重复执行核心功能
  3. 监控内存使用变化
- **预期结果**：内存使用稳定，无明显泄漏

## 4. 边界条件和异常测试

### 4.1 输入验证测试

**测试用例TC-BOUND-001：空输入处理**
- **测试目的**：验证空输入的处理
- **测试数据**：空字符串、None值、空列表
- **预期结果**：系统优雅处理，返回适当错误信息

**测试用例TC-BOUND-002：超大输入处理**
- **测试目的**：验证超大输入的处理
- **测试数据**：1000个备选方案，100个指标
- **预期结果**：系统能处理或给出合理的限制提示

**测试用例TC-BOUND-003：无效数据格式**
- **测试目的**：验证无效数据格式的处理
- **测试数据**：损坏的Excel文件、格式错误的JSON
- **预期结果**：系统检测到错误并给出明确提示

### 4.2 错误恢复测试

**测试用例TC-ERROR-001：网络中断恢复**
- **测试目的**：验证网络中断后的恢复能力
- **测试步骤**：
  1. 在操作过程中断开网络
  2. 恢复网络连接
  3. 验证系统状态
- **预期结果**：系统能够恢复正常工作

**测试用例TC-ERROR-002：文件系统错误**
- **测试目的**：验证文件系统错误的处理
- **测试步骤**：
  1. 模拟磁盘空间不足
  2. 模拟文件权限错误
  3. 验证错误处理
- **预期结果**：系统给出明确的错误提示

## 5. 兼容性测试

### 5.1 操作系统兼容性

**测试用例TC-COMPAT-001：Windows兼容性**
- **测试环境**：Windows 10, Windows 11
- **测试内容**：完整功能测试
- **预期结果**：所有功能正常工作

**测试用例TC-COMPAT-002：Linux兼容性**
- **测试环境**：Ubuntu 20.04, CentOS 8
- **测试内容**：完整功能测试
- **预期结果**：所有功能正常工作

### 5.2 浏览器兼容性

**测试用例TC-COMPAT-003：主流浏览器测试**
- **测试环境**：Chrome, Firefox, Edge, Safari
- **测试内容**：Web界面功能测试
- **预期结果**：界面在各浏览器中正常显示和工作

## 6. 安全测试

### 6.1 输入安全测试

**测试用例TC-SEC-001：SQL注入防护**
- **测试目的**：验证SQL注入攻击的防护
- **测试数据**：包含SQL注入代码的输入
- **预期结果**：系统安全处理，不执行恶意代码

**测试用例TC-SEC-002：XSS攻击防护**
- **测试目的**：验证跨站脚本攻击的防护
- **测试数据**：包含JavaScript代码的输入
- **预期结果**：系统过滤或转义恶意脚本

### 6.2 文件上传安全

**测试用例TC-SEC-003：恶意文件上传**
- **测试目的**：验证恶意文件上传的防护
- **测试数据**：可执行文件、超大文件
- **预期结果**：系统拒绝上传并给出提示

## 7. 可用性测试

### 7.1 用户体验测试

**测试用例TC-UX-001：界面友好性**
- **测试目的**：评估界面的用户友好性
- **测试方法**：用户操作观察和反馈收集
- **评估标准**：操作直观、信息清晰、反馈及时

**测试用例TC-UX-002：错误信息可理解性**
- **测试目的**：验证错误信息的可理解性
- **测试步骤**：
  1. 故意触发各种错误
  2. 评估错误信息的清晰度
  3. 验证解决方案的提示
- **预期结果**：错误信息清晰，提供解决建议

## 8. 测试数据管理

### 8.1 测试数据分类
- **基础数据**：标准测试用例数据
- **边界数据**：极值和边界条件数据
- **异常数据**：错误和异常情况数据
- **性能数据**：大规模性能测试数据

### 8.2 测试数据维护
- 定期更新测试数据
- 保持数据的代表性和完整性
- 建立数据版本控制机制
- 确保测试数据的安全性

## 9. 测试执行计划

### 9.1 测试阶段划分
1. **单元测试阶段**：开发过程中持续执行
2. **集成测试阶段**：模块集成后执行
3. **系统测试阶段**：完整系统测试
4. **验收测试阶段**：用户验收测试

### 9.2 测试时间安排
- 单元测试：开发期间持续进行
- 集成测试：2天
- 系统测试：3天
- 性能测试：1天
- 验收测试：2天

### 9.3 测试资源分配
- 测试人员：2-3人
- 测试环境：3套（Windows、Linux、macOS）
- 测试工具：pytest、selenium、locust等

## 10. 测试通过标准

### 10.1 功能测试通过标准
- 所有高优先级测试用例100%通过
- 中优先级测试用例95%以上通过
- 低优先级测试用例90%以上通过

### 10.2 性能测试通过标准
- 响应时间满足性能指标要求
- 系统资源使用在合理范围内
- 并发处理能力满足预期

### 10.3 质量标准
- 代码覆盖率达到70%以上
- 无严重级别的缺陷
- 高级别缺陷修复率100%
- 中级别缺陷修复率95%以上
