{"metadata": {"name": "态势分析场景测试数据", "description": "用于测试态势分析任务分解的标准数据集", "version": "1.0", "created_date": "2024-01-01", "scenario_type": "situation_analysis"}, "root_task": {"name": "态势分析任务", "description": "完整的态势分析任务，包含数据收集、分析和展示", "task_type": "MISSION_TASK", "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.8, "importance": 0.9, "urgency": 0.7, "frequency": 0.6, "duration": 1800, "error_rate": 0.05, "workload": 0.7}}, "expected_subtasks": [{"name": "数据收集", "description": "收集态势分析所需的各类数据", "task_type": "ZZ_TASK", "executor_type": "MACHINE", "level": 1, "attributes": {"complexity": 0.6, "importance": 0.8, "urgency": 0.8, "frequency": 0.9, "duration": 600, "error_rate": 0.03, "workload": 0.4}, "expected_children": [{"name": "传感器数据采集", "description": "从各类传感器采集原始数据", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "通信数据接收", "description": "接收来自通信网络的数据", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "历史数据查询", "description": "查询相关的历史数据", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}]}, {"name": "数据分析", "description": "对收集的数据进行分析处理", "task_type": "ZZ_TASK", "executor_type": "HUMAN_MACHINE", "level": 1, "attributes": {"complexity": 0.9, "importance": 0.9, "urgency": 0.7, "frequency": 0.7, "duration": 900, "error_rate": 0.08, "workload": 0.8}, "expected_children": [{"name": "数据预处理", "description": "清洗和预处理原始数据", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "模式识别", "description": "识别数据中的关键模式", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}, {"name": "趋势分析", "description": "分析态势发展趋势", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN", "level": 2}, {"name": "异常检测", "description": "检测异常情况和威胁", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}]}, {"name": "态势展示", "description": "将分析结果以可视化方式展示", "task_type": "ZZ_TASK", "executor_type": "HUMAN_MACHINE", "level": 1, "attributes": {"complexity": 0.5, "importance": 0.7, "urgency": 0.6, "frequency": 0.8, "duration": 300, "error_rate": 0.02, "workload": 0.5}, "expected_children": [{"name": "态势图生成", "description": "生成态势感知图表", "task_type": "TYPICAL_FUNCTION", "executor_type": "MACHINE", "level": 2}, {"name": "报告编制", "description": "编制态势分析报告", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN", "level": 2}, {"name": "预警信息发布", "description": "发布预警和建议信息", "task_type": "TYPICAL_FUNCTION", "executor_type": "HUMAN_MACHINE", "level": 2}]}], "test_scenarios": [{"name": "标准态势分析", "description": "标准的态势分析流程测试", "input_parameters": {"data_sources": ["sensor", "communication", "history"], "analysis_depth": "standard", "output_format": "visual_report"}, "expected_results": {"subtask_count": 3, "total_duration": 1800, "machine_tasks": 5, "human_tasks": 2, "human_machine_tasks": 3}}, {"name": "快速态势评估", "description": "紧急情况下的快速态势评估", "input_parameters": {"data_sources": ["sensor", "communication"], "analysis_depth": "quick", "output_format": "alert_only"}, "expected_results": {"subtask_count": 2, "total_duration": 900, "urgency_level": "high"}}, {"name": "深度态势分析", "description": "详细的态势分析和预测", "input_parameters": {"data_sources": ["sensor", "communication", "history", "external"], "analysis_depth": "deep", "output_format": "comprehensive_report"}, "expected_results": {"subtask_count": 4, "total_duration": 3600, "complexity_level": "high"}}], "validation_rules": [{"rule": "task_hierarchy_completeness", "description": "验证任务层次结构的完整性", "criteria": ["根任务必须存在", "每个子任务必须有明确的父任务", "任务层次不超过6层", "每个任务必须有执行者类型"]}, {"rule": "attribute_consistency", "description": "验证任务属性的一致性", "criteria": ["所有属性值在0-1范围内", "子任务的重要性不超过父任务", "机器任务的错误率较低", "人机协作任务的复杂度适中"]}, {"rule": "executor_assignment", "description": "验证执行者分配的合理性", "criteria": ["数据收集类任务优先分配给机器", "决策类任务优先分配给人或人机协作", "重复性任务优先分配给机器", "创造性任务优先分配给人"]}], "performance_benchmarks": {"decomposition_time": {"target": "< 2 seconds", "acceptable": "< 5 seconds", "description": "任务分解的响应时间"}, "memory_usage": {"target": "< 100 MB", "acceptable": "< 200 MB", "description": "任务分解过程的内存使用"}, "accuracy": {"target": "> 95%", "acceptable": "> 90%", "description": "任务分解结果的准确性"}}, "error_test_cases": [{"name": "无效场景名称", "input": "invalid_scenario_name", "expected_error": "ValueError", "expected_message": "不支持的场景类型"}, {"name": "空任务名称", "input": "", "expected_error": "ValueError", "expected_message": "任务名称不能为空"}, {"name": "无效任务类型", "input": "INVALID_TASK_TYPE", "expected_error": "ValueError", "expected_message": "无效的任务类型"}]}