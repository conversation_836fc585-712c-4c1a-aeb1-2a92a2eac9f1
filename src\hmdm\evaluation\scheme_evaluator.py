"""
方案评估器

实现多方案的综合评估和最优方案推荐功能
"""

from typing import List, Dict, Optional, Any, Tuple
import numpy as np
from datetime import datetime
import logging

from ..models.evaluation_models import (
    EvaluationScheme, EvaluationResult, IndicatorValue, IndicatorType
)
from ..models.decision_models import Alternative, DecisionMatrix, FuzzyNumber
from ..decision.fuzzy_decision_engine import FuzzyDecisionEngine
from .military_indicators import MilitaryIndicatorSystem


class SchemeEvaluator:
    """方案评估器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.decision_engine = FuzzyDecisionEngine()
    
    def evaluate_single_scheme(self, 
                             alternative: Alternative,
                             evaluation_scheme: EvaluationScheme,
                             data_source: Dict[str, Any]) -> EvaluationResult:
        """评估单个方案"""
        result = EvaluationResult(
            scheme_id=evaluation_scheme.id,
            target_id=alternative.id,
            target_name=alternative.name
        )
        
        # 计算各指标值
        for indicator_id, indicator in evaluation_scheme.indicators.items():
            try:
                # 从数据源获取原始值
                raw_value = self._extract_indicator_value(
                    indicator, alternative, data_source
                )
                
                # 标准化值
                normalized_value = indicator.normalize_value(raw_value)
                
                # 创建指标值对象
                indicator_value = IndicatorValue(
                    indicator_id=indicator_id,
                    raw_value=raw_value,
                    normalized_value=normalized_value,
                    source=f"方案{alternative.name}数据"
                )
                
                result.add_indicator_value(indicator_value)
                
            except Exception as e:
                self.logger.warning(f"计算指标 {indicator.name} 失败: {e}")
                # 使用默认值
                default_value = IndicatorValue(
                    indicator_id=indicator_id,
                    raw_value=0.0,
                    normalized_value=0.0,
                    confidence=0.0,
                    source="默认值"
                )
                result.add_indicator_value(default_value)
        
        # 计算综合得分
        result.calculate_total_score(evaluation_scheme)
        result.calculate_type_scores(evaluation_scheme)
        
        return result
    
    def evaluate_multiple_schemes(self,
                                alternatives: List[Alternative],
                                evaluation_scheme: EvaluationScheme,
                                data_sources: Dict[str, Dict[str, Any]]) -> List[EvaluationResult]:
        """评估多个方案"""
        results = []
        
        for alternative in alternatives:
            data_source = data_sources.get(alternative.id, {})
            try:
                result = self.evaluate_single_scheme(
                    alternative, evaluation_scheme, data_source
                )
                results.append(result)
            except Exception as e:
                self.logger.error(f"评估方案 {alternative.name} 失败: {e}")
        
        return results
    
    def _extract_indicator_value(self, 
                               indicator,
                               alternative: Alternative,
                               data_source: Dict[str, Any]) -> float:
        """从数据源提取指标值"""
        # 优先从数据源获取
        if indicator.name in data_source:
            return float(data_source[indicator.name])
        
        if indicator.id in data_source:
            return float(data_source[indicator.id])
        
        # 从方案属性获取
        if indicator.name in alternative.attributes:
            return float(alternative.attributes[indicator.name])
        
        # 从模糊值获取
        if indicator.id in alternative.fuzzy_values:
            return alternative.fuzzy_values[indicator.id].defuzzify()
        
        # 根据指标类型使用默认计算方法
        return self._calculate_default_indicator_value(indicator, alternative)
    
    def _calculate_default_indicator_value(self, indicator, alternative: Alternative) -> float:
        """计算默认指标值"""
        if indicator.indicator_type == IndicatorType.WORKLOAD:
            return self._calculate_workload_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.EFFICIENCY:
            return self._calculate_efficiency_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.RELIABILITY:
            return self._calculate_reliability_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.USABILITY:
            return self._calculate_usability_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.SAFETY:
            return self._calculate_safety_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.COST:
            return self._calculate_cost_indicator(alternative)
        elif indicator.indicator_type == IndicatorType.PERFORMANCE:
            return self._calculate_performance_indicator(alternative)
        else:
            return 0.5  # 默认中等值
    
    def _calculate_workload_indicator(self, alternative: Alternative) -> float:
        """计算负荷指标"""
        # 基于方案属性计算负荷
        workload_factors = []
        
        # 从属性中提取相关因子
        if "complexity" in alternative.attributes:
            workload_factors.append(alternative.attributes["complexity"])
        
        if "task_count" in alternative.attributes:
            task_count = alternative.attributes["task_count"]
            workload_factors.append(min(1.0, task_count / 10.0))  # 假设10个任务为满负荷
        
        if "duration" in alternative.attributes:
            duration = alternative.attributes["duration"]
            workload_factors.append(min(1.0, duration / 3600.0))  # 假设1小时为满负荷
        
        return np.mean(workload_factors) if workload_factors else 0.5
    
    def _calculate_efficiency_indicator(self, alternative: Alternative) -> float:
        """计算效率指标"""
        efficiency_factors = []
        
        if "processing_speed" in alternative.attributes:
            efficiency_factors.append(alternative.attributes["processing_speed"])
        
        if "automation_level" in alternative.attributes:
            efficiency_factors.append(alternative.attributes["automation_level"])
        
        if "resource_utilization" in alternative.attributes:
            efficiency_factors.append(alternative.attributes["resource_utilization"])
        
        return np.mean(efficiency_factors) if efficiency_factors else 0.5
    
    def _calculate_reliability_indicator(self, alternative: Alternative) -> float:
        """计算可靠性指标"""
        reliability_factors = []
        
        if "error_rate" in alternative.attributes:
            error_rate = alternative.attributes["error_rate"]
            reliability_factors.append(1.0 - error_rate)  # 错误率越低，可靠性越高
        
        if "system_stability" in alternative.attributes:
            reliability_factors.append(alternative.attributes["system_stability"])
        
        if "fault_tolerance" in alternative.attributes:
            reliability_factors.append(alternative.attributes["fault_tolerance"])
        
        return np.mean(reliability_factors) if reliability_factors else 0.5
    
    def _calculate_usability_indicator(self, alternative: Alternative) -> float:
        """计算可用性指标"""
        usability_factors = []
        
        if "user_satisfaction" in alternative.attributes:
            usability_factors.append(alternative.attributes["user_satisfaction"])
        
        if "learning_curve" in alternative.attributes:
            learning_curve = alternative.attributes["learning_curve"]
            usability_factors.append(1.0 - learning_curve)  # 学习曲线越平缓，可用性越高
        
        if "interface_complexity" in alternative.attributes:
            interface_complexity = alternative.attributes["interface_complexity"]
            usability_factors.append(1.0 - interface_complexity)
        
        return np.mean(usability_factors) if usability_factors else 0.5
    
    def _calculate_safety_indicator(self, alternative: Alternative) -> float:
        """计算安全性指标"""
        safety_factors = []
        
        if "risk_level" in alternative.attributes:
            risk_level = alternative.attributes["risk_level"]
            safety_factors.append(1.0 - risk_level)  # 风险越低，安全性越高
        
        if "security_measures" in alternative.attributes:
            safety_factors.append(alternative.attributes["security_measures"])
        
        if "compliance_level" in alternative.attributes:
            safety_factors.append(alternative.attributes["compliance_level"])
        
        return np.mean(safety_factors) if safety_factors else 0.5
    
    def _calculate_cost_indicator(self, alternative: Alternative) -> float:
        """计算成本指标"""
        cost_factors = []
        
        if "development_cost" in alternative.attributes:
            dev_cost = alternative.attributes["development_cost"]
            # 假设最大成本为100万，标准化到[0,1]
            cost_factors.append(min(1.0, dev_cost / 1000000.0))
        
        if "operational_cost" in alternative.attributes:
            op_cost = alternative.attributes["operational_cost"]
            # 假设最大运营成本为50万/年
            cost_factors.append(min(1.0, op_cost / 500000.0))
        
        if "maintenance_cost" in alternative.attributes:
            maint_cost = alternative.attributes["maintenance_cost"]
            # 假设最大维护成本为20万/年
            cost_factors.append(min(1.0, maint_cost / 200000.0))
        
        return np.mean(cost_factors) if cost_factors else 0.5
    
    def _calculate_performance_indicator(self, alternative: Alternative) -> float:
        """计算性能指标"""
        performance_factors = []
        
        if "response_time" in alternative.attributes:
            response_time = alternative.attributes["response_time"]
            # 响应时间越短，性能越好（假设最大可接受响应时间为10秒）
            performance_factors.append(max(0.0, 1.0 - response_time / 10.0))
        
        if "throughput" in alternative.attributes:
            throughput = alternative.attributes["throughput"]
            # 假设最大吞吐量为1000
            performance_factors.append(min(1.0, throughput / 1000.0))
        
        if "accuracy" in alternative.attributes:
            performance_factors.append(alternative.attributes["accuracy"])
        
        return np.mean(performance_factors) if performance_factors else 0.5
    
    def recommend_best_scheme(self,
                            alternatives: List[Alternative],
                            evaluation_scheme: EvaluationScheme,
                            data_sources: Dict[str, Dict[str, Any]],
                            method: str = "WRDM") -> Tuple[Alternative, EvaluationResult, Dict[str, Any]]:
        """推荐最佳方案"""
        # 评估所有方案
        evaluation_results = self.evaluate_multiple_schemes(
            alternatives, evaluation_scheme, data_sources
        )
        
        if not evaluation_results:
            raise ValueError("没有可评估的方案")
        
        # 构建决策矩阵
        decision_matrix = DecisionMatrix(
            name="方案评估决策矩阵",
            evaluation_scheme=evaluation_scheme
        )
        
        # 添加备选方案和模糊值
        for i, alternative in enumerate(alternatives):
            # 为每个指标创建模糊值
            for indicator_id in evaluation_scheme.indicators.keys():
                if i < len(evaluation_results):
                    result = evaluation_results[i]
                    if indicator_id in result.indicator_values:
                        value = result.indicator_values[indicator_id].normalized_value
                        # 创建三角模糊数
                        fuzzy_value = FuzzyNumber(parameters=[
                            max(0.0, value - 0.1),
                            value,
                            min(1.0, value + 0.1)
                        ])
                        alternative.add_fuzzy_value(indicator_id, fuzzy_value)
            
            decision_matrix.add_alternative(alternative)
        
        # 执行决策
        if method.upper() == "WRDM":
            decision_result = self.decision_engine.weighted_relative_deviation_method(decision_matrix)
        elif method.upper() == "TOPSIS":
            decision_result = self.decision_engine.topsis_method(decision_matrix)
        elif method.upper() == "FUZZY_AHP":
            decision_result = self.decision_engine.fuzzy_ahp_method(decision_matrix)
        else:
            raise ValueError(f"不支持的决策方法: {method}")
        
        # 获取推荐方案
        recommended_alt_id = decision_result.recommended_alternative_id
        recommended_alternative = None
        recommended_evaluation = None
        
        for alternative in alternatives:
            if alternative.id == recommended_alt_id:
                recommended_alternative = alternative
                break
        
        for result in evaluation_results:
            if result.target_id == recommended_alt_id:
                recommended_evaluation = result
                break
        
        if not recommended_alternative or not recommended_evaluation:
            raise ValueError("无法找到推荐方案的详细信息")
        
        # 生成推荐报告
        recommendation_report = {
            "recommended_alternative": recommended_alternative.to_dict(),
            "evaluation_result": recommended_evaluation.to_dict(),
            "decision_result": decision_result.to_dict(),
            "all_evaluations": [result.to_dict() for result in evaluation_results],
            "decision_matrix": decision_matrix.to_dict(),
            "recommendation_confidence": decision_result.confidence,
            "method_used": method,
            "timestamp": datetime.now().isoformat()
        }
        
        return recommended_alternative, recommended_evaluation, recommendation_report
    
    def compare_schemes(self,
                       alternatives: List[Alternative],
                       evaluation_scheme: EvaluationScheme,
                       data_sources: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """比较多个方案"""
        # 评估所有方案
        evaluation_results = self.evaluate_multiple_schemes(
            alternatives, evaluation_scheme, data_sources
        )
        
        if not evaluation_results:
            return {}
        
        # 构建比较报告
        comparison_report = {
            "summary": {
                "total_alternatives": len(alternatives),
                "evaluation_scheme": evaluation_scheme.name,
                "evaluation_time": datetime.now().isoformat()
            },
            "detailed_results": [],
            "ranking_by_total_score": [],
            "ranking_by_indicator_type": {},
            "statistical_analysis": {}
        }
        
        # 详细结果
        for i, result in enumerate(evaluation_results):
            alternative = alternatives[i]
            comparison_report["detailed_results"].append({
                "alternative": alternative.to_dict(),
                "evaluation": result.to_dict()
            })
        
        # 按总分排序
        sorted_results = sorted(evaluation_results, key=lambda x: x.total_score, reverse=True)
        comparison_report["ranking_by_total_score"] = [
            {
                "rank": i + 1,
                "alternative_id": result.target_id,
                "alternative_name": result.target_name,
                "total_score": result.total_score,
                "normalized_score": result.normalized_score
            }
            for i, result in enumerate(sorted_results)
        ]
        
        # 按指标类型排序
        for indicator_type in IndicatorType:
            type_rankings = []
            for result in evaluation_results:
                if indicator_type.value in result.type_scores:
                    type_rankings.append({
                        "alternative_id": result.target_id,
                        "alternative_name": result.target_name,
                        "score": result.type_scores[indicator_type.value]
                    })
            
            type_rankings.sort(key=lambda x: x["score"], reverse=True)
            for i, item in enumerate(type_rankings):
                item["rank"] = i + 1
            
            comparison_report["ranking_by_indicator_type"][indicator_type.value] = type_rankings
        
        # 统计分析
        total_scores = [result.total_score for result in evaluation_results]
        comparison_report["statistical_analysis"] = {
            "mean_score": np.mean(total_scores),
            "std_score": np.std(total_scores),
            "min_score": np.min(total_scores),
            "max_score": np.max(total_scores),
            "score_range": np.max(total_scores) - np.min(total_scores)
        }
        
        return comparison_report

    def create_default_evaluation_scheme(self) -> EvaluationScheme:
        """创建默认评估方案"""
        from ..models.evaluation_models import IndicatorDefinition, AggregationMethod

        scheme = EvaluationScheme(
            name="默认人机功能分配评估方案",
            description="基于负荷、效率、可靠性等维度的综合评估方案",
            aggregation_method=AggregationMethod.WEIGHTED_SUM
        )

        # 定义默认指标
        indicators = [
            # 负荷指标
            IndicatorDefinition(
                name="心理负荷",
                description="操作者的心理工作负荷",
                indicator_type=IndicatorType.WORKLOAD,
                min_value=0.0, max_value=1.0,
                is_benefit=False,  # 负荷越低越好
                unit="标准化值"
            ),
            IndicatorDefinition(
                name="物理负荷",
                description="操作者的物理工作负荷",
                indicator_type=IndicatorType.WORKLOAD,
                min_value=0.0, max_value=1.0,
                is_benefit=False,
                unit="标准化值"
            ),

            # 效率指标
            IndicatorDefinition(
                name="任务完成时间",
                description="完成任务所需的时间",
                indicator_type=IndicatorType.EFFICIENCY,
                min_value=0.0, max_value=3600.0,
                is_benefit=False,  # 时间越短越好
                unit="秒"
            ),
            IndicatorDefinition(
                name="处理速度",
                description="信息处理速度",
                indicator_type=IndicatorType.EFFICIENCY,
                min_value=0.0, max_value=1.0,
                is_benefit=True,
                unit="标准化值"
            ),

            # 可靠性指标
            IndicatorDefinition(
                name="错误率",
                description="操作错误发生率",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0, max_value=1.0,
                is_benefit=False,  # 错误率越低越好
                unit="比例"
            ),
            IndicatorDefinition(
                name="系统稳定性",
                description="系统运行稳定性",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0, max_value=1.0,
                is_benefit=True,
                unit="标准化值"
            ),

            # 可用性指标
            IndicatorDefinition(
                name="用户满意度",
                description="用户对系统的满意程度",
                indicator_type=IndicatorType.USABILITY,
                min_value=0.0, max_value=1.0,
                is_benefit=True,
                unit="标准化值"
            ),

            # 安全性指标
            IndicatorDefinition(
                name="安全风险等级",
                description="系统安全风险等级",
                indicator_type=IndicatorType.SAFETY,
                min_value=0.0, max_value=1.0,
                is_benefit=False,  # 风险越低越好
                unit="标准化值"
            ),

            # 成本指标
            IndicatorDefinition(
                name="开发成本",
                description="系统开发成本",
                indicator_type=IndicatorType.COST,
                min_value=0.0, max_value=1000000.0,
                is_benefit=False,  # 成本越低越好
                unit="元"
            ),

            # 性能指标
            IndicatorDefinition(
                name="响应时间",
                description="系统响应时间",
                indicator_type=IndicatorType.PERFORMANCE,
                min_value=0.0, max_value=10.0,
                is_benefit=False,  # 响应时间越短越好
                unit="秒"
            )
        ]

        # 添加指标和权重
        default_weights = {
            IndicatorType.WORKLOAD: 0.20,
            IndicatorType.EFFICIENCY: 0.25,
            IndicatorType.RELIABILITY: 0.20,
            IndicatorType.USABILITY: 0.15,
            IndicatorType.SAFETY: 0.10,
            IndicatorType.COST: 0.05,
            IndicatorType.PERFORMANCE: 0.05
        }

        for indicator in indicators:
            weight = default_weights.get(indicator.indicator_type, 0.1)
            scheme.add_indicator(indicator, weight)

        # 归一化权重
        scheme.normalize_weights()

        return scheme

    def create_military_evaluation_scheme(self,
                                         scheme_name: str = "军事专业评估方案",
                                         focus_area: str = "综合") -> EvaluationScheme:
        """创建军事专业评估方案"""
        scheme = EvaluationScheme(
            id=f"military_{focus_area.lower()}",
            name=scheme_name,
            description=f"针对{focus_area}的军事专业评估方案"
        )

        military_indicators = MilitaryIndicatorSystem.get_military_indicators()

        # 根据关注领域选择指标
        if focus_area == "作战效能":
            selected_indicators = military_indicators["作战效能指标"]
            for indicator in selected_indicators:
                weight = indicator.military_attributes.get('weight', 1.0) if hasattr(indicator, 'military_attributes') else 1.0
                scheme.add_indicator(indicator, weight)
        elif focus_area == "指挥控制":
            selected_indicators = military_indicators["指挥控制指标"]
            for indicator in selected_indicators:
                weight = indicator.military_attributes.get('weight', 1.0) if hasattr(indicator, 'military_attributes') else 1.0
                scheme.add_indicator(indicator, weight)
        elif focus_area == "综合":
            # 综合评估，包含所有类别的代表性指标
            focus_weights = {
                "作战效能指标": 0.30,
                "指挥控制指标": 0.25,
                "保障支撑指标": 0.20,
                "信息系统指标": 0.15,
                "人员素质指标": 0.05,
                "装备技术指标": 0.03,
                "环境适应指标": 0.02
            }

            for category, category_weight in focus_weights.items():
                if category in military_indicators:
                    indicators = military_indicators[category]
                    # 选择每个类别中权重最高的指标
                    def get_indicator_weight(indicator):
                        if hasattr(indicator, 'military_attributes') and isinstance(indicator.military_attributes, dict):
                            return indicator.military_attributes.get('weight', 1.0)
                        return 1.0

                    top_indicators = sorted(indicators, key=get_indicator_weight, reverse=True)[:2]
                    for indicator in top_indicators:
                        indicator_weight = get_indicator_weight(indicator)
                        adjusted_weight = indicator_weight * category_weight
                        scheme.add_indicator(indicator, adjusted_weight)

        # 归一化权重
        scheme.normalize_weights()

        return scheme

    def evaluate_with_military_indicators(self,
                                        alternatives: List[Alternative],
                                        focus_area: str = "综合",
                                        data_source: Optional[Dict[str, Any]] = None) -> List[EvaluationResult]:
        """使用军事指标评估方案"""
        if not alternatives:
            raise ValueError("备选方案列表不能为空")

        # 创建军事评估方案
        military_scheme = self.create_military_evaluation_scheme(focus_area=focus_area)

        # 评估所有方案
        results = []
        for alternative in alternatives:
            result = self.evaluate_single_scheme(
                alternative=alternative,
                evaluation_scheme=military_scheme,
                data_source=data_source or {}
            )
            results.append(result)

        # 按总分排序
        results.sort(key=lambda x: x.total_score, reverse=True)

        return results

    def get_military_indicator_categories(self) -> List[str]:
        """获取军事指标类别列表"""
        return list(MilitaryIndicatorSystem.get_military_indicators().keys())

    def analyze_military_performance_gap(self,
                                       current_alternative: Alternative,
                                       target_scores: Dict[str, float],
                                       focus_area: str = "综合") -> Dict[str, Any]:
        """分析军事性能差距"""
        # 创建军事评估方案
        military_scheme = self.create_military_evaluation_scheme(focus_area=focus_area)

        # 评估当前方案
        current_result = self.evaluate_single_scheme(
            alternative=current_alternative,
            evaluation_scheme=military_scheme,
            data_source={}
        )

        # 分析差距
        gaps = {}
        improvement_priorities = []

        for indicator_id, target_score in target_scores.items():
            current_score = 0.0
            if indicator_id in current_result.indicator_values:
                indicator_value = current_result.indicator_values[indicator_id]
                current_score = indicator_value.normalized_value

            gap = target_score - current_score
            gaps[indicator_id] = {
                "current_score": current_score,
                "target_score": target_score,
                "gap": gap,
                "improvement_needed": gap > 0.1  # 10%以上差距需要改进
            }

            if gap > 0.1:
                improvement_priorities.append({
                    "indicator_id": indicator_id,
                    "gap": gap,
                    "priority": "高" if gap > 0.3 else "中" if gap > 0.2 else "低"
                })

        # 按差距大小排序
        improvement_priorities.sort(key=lambda x: x["gap"], reverse=True)

        return {
            "current_total_score": current_result.total_score,
            "target_total_score": sum(target_scores.values()) / len(target_scores),
            "overall_gap": sum(target_scores.values()) / len(target_scores) - current_result.total_score,
            "indicator_gaps": gaps,
            "improvement_priorities": improvement_priorities,
            "analysis_time": datetime.now().isoformat()
        }
