"""
增强安全管理系统测试

测试增强安全管理器的各项功能
"""

import pytest
import tempfile
import os
import json
from datetime import datetime, timedelta
from src.hmdm.security.enhanced_security import (
    EnhancedSecurityManager, User, AuditEvent, 
    AuthenticationMethod, PermissionType, AuditEventType
)
from src.hmdm.security.military_security import SecurityLevel


class TestEnhancedSecurityManager:
    """增强安全管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 配置参数
        config = {
            'session_timeout': 1800,  # 30分钟
            'max_login_attempts': 3,
            'password_min_length': 8,
            'enable_encryption': True,
            'audit_file': os.path.join(self.temp_dir, 'audit.log'),
            'encryption_key_file': os.path.join(self.temp_dir, 'encryption.key'),
            'jwt_secret': 'test_secret_key',
            'default_admin_username': 'test_admin',
            'default_admin_password': 'TestAdmin123!'
        }
        
        self.security_manager = EnhancedSecurityManager(config)
    
    def teardown_method(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_security_manager_initialization(self):
        """测试安全管理器初始化"""
        assert self.security_manager is not None
        assert self.security_manager.enable_encryption is True
        assert self.security_manager.session_timeout == 1800
        assert self.security_manager.max_login_attempts == 3
        assert len(self.security_manager.users) == 1  # 默认管理员用户
    
    def test_create_user(self):
        """测试创建用户"""
        # 创建普通用户
        user = self.security_manager.create_user(
            username='testuser',
            password='TestPass123!',
            security_level=SecurityLevel.SECRET,
            permissions=[PermissionType.READ, PermissionType.WRITE],
            roles=['operator']
        )
        
        assert user is not None
        assert user.username == 'testuser'
        assert user.security_level == SecurityLevel.SECRET
        assert PermissionType.READ in user.permissions
        assert PermissionType.WRITE in user.permissions
        assert 'operator' in user.roles
        assert user.is_active is True
        assert user.is_locked is False
    
    def test_create_duplicate_user(self):
        """测试创建重复用户"""
        # 创建第一个用户
        self.security_manager.create_user(
            username='duplicate_user',
            password='TestPass123!'
        )
        
        # 尝试创建同名用户应该失败
        with pytest.raises(ValueError, match="用户名 duplicate_user 已存在"):
            self.security_manager.create_user(
                username='duplicate_user',
                password='AnotherPass123!'
            )
    
    def test_weak_password_rejection(self):
        """测试弱密码拒绝"""
        # 测试过短密码
        with pytest.raises(ValueError, match="密码不符合安全要求"):
            self.security_manager.create_user(
                username='weakpass_user',
                password='123'
            )
        
        # 测试简单密码
        with pytest.raises(ValueError, match="密码不符合安全要求"):
            self.security_manager.create_user(
                username='simple_user',
                password='password'
            )
    
    def test_user_authentication_success(self):
        """测试用户认证成功"""
        # 创建测试用户
        self.security_manager.create_user(
            username='auth_user',
            password='AuthPass123!',
            security_level=SecurityLevel.CONFIDENTIAL
        )
        
        # 认证用户
        token = self.security_manager.authenticate_user(
            username='auth_user',
            password='AuthPass123!',
            ip_address='127.0.0.1',
            user_agent='test-agent'
        )
        
        assert token is not None
        assert len(token) > 0
        
        # 验证会话
        user = self.security_manager.validate_session(token)
        assert user is not None
        assert user.username == 'auth_user'
        assert user.last_login is not None
    
    def test_user_authentication_failure(self):
        """测试用户认证失败"""
        # 创建测试用户
        self.security_manager.create_user(
            username='auth_fail_user',
            password='CorrectPass123!'
        )
        
        # 使用错误密码认证
        token = self.security_manager.authenticate_user(
            username='auth_fail_user',
            password='WrongPassword',
            ip_address='127.0.0.1'
        )
        
        assert token is None
        
        # 验证登录尝试次数增加
        user = None
        for u in self.security_manager.users.values():
            if u.username == 'auth_fail_user':
                user = u
                break
        
        assert user is not None
        assert user.login_attempts == 1
    
    def test_account_lockout(self):
        """测试账户锁定"""
        # 创建测试用户
        self.security_manager.create_user(
            username='lockout_user',
            password='LockoutPass123!'
        )
        
        # 多次失败登录
        for i in range(3):
            token = self.security_manager.authenticate_user(
                username='lockout_user',
                password='WrongPassword',
                ip_address='127.0.0.1'
            )
            assert token is None
        
        # 验证账户被锁定
        user = None
        for u in self.security_manager.users.values():
            if u.username == 'lockout_user':
                user = u
                break
        
        assert user is not None
        assert user.is_locked is True
        assert user.login_attempts >= 3
        
        # 即使使用正确密码也无法登录
        token = self.security_manager.authenticate_user(
            username='lockout_user',
            password='LockoutPass123!',
            ip_address='127.0.0.1'
        )
        assert token is None
    
    def test_session_validation(self):
        """测试会话验证"""
        # 创建用户并认证
        self.security_manager.create_user(
            username='session_user',
            password='SessionPass123!'
        )
        
        token = self.security_manager.authenticate_user(
            username='session_user',
            password='SessionPass123!'
        )
        
        # 验证有效会话
        user = self.security_manager.validate_session(token)
        assert user is not None
        assert user.username == 'session_user'
        
        # 验证无效会话
        invalid_user = self.security_manager.validate_session('invalid_token')
        assert invalid_user is None
    
    def test_permission_check(self):
        """测试权限检查"""
        # 创建有权限的用户
        user = self.security_manager.create_user(
            username='perm_user',
            password='PermPass123!',
            security_level=SecurityLevel.SECRET,
            permissions=[PermissionType.READ, PermissionType.WRITE]
        )
        
        # 检查拥有的权限
        assert self.security_manager.check_permission(
            user, 'test_resource', PermissionType.READ, SecurityLevel.INTERNAL
        ) is True
        
        assert self.security_manager.check_permission(
            user, 'test_resource', PermissionType.WRITE, SecurityLevel.INTERNAL
        ) is True
        
        # 检查没有的权限
        assert self.security_manager.check_permission(
            user, 'test_resource', PermissionType.DELETE, SecurityLevel.INTERNAL
        ) is False
        
        # 检查安全等级不足
        assert self.security_manager.check_permission(
            user, 'test_resource', PermissionType.READ, SecurityLevel.TOP_SECRET
        ) is False
    
    def test_admin_permission(self):
        """测试管理员权限"""
        # 创建管理员用户
        admin_user = self.security_manager.create_user(
            username='admin_user',
            password='AdminPass123!',
            security_level=SecurityLevel.TOP_SECRET,
            permissions=[PermissionType.ADMIN]
        )
        
        # 管理员应该有所有权限
        assert self.security_manager.check_permission(
            admin_user, 'test_resource', PermissionType.READ, SecurityLevel.INTERNAL
        ) is True
        
        assert self.security_manager.check_permission(
            admin_user, 'test_resource', PermissionType.DELETE, SecurityLevel.INTERNAL
        ) is True
    
    def test_data_encryption(self):
        """测试数据加密"""
        test_data = "这是需要加密的敏感数据"
        
        # 加密数据
        encrypted_data = self.security_manager.encrypt_data(test_data)
        assert encrypted_data != test_data
        assert len(encrypted_data) > len(test_data)
        
        # 解密数据
        decrypted_data = self.security_manager.decrypt_data(encrypted_data)
        assert decrypted_data == test_data
    
    def test_password_change(self):
        """测试密码修改"""
        # 创建用户
        user = self.security_manager.create_user(
            username='change_pass_user',
            password='OldPass123!'
        )
        
        # 修改密码
        success = self.security_manager.change_password(
            user.user_id,
            'OldPass123!',
            'NewPass123!'
        )
        assert success is True
        
        # 使用新密码登录
        token = self.security_manager.authenticate_user(
            username='change_pass_user',
            password='NewPass123!'
        )
        assert token is not None
        
        # 使用旧密码登录应该失败
        old_token = self.security_manager.authenticate_user(
            username='change_pass_user',
            password='OldPass123!'
        )
        assert old_token is None
    
    def test_user_logout(self):
        """测试用户登出"""
        # 创建用户并登录
        self.security_manager.create_user(
            username='logout_user',
            password='LogoutPass123!'
        )
        
        token = self.security_manager.authenticate_user(
            username='logout_user',
            password='LogoutPass123!'
        )
        
        # 验证会话有效
        user = self.security_manager.validate_session(token)
        assert user is not None
        
        # 登出
        success = self.security_manager.logout_user(token)
        assert success is True
        
        # 验证会话已失效
        user_after_logout = self.security_manager.validate_session(token)
        assert user_after_logout is None
    
    def test_audit_events(self):
        """测试审计事件"""
        # 创建用户（会产生审计事件）
        self.security_manager.create_user(
            username='audit_user',
            password='AuditPass123!'
        )
        
        # 认证用户（会产生审计事件）
        token = self.security_manager.authenticate_user(
            username='audit_user',
            password='AuditPass123!',
            ip_address='*************',
            user_agent='test-browser'
        )
        
        # 获取审计事件
        events = self.security_manager.get_audit_events(limit=10)
        
        assert len(events) >= 2  # 至少有创建用户和登录事件
        
        # 验证登录事件
        login_events = [e for e in events if e.event_type == AuditEventType.LOGIN]
        assert len(login_events) >= 1
        
        login_event = login_events[0]
        assert login_event.username == 'audit_user'
        assert login_event.result == 'success'
        assert login_event.ip_address == '*************'
        assert login_event.user_agent == 'test-browser'
    
    def test_security_statistics(self):
        """测试安全统计"""
        # 创建几个用户
        for i in range(3):
            self.security_manager.create_user(
                username=f'stats_user_{i}',
                password='StatsPass123!'
            )
        
        # 获取统计信息
        stats = self.security_manager.get_security_statistics()
        
        assert 'users' in stats
        assert 'audit_events' in stats
        assert 'security' in stats
        
        # 验证用户统计
        user_stats = stats['users']
        assert user_stats['total'] >= 4  # 3个测试用户 + 1个默认管理员
        assert user_stats['active'] >= 4
        assert user_stats['locked'] == 0
        
        # 验证安全统计
        security_stats = stats['security']
        assert security_stats['encryption_enabled'] is True
