"""
HMDM配置管理器

统一管理系统配置、模块配置和用户配置
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dataclasses import dataclass, field, asdict
from datetime import datetime

from .config import SystemConfig
from ..allocation.allocation_config import AllocationConfig, load_allocation_config, save_allocation_config
from ..security.military_security import SecurityLevel


@dataclass
class ConfigProfile:
    """配置档案"""
    name: str
    description: str
    created_time: datetime
    modified_time: datetime
    system_config: SystemConfig
    allocation_config: AllocationConfig
    custom_settings: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 配置文件路径
        self.system_config_file = self.config_dir / "system_config.json"
        self.allocation_config_file = self.config_dir / "allocation_config.json"
        self.profiles_dir = self.config_dir / "profiles"
        self.profiles_dir.mkdir(exist_ok=True)
        
        # 当前配置
        self.current_system_config: Optional[SystemConfig] = None
        self.current_allocation_config: Optional[AllocationConfig] = None
        self.current_profile: Optional[str] = None
        
        # 加载默认配置
        self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认配置"""
        try:
            # 加载系统配置
            if self.system_config_file.exists():
                self.current_system_config = self.load_system_config()
            else:
                self.current_system_config = SystemConfig()
                self.save_system_config(self.current_system_config)
            
            # 加载人机分配配置
            if self.allocation_config_file.exists():
                self.current_allocation_config = load_allocation_config(str(self.allocation_config_file))
            else:
                self.current_allocation_config = AllocationConfig()
                save_allocation_config(self.current_allocation_config, str(self.allocation_config_file))
            
            self.logger.info("默认配置加载成功")
            
        except Exception as e:
            self.logger.error(f"加载默认配置失败: {e}")
            # 使用内置默认配置
            self.current_system_config = SystemConfig()
            self.current_allocation_config = AllocationConfig()
    
    def load_system_config(self, config_file: Optional[str] = None) -> SystemConfig:
        """加载系统配置"""
        config_file = config_file or str(self.system_config_file)
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config = SystemConfig.from_dict(config_data)
            self.logger.info(f"系统配置加载成功: {config_file}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载系统配置失败: {e}")
            return SystemConfig()
    
    def save_system_config(self, config: SystemConfig, config_file: Optional[str] = None) -> bool:
        """保存系统配置"""
        config_file = config_file or str(self.system_config_file)
        
        try:
            config_data = config.to_dict()
            
            # 处理特殊类型
            if 'allocation_config' in config_data and config_data['allocation_config']:
                if hasattr(config_data['allocation_config'], 'to_dict'):
                    config_data['allocation_config'] = config_data['allocation_config'].to_dict()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.current_system_config = config
            self.logger.info(f"系统配置保存成功: {config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存系统配置失败: {e}")
            return False
    
    def load_allocation_config(self, config_file: Optional[str] = None) -> AllocationConfig:
        """加载人机分配配置"""
        config_file = config_file or str(self.allocation_config_file)
        
        try:
            config = load_allocation_config(config_file)
            self.current_allocation_config = config
            self.logger.info(f"人机分配配置加载成功: {config_file}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载人机分配配置失败: {e}")
            return AllocationConfig()
    
    def save_allocation_config(self, config: AllocationConfig, config_file: Optional[str] = None) -> bool:
        """保存人机分配配置"""
        config_file = config_file or str(self.allocation_config_file)
        
        try:
            success = save_allocation_config(config, config_file)
            if success:
                self.current_allocation_config = config
                self.logger.info(f"人机分配配置保存成功: {config_file}")
            return success
            
        except Exception as e:
            self.logger.error(f"保存人机分配配置失败: {e}")
            return False
    
    def create_profile(self, name: str, description: str = "") -> bool:
        """创建配置档案"""
        try:
            if not self.current_system_config or not self.current_allocation_config:
                raise ValueError("当前配置不完整")
            
            profile = ConfigProfile(
                name=name,
                description=description,
                created_time=datetime.now(),
                modified_time=datetime.now(),
                system_config=self.current_system_config,
                allocation_config=self.current_allocation_config
            )
            
            profile_file = self.profiles_dir / f"{name}.json"
            profile_data = {
                'name': profile.name,
                'description': profile.description,
                'created_time': profile.created_time.isoformat(),
                'modified_time': profile.modified_time.isoformat(),
                'system_config': profile.system_config.to_dict(),
                'allocation_config': profile.allocation_config.to_dict(),
                'custom_settings': profile.custom_settings
            }
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"配置档案创建成功: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建配置档案失败: {e}")
            return False
    
    def load_profile(self, name: str) -> bool:
        """加载配置档案"""
        try:
            profile_file = self.profiles_dir / f"{name}.json"
            if not profile_file.exists():
                raise FileNotFoundError(f"配置档案不存在: {name}")
            
            with open(profile_file, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            # 重建配置对象
            system_config = SystemConfig.from_dict(profile_data['system_config'])
            allocation_config = AllocationConfig.from_dict(profile_data['allocation_config'])
            
            # 应用配置
            self.current_system_config = system_config
            self.current_allocation_config = allocation_config
            self.current_profile = name
            
            # 保存为当前配置
            self.save_system_config(system_config)
            self.save_allocation_config(allocation_config)
            
            self.logger.info(f"配置档案加载成功: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置档案失败: {e}")
            return False
    
    def list_profiles(self) -> List[Dict[str, Any]]:
        """列出所有配置档案"""
        profiles = []
        
        try:
            for profile_file in self.profiles_dir.glob("*.json"):
                with open(profile_file, 'r', encoding='utf-8') as f:
                    profile_data = json.load(f)
                
                profiles.append({
                    'name': profile_data['name'],
                    'description': profile_data.get('description', ''),
                    'created_time': profile_data.get('created_time', ''),
                    'modified_time': profile_data.get('modified_time', ''),
                    'is_current': profile_data['name'] == self.current_profile
                })
            
        except Exception as e:
            self.logger.error(f"列出配置档案失败: {e}")
        
        return sorted(profiles, key=lambda x: x['modified_time'], reverse=True)
    
    def delete_profile(self, name: str) -> bool:
        """删除配置档案"""
        try:
            profile_file = self.profiles_dir / f"{name}.json"
            if not profile_file.exists():
                raise FileNotFoundError(f"配置档案不存在: {name}")
            
            profile_file.unlink()
            
            if self.current_profile == name:
                self.current_profile = None
            
            self.logger.info(f"配置档案删除成功: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除配置档案失败: {e}")
            return False
    
    def export_config(self, export_file: str, include_profiles: bool = False) -> bool:
        """导出配置"""
        try:
            export_data = {
                'export_time': datetime.now().isoformat(),
                'system_config': self.current_system_config.to_dict() if self.current_system_config else {},
                'allocation_config': self.current_allocation_config.to_dict() if self.current_allocation_config else {}
            }
            
            if include_profiles:
                export_data['profiles'] = []
                for profile_info in self.list_profiles():
                    profile_file = self.profiles_dir / f"{profile_info['name']}.json"
                    with open(profile_file, 'r', encoding='utf-8') as f:
                        profile_data = json.load(f)
                    export_data['profiles'].append(profile_data)
            
            # 根据文件扩展名选择格式
            if export_file.endswith('.yaml') or export_file.endswith('.yml'):
                with open(export_file, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True)
            else:
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"配置导出成功: {export_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str, import_profiles: bool = False) -> bool:
        """导入配置"""
        try:
            # 根据文件扩展名选择解析方式
            if import_file.endswith('.yaml') or import_file.endswith('.yml'):
                with open(import_file, 'r', encoding='utf-8') as f:
                    import_data = yaml.safe_load(f)
            else:
                with open(import_file, 'r', encoding='utf-8') as f:
                    import_data = json.load(f)
            
            # 导入系统配置
            if 'system_config' in import_data:
                system_config = SystemConfig.from_dict(import_data['system_config'])
                self.current_system_config = system_config
                self.save_system_config(system_config)
            
            # 导入人机分配配置
            if 'allocation_config' in import_data:
                allocation_config = AllocationConfig.from_dict(import_data['allocation_config'])
                self.current_allocation_config = allocation_config
                self.save_allocation_config(allocation_config)
            
            # 导入配置档案
            if import_profiles and 'profiles' in import_data:
                for profile_data in import_data['profiles']:
                    profile_file = self.profiles_dir / f"{profile_data['name']}.json"
                    with open(profile_file, 'w', encoding='utf-8') as f:
                        json.dump(profile_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"配置导入成功: {import_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, Any]:
        """验证当前配置"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'system_config_valid': True,
            'allocation_config_valid': True
        }
        
        try:
            # 验证系统配置
            if not self.current_system_config:
                validation_result['errors'].append('系统配置缺失')
                validation_result['system_config_valid'] = False
            else:
                # 检查必要的目录
                for dir_attr in ['data_directory', 'backup_directory', 'temp_directory']:
                    dir_path = getattr(self.current_system_config, dir_attr, '')
                    if not dir_path:
                        validation_result['warnings'].append(f'{dir_attr} 未配置')
                    elif not os.path.exists(dir_path):
                        validation_result['warnings'].append(f'{dir_attr} 目录不存在: {dir_path}')
                
                # 检查模块配置
                if not self.current_system_config.enabled_modules:
                    validation_result['warnings'].append('未启用任何模块')
            
            # 验证人机分配配置
            if not self.current_allocation_config:
                validation_result['errors'].append('人机分配配置缺失')
                validation_result['allocation_config_valid'] = False
            else:
                if not self.current_allocation_config.validate():
                    validation_result['errors'].append('人机分配配置验证失败')
                    validation_result['allocation_config_valid'] = False
            
            # 如果有错误，标记为无效
            if validation_result['errors']:
                validation_result['is_valid'] = False
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f'配置验证过程中发生错误: {str(e)}')
        
        return validation_result
    
    def get_current_config_summary(self) -> Dict[str, Any]:
        """获取当前配置摘要"""
        return {
            'system_config': {
                'system_name': self.current_system_config.system_name if self.current_system_config else 'N/A',
                'version': self.current_system_config.version if self.current_system_config else 'N/A',
                'environment': self.current_system_config.environment if self.current_system_config else 'N/A',
                'enabled_modules_count': len(self.current_system_config.enabled_modules) if self.current_system_config else 0
            },
            'allocation_config': {
                'allocation_mode': self.current_allocation_config.allocation_mode.value if self.current_allocation_config else 'N/A',
                'optimization_objective': self.current_allocation_config.optimization_objective.value if self.current_allocation_config else 'N/A',
                'default_scheme_count': self.current_allocation_config.default_scheme_count if self.current_allocation_config else 0
            },
            'current_profile': self.current_profile,
            'config_files': {
                'system_config_exists': self.system_config_file.exists(),
                'allocation_config_exists': self.allocation_config_file.exists(),
                'profiles_count': len(list(self.profiles_dir.glob("*.json")))
            }
        }


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def reset_config_manager():
    """重置全局配置管理器实例"""
    global _config_manager
    _config_manager = None
