"""
军事决策支持与优化模块

提供军事环境下的专业决策支持，包括作战方案生成、资源优化配置、风险评估等功能。
"""

import numpy as np
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import math
from itertools import combinations

from ..models.decision_models import DecisionMethod, DecisionResult
from ..security.military_security import SecurityLevel
from ..analysis.situation_awareness import SituationAssessment, ThreatLevel


class DecisionType(Enum):
    """决策类型"""
    TACTICAL = "战术决策"
    OPERATIONAL = "作战决策"
    STRATEGIC = "战略决策"
    EMERGENCY = "应急决策"
    RESOURCE_ALLOCATION = "资源配置"
    FORCE_DEPLOYMENT = "兵力部署"


class OperationalPhase(Enum):
    """作战阶段"""
    PREPARATION = "准备阶段"
    APPROACH = "接敌阶段"
    ENGAGEMENT = "交战阶段"
    EXPLOITATION = "扩张阶段"
    CONSOLIDATION = "巩固阶段"
    WITHDRAWAL = "撤退阶段"


class ResourceType(Enum):
    """资源类型"""
    PERSONNEL = "人员"
    EQUIPMENT = "装备"
    AMMUNITION = "弹药"
    FUEL = "燃料"
    SUPPLIES = "补给"
    INTELLIGENCE = "情报"
    TIME = "时间"
    SPACE = "空间"


@dataclass
class MilitaryResource:
    """军事资源"""
    id: str
    name: str
    resource_type: ResourceType
    quantity: float
    unit: str = ""
    quality: float = 1.0  # 质量系数 0-1
    availability: float = 1.0  # 可用性 0-1
    location: str = ""
    constraints: Dict[str, Any] = field(default_factory=dict)
    cost: float = 0.0
    
    def effective_quantity(self) -> float:
        """计算有效数量"""
        return self.quantity * self.quality * self.availability


@dataclass
class MilitaryObjective:
    """军事目标"""
    id: str
    name: str
    description: str
    priority: float = 1.0  # 优先级权重
    success_criteria: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    deadline: Optional[datetime] = None
    resources_required: Dict[str, float] = field(default_factory=dict)
    risk_tolerance: float = 0.5  # 风险容忍度 0-1
    
    def is_achievable(self, available_resources: Dict[str, MilitaryResource]) -> bool:
        """检查目标是否可达成"""
        for resource_id, required_qty in self.resources_required.items():
            if resource_id not in available_resources:
                return False
            
            available_qty = available_resources[resource_id].effective_quantity()
            if available_qty < required_qty:
                return False
        
        return True


@dataclass
class MilitaryOption:
    """军事方案选项"""
    id: str
    name: str
    description: str
    decision_type: DecisionType
    phase: OperationalPhase
    objectives: List[str] = field(default_factory=list)  # 目标ID列表
    resource_requirements: Dict[str, float] = field(default_factory=dict)
    expected_outcomes: Dict[str, float] = field(default_factory=dict)
    risks: Dict[str, float] = field(default_factory=dict)
    duration: float = 0.0  # 执行时间（小时）
    success_probability: float = 0.5
    cost: float = 0.0
    
    def calculate_effectiveness(self, objectives: List[MilitaryObjective]) -> float:
        """计算方案效果"""
        if not objectives:
            return 0.0
        
        total_effectiveness = 0.0
        total_weight = 0.0
        
        for obj in objectives:
            if obj.id in self.objectives:
                # 基于成功概率和目标优先级计算效果
                effectiveness = self.success_probability * obj.priority
                total_effectiveness += effectiveness
                total_weight += obj.priority
        
        return total_effectiveness / max(1.0, total_weight)


class MilitaryDecisionSupport:
    """军事决策支持系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 决策历史
        self.decision_history: List[Dict[str, Any]] = []
        
        # 决策规则库
        self.decision_rules = {
            DecisionType.TACTICAL: self._tactical_decision_rules,
            DecisionType.OPERATIONAL: self._operational_decision_rules,
            DecisionType.STRATEGIC: self._strategic_decision_rules,
            DecisionType.EMERGENCY: self._emergency_decision_rules
        }
        
        # 风险评估参数
        self.risk_factors = {
            "enemy_strength": 0.3,
            "weather_conditions": 0.1,
            "terrain_difficulty": 0.15,
            "supply_line_security": 0.2,
            "time_pressure": 0.15,
            "intelligence_quality": 0.1
        }
    
    def generate_decision_options(self, 
                                situation: SituationAssessment,
                                objectives: List[MilitaryObjective],
                                available_resources: Dict[str, MilitaryResource],
                                decision_type: DecisionType = DecisionType.TACTICAL) -> List[MilitaryOption]:
        """生成决策方案选项"""
        try:
            options = []
            
            # 根据决策类型和当前态势生成方案
            if decision_type == DecisionType.TACTICAL:
                options.extend(self._generate_tactical_options(situation, objectives, available_resources))
            elif decision_type == DecisionType.OPERATIONAL:
                options.extend(self._generate_operational_options(situation, objectives, available_resources))
            elif decision_type == DecisionType.EMERGENCY:
                options.extend(self._generate_emergency_options(situation, objectives, available_resources))
            elif decision_type == DecisionType.RESOURCE_ALLOCATION:
                options.extend(self._generate_resource_allocation_options(objectives, available_resources))
            
            # 评估每个方案的可行性
            feasible_options = []
            for option in options:
                if self._evaluate_option_feasibility(option, available_resources, objectives):
                    feasible_options.append(option)
            
            self.logger.info(f"生成 {len(feasible_options)} 个可行决策方案")
            return feasible_options
            
        except Exception as e:
            self.logger.error(f"决策方案生成失败: {e}")
            return []
    
    def evaluate_options(self, 
                        options: List[MilitaryOption],
                        objectives: List[MilitaryObjective],
                        situation: SituationAssessment,
                        evaluation_criteria: Dict[str, float] = None) -> List[Tuple[MilitaryOption, float]]:
        """评估决策方案"""
        if not evaluation_criteria:
            evaluation_criteria = {
                "effectiveness": 0.3,
                "feasibility": 0.25,
                "risk": 0.2,
                "cost": 0.15,
                "time": 0.1
            }
        
        evaluated_options = []
        
        for option in options:
            score = self._calculate_option_score(option, objectives, situation, evaluation_criteria)
            evaluated_options.append((option, score))
        
        # 按分数排序
        evaluated_options.sort(key=lambda x: x[1], reverse=True)
        
        return evaluated_options
    
    def recommend_decision(self,
                          situation: SituationAssessment,
                          objectives: List[MilitaryObjective],
                          available_resources: Dict[str, MilitaryResource],
                          decision_type: DecisionType = DecisionType.TACTICAL,
                          risk_tolerance: float = 0.5) -> Dict[str, Any]:
        """推荐最优决策方案"""
        try:
            # 生成方案选项
            options = self.generate_decision_options(situation, objectives, available_resources, decision_type)
            
            if not options:
                return {
                    "status": "no_options",
                    "message": "无可行决策方案",
                    "recommendation": None
                }
            
            # 评估方案
            evaluated_options = self.evaluate_options(options, objectives, situation)
            
            # 应用风险容忍度过滤
            acceptable_options = []
            for option, score in evaluated_options:
                option_risk = self._calculate_option_risk(option, situation)
                if option_risk <= risk_tolerance:
                    acceptable_options.append((option, score))
            
            if not acceptable_options:
                # 如果没有符合风险容忍度的方案，选择风险最低的
                min_risk_option = min(evaluated_options, key=lambda x: self._calculate_option_risk(x[0], situation))
                acceptable_options = [min_risk_option]
            
            # 选择最佳方案
            best_option, best_score = acceptable_options[0]
            
            # 生成决策建议
            recommendation = {
                "status": "success",
                "recommended_option": {
                    "id": best_option.id,
                    "name": best_option.name,
                    "description": best_option.description,
                    "score": best_score,
                    "effectiveness": best_option.calculate_effectiveness(objectives),
                    "success_probability": best_option.success_probability,
                    "estimated_cost": best_option.cost,
                    "estimated_duration": best_option.duration,
                    "risk_level": self._calculate_option_risk(best_option, situation)
                },
                "alternatives": [
                    {
                        "id": opt.id,
                        "name": opt.name,
                        "score": score,
                        "risk_level": self._calculate_option_risk(opt, situation)
                    }
                    for opt, score in acceptable_options[1:3]  # 显示前2个备选方案
                ],
                "decision_rationale": self._generate_decision_rationale(best_option, situation, objectives),
                "implementation_guidance": self._generate_implementation_guidance(best_option, available_resources),
                "risk_mitigation": self._generate_risk_mitigation_measures(best_option, situation)
            }
            
            # 记录决策历史
            self._record_decision(recommendation, situation, objectives)
            
            return recommendation
            
        except Exception as e:
            self.logger.error(f"决策推荐失败: {e}")
            return {
                "status": "error",
                "message": f"决策推荐过程中发生错误: {str(e)}",
                "recommendation": None
            }
    
    def optimize_resource_allocation(self,
                                   objectives: List[MilitaryObjective],
                                   available_resources: Dict[str, MilitaryResource],
                                   constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """优化资源配置"""
        try:
            # 使用线性规划方法优化资源分配
            allocation_result = self._solve_resource_allocation(objectives, available_resources, constraints)
            
            return {
                "status": "success",
                "allocation": allocation_result["allocation"],
                "objective_achievement": allocation_result["achievement"],
                "resource_utilization": allocation_result["utilization"],
                "optimization_score": allocation_result["score"]
            }
            
        except Exception as e:
            self.logger.error(f"资源配置优化失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def _generate_tactical_options(self, situation: SituationAssessment, 
                                 objectives: List[MilitaryObjective],
                                 resources: Dict[str, MilitaryResource]) -> List[MilitaryOption]:
        """生成战术决策方案"""
        options = []
        
        # 基于威胁等级生成不同的战术选项
        if situation.threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]:
            # 高威胁情况下的防御性选项
            options.append(MilitaryOption(
                id="tactical_defense",
                name="防御部署",
                description="采取防御态势，巩固阵地",
                decision_type=DecisionType.TACTICAL,
                phase=OperationalPhase.PREPARATION,
                success_probability=0.8,
                duration=2.0,
                cost=100.0
            ))
            
            options.append(MilitaryOption(
                id="tactical_withdrawal",
                name="战术撤退",
                description="有序撤退到安全位置",
                decision_type=DecisionType.TACTICAL,
                phase=OperationalPhase.WITHDRAWAL,
                success_probability=0.9,
                duration=1.5,
                cost=50.0
            ))
        
        elif situation.threat_level in [ThreatLevel.LOW, ThreatLevel.MODERATE]:
            # 中低威胁情况下的进攻性选项
            options.append(MilitaryOption(
                id="tactical_advance",
                name="战术推进",
                description="向目标区域推进",
                decision_type=DecisionType.TACTICAL,
                phase=OperationalPhase.APPROACH,
                success_probability=0.7,
                duration=3.0,
                cost=150.0
            ))
            
            options.append(MilitaryOption(
                id="tactical_flanking",
                name="侧翼机动",
                description="从侧翼接近目标",
                decision_type=DecisionType.TACTICAL,
                phase=OperationalPhase.APPROACH,
                success_probability=0.6,
                duration=4.0,
                cost=200.0
            ))
        
        return options
    
    def _generate_operational_options(self, situation: SituationAssessment,
                                    objectives: List[MilitaryObjective],
                                    resources: Dict[str, MilitaryResource]) -> List[MilitaryOption]:
        """生成作战决策方案"""
        options = []
        
        # 基于目标数量和资源情况生成作战方案
        if len(objectives) > 1:
            options.append(MilitaryOption(
                id="sequential_operations",
                name="序列作战",
                description="按优先级依次完成目标",
                decision_type=DecisionType.OPERATIONAL,
                phase=OperationalPhase.ENGAGEMENT,
                success_probability=0.75,
                duration=8.0,
                cost=500.0
            ))
            
            options.append(MilitaryOption(
                id="parallel_operations",
                name="并行作战",
                description="同时执行多个目标任务",
                decision_type=DecisionType.OPERATIONAL,
                phase=OperationalPhase.ENGAGEMENT,
                success_probability=0.6,
                duration=5.0,
                cost=800.0
            ))
        
        return options
    
    def _generate_emergency_options(self, situation: SituationAssessment,
                                  objectives: List[MilitaryObjective],
                                  resources: Dict[str, MilitaryResource]) -> List[MilitaryOption]:
        """生成应急决策方案"""
        options = []
        
        # 应急情况下的快速响应方案
        options.append(MilitaryOption(
            id="immediate_response",
            name="立即响应",
            description="使用现有资源立即行动",
            decision_type=DecisionType.EMERGENCY,
            phase=OperationalPhase.ENGAGEMENT,
            success_probability=0.5,
            duration=0.5,
            cost=50.0
        ))
        
        options.append(MilitaryOption(
            id="call_reinforcement",
            name="请求增援",
            description="请求额外资源支援",
            decision_type=DecisionType.EMERGENCY,
            phase=OperationalPhase.PREPARATION,
            success_probability=0.8,
            duration=2.0,
            cost=300.0
        ))
        
        return options
    
    def _generate_resource_allocation_options(self, objectives: List[MilitaryObjective],
                                            resources: Dict[str, MilitaryResource]) -> List[MilitaryOption]:
        """生成资源配置方案"""
        options = []
        
        # 均衡分配方案
        options.append(MilitaryOption(
            id="balanced_allocation",
            name="均衡分配",
            description="资源在各目标间均衡分配",
            decision_type=DecisionType.RESOURCE_ALLOCATION,
            phase=OperationalPhase.PREPARATION,
            success_probability=0.7,
            duration=1.0,
            cost=0.0
        ))
        
        # 优先级分配方案
        options.append(MilitaryOption(
            id="priority_allocation",
            name="优先级分配",
            description="按目标优先级分配资源",
            decision_type=DecisionType.RESOURCE_ALLOCATION,
            phase=OperationalPhase.PREPARATION,
            success_probability=0.8,
            duration=1.0,
            cost=0.0
        ))
        
        return options
    
    def _evaluate_option_feasibility(self, option: MilitaryOption,
                                   resources: Dict[str, MilitaryResource],
                                   objectives: List[MilitaryObjective]) -> bool:
        """评估方案可行性"""
        # 检查资源需求是否满足
        for resource_id, required_qty in option.resource_requirements.items():
            if resource_id not in resources:
                return False
            
            available_qty = resources[resource_id].effective_quantity()
            if available_qty < required_qty:
                return False
        
        return True
    
    def _calculate_option_score(self, option: MilitaryOption,
                              objectives: List[MilitaryObjective],
                              situation: SituationAssessment,
                              criteria: Dict[str, float]) -> float:
        """计算方案综合得分"""
        score = 0.0
        
        # 效果性评分
        effectiveness = option.calculate_effectiveness(objectives)
        score += effectiveness * criteria.get("effectiveness", 0.3)
        
        # 可行性评分（基于成功概率）
        feasibility = option.success_probability
        score += feasibility * criteria.get("feasibility", 0.25)
        
        # 风险评分（风险越低分数越高）
        risk = self._calculate_option_risk(option, situation)
        risk_score = 1.0 - risk
        score += risk_score * criteria.get("risk", 0.2)
        
        # 成本评分（成本越低分数越高）
        max_cost = 1000.0  # 假设最大成本
        cost_score = 1.0 - min(option.cost / max_cost, 1.0)
        score += cost_score * criteria.get("cost", 0.15)
        
        # 时间评分（时间越短分数越高）
        max_time = 24.0  # 假设最大时间24小时
        time_score = 1.0 - min(option.duration / max_time, 1.0)
        score += time_score * criteria.get("time", 0.1)
        
        return score
    
    def _calculate_option_risk(self, option: MilitaryOption, situation: SituationAssessment) -> float:
        """计算方案风险"""
        base_risk = 1.0 - option.success_probability
        
        # 根据威胁等级调整风险
        threat_multiplier = {
            ThreatLevel.MINIMAL: 0.5,
            ThreatLevel.LOW: 0.7,
            ThreatLevel.MODERATE: 1.0,
            ThreatLevel.HIGH: 1.3,
            ThreatLevel.CRITICAL: 1.5
        }
        
        adjusted_risk = base_risk * threat_multiplier.get(situation.threat_level, 1.0)
        return min(adjusted_risk, 1.0)
    
    def _solve_resource_allocation(self, objectives: List[MilitaryObjective],
                                 resources: Dict[str, MilitaryResource],
                                 constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """求解资源分配优化问题"""
        # 简化的资源分配算法
        allocation = {}
        total_priority = sum(obj.priority for obj in objectives)
        
        for obj in objectives:
            obj_allocation = {}
            priority_ratio = obj.priority / total_priority
            
            for resource_id, resource in resources.items():
                allocated_qty = resource.effective_quantity() * priority_ratio
                obj_allocation[resource_id] = allocated_qty
            
            allocation[obj.id] = obj_allocation
        
        return {
            "allocation": allocation,
            "achievement": 0.8,  # 简化的成就度
            "utilization": 0.9,  # 简化的利用率
            "score": 0.85  # 简化的优化分数
        }
    
    def _generate_decision_rationale(self, option: MilitaryOption,
                                   situation: SituationAssessment,
                                   objectives: List[MilitaryObjective]) -> str:
        """生成决策理由"""
        rationale = f"推荐方案'{option.name}'的主要理由：\n"
        rationale += f"1. 成功概率较高（{option.success_probability:.1%}）\n"
        rationale += f"2. 适合当前威胁等级（{situation.threat_level.value}）\n"
        rationale += f"3. 预计执行时间合理（{option.duration}小时）\n"
        rationale += f"4. 资源消耗可控（成本{option.cost}）"
        
        return rationale
    
    def _generate_implementation_guidance(self, option: MilitaryOption,
                                        resources: Dict[str, MilitaryResource]) -> List[str]:
        """生成实施指导"""
        guidance = [
            f"按照{option.phase.value}的标准程序执行",
            "确保通信联络畅通",
            "建立进度监控机制",
            "准备应急预案"
        ]
        
        if option.resource_requirements:
            guidance.append("确认所需资源已到位")
        
        return guidance
    
    def _generate_risk_mitigation_measures(self, option: MilitaryOption,
                                         situation: SituationAssessment) -> List[str]:
        """生成风险缓解措施"""
        measures = [
            "建立实时态势监控",
            "制定应急撤退计划",
            "保持与上级的通信联系"
        ]
        
        if situation.threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]:
            measures.extend([
                "增加警戒力量",
                "准备医疗救护",
                "确保弹药充足"
            ])
        
        return measures
    
    def _record_decision(self, recommendation: Dict[str, Any],
                        situation: SituationAssessment,
                        objectives: List[MilitaryObjective]) -> None:
        """记录决策历史"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "situation_id": situation.id,
            "threat_level": situation.threat_level.value,
            "objectives_count": len(objectives),
            "recommended_option": recommendation["recommended_option"]["id"],
            "decision_score": recommendation["recommended_option"]["score"]
        }
        
        self.decision_history.append(record)
        
        # 限制历史记录数量
        if len(self.decision_history) > 1000:
            self.decision_history = self.decision_history[-500:]
    
    def _tactical_decision_rules(self) -> Dict[str, Any]:
        """战术决策规则"""
        return {
            "max_duration": 6.0,  # 最大持续时间6小时
            "min_success_probability": 0.4,
            "preferred_phases": [OperationalPhase.APPROACH, OperationalPhase.ENGAGEMENT]
        }
    
    def _operational_decision_rules(self) -> Dict[str, Any]:
        """作战决策规则"""
        return {
            "max_duration": 24.0,  # 最大持续时间24小时
            "min_success_probability": 0.6,
            "preferred_phases": [OperationalPhase.PREPARATION, OperationalPhase.ENGAGEMENT]
        }
    
    def _strategic_decision_rules(self) -> Dict[str, Any]:
        """战略决策规则"""
        return {
            "max_duration": 168.0,  # 最大持续时间7天
            "min_success_probability": 0.7,
            "preferred_phases": [OperationalPhase.PREPARATION]
        }
    
    def _emergency_decision_rules(self) -> Dict[str, Any]:
        """应急决策规则"""
        return {
            "max_duration": 1.0,  # 最大持续时间1小时
            "min_success_probability": 0.3,
            "preferred_phases": [OperationalPhase.ENGAGEMENT]
        }
