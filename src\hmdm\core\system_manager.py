"""
HMDM系统管理器

统一管理各个子系统的配置、初始化、数据流和生命周期。
提供系统级的协调和集成功能。
"""

import json
import logging
import logging.handlers
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import os
from pathlib import Path

from ..analysis.situation_awareness import SituationAwarenessEngine
from ..communication.military_comms import MilitaryCommunicationSystem
from ..decision.military_decision_support import MilitaryDecisionSupport
from ..training.military_training import MilitaryTrainingSystem
from ..knowledge.military_knowledge_base import MilitaryKnowledgeBase
from ..simulation.military_simulation import MilitarySimulationEngine
from ..scenarios.decision_support_scenarios import DecisionSupportScenarios
from ..allocation.human_machine_allocation_system import HumanMachineAllocationSystem
from ..allocation.allocation_config import AllocationConfig, load_allocation_config
from ..security.military_security import SecurityLevel
from .config import SystemConfig, SystemStatus, ModuleStatus, ModuleInfo
from .config_manager import ConfigManager, get_config_manager


class HMDMSystemManager:
    """HMDM系统管理器"""
    
    def __init__(self, config_file: Optional[str] = None, config_manager: Optional[ConfigManager] = None):
        self.logger = logging.getLogger(__name__)

        # 系统状态
        self.status = SystemStatus.INITIALIZING
        self.start_time: Optional[datetime] = None

        # 初始化配置管理器
        self.config_manager = config_manager or get_config_manager()

        # 配置管理
        if config_file:
            # 如果指定了配置文件，加载该文件
            self.config = self._load_config(config_file)
        else:
            # 使用配置管理器的当前配置
            self.config = self.config_manager.current_system_config or SystemConfig()
            # 确保allocation配置同步
            if self.config_manager.current_allocation_config:
                self.config.allocation_config = self.config_manager.current_allocation_config

        self._setup_logging()
        
        # 模块管理
        self.modules: Dict[str, ModuleInfo] = {}
        self._init_module_registry()
        
        # 事件系统
        self.event_handlers: Dict[str, List[Callable]] = {}
        self._event_lock = threading.Lock()
        
        # 数据流管理
        self.data_channels: Dict[str, Any] = {}
        
        # 统计信息
        self.stats = {
            "start_time": None,
            "uptime": 0,
            "processed_requests": 0,
            "error_count": 0,
            "module_restarts": 0
        }
        
        self.logger.info("HMDM系统管理器初始化完成")
    
    def start_system(self) -> bool:
        """启动系统"""
        try:
            self.logger.info("开始启动HMDM系统...")
            self.status = SystemStatus.INITIALIZING
            
            # 创建必要的目录
            self._create_directories()
            
            # 按依赖顺序启动模块
            startup_order = self._calculate_startup_order()
            
            for module_name in startup_order:
                if module_name in self.config.enabled_modules:
                    success = self._start_module(module_name)
                    if not success:
                        self.logger.error(f"模块 {module_name} 启动失败")
                        self.status = SystemStatus.ERROR
                        return False
            
            # 建立模块间的数据连接
            self._setup_data_channels()
            
            # 启动事件处理
            self._start_event_processing()
            
            self.status = SystemStatus.RUNNING
            self.start_time = datetime.now()
            self.stats["start_time"] = self.start_time
            
            self.logger.info("HMDM系统启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            self.status = SystemStatus.ERROR
            return False
    
    def stop_system(self) -> bool:
        """停止系统"""
        try:
            self.logger.info("开始停止HMDM系统...")
            self.status = SystemStatus.STOPPING
            
            # 按相反顺序停止模块
            shutdown_order = list(reversed(self._calculate_startup_order()))
            
            for module_name in shutdown_order:
                if module_name in self.modules:
                    self._stop_module(module_name)
            
            # 清理资源
            self._cleanup_resources()
            
            self.status = SystemStatus.STOPPED
            self.logger.info("HMDM系统已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"系统停止失败: {e}")
            self.status = SystemStatus.ERROR
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        uptime = 0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "system_status": self.status.value,
            "uptime_seconds": uptime,
            "modules": {
                name: {
                    "status": info.status.value,
                    "last_error": info.last_error,
                    "start_time": info.start_time.isoformat() if info.start_time else None
                }
                for name, info in self.modules.items()
            },
            "statistics": {
                **self.stats,
                "uptime": uptime
            },
            "configuration": {
                "enabled_modules": self.config.enabled_modules,
                "security_level": self.config.security_level.value,
                "environment": self.config.environment
            }
        }
    
    def get_module(self, module_name: str) -> Optional[Any]:
        """获取模块实例"""
        if module_name in self.modules:
            module_info = self.modules[module_name]
            if module_info.status == ModuleStatus.RUNNING:
                return module_info.instance
        return None
    
    def restart_module(self, module_name: str) -> bool:
        """重启模块"""
        try:
            self.logger.info(f"重启模块: {module_name}")
            
            # 停止模块
            self._stop_module(module_name)
            
            # 启动模块
            success = self._start_module(module_name)
            
            if success:
                self.stats["module_restarts"] += 1
                self.logger.info(f"模块 {module_name} 重启成功")
            else:
                self.logger.error(f"模块 {module_name} 重启失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"重启模块 {module_name} 失败: {e}")
            return False
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """注册事件处理器"""
        with self._event_lock:
            if event_type not in self.event_handlers:
                self.event_handlers[event_type] = []
            self.event_handlers[event_type].append(handler)
    
    def emit_event(self, event_type: str, data: Any = None) -> None:
        """发送事件"""
        with self._event_lock:
            if event_type in self.event_handlers:
                for handler in self.event_handlers[event_type]:
                    try:
                        handler(data)
                    except Exception as e:
                        self.logger.error(f"事件处理器执行失败: {e}")
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            # 验证配置
            if not self._validate_config(new_config):
                return False
            
            # 更新配置
            for key, value in new_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            # 保存配置
            self._save_config()
            
            # 通知模块配置更新
            self.emit_event("config_updated", new_config)
            
            self.logger.info("系统配置已更新")
            return True
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return False
    
    def _load_config(self, config_file: Optional[str]) -> SystemConfig:
        """加载配置"""
        config = SystemConfig()

        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                config = SystemConfig.from_dict(config_dict)
            except Exception as e:
                logging.warning(f"加载配置文件失败，使用默认配置: {e}")

        # 加载人机分配配置
        try:
            config.allocation_config = load_allocation_config(config.allocation_config_file)
            self.logger.info("人机分配配置加载成功")
        except Exception as e:
            self.logger.warning(f"加载人机分配配置失败，使用默认配置: {e}")
            config.allocation_config = AllocationConfig()

        return config
    
    def _save_config(self, config_file: str = "config/system_config.json") -> None:
        """保存配置"""
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def _setup_logging(self) -> None:
        """设置日志"""
        # 创建日志目录
        log_dir = os.path.dirname(self.config.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        # 获取根日志记录器
        root_logger = logging.getLogger()

        # 清除现有处理器以避免重复
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)

        # 创建文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            self.config.log_file,
            maxBytes=self.config.log_max_size,
            backupCount=self.config.log_backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

        # 配置根日志记录器
        root_logger.setLevel(getattr(logging, self.config.log_level))
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def _init_module_registry(self) -> None:
        """初始化模块注册表"""
        # 定义模块依赖关系
        module_configs = {
            "situation_awareness": ModuleInfo(
                name="situation_awareness",
                dependencies=[]
            ),
            "communication": ModuleInfo(
                name="communication", 
                dependencies=[]
            ),
            "knowledge_base": ModuleInfo(
                name="knowledge_base",
                dependencies=[]
            ),
            "decision_support": ModuleInfo(
                name="decision_support",
                dependencies=["knowledge_base", "situation_awareness"]
            ),
            "training": ModuleInfo(
                name="training",
                dependencies=["knowledge_base"]
            ),
            "simulation": ModuleInfo(
                name="simulation",
                dependencies=[]
            ),
            "scenarios": ModuleInfo(
                name="scenarios",
                dependencies=["decision_support", "knowledge_base", "situation_awareness", "training", "simulation", "communication"]
            ),
            "human_machine_allocation": ModuleInfo(
                name="human_machine_allocation",
                dependencies=["knowledge_base"]
            )
        }
        
        self.modules = module_configs

    def _calculate_startup_order(self) -> List[str]:
        """计算模块启动顺序（拓扑排序）"""
        visited = set()
        temp_visited = set()
        order = []

        def visit(module_name: str):
            if module_name in temp_visited:
                raise ValueError(f"检测到循环依赖: {module_name}")

            if module_name not in visited:
                temp_visited.add(module_name)

                if module_name in self.modules:
                    for dep in self.modules[module_name].dependencies:
                        visit(dep)

                temp_visited.remove(module_name)
                visited.add(module_name)
                order.append(module_name)

        for module_name in self.modules.keys():
            if module_name not in visited:
                visit(module_name)

        return order

    def _start_module(self, module_name: str) -> bool:
        """启动单个模块"""
        try:
            if module_name not in self.modules:
                self.logger.error(f"未知模块: {module_name}")
                return False

            module_info = self.modules[module_name]
            module_info.status = ModuleStatus.LOADING

            # 检查依赖
            for dep in module_info.dependencies:
                if dep not in self.modules or self.modules[dep].status != ModuleStatus.RUNNING:
                    self.logger.error(f"模块 {module_name} 的依赖 {dep} 未就绪")
                    module_info.status = ModuleStatus.ERROR
                    return False

            # 创建模块实例
            instance = self._create_module_instance(module_name)
            if instance is None:
                module_info.status = ModuleStatus.ERROR
                module_info.last_error = "模块实例创建失败"
                return False

            module_info.instance = instance
            module_info.status = ModuleStatus.RUNNING
            module_info.start_time = datetime.now()
            module_info.last_error = None

            self.logger.info(f"模块 {module_name} 启动成功")
            return True

        except Exception as e:
            self.logger.error(f"启动模块 {module_name} 失败: {e}")
            if module_name in self.modules:
                self.modules[module_name].status = ModuleStatus.ERROR
                self.modules[module_name].last_error = str(e)
            return False

    def _stop_module(self, module_name: str) -> bool:
        """停止单个模块"""
        try:
            if module_name not in self.modules:
                return True

            module_info = self.modules[module_name]

            if module_info.instance and hasattr(module_info.instance, 'shutdown'):
                module_info.instance.shutdown()

            module_info.instance = None
            module_info.status = ModuleStatus.NOT_LOADED
            module_info.start_time = None

            self.logger.info(f"模块 {module_name} 已停止")
            return True

        except Exception as e:
            self.logger.error(f"停止模块 {module_name} 失败: {e}")
            return False

    def _create_module_instance(self, module_name: str) -> Optional[Any]:
        """创建模块实例"""
        try:
            if module_name == "situation_awareness":
                return SituationAwarenessEngine()
            elif module_name == "communication":
                return MilitaryCommunicationSystem()
            elif module_name == "decision_support":
                return MilitaryDecisionSupport()
            elif module_name == "training":
                return MilitaryTrainingSystem()
            elif module_name == "knowledge_base":
                return MilitaryKnowledgeBase()
            elif module_name == "simulation":
                return MilitarySimulationEngine()
            elif module_name == "scenarios":
                return DecisionSupportScenarios()
            elif module_name == "human_machine_allocation":
                # 传递allocation配置
                allocation_system = HumanMachineAllocationSystem()
                if self.config.allocation_config:
                    allocation_system.config = self.config.allocation_config.to_dict()
                return allocation_system
            else:
                self.logger.error(f"未知模块类型: {module_name}")
                return None

        except Exception as e:
            self.logger.error(f"创建模块实例失败 {module_name}: {e}")
            return None

    def _setup_data_channels(self) -> None:
        """建立模块间的数据通道"""
        try:
            # 态势感知 -> 决策支持
            if ("situation_awareness" in self.modules and
                "decision_support" in self.modules and
                "situation_awareness" in self.config.enabled_modules and
                "decision_support" in self.config.enabled_modules):
                sa_engine = self.modules["situation_awareness"].instance
                ds_engine = self.modules["decision_support"].instance

                if sa_engine and ds_engine:
                    # 这里可以建立数据订阅关系
                    self.data_channels["sa_to_ds"] = {
                        "source": sa_engine,
                        "target": ds_engine,
                        "data_type": "situation_assessment"
                    }

            # 知识库 -> 决策支持
            if ("knowledge_base" in self.modules and
                "decision_support" in self.modules and
                "knowledge_base" in self.config.enabled_modules and
                "decision_support" in self.config.enabled_modules):
                kb_engine = self.modules["knowledge_base"].instance
                ds_engine = self.modules["decision_support"].instance

                if kb_engine and ds_engine:
                    self.data_channels["kb_to_ds"] = {
                        "source": kb_engine,
                        "target": ds_engine,
                        "data_type": "expert_knowledge"
                    }

            # 人机分配系统的数据通道
            if ("human_machine_allocation" in self.modules and
                "human_machine_allocation" in self.config.enabled_modules):
                hma_system = self.modules["human_machine_allocation"].instance

                if hma_system:
                    # 知识库 -> 人机分配
                    if ("knowledge_base" in self.modules and
                        "knowledge_base" in self.config.enabled_modules):
                        kb_engine = self.modules["knowledge_base"].instance
                        if kb_engine:
                            self.data_channels["kb_to_hma"] = {
                                "source": kb_engine,
                                "target": hma_system,
                                "data_type": "domain_knowledge"
                            }

                    # 决策支持 -> 人机分配 (如果启用)
                    if ("decision_support" in self.modules and
                        "decision_support" in self.config.enabled_modules):
                        ds_engine = self.modules["decision_support"].instance
                        if ds_engine:
                            self.data_channels["ds_to_hma"] = {
                                "source": ds_engine,
                                "target": hma_system,
                                "data_type": "decision_context"
                            }

            # 通信系统 -> 所有模块
            if "communication" in self.modules:
                comm_system = self.modules["communication"].instance
                if comm_system:
                    for module_name, module_info in self.modules.items():
                        if module_name != "communication" and module_info.instance:
                            self.data_channels[f"comm_to_{module_name}"] = {
                                "source": comm_system,
                                "target": module_info.instance,
                                "data_type": "communication"
                            }

            self.logger.info("数据通道建立完成")

        except Exception as e:
            self.logger.error(f"建立数据通道失败: {e}")

    def _start_event_processing(self) -> None:
        """启动事件处理"""
        # 注册系统级事件处理器
        self.register_event_handler("module_error", self._handle_module_error)
        self.register_event_handler("system_warning", self._handle_system_warning)

        self.logger.info("事件处理系统已启动")

    def _handle_module_error(self, data: Any) -> None:
        """处理模块错误"""
        self.stats["error_count"] += 1
        self.logger.error(f"模块错误: {data}")

        # 可以实现自动重启逻辑
        if isinstance(data, dict) and "module_name" in data:
            module_name = data["module_name"]
            if data.get("auto_restart", False):
                self.restart_module(module_name)

    def _handle_system_warning(self, data: Any) -> None:
        """处理系统警告"""
        self.logger.warning(f"系统警告: {data}")

    def _create_directories(self) -> None:
        """创建必要的目录"""
        directories = [
            self.config.data_directory,
            self.config.backup_directory,
            self.config.temp_directory,
            os.path.dirname(self.config.log_file)
        ]

        for directory in directories:
            if directory:
                os.makedirs(directory, exist_ok=True)

    def _cleanup_resources(self) -> None:
        """清理资源"""
        try:
            # 清理数据通道
            self.data_channels.clear()

            # 清理事件处理器
            with self._event_lock:
                self.event_handlers.clear()

            # 清理临时文件
            temp_dir = Path(self.config.temp_directory)
            if temp_dir.exists():
                for temp_file in temp_dir.glob("*"):
                    try:
                        temp_file.unlink()
                    except Exception:
                        pass

            self.logger.info("资源清理完成")

        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")

    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            # 对于配置更新，不要求所有必需字段都存在
            # 只验证提供的字段是否有效

            # 验证环境配置（如果提供了的话）
            if "environment" in config:
                valid_environments = ["development", "testing", "production"]
                if config.get("environment") not in valid_environments:
                    self.logger.error(f"无效的环境配置: {config.get('environment')}")
                    return False

            # 验证日志级别（如果提供了的话）
            if "log_level" in config:
                valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
                if config.get("log_level") not in valid_log_levels:
                    self.logger.error(f"无效的日志级别: {config.get('log_level')}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False

    def get_allocation_system(self) -> Optional[HumanMachineAllocationSystem]:
        """获取人机分配系统实例"""
        if ("human_machine_allocation" in self.modules and
            self.modules["human_machine_allocation"].status == ModuleStatus.RUNNING):
            return self.modules["human_machine_allocation"].instance
        return None

    def get_module_instance(self, module_name: str) -> Optional[Any]:
        """获取指定模块的实例"""
        if (module_name in self.modules and
            self.modules[module_name].status == ModuleStatus.RUNNING):
            return self.modules[module_name].instance
        return None

    def get_allocation_config(self) -> Optional[AllocationConfig]:
        """获取人机分配配置"""
        return self.config.allocation_config

    def update_allocation_config(self, config: AllocationConfig) -> bool:
        """更新人机分配配置"""
        try:
            # 验证配置
            if not config.validate():
                return False

            # 更新配置
            self.config.allocation_config = config

            # 如果allocation系统正在运行，更新其配置
            allocation_system = self.get_allocation_system()
            if allocation_system:
                allocation_system.config = config.to_dict()
                self.logger.info("人机分配系统配置已更新")

            return True

        except Exception as e:
            self.logger.error(f"更新人机分配配置失败: {e}")
            return False

    def save_allocation_config(self, config_file: Optional[str] = None) -> bool:
        """保存人机分配配置"""
        try:
            if not self.config.allocation_config:
                return False

            # 使用配置管理器保存
            success = self.config_manager.save_allocation_config(self.config.allocation_config, config_file)
            if success:
                self.logger.info("人机分配配置保存成功")
            return success

        except Exception as e:
            self.logger.error(f"保存人机分配配置失败: {e}")
            return False

    def get_config_manager(self) -> ConfigManager:
        """获取配置管理器"""
        return self.config_manager

    def create_config_profile(self, name: str, description: str = "") -> bool:
        """创建配置档案"""
        try:
            # 更新配置管理器的当前配置
            self.config_manager.current_system_config = self.config
            if self.config.allocation_config:
                self.config_manager.current_allocation_config = self.config.allocation_config

            return self.config_manager.create_profile(name, description)
        except Exception as e:
            self.logger.error(f"创建配置档案失败: {e}")
            return False

    def load_config_profile(self, name: str) -> bool:
        """加载配置档案"""
        try:
            success = self.config_manager.load_profile(name)
            if success:
                # 更新系统配置
                self.config = self.config_manager.current_system_config
                self.logger.info(f"配置档案加载成功: {name}")
            return success
        except Exception as e:
            self.logger.error(f"加载配置档案失败: {e}")
            return False

    def list_config_profiles(self) -> List[Dict[str, Any]]:
        """列出配置档案"""
        return self.config_manager.list_profiles()

    def export_config(self, export_file: str, include_profiles: bool = False) -> bool:
        """导出配置"""
        try:
            # 更新配置管理器的当前配置
            self.config_manager.current_system_config = self.config
            if self.config.allocation_config:
                self.config_manager.current_allocation_config = self.config.allocation_config

            return self.config_manager.export_config(export_file, include_profiles)
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False

    def import_config(self, import_file: str, import_profiles: bool = False) -> bool:
        """导入配置"""
        try:
            success = self.config_manager.import_config(import_file, import_profiles)
            if success:
                # 更新系统配置
                self.config = self.config_manager.current_system_config
                self.logger.info("配置导入成功")
            return success
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False

    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        try:
            # 更新配置管理器的当前配置
            self.config_manager.current_system_config = self.config
            if self.config.allocation_config:
                self.config_manager.current_allocation_config = self.config.allocation_config

            return self.config_manager.validate_config()
        except Exception as e:
            self.logger.error(f"验证配置失败: {e}")
            return {
                'is_valid': False,
                'errors': [f'配置验证过程中发生错误: {str(e)}'],
                'warnings': []
            }
