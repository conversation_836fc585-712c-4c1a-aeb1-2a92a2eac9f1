{"metadata": {"name": "压力测试配置", "description": "系统压力测试和性能基准测试配置", "version": "1.0", "created_date": "2024-01-01"}, "test_configurations": [{"name": "小规模性能测试", "description": "基础性能测试配置", "parameters": {"alternatives_count": 10, "indicators_count": 8, "iterations": 100, "concurrent_users": 1}, "performance_targets": {"max_response_time": 2.0, "max_memory_usage": 100, "min_throughput": 50}}, {"name": "中等规模性能测试", "description": "常规业务场景性能测试", "parameters": {"alternatives_count": 50, "indicators_count": 15, "iterations": 50, "concurrent_users": 5}, "performance_targets": {"max_response_time": 5.0, "max_memory_usage": 200, "min_throughput": 20}}, {"name": "大规模性能测试", "description": "高负载场景性能测试", "parameters": {"alternatives_count": 100, "indicators_count": 25, "iterations": 20, "concurrent_users": 10}, "performance_targets": {"max_response_time": 10.0, "max_memory_usage": 500, "min_throughput": 10}}, {"name": "极限压力测试", "description": "系统极限能力测试", "parameters": {"alternatives_count": 500, "indicators_count": 50, "iterations": 5, "concurrent_users": 20}, "performance_targets": {"max_response_time": 30.0, "max_memory_usage": 1000, "min_throughput": 2}}], "test_scenarios": [{"name": "任务分解性能测试", "description": "测试任务分解模块的性能", "test_cases": [{"scenario": "态势分析", "task_depth": 3, "subtask_count": 10, "expected_time": 2.0}, {"scenario": "威胁计算", "task_depth": 4, "subtask_count": 15, "expected_time": 3.0}, {"scenario": "辅助决策", "task_depth": 3, "subtask_count": 12, "expected_time": 2.5}]}, {"name": "决策算法性能测试", "description": "测试各决策算法的性能", "algorithms": [{"name": "WRDM", "complexity": "O(mn)", "expected_time_per_100_alternatives": 1.0}, {"name": "TOPSIS", "complexity": "O(mn)", "expected_time_per_100_alternatives": 0.8}, {"name": "Fuzzy AHP", "complexity": "O(n²)", "expected_time_per_100_alternatives": 2.0}]}, {"name": "数据处理性能测试", "description": "测试数据加载和处理性能", "test_cases": [{"data_type": "Excel文件", "file_size_mb": 1, "expected_load_time": 1.0}, {"data_type": "Excel文件", "file_size_mb": 10, "expected_load_time": 5.0}, {"data_type": "JSON文件", "file_size_mb": 5, "expected_load_time": 2.0}]}, {"name": "Web界面性能测试", "description": "测试Web界面的响应性能", "test_cases": [{"operation": "页面加载", "expected_time": 2.0, "concurrent_users": 10}, {"operation": "方案评估", "expected_time": 5.0, "concurrent_users": 5}, {"operation": "结果展示", "expected_time": 1.0, "concurrent_users": 10}]}], "monitoring_metrics": [{"name": "响应时间", "unit": "秒", "measurement_method": "平均值", "alert_threshold": 10.0}, {"name": "内存使用", "unit": "MB", "measurement_method": "峰值", "alert_threshold": 1000}, {"name": "CPU使用率", "unit": "%", "measurement_method": "平均值", "alert_threshold": 80}, {"name": "吞吐量", "unit": "请求/秒", "measurement_method": "平均值", "alert_threshold": 1}, {"name": "错误率", "unit": "%", "measurement_method": "比例", "alert_threshold": 5}], "load_testing_profiles": [{"name": "渐进式负载", "description": "逐步增加负载直到系统极限", "pattern": "ramp_up", "start_users": 1, "max_users": 50, "ramp_duration": 300, "hold_duration": 600}, {"name": "突发负载", "description": "突然增加大量负载", "pattern": "spike", "base_users": 5, "spike_users": 50, "spike_duration": 60}, {"name": "持续负载", "description": "长时间稳定负载", "pattern": "constant", "users": 20, "duration": 1800}], "performance_benchmarks": {"baseline": {"alternatives": 10, "indicators": 5, "response_time": 1.0, "memory_usage": 50, "cpu_usage": 20}, "targets": {"response_time_improvement": 0.8, "memory_efficiency": 0.9, "throughput_increase": 1.2}}}