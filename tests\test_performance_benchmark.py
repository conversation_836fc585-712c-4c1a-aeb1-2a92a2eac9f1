"""
HMDM系统性能基准测试

测试系统在不同负载下的响应时间和资源使用情况
建立性能基准，为系统优化提供参考
"""

import unittest
import time
import threading
import psutil
import os
import tempfile
import shutil
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

import sys
import os
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.hmdm.core.system_manager import HMDMSystemManager
from src.hmdm.core.config_manager import ConfigManager
from src.hmdm.allocation.human_machine_allocation_system import HumanMachineAllocationSystem
from src.hmdm.allocation.allocation_config import AllocationConfig, AllocationMode, OptimizationObjective
from src.hmdm.models import Task, TaskType
from src.hmdm.models.task_models import TaskHierarchy


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.memory_usage = []
        self.cpu_usage = []
        self.response_times = []
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.memory_usage = []
        self.cpu_usage = []
        self.response_times = []
        
    def record_response_time(self, response_time: float):
        """记录响应时间"""
        self.response_times.append(response_time)
        
    def record_system_metrics(self):
        """记录系统指标"""
        process = psutil.Process(os.getpid())
        self.memory_usage.append(process.memory_info().rss / 1024 / 1024)  # MB
        self.cpu_usage.append(process.cpu_percent())
        
    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()
        
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.response_times:
            return {
                'total_time': self.end_time - self.start_time if self.end_time and self.start_time else 0,
                'avg_memory_mb': sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0,
                'max_memory_mb': max(self.memory_usage) if self.memory_usage else 0,
                'avg_cpu_percent': sum(self.cpu_usage) / len(self.cpu_usage) if self.cpu_usage else 0,
                'max_cpu_percent': max(self.cpu_usage) if self.cpu_usage else 0,
                'response_times': []
            }
            
        return {
            'total_time': self.end_time - self.start_time,
            'avg_response_time': sum(self.response_times) / len(self.response_times),
            'min_response_time': min(self.response_times),
            'max_response_time': max(self.response_times),
            'total_requests': len(self.response_times),
            'requests_per_second': len(self.response_times) / (self.end_time - self.start_time) if (self.end_time - self.start_time) > 0 else 0,
            'avg_memory_mb': sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0,
            'max_memory_mb': max(self.memory_usage) if self.memory_usage else 0,
            'avg_cpu_percent': sum(self.cpu_usage) / len(self.cpu_usage) if self.cpu_usage else 0,
            'max_cpu_percent': max(self.cpu_usage) if self.cpu_usage else 0
        }


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.system_manager = HMDMSystemManager()
        self.allocation_system = HumanMachineAllocationSystem()
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_task(self, task_id: int = 0) -> Task:
        """创建测试任务"""
        return Task(
            name=f"性能测试任务_{task_id}",
            description=f"用于性能测试的任务{task_id}",
            task_type=TaskType.MISSION_TASK,
            attributes={
                'complexity': 0.5 + (task_id % 5) * 0.1,
                'importance': 0.6 + (task_id % 4) * 0.1,
                'urgency': 0.7 + (task_id % 3) * 0.1
            }
        )
    
    def test_single_task_allocation_performance(self):
        """测试单任务分配性能"""
        print("\n测试单任务分配性能...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 创建测试任务
        task = self.create_test_task()
        task_hierarchy = TaskHierarchy(root_task_id=task.id)
        task_hierarchy.add_task(task)
        
        # 执行分配
        start_time = time.time()
        try:
            result = self.allocation_system.allocate_functions(task_hierarchy)
            response_time = time.time() - start_time
            metrics.record_response_time(response_time)
            
            # 验证结果
            self.assertIsNotNone(result)
            
        except Exception as e:
            self.skipTest(f"单任务分配测试跳过: {e}")
        
        metrics.record_system_metrics()
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"单任务分配性能:")
        print(f"  响应时间: {summary.get('avg_response_time', 0):.3f}秒")
        print(f"  内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        print(f"  CPU使用: {summary.get('avg_cpu_percent', 0):.1f}%")
        
        # 性能基准：单任务分配应在2秒内完成
        if summary.get('avg_response_time', 0) > 0:
            self.assertLess(summary['avg_response_time'], 2.0, "单任务分配响应时间超过2秒")
    
    def test_multiple_tasks_allocation_performance(self):
        """测试多任务分配性能"""
        print("\n测试多任务分配性能...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 创建多个测试任务
        task_count = 5
        for i in range(task_count):
            task = self.create_test_task(i)
            task_hierarchy = TaskHierarchy(root_task_id=task.id)
            task_hierarchy.add_task(task)
            
            start_time = time.time()
            try:
                result = self.allocation_system.allocate_functions(task_hierarchy)
                response_time = time.time() - start_time
                metrics.record_response_time(response_time)
                
                # 记录系统指标
                if i % 2 == 0:  # 每隔一个任务记录一次系统指标
                    metrics.record_system_metrics()
                
            except Exception as e:
                print(f"任务{i}分配失败: {e}")
                continue
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"多任务分配性能 ({task_count}个任务):")
        print(f"  平均响应时间: {summary.get('avg_response_time', 0):.3f}秒")
        print(f"  最大响应时间: {summary.get('max_response_time', 0):.3f}秒")
        print(f"  总处理时间: {summary.get('total_time', 0):.3f}秒")
        print(f"  处理速度: {summary.get('requests_per_second', 0):.2f}任务/秒")
        print(f"  平均内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        print(f"  最大内存使用: {summary.get('max_memory_mb', 0):.1f}MB")
        
        # 性能基准：平均响应时间应在3秒内
        if summary.get('avg_response_time', 0) > 0:
            self.assertLess(summary['avg_response_time'], 3.0, "多任务平均响应时间超过3秒")
    
    def test_concurrent_allocation_performance(self):
        """测试并发分配性能"""
        print("\n测试并发分配性能...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        def allocate_task(task_id: int) -> float:
            """分配单个任务并返回响应时间"""
            task = self.create_test_task(task_id)
            task_hierarchy = TaskHierarchy(root_task_id=task.id)
            task_hierarchy.add_task(task)
            
            start_time = time.time()
            try:
                # 为每个线程创建独立的分配系统实例
                allocation_system = HumanMachineAllocationSystem()
                result = allocation_system.allocate_functions(task_hierarchy)
                return time.time() - start_time
            except Exception as e:
                print(f"并发任务{task_id}失败: {e}")
                return -1
        
        # 使用线程池执行并发任务
        concurrent_tasks = 3
        with ThreadPoolExecutor(max_workers=concurrent_tasks) as executor:
            futures = [executor.submit(allocate_task, i) for i in range(concurrent_tasks)]
            
            for future in as_completed(futures):
                response_time = future.result()
                if response_time > 0:
                    metrics.record_response_time(response_time)
                
                # 记录系统指标
                metrics.record_system_metrics()
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"并发分配性能 ({concurrent_tasks}个并发任务):")
        print(f"  平均响应时间: {summary.get('avg_response_time', 0):.3f}秒")
        print(f"  最大响应时间: {summary.get('max_response_time', 0):.3f}秒")
        print(f"  总处理时间: {summary.get('total_time', 0):.3f}秒")
        print(f"  成功任务数: {summary.get('total_requests', 0)}")
        print(f"  平均内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        print(f"  最大内存使用: {summary.get('max_memory_mb', 0):.1f}MB")
        print(f"  平均CPU使用: {summary.get('avg_cpu_percent', 0):.1f}%")
        
        # 性能基准：并发处理不应显著增加响应时间
        if summary.get('avg_response_time', 0) > 0:
            self.assertLess(summary['avg_response_time'], 5.0, "并发分配平均响应时间超过5秒")
    
    def test_config_management_performance(self):
        """测试配置管理性能"""
        print("\n测试配置管理性能...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        config_manager = ConfigManager(self.temp_dir)
        
        # 测试配置保存性能
        operations = 10
        for i in range(operations):
            start_time = time.time()
            
            # 创建测试配置
            config = AllocationConfig()
            config.allocation_mode = AllocationMode.AUTOMATIC
            config.default_scheme_count = 5 + i
            
            # 保存配置
            success = config_manager.save_allocation_config(config)
            self.assertTrue(success)
            
            response_time = time.time() - start_time
            metrics.record_response_time(response_time)
            
            if i % 3 == 0:
                metrics.record_system_metrics()
        
        # 测试配置档案操作性能
        for i in range(5):
            start_time = time.time()
            
            success = config_manager.create_profile(
                f"性能测试配置_{i}",
                f"性能测试配置档案{i}"
            )
            self.assertTrue(success)
            
            response_time = time.time() - start_time
            metrics.record_response_time(response_time)
            metrics.record_system_metrics()
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"配置管理性能:")
        print(f"  平均操作时间: {summary.get('avg_response_time', 0):.3f}秒")
        print(f"  最大操作时间: {summary.get('max_response_time', 0):.3f}秒")
        print(f"  总操作数: {summary.get('total_requests', 0)}")
        print(f"  操作速度: {summary.get('requests_per_second', 0):.2f}操作/秒")
        print(f"  平均内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        
        # 性能基准：配置操作应在1秒内完成
        if summary.get('avg_response_time', 0) > 0:
            self.assertLess(summary['avg_response_time'], 1.0, "配置管理平均操作时间超过1秒")
    
    def test_system_startup_performance(self):
        """测试系统启动性能"""
        print("\n测试系统启动性能...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 测试系统管理器启动
        start_time = time.time()
        system_manager = HMDMSystemManager()
        startup_time = time.time() - start_time
        metrics.record_response_time(startup_time)
        metrics.record_system_metrics()
        
        # 测试配置管理器启动
        start_time = time.time()
        config_manager = system_manager.get_config_manager()
        config_startup_time = time.time() - start_time
        metrics.record_response_time(config_startup_time)
        metrics.record_system_metrics()
        
        # 测试人机分配系统启动
        start_time = time.time()
        allocation_system = HumanMachineAllocationSystem()
        allocation_startup_time = time.time() - start_time
        metrics.record_response_time(allocation_startup_time)
        metrics.record_system_metrics()
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"系统启动性能:")
        print(f"  系统管理器启动: {startup_time:.3f}秒")
        print(f"  配置管理器启动: {config_startup_time:.3f}秒")
        print(f"  人机分配系统启动: {allocation_startup_time:.3f}秒")
        print(f"  平均启动时间: {summary.get('avg_response_time', 0):.3f}秒")
        print(f"  内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        
        # 性能基准：系统启动应在5秒内完成
        self.assertLess(startup_time, 5.0, "系统管理器启动时间超过5秒")
        self.assertLess(allocation_startup_time, 3.0, "人机分配系统启动时间超过3秒")
    
    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        print("\n测试内存使用稳定性...")
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 执行多次操作，监控内存使用
        iterations = 20
        for i in range(iterations):
            # 创建和处理任务
            task = self.create_test_task(i)
            task_hierarchy = TaskHierarchy(root_task_id=task.id)
            task_hierarchy.add_task(task)
            
            try:
                result = self.allocation_system.allocate_functions(task_hierarchy)
            except Exception:
                pass  # 忽略错误，专注于内存监控
            
            # 记录内存使用
            metrics.record_system_metrics()
            
            # 短暂休息
            time.sleep(0.1)
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        print(f"内存使用稳定性 ({iterations}次迭代):")
        print(f"  平均内存使用: {summary.get('avg_memory_mb', 0):.1f}MB")
        print(f"  最大内存使用: {summary.get('max_memory_mb', 0):.1f}MB")
        print(f"  内存使用变化: {summary.get('max_memory_mb', 0) - summary.get('avg_memory_mb', 0):.1f}MB")
        
        # 检查内存泄漏：内存使用变化不应超过50MB
        memory_variation = summary.get('max_memory_mb', 0) - summary.get('avg_memory_mb', 0)
        self.assertLess(memory_variation, 50.0, f"内存使用变化过大: {memory_variation:.1f}MB")
    
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*60)
        print("HMDM系统性能基准报告")
        print("="*60)
        print("测试环境:")
        print(f"  Python版本: {os.sys.version}")
        print(f"  CPU核心数: {psutil.cpu_count()}")
        print(f"  总内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
        print(f"  可用内存: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f}GB")
        print("\n性能基准:")
        print("  - 单任务分配: < 2秒")
        print("  - 多任务平均分配: < 3秒")
        print("  - 并发分配: < 5秒")
        print("  - 配置操作: < 1秒")
        print("  - 系统启动: < 5秒")
        print("  - 内存使用变化: < 50MB")
        print("="*60)


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加性能测试
    suite.addTest(TestPerformanceBenchmark('test_system_startup_performance'))
    suite.addTest(TestPerformanceBenchmark('test_single_task_allocation_performance'))
    suite.addTest(TestPerformanceBenchmark('test_multiple_tasks_allocation_performance'))
    suite.addTest(TestPerformanceBenchmark('test_concurrent_allocation_performance'))
    suite.addTest(TestPerformanceBenchmark('test_config_management_performance'))
    suite.addTest(TestPerformanceBenchmark('test_memory_usage_stability'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成报告
    test_instance = TestPerformanceBenchmark()
    test_instance.generate_performance_report()
