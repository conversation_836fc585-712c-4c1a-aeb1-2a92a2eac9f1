"""
军事场景任务分解模板

本模块定义了针对联合ZZ指挥系统的专业军事场景任务分解模板，
包括态势分析、威胁计算、辅助决策等典型军事任务场景。
"""

from typing import Dict, List, Any
from ..models.task_models import ExecutorType


class MilitaryScenarioTemplates:
    """军事场景模板管理器"""
    
    @staticmethod
    def get_military_scenario_templates() -> Dict[str, List[Dict[str, Any]]]:
        """获取军事场景模板"""
        return {
            "态势分析": MilitaryScenarioTemplates._get_situation_analysis_template(),
            "威胁计算": MilitaryScenarioTemplates._get_threat_calculation_template(),
            "辅助决策": MilitaryScenarioTemplates._get_decision_support_template(),
            "作战指挥": MilitaryScenarioTemplates._get_combat_command_template(),
            "情报处理": MilitaryScenarioTemplates._get_intelligence_processing_template()
        }
    
    @staticmethod
    def _get_situation_analysis_template() -> List[Dict[str, Any]]:
        """态势分析场景模板"""
        return [
            {
                "name": "情报收集与处理",
                "description": "多源情报收集、融合和初步处理",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.7,
                    "importance": 0.9,
                    "urgency": 0.8,
                    "frequency": 0.9,
                    "duration": 25.0,
                    "error_rate": 0.03,
                    "workload": 0.6,
                    "military_priority": "高",
                    "security_level": "机密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.85
                },
                "military_attributes": {
                    "operational_domain": "信息域",
                    "force_type": "情报部队",
                    "equipment_dependency": 0.8,
                    "training_requirement": "专业",
                    "coordination_level": "联合"
                },
                "subtasks": [
                    {
                        "name": "多源情报融合",
                        "description": "融合雷达、光电、电子侦察等多源情报",
                        "executor_type": ExecutorType.MACHINE,
                        "military_function": "情报融合",
                        "technical_complexity": 0.9
                    },
                    {
                        "name": "情报分析处理",
                        "description": "对融合后的情报进行分析和处理",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "情报分析",
                        "technical_complexity": 0.7
                    },
                    {
                        "name": "情报质量评估",
                        "description": "评估情报的可靠性和时效性",
                        "executor_type": ExecutorType.HUMAN,
                        "military_function": "质量控制",
                        "technical_complexity": 0.6
                    }
                ]
            },
            {
                "name": "态势感知与理解",
                "description": "基于情报信息进行态势感知和理解",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.9,
                    "importance": 0.95,
                    "urgency": 0.9,
                    "frequency": 0.8,
                    "duration": 35.0,
                    "error_rate": 0.05,
                    "workload": 0.8,
                    "military_priority": "最高",
                    "security_level": "绝密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.9
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "指挥部队",
                    "equipment_dependency": 0.7,
                    "training_requirement": "高级",
                    "coordination_level": "战略"
                },
                "subtasks": [
                    {
                        "name": "敌情分析",
                        "description": "分析敌方兵力部署、作战意图和能力",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "敌情研判",
                        "technical_complexity": 0.8
                    },
                    {
                        "name": "我情掌握",
                        "description": "掌握我方兵力状态和作战能力",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "我情评估",
                        "technical_complexity": 0.6
                    },
                    {
                        "name": "环境评估",
                        "description": "评估作战环境和地理条件",
                        "executor_type": ExecutorType.MACHINE,
                        "military_function": "环境分析",
                        "technical_complexity": 0.7
                    }
                ]
            },
            {
                "name": "态势预测与预警",
                "description": "基于当前态势进行发展趋势预测和预警",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.8,
                    "importance": 0.9,
                    "urgency": 0.95,
                    "frequency": 0.6,
                    "duration": 20.0,
                    "error_rate": 0.06,
                    "workload": 0.7,
                    "military_priority": "高",
                    "security_level": "机密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.8
                },
                "military_attributes": {
                    "operational_domain": "信息域",
                    "force_type": "预警部队",
                    "equipment_dependency": 0.8,
                    "training_requirement": "专业",
                    "coordination_level": "战术"
                },
                "subtasks": [
                    {
                        "name": "趋势预测",
                        "description": "预测态势发展趋势和可能变化",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "态势预测",
                        "technical_complexity": 0.9
                    },
                    {
                        "name": "威胁评估",
                        "description": "评估潜在威胁和风险等级",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "威胁分析",
                        "technical_complexity": 0.8
                    },
                    {
                        "name": "预警发布",
                        "description": "及时发布态势预警信息",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "预警通报",
                        "technical_complexity": 0.5
                    }
                ]
            }
        ]
    
    @staticmethod
    def _get_threat_calculation_template() -> List[Dict[str, Any]]:
        """威胁计算场景模板"""
        return [
            {
                "name": "威胁识别",
                "description": "识别和分类各类威胁目标",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.8,
                    "importance": 0.95,
                    "urgency": 0.9,
                    "frequency": 0.7,
                    "duration": 30.0,
                    "error_rate": 0.04,
                    "workload": 0.7,
                    "military_priority": "最高",
                    "security_level": "机密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.9
                },
                "military_attributes": {
                    "operational_domain": "物理域",
                    "force_type": "侦察部队",
                    "equipment_dependency": 0.9,
                    "training_requirement": "专业",
                    "coordination_level": "战术"
                },
                "subtasks": [
                    {
                        "name": "目标检测识别",
                        "description": "检测和识别潜在威胁目标",
                        "executor_type": ExecutorType.MACHINE,
                        "military_function": "目标识别",
                        "technical_complexity": 0.8
                    },
                    {
                        "name": "威胁分类判断",
                        "description": "对识别的威胁进行分类和判断",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "威胁分类",
                        "technical_complexity": 0.7
                    },
                    {
                        "name": "意图推断分析",
                        "description": "推断威胁目标的作战意图",
                        "executor_type": ExecutorType.HUMAN,
                        "military_function": "意图分析",
                        "technical_complexity": 0.9
                    }
                ]
            },
            {
                "name": "威胁评估",
                "description": "评估威胁等级和影响范围",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.9,
                    "importance": 0.95,
                    "urgency": 0.8,
                    "frequency": 0.6,
                    "duration": 45.0,
                    "error_rate": 0.05,
                    "workload": 0.8,
                    "military_priority": "最高",
                    "security_level": "绝密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.85
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "分析部队",
                    "equipment_dependency": 0.6,
                    "training_requirement": "高级",
                    "coordination_level": "战略"
                },
                "subtasks": [
                    {
                        "name": "威胁等级评定",
                        "description": "评定威胁的等级和严重程度",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "威胁评级",
                        "technical_complexity": 0.8
                    },
                    {
                        "name": "影响范围分析",
                        "description": "分析威胁可能的影响范围和程度",
                        "executor_type": ExecutorType.MACHINE,
                        "military_function": "影响评估",
                        "technical_complexity": 0.7
                    },
                    {
                        "name": "时效性评估",
                        "description": "评估威胁的时间紧迫性",
                        "executor_type": ExecutorType.HUMAN,
                        "military_function": "时效分析",
                        "technical_complexity": 0.6
                    }
                ]
            },
            {
                "name": "对策生成",
                "description": "生成应对威胁的具体对策方案",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.8,
                    "importance": 0.9,
                    "urgency": 0.85,
                    "frequency": 0.5,
                    "duration": 40.0,
                    "error_rate": 0.06,
                    "workload": 0.75,
                    "military_priority": "高",
                    "security_level": "机密",
                    "real_time_requirement": False,
                    "combat_effectiveness": 0.8
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "作战部队",
                    "equipment_dependency": 0.5,
                    "training_requirement": "高级",
                    "coordination_level": "联合"
                },
                "subtasks": [
                    {
                        "name": "应对方案制定",
                        "description": "制定针对性的应对方案",
                        "executor_type": ExecutorType.HUMAN,
                        "military_function": "方案制定",
                        "technical_complexity": 0.8
                    },
                    {
                        "name": "资源需求分析",
                        "description": "分析执行方案所需的资源",
                        "executor_type": ExecutorType.HUMAN_MACHINE,
                        "military_function": "资源评估",
                        "technical_complexity": 0.6
                    },
                    {
                        "name": "效果预估",
                        "description": "预估对策方案的执行效果",
                        "executor_type": ExecutorType.MACHINE,
                        "military_function": "效果评估",
                        "technical_complexity": 0.7
                    }
                ]
            }
        ]

    @staticmethod
    def _get_decision_support_template() -> List[Dict[str, Any]]:
        """辅助决策场景模板"""
        return [
            {
                "name": "决策问题分析",
                "description": "分析和定义决策问题的核心要素",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.7,
                    "importance": 0.9,
                    "urgency": 0.7,
                    "frequency": 0.6,
                    "duration": 30.0,
                    "error_rate": 0.05,
                    "workload": 0.6,
                    "military_priority": "高",
                    "security_level": "机密",
                    "real_time_requirement": False,
                    "combat_effectiveness": 0.8
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "参谋部队",
                    "equipment_dependency": 0.4,
                    "training_requirement": "高级",
                    "coordination_level": "战略"
                }
            },
            {
                "name": "方案生成评估",
                "description": "生成备选方案并进行初步评估",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.8,
                    "importance": 0.85,
                    "urgency": 0.6,
                    "frequency": 0.5,
                    "duration": 60.0,
                    "error_rate": 0.06,
                    "workload": 0.7,
                    "military_priority": "中",
                    "security_level": "机密",
                    "real_time_requirement": False,
                    "combat_effectiveness": 0.75
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "参谋部队",
                    "equipment_dependency": 0.6,
                    "training_requirement": "专业",
                    "coordination_level": "战术"
                }
            },
            {
                "name": "决策支持",
                "description": "提供决策支持和推荐方案",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.9,
                    "importance": 0.95,
                    "urgency": 0.8,
                    "frequency": 0.4,
                    "duration": 45.0,
                    "error_rate": 0.04,
                    "workload": 0.8,
                    "military_priority": "最高",
                    "security_level": "绝密",
                    "real_time_requirement": False,
                    "combat_effectiveness": 0.9
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "指挥部队",
                    "equipment_dependency": 0.7,
                    "training_requirement": "高级",
                    "coordination_level": "战略"
                }
            }
        ]

    @staticmethod
    def _get_combat_command_template() -> List[Dict[str, Any]]:
        """作战指挥场景模板"""
        return [
            {
                "name": "作战筹划",
                "description": "制定作战计划和部署方案",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.9,
                    "importance": 0.95,
                    "urgency": 0.7,
                    "frequency": 0.3,
                    "duration": 120.0,
                    "error_rate": 0.03,
                    "workload": 0.8,
                    "military_priority": "最高",
                    "security_level": "绝密",
                    "real_time_requirement": False,
                    "combat_effectiveness": 0.95
                },
                "military_attributes": {
                    "operational_domain": "认知域",
                    "force_type": "指挥部队",
                    "equipment_dependency": 0.6,
                    "training_requirement": "高级",
                    "coordination_level": "战略"
                }
            }
        ]

    @staticmethod
    def _get_intelligence_processing_template() -> List[Dict[str, Any]]:
        """情报处理场景模板"""
        return [
            {
                "name": "情报搜集",
                "description": "多渠道搜集各类情报信息",
                "executor_type": ExecutorType.HUMAN_MACHINE,
                "attributes": {
                    "complexity": 0.6,
                    "importance": 0.85,
                    "urgency": 0.8,
                    "frequency": 0.9,
                    "duration": 40.0,
                    "error_rate": 0.05,
                    "workload": 0.5,
                    "military_priority": "高",
                    "security_level": "机密",
                    "real_time_requirement": True,
                    "combat_effectiveness": 0.8
                },
                "military_attributes": {
                    "operational_domain": "信息域",
                    "force_type": "情报部队",
                    "equipment_dependency": 0.9,
                    "training_requirement": "专业",
                    "coordination_level": "战术"
                }
            }
        ]
