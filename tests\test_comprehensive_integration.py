"""
综合集成测试套件

测试HMDM系统各模块间的协同工作和端到端功能
"""

import pytest
import tempfile
import os
import time
from datetime import datetime, timedelta

from src.hmdm.core.system_manager import HMDMSystemManager
from src.hmdm.analysis.situation_awareness import SituationAwarenessEngine
from src.hmdm.decision.military_decision_support import MilitaryDecisionSupport
from src.hmdm.training.military_training import MilitaryTrainingSystem
from src.hmdm.knowledge.military_knowledge_base import MilitaryKnowledgeBase
from src.hmdm.simulation.military_simulation import MilitarySimulationEngine
from src.hmdm.security.enhanced_security import EnhancedSecurityManager
from src.hmdm.ml.intelligent_prediction_engine import IntelligentPredictionEngine
from src.hmdm.monitoring.performance_monitor import PerformanceMonitor
from src.hmdm.optimization.cache_manager import CacheManager
from src.hmdm.scenarios.decision_support_scenarios import DecisionSupportScenarios
from src.hmdm.security.military_security import SecurityLevel


class TestComprehensiveIntegration:
    """综合集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 初始化系统管理器
        self.system_manager = HMDMSystemManager()
        
        # 初始化各个子系统
        self.situation_system = SituationAwarenessEngine()
        self.decision_system = MilitaryDecisionSupport()
        self.training_system = MilitaryTrainingSystem()
        self.knowledge_base = MilitaryKnowledgeBase()
        self.simulation_system = MilitarySimulationEngine()
        
        # 初始化安全系统
        security_config = {
            'audit_file': os.path.join(self.temp_dir, 'audit.log'),
            'encryption_key_file': os.path.join(self.temp_dir, 'encryption.key'),
            'default_admin_username': 'test_admin',
            'default_admin_password': 'TestAdmin123!'
        }
        self.security_manager = EnhancedSecurityManager(security_config)
        
        # 初始化机器学习系统
        self.prediction_engine = IntelligentPredictionEngine(
            os.path.join(self.temp_dir, 'models')
        )
        
        # 初始化监控系统
        monitor_config = {
            'monitoring_enabled': True,
            'collection_interval': 1
        }
        self.performance_monitor = PerformanceMonitor(monitor_config)
        
        # 初始化缓存系统
        cache_config = {
            'max_memory_size': 100,
            'enable_disk_cache': True,
            'disk_cache_dir': os.path.join(self.temp_dir, 'cache')
        }
        self.cache_manager = CacheManager(cache_config, self.performance_monitor)
        
        # 初始化场景系统
        self.scenario_system = DecisionSupportScenarios()
    
    def teardown_method(self):
        """测试后清理"""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring_service()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_system_initialization(self):
        """测试系统初始化"""
        assert self.system_manager is not None
        assert self.situation_system is not None
        assert self.decision_system is not None
        assert self.training_system is not None
        assert self.knowledge_base is not None
        assert self.simulation_system is not None
        assert self.security_manager is not None
        assert self.prediction_engine is not None
        assert self.performance_monitor is not None
        assert self.cache_manager is not None
        assert self.scenario_system is not None
    
    def test_security_integration(self):
        """测试安全系统集成"""
        # 创建测试用户
        user = self.security_manager.create_user(
            username='integration_user',
            password='IntegrationTest123!',
            security_level=SecurityLevel.SECRET
        )
        
        assert user is not None
        
        # 用户认证
        token = self.security_manager.authenticate_user(
            username='integration_user',
            password='IntegrationTest123!',
            ip_address='127.0.0.1'
        )
        
        assert token is not None
        
        # 验证会话
        authenticated_user = self.security_manager.validate_session(token)
        assert authenticated_user is not None
        assert authenticated_user.username == 'integration_user'
        
        # 检查审计日志
        audit_events = self.security_manager.get_audit_events(limit=10)
        assert len(audit_events) >= 2  # 至少有创建用户和登录事件
    
    def test_knowledge_base_integration(self):
        """测试知识库集成"""
        # 搜索知识（使用默认知识）
        results = self.knowledge_base.search_knowledge("战术")
        assert isinstance(results, list)

        # 获取知识库统计
        stats = self.knowledge_base.get_knowledge_statistics()
        assert 'total_items' in stats
        assert stats['total_items'] >= 0

        # 测试推理功能
        self.knowledge_base.add_fact("敌方兵力优势")
        inferred = self.knowledge_base.infer_knowledge()
        assert isinstance(inferred, list)
    
    def test_situation_awareness_integration(self):
        """测试态势感知集成"""
        # 测试态势感知引擎基本功能
        assert self.situation_system is not None

        # 测试威胁评估功能（如果存在）
        if hasattr(self.situation_system, 'assess_threat'):
            threat_data = {
                'location': {'lat': 39.9042, 'lon': 116.4074},
                'threat_level': 3,
                'unit_status': 'active'
            }
            assessment = self.situation_system.assess_threat(threat_data)
            assert assessment is not None

        # 测试基本统计功能
        if hasattr(self.situation_system, 'get_statistics'):
            stats = self.situation_system.get_statistics()
            assert isinstance(stats, dict)
    
    def test_decision_support_integration(self):
        """测试决策支持集成"""
        # 测试决策支持系统基本功能
        assert self.decision_system is not None

        # 测试决策统计功能（如果存在）
        if hasattr(self.decision_system, 'get_decision_statistics'):
            stats = self.decision_system.get_decision_statistics()
            assert isinstance(stats, dict)

        # 测试决策历史功能（如果存在）
        if hasattr(self.decision_system, 'get_decision_history'):
            history = self.decision_system.get_decision_history(limit=10)
            assert isinstance(history, list)
    
    def test_training_system_integration(self):
        """测试训练系统集成"""
        # 测试训练系统基本功能
        assert self.training_system is not None

        # 测试训练统计功能（如果存在）
        if hasattr(self.training_system, 'get_training_statistics'):
            stats = self.training_system.get_training_statistics()
            assert isinstance(stats, dict)

        # 测试训练历史功能（如果存在）
        if hasattr(self.training_system, 'get_training_history'):
            history = self.training_system.get_training_history(limit=10)
            assert isinstance(history, list)
    
    def test_simulation_integration(self):
        """测试仿真系统集成"""
        # 测试仿真系统基本功能
        assert self.simulation_system is not None

        # 测试仿真统计功能（如果存在）
        if hasattr(self.simulation_system, 'get_simulation_statistics'):
            stats = self.simulation_system.get_simulation_statistics()
            assert isinstance(stats, dict)

        # 测试仿真历史功能（如果存在）
        if hasattr(self.simulation_system, 'get_simulation_history'):
            history = self.simulation_system.get_simulation_history(limit=10)
            assert isinstance(history, list)
    
    def test_ml_prediction_integration(self):
        """测试机器学习预测集成"""
        # 创建预测模型配置
        from src.hmdm.ml.intelligent_prediction_engine import ModelConfig, ModelType, PredictionTask
        
        model_config = ModelConfig(
            model_id="integration_test_model",
            model_type=ModelType.CLASSIFICATION,
            task_type=PredictionTask.THREAT_ASSESSMENT,
            algorithm="random_forest",
            parameters={"n_estimators": 5, "random_state": 42},
            feature_columns=["threat_level", "unit_count"],
            target_column="risk_category"
        )
        
        # 创建模型
        success = self.prediction_engine.create_model(model_config)
        assert success is True
        
        # 创建训练数据
        import pandas as pd
        import numpy as np
        
        np.random.seed(42)
        training_data = pd.DataFrame({
            'threat_level': np.random.randint(1, 6, 50),
            'unit_count': np.random.randint(1, 11, 50),
            'risk_category': np.random.choice([0, 1], 50)
        })
        
        # 训练模型
        train_success = self.prediction_engine.train_model("integration_test_model", training_data)
        assert train_success is True
        
        # 进行预测
        test_input = {'threat_level': 3, 'unit_count': 5}
        prediction_result = self.prediction_engine.predict("integration_test_model", test_input)
        
        assert prediction_result is not None
        assert prediction_result.prediction in [0, 1]
        assert 0 <= prediction_result.confidence <= 1
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        # 记录一些性能指标
        self.performance_monitor.record_metric("integration_test_metric", 75.0)
        self.performance_monitor.record_request_time(0.5, "/api/integration_test")
        self.performance_monitor.record_error("test_error", "/api/test")
        
        # 获取当前指标
        current_metrics = self.performance_monitor.get_current_metrics()
        assert "integration_test_metric" in current_metrics
        assert "response_time" in current_metrics
        assert "error_count" in current_metrics
        
        # 获取性能摘要
        summary = self.performance_monitor.get_performance_summary()
        assert 'system_resources' in summary
        assert 'performance_metrics' in summary
        
        # 检查系统资源监控
        resources = summary['system_resources']
        assert 'cpu_percent' in resources
        assert 'memory_percent' in resources
    
    def test_cache_integration(self):
        """测试缓存集成"""
        # 测试缓存存取
        cache_key = "integration_test_key"
        cache_value = {"test": "integration_data", "timestamp": datetime.now().isoformat()}
        
        # 存储到缓存
        success = self.cache_manager.put(cache_key, cache_value, ttl=60)
        assert success is True
        
        # 从缓存获取
        retrieved_value = self.cache_manager.get(cache_key)
        assert retrieved_value is not None
        assert retrieved_value["test"] == "integration_data"
        
        # 测试缓存装饰器
        call_count = 0
        
        @self.cache_manager.cache_decorator(ttl=30)
        def expensive_integration_function(x, y):
            nonlocal call_count
            call_count += 1
            return x * y + call_count
        
        # 第一次调用
        result1 = expensive_integration_function(3, 4)
        assert call_count == 1
        
        # 第二次调用相同参数，应该从缓存获取
        result2 = expensive_integration_function(3, 4)
        assert result2 == result1
        assert call_count == 1  # 没有增加
        
        # 获取缓存统计
        cache_stats = self.cache_manager.get_stats()
        assert 'hit_rate' in cache_stats
        assert 'total_requests' in cache_stats
    
    def test_scenario_integration(self):
        """测试场景系统集成"""
        # 测试场景系统基本功能
        assert self.scenario_system is not None

        # 测试场景统计功能（如果存在）
        if hasattr(self.scenario_system, 'get_scenario_statistics'):
            stats = self.scenario_system.get_scenario_statistics()
            assert isinstance(stats, dict)

        # 测试场景模板功能（如果存在）
        if hasattr(self.scenario_system, 'get_scenario_templates'):
            templates = self.scenario_system.get_scenario_templates()
            assert isinstance(templates, list)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 1. 安全认证
        token = self.security_manager.authenticate_user(
            username='test_admin',
            password='TestAdmin123!'
        )
        assert token is not None

        # 2. 测试缓存功能
        test_key = "workflow_test"
        test_value = {"timestamp": datetime.now().isoformat(), "test": True}

        cache_success = self.cache_manager.put(test_key, test_value)
        assert cache_success is True

        cached_value = self.cache_manager.get(test_key)
        assert cached_value is not None
        assert cached_value["test"] is True

        # 3. 记录性能指标
        self.performance_monitor.record_metric("workflow_completion", 1.0)

        # 4. 验证审计日志
        audit_events = self.security_manager.get_audit_events(limit=20)
        assert len(audit_events) > 0

        # 5. 获取系统整体状态
        system_status = {
            'security_stats': self.security_manager.get_security_statistics(),
            'performance_summary': self.performance_monitor.get_performance_summary(),
            'cache_stats': self.cache_manager.get_stats(),
            'knowledge_stats': self.knowledge_base.get_knowledge_statistics()
        }

        # 验证系统状态
        assert 'users' in system_status['security_stats']
        assert 'system_resources' in system_status['performance_summary']
        assert 'hit_rate' in system_status['cache_stats']
        assert 'total_items' in system_status['knowledge_stats']

        # 整个工作流执行成功
        return True
