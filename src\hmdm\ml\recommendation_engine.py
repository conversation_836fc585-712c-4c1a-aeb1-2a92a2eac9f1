"""
智能推荐引擎

提供基于机器学习的智能推荐功能，包括：
- 决策方案推荐
- 资源配置推荐
- 训练计划推荐
- 战术策略推荐
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from collections import defaultdict

# 机器学习库
try:
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from ..security.military_security import SecurityLevel


class RecommendationType(Enum):
    """推荐类型"""
    DECISION = "决策推荐"
    RESOURCE = "资源推荐"
    TRAINING = "训练推荐"
    TACTICAL = "战术推荐"
    EQUIPMENT = "装备推荐"


class RecommendationMethod(Enum):
    """推荐方法"""
    COLLABORATIVE_FILTERING = "协同过滤"
    CONTENT_BASED = "基于内容"
    HYBRID = "混合推荐"
    KNOWLEDGE_BASED = "基于知识"


@dataclass
class RecommendationItem:
    """推荐项目"""
    item_id: str
    title: str
    description: str
    category: str
    features: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    security_level: SecurityLevel = SecurityLevel.INTERNAL
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_id': self.item_id,
            'title': self.title,
            'description': self.description,
            'category': self.category,
            'features': self.features,
            'metadata': self.metadata,
            'security_level': self.security_level.value
        }


@dataclass
class RecommendationResult:
    """推荐结果"""
    item: RecommendationItem
    score: float
    confidence: float
    reason: str
    method: RecommendationMethod
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item': self.item.to_dict(),
            'score': self.score,
            'confidence': self.confidence,
            'reason': self.reason,
            'method': self.method.value,
            'timestamp': self.timestamp.isoformat()
        }


class IntelligentRecommendationEngine:
    """智能推荐引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 推荐项目库
        self.items: Dict[str, RecommendationItem] = {}
        
        # 用户行为历史
        self.user_interactions: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # 推荐历史
        self.recommendation_history: List[Dict[str, Any]] = []
        
        # 特征向量化器
        if SKLEARN_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(max_features=1000)
            self.item_features_matrix = None
        
        # 聚类模型
        self.kmeans_model = None
        self.item_clusters = {}
        
        # 初始化推荐项目
        self._initialize_recommendation_items()
        
        self.logger.info("智能推荐引擎初始化完成")
    
    def add_item(self, item: RecommendationItem) -> bool:
        """添加推荐项目"""
        try:
            self.items[item.item_id] = item
            
            # 重新计算特征矩阵
            self._update_feature_matrix()
            
            self.logger.info(f"推荐项目已添加: {item.item_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加推荐项目失败: {e}")
            return False
    
    def record_interaction(self, user_id: str, item_id: str, 
                          interaction_type: str, rating: Optional[float] = None) -> bool:
        """记录用户交互"""
        try:
            interaction = {
                'item_id': item_id,
                'interaction_type': interaction_type,  # view, like, dislike, use, etc.
                'rating': rating,
                'timestamp': datetime.now().isoformat()
            }
            
            self.user_interactions[user_id].append(interaction)
            
            self.logger.info(f"用户交互已记录: {user_id} -> {item_id} ({interaction_type})")
            return True
            
        except Exception as e:
            self.logger.error(f"记录用户交互失败: {e}")
            return False
    
    def get_recommendations(self, user_id: str, recommendation_type: RecommendationType,
                          context: Dict[str, Any] = None, top_k: int = 5) -> List[RecommendationResult]:
        """获取推荐"""
        try:
            context = context or {}
            
            # 根据推荐类型过滤项目
            filtered_items = self._filter_items_by_type(recommendation_type)
            
            if not filtered_items:
                return []
            
            # 获取用户历史交互
            user_history = self.user_interactions.get(user_id, [])
            
            # 选择推荐方法
            if len(user_history) >= 5:
                # 有足够历史数据，使用协同过滤
                recommendations = self._collaborative_filtering(user_id, filtered_items, context, top_k)
            else:
                # 历史数据不足，使用基于内容的推荐
                recommendations = self._content_based_recommendation(user_id, filtered_items, context, top_k)
            
            # 记录推荐历史
            self._record_recommendation_history(user_id, recommendation_type, recommendations)
            
            self.logger.info(f"为用户 {user_id} 生成了 {len(recommendations)} 个推荐")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"获取推荐失败: {e}")
            return []
    
    def get_similar_items(self, item_id: str, top_k: int = 5) -> List[Tuple[RecommendationItem, float]]:
        """获取相似项目"""
        try:
            if item_id not in self.items or not SKLEARN_AVAILABLE:
                return []
            
            if self.item_features_matrix is None:
                self._update_feature_matrix()
            
            # 找到目标项目的索引
            item_ids = list(self.items.keys())
            if item_id not in item_ids:
                return []
            
            target_index = item_ids.index(item_id)
            target_vector = self.item_features_matrix[target_index].reshape(1, -1)
            
            # 计算相似度
            similarities = cosine_similarity(target_vector, self.item_features_matrix).flatten()
            
            # 排序并返回top_k（排除自身）
            similar_indices = np.argsort(similarities)[::-1][1:top_k+1]
            
            similar_items = []
            for idx in similar_indices:
                similar_item_id = item_ids[idx]
                similarity_score = similarities[idx]
                similar_items.append((self.items[similar_item_id], float(similarity_score)))
            
            return similar_items
            
        except Exception as e:
            self.logger.error(f"获取相似项目失败: {e}")
            return []
    
    def get_recommendation_statistics(self) -> Dict[str, Any]:
        """获取推荐统计"""
        try:
            total_items = len(self.items)
            total_users = len(self.user_interactions)
            total_interactions = sum(len(interactions) for interactions in self.user_interactions.values())
            total_recommendations = len(self.recommendation_history)
            
            # 统计推荐类型分布
            type_distribution = defaultdict(int)
            for rec in self.recommendation_history:
                type_distribution[rec['type']] += 1
            
            # 统计项目类别分布
            category_distribution = defaultdict(int)
            for item in self.items.values():
                category_distribution[item.category] += 1
            
            return {
                'total_items': total_items,
                'total_users': total_users,
                'total_interactions': total_interactions,
                'total_recommendations': total_recommendations,
                'recommendation_type_distribution': dict(type_distribution),
                'item_category_distribution': dict(category_distribution),
                'average_interactions_per_user': total_interactions / max(total_users, 1)
            }
            
        except Exception as e:
            self.logger.error(f"获取推荐统计失败: {e}")
            return {}
    
    def _filter_items_by_type(self, recommendation_type: RecommendationType) -> Dict[str, RecommendationItem]:
        """根据类型过滤项目"""
        type_mapping = {
            RecommendationType.DECISION: ['decision', 'strategy', 'plan'],
            RecommendationType.RESOURCE: ['resource', 'equipment', 'personnel'],
            RecommendationType.TRAINING: ['training', 'exercise', 'drill'],
            RecommendationType.TACTICAL: ['tactical', 'combat', 'operation'],
            RecommendationType.EQUIPMENT: ['equipment', 'weapon', 'vehicle']
        }
        
        relevant_categories = type_mapping.get(recommendation_type, [])
        
        filtered_items = {}
        for item_id, item in self.items.items():
            if any(cat in item.category.lower() for cat in relevant_categories):
                filtered_items[item_id] = item
        
        return filtered_items
    
    def _collaborative_filtering(self, user_id: str, items: Dict[str, RecommendationItem],
                               context: Dict[str, Any], top_k: int) -> List[RecommendationResult]:
        """协同过滤推荐"""
        try:
            recommendations = []
            user_history = self.user_interactions[user_id]
            
            # 获取用户喜欢的项目
            liked_items = set()
            for interaction in user_history:
                if interaction['interaction_type'] in ['like', 'use'] or (interaction.get('rating', 0) >= 4):
                    liked_items.add(interaction['item_id'])
            
            # 找到相似用户
            similar_users = self._find_similar_users(user_id)
            
            # 基于相似用户的偏好推荐
            item_scores = defaultdict(float)
            for similar_user_id, similarity in similar_users[:10]:  # 取前10个相似用户
                similar_user_history = self.user_interactions[similar_user_id]
                
                for interaction in similar_user_history:
                    item_id = interaction['item_id']
                    if item_id in items and item_id not in liked_items:
                        score = similarity
                        if interaction.get('rating'):
                            score *= interaction['rating'] / 5.0
                        item_scores[item_id] += score
            
            # 排序并创建推荐结果
            sorted_items = sorted(item_scores.items(), key=lambda x: x[1], reverse=True)
            
            for item_id, score in sorted_items[:top_k]:
                if item_id in items:
                    recommendation = RecommendationResult(
                        item=items[item_id],
                        score=float(score),
                        confidence=min(0.9, score / max(item_scores.values()) if item_scores else 0.5),
                        reason=f"基于相似用户的偏好推荐",
                        method=RecommendationMethod.COLLABORATIVE_FILTERING
                    )
                    recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"协同过滤推荐失败: {e}")
            return []
    
    def _content_based_recommendation(self, user_id: str, items: Dict[str, RecommendationItem],
                                    context: Dict[str, Any], top_k: int) -> List[RecommendationResult]:
        """基于内容的推荐"""
        try:
            recommendations = []
            
            # 获取用户偏好特征
            user_preferences = self._get_user_preferences(user_id)
            
            # 计算项目得分
            item_scores = []
            for item_id, item in items.items():
                score = self._calculate_content_score(item, user_preferences, context)
                item_scores.append((item_id, score))
            
            # 排序并创建推荐结果
            item_scores.sort(key=lambda x: x[1], reverse=True)
            
            for item_id, score in item_scores[:top_k]:
                recommendation = RecommendationResult(
                    item=items[item_id],
                    score=float(score),
                    confidence=min(0.8, score),
                    reason=f"基于内容特征匹配",
                    method=RecommendationMethod.CONTENT_BASED
                )
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"基于内容的推荐失败: {e}")
            return []

    def _find_similar_users(self, user_id: str, top_k: int = 10) -> List[Tuple[str, float]]:
        """找到相似用户"""
        try:
            target_user_history = self.user_interactions[user_id]
            target_items = set(interaction['item_id'] for interaction in target_user_history)

            if not target_items:
                return []

            similar_users = []

            for other_user_id, other_history in self.user_interactions.items():
                if other_user_id == user_id:
                    continue

                other_items = set(interaction['item_id'] for interaction in other_history)

                # 计算Jaccard相似度
                intersection = len(target_items.intersection(other_items))
                union = len(target_items.union(other_items))

                if union > 0:
                    similarity = intersection / union
                    similar_users.append((other_user_id, similarity))

            # 排序并返回top_k
            similar_users.sort(key=lambda x: x[1], reverse=True)
            return similar_users[:top_k]

        except Exception as e:
            self.logger.error(f"查找相似用户失败: {e}")
            return []

    def _get_user_preferences(self, user_id: str) -> Dict[str, float]:
        """获取用户偏好"""
        try:
            preferences = defaultdict(float)
            user_history = self.user_interactions[user_id]

            for interaction in user_history:
                item_id = interaction['item_id']
                if item_id in self.items:
                    item = self.items[item_id]

                    # 根据交互类型给予不同权重
                    weight = 1.0
                    if interaction['interaction_type'] == 'like':
                        weight = 2.0
                    elif interaction['interaction_type'] == 'dislike':
                        weight = -1.0
                    elif interaction['interaction_type'] == 'use':
                        weight = 1.5

                    # 如果有评分，使用评分
                    if interaction.get('rating'):
                        weight = interaction['rating'] / 5.0

                    # 更新类别偏好
                    preferences[item.category] += weight

                    # 更新特征偏好
                    for feature, value in item.features.items():
                        if isinstance(value, (int, float)):
                            preferences[f"feature_{feature}"] += weight * value
                        elif isinstance(value, str):
                            preferences[f"feature_{feature}_{value}"] += weight

            return dict(preferences)

        except Exception as e:
            self.logger.error(f"获取用户偏好失败: {e}")
            return {}

    def _calculate_content_score(self, item: RecommendationItem,
                               user_preferences: Dict[str, float],
                               context: Dict[str, Any]) -> float:
        """计算基于内容的得分"""
        try:
            score = 0.0

            # 类别匹配得分
            category_score = user_preferences.get(item.category, 0.0)
            score += category_score * 0.3

            # 特征匹配得分
            feature_score = 0.0
            for feature, value in item.features.items():
                if isinstance(value, (int, float)):
                    pref_key = f"feature_{feature}"
                    if pref_key in user_preferences:
                        feature_score += user_preferences[pref_key] * value * 0.1
                elif isinstance(value, str):
                    pref_key = f"feature_{feature}_{value}"
                    if pref_key in user_preferences:
                        feature_score += user_preferences[pref_key] * 0.2

            score += feature_score * 0.4

            # 上下文匹配得分
            context_score = self._calculate_context_score(item, context)
            score += context_score * 0.3

            # 归一化到0-1范围
            return max(0.0, min(1.0, score))

        except Exception as e:
            self.logger.error(f"计算内容得分失败: {e}")
            return 0.0

    def _calculate_context_score(self, item: RecommendationItem, context: Dict[str, Any]) -> float:
        """计算上下文得分"""
        try:
            if not context:
                return 0.5  # 默认得分

            score = 0.0

            # 紧急程度匹配
            if 'urgency' in context and 'urgency' in item.features:
                urgency_diff = abs(context['urgency'] - item.features['urgency'])
                score += (1.0 - urgency_diff / 10.0) * 0.3

            # 资源需求匹配
            if 'resource_level' in context and 'resource_requirement' in item.features:
                resource_match = min(context['resource_level'], item.features['resource_requirement'])
                score += resource_match / max(context['resource_level'], item.features['resource_requirement']) * 0.3

            # 时间约束匹配
            if 'time_constraint' in context and 'duration' in item.features:
                if item.features['duration'] <= context['time_constraint']:
                    score += 0.4
                else:
                    score += max(0.0, 0.4 - (item.features['duration'] - context['time_constraint']) * 0.1)

            return max(0.0, min(1.0, score))

        except Exception as e:
            self.logger.error(f"计算上下文得分失败: {e}")
            return 0.5

    def _update_feature_matrix(self):
        """更新特征矩阵"""
        try:
            if not SKLEARN_AVAILABLE or not self.items:
                return

            # 构建文本特征
            texts = []
            for item in self.items.values():
                text = f"{item.title} {item.description} {item.category}"
                texts.append(text)

            # TF-IDF向量化
            self.item_features_matrix = self.tfidf_vectorizer.fit_transform(texts).toarray()

            # 聚类分析
            if len(self.items) >= 3:
                n_clusters = min(5, len(self.items) // 2)
                self.kmeans_model = KMeans(n_clusters=n_clusters, random_state=42)
                cluster_labels = self.kmeans_model.fit_predict(self.item_features_matrix)

                # 更新项目聚类信息
                for i, (item_id, item) in enumerate(self.items.items()):
                    self.item_clusters[item_id] = int(cluster_labels[i])

            self.logger.info("特征矩阵已更新")

        except Exception as e:
            self.logger.error(f"更新特征矩阵失败: {e}")

    def _record_recommendation_history(self, user_id: str, recommendation_type: RecommendationType,
                                     recommendations: List[RecommendationResult]):
        """记录推荐历史"""
        try:
            history_entry = {
                'user_id': user_id,
                'type': recommendation_type.value,
                'recommendations': [rec.to_dict() for rec in recommendations],
                'timestamp': datetime.now().isoformat()
            }

            self.recommendation_history.append(history_entry)

            # 限制历史记录数量
            if len(self.recommendation_history) > 1000:
                self.recommendation_history = self.recommendation_history[-1000:]

        except Exception as e:
            self.logger.error(f"记录推荐历史失败: {e}")

    def _initialize_recommendation_items(self):
        """初始化推荐项目"""
        try:
            # 决策方案项目
            decision_items = [
                RecommendationItem(
                    item_id="decision_001",
                    title="快速反应决策方案",
                    description="适用于紧急情况的快速决策流程",
                    category="decision",
                    features={
                        "urgency": 8,
                        "resource_requirement": 3,
                        "duration": 30,
                        "complexity": 5
                    }
                ),
                RecommendationItem(
                    item_id="decision_002",
                    title="综合评估决策方案",
                    description="全面分析各种因素的决策方案",
                    category="decision",
                    features={
                        "urgency": 4,
                        "resource_requirement": 7,
                        "duration": 120,
                        "complexity": 8
                    }
                )
            ]

            # 训练计划项目
            training_items = [
                RecommendationItem(
                    item_id="training_001",
                    title="基础战术训练",
                    description="基本战术技能训练计划",
                    category="training",
                    features={
                        "difficulty": 3,
                        "duration": 480,
                        "participants": 20,
                        "equipment_needed": 2
                    }
                ),
                RecommendationItem(
                    item_id="training_002",
                    title="高级作战演练",
                    description="复杂作战场景演练",
                    category="training",
                    features={
                        "difficulty": 8,
                        "duration": 1440,
                        "participants": 100,
                        "equipment_needed": 8
                    }
                )
            ]

            # 装备推荐项目
            equipment_items = [
                RecommendationItem(
                    item_id="equipment_001",
                    title="轻型装甲车",
                    description="适用于快速机动的轻型装甲车辆",
                    category="equipment",
                    features={
                        "mobility": 9,
                        "protection": 6,
                        "firepower": 7,
                        "cost": 5
                    }
                ),
                RecommendationItem(
                    item_id="equipment_002",
                    title="重型坦克",
                    description="高防护重型主战坦克",
                    category="equipment",
                    features={
                        "mobility": 4,
                        "protection": 10,
                        "firepower": 10,
                        "cost": 9
                    }
                )
            ]

            # 添加所有项目
            all_items = decision_items + training_items + equipment_items
            for item in all_items:
                self.items[item.item_id] = item

            # 更新特征矩阵
            self._update_feature_matrix()

            self.logger.info(f"已初始化 {len(all_items)} 个推荐项目")

        except Exception as e:
            self.logger.error(f"初始化推荐项目失败: {e}")
