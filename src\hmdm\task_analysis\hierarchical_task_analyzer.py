"""
层次任务分析器

基于层次任务分析法(HTA)实现任务的层次化分解
"""

from typing import List, Dict, Optional, Any, Tuple
import uuid
from datetime import datetime

from ..models.task_models import (
    Task, TaskType, TaskHierarchy, TaskAttribute, IOPattern, ExecutorType
)
from .military_scenarios import MilitaryScenarioTemplates


class HierarchicalTaskAnalyzer:
    """层次任务分析器"""
    
    def __init__(self):
        self.task_hierarchy = None
        self.decomposition_rules = self._init_decomposition_rules()
    
    def _init_decomposition_rules(self) -> Dict[TaskType, List[TaskType]]:
        """初始化分解规则"""
        return {
            TaskType.MISSION_TASK: [TaskType.ZZ_TASK],
            TaskType.ZZ_TASK: [TaskType.TYPICAL_FUNCTION],
            TaskType.TYPICAL_FUNCTION: [TaskType.INTERACTION_PROCESS],
            TaskType.INTERACTION_PROCESS: [TaskType.OPERATION_SEQUENCE],
            TaskType.OPERATION_SEQUENCE: [TaskType.META_OPERATION],
            TaskType.META_OPERATION: []  # 最底层，不再分解
        }
    
    def create_task_hierarchy(self, root_task: Task) -> TaskHierarchy:
        """创建任务层次结构"""
        self.task_hierarchy = TaskHierarchy(root_task_id=root_task.id)
        self.task_hierarchy.add_task(root_task)
        return self.task_hierarchy
    
    def decompose_task(self, task_id: str, subtask_specs: List[Dict[str, Any]]) -> List[Task]:
        """分解任务为子任务"""
        if not self.task_hierarchy:
            raise ValueError("任务层次结构未初始化")
        
        parent_task = self.task_hierarchy.get_task(task_id)
        if not parent_task:
            raise ValueError(f"任务 {task_id} 不存在")
        
        # 检查分解规则
        allowed_child_types = self.decomposition_rules.get(parent_task.task_type, [])
        if not allowed_child_types:
            raise ValueError(f"任务类型 {parent_task.task_type.value} 不能再分解")
        
        subtasks = []
        for i, spec in enumerate(subtask_specs):
            # 创建子任务
            subtask = Task(
                name=spec.get("name", f"{parent_task.name}_子任务_{i+1}"),
                description=spec.get("description", ""),
                task_type=allowed_child_types[0],  # 使用第一个允许的子类型
                parent_id=parent_task.id,
                level=parent_task.level + 1,
                executor_type=spec.get("executor_type", ExecutorType.HUMAN_MACHINE)
            )
            
            # 设置任务属性
            if "attributes" in spec:
                attr_dict = spec["attributes"]
                subtask.attributes = TaskAttribute(
                    complexity=attr_dict.get("complexity", 0.0),
                    importance=attr_dict.get("importance", 0.0),
                    urgency=attr_dict.get("urgency", 0.0),
                    frequency=attr_dict.get("frequency", 0.0),
                    duration=attr_dict.get("duration", 0.0),
                    error_rate=attr_dict.get("error_rate", 0.0),
                    workload=attr_dict.get("workload", 0.0),
                    military_priority=attr_dict.get("military_priority", ""),
                    security_level=attr_dict.get("security_level", ""),
                    real_time_requirement=attr_dict.get("real_time_requirement", False),
                    combat_effectiveness=attr_dict.get("combat_effectiveness", 0.0)
                )
            
            # 设置IO模式
            if "io_pattern" in spec:
                io_dict = spec["io_pattern"]
                subtask.io_pattern = IOPattern(
                    input_description=io_dict.get("input_description", ""),
                    action_description=io_dict.get("action_description", ""),
                    output_description=io_dict.get("output_description", ""),
                    input_type=io_dict.get("input_type", ""),
                    output_type=io_dict.get("output_type", "")
                )
            
            # 设置其他属性
            subtask.prerequisites = spec.get("prerequisites", [])
            subtask.constraints = spec.get("constraints", [])
            subtask.tags = spec.get("tags", [])
            subtask.metadata = spec.get("metadata", {})
            
            # 添加到层次结构
            self.task_hierarchy.add_task(subtask)
            subtasks.append(subtask)
        
        return subtasks
    
    def auto_decompose_by_scenario(self, task_id: str, scenario: str) -> List[Task]:
        """根据典型场景自动分解任务"""
        scenario_templates = self._get_scenario_templates()

        if scenario not in scenario_templates:
            raise ValueError(f"不支持的场景类型: {scenario}")

        template = scenario_templates[scenario]
        return self.decompose_task(task_id, template)

    def auto_decompose_by_military_scenario(self, task_id: str, scenario: str) -> List[Task]:
        """根据军事场景自动分解任务"""
        military_templates = MilitaryScenarioTemplates.get_military_scenario_templates()

        if scenario not in military_templates:
            raise ValueError(f"不支持的军事场景类型: {scenario}")

        template = military_templates[scenario]
        return self.decompose_task(task_id, template)

    def get_available_military_scenarios(self) -> List[str]:
        """获取可用的军事场景列表"""
        return list(MilitaryScenarioTemplates.get_military_scenario_templates().keys())
    
    def _get_scenario_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取场景模板"""
        return {
            "态势分析": [
                {
                    "name": "数据收集",
                    "description": "收集相关态势数据",
                    "executor_type": ExecutorType.MACHINE,
                    "attributes": {
                        "complexity": 0.3,
                        "importance": 0.9,
                        "duration": 30.0,
                        "workload": 0.2
                    },
                    "io_pattern": {
                        "input_description": "传感器数据、情报信息",
                        "action_description": "数据采集和预处理",
                        "output_description": "结构化态势数据",
                        "input_type": "多源数据",
                        "output_type": "标准化数据"
                    }
                },
                {
                    "name": "数据分析",
                    "description": "分析态势数据并识别模式",
                    "executor_type": ExecutorType.HUMAN_MACHINE,
                    "attributes": {
                        "complexity": 0.7,
                        "importance": 0.9,
                        "duration": 120.0,
                        "workload": 0.6
                    },
                    "io_pattern": {
                        "input_description": "结构化态势数据",
                        "action_description": "模式识别和趋势分析",
                        "output_description": "态势评估结果",
                        "input_type": "标准化数据",
                        "output_type": "分析报告"
                    }
                },
                {
                    "name": "态势展示",
                    "description": "可视化展示态势分析结果",
                    "executor_type": ExecutorType.MACHINE,
                    "attributes": {
                        "complexity": 0.4,
                        "importance": 0.7,
                        "duration": 15.0,
                        "workload": 0.3
                    },
                    "io_pattern": {
                        "input_description": "态势评估结果",
                        "action_description": "数据可视化和界面展示",
                        "output_description": "态势显示界面",
                        "input_type": "分析报告",
                        "output_type": "可视化界面"
                    }
                }
            ],
            "威胁计算": [
                {
                    "name": "威胁识别",
                    "description": "识别潜在威胁目标",
                    "executor_type": ExecutorType.MACHINE,
                    "attributes": {
                        "complexity": 0.6,
                        "importance": 0.95,
                        "duration": 45.0,
                        "workload": 0.4
                    }
                },
                {
                    "name": "威胁评估",
                    "description": "评估威胁等级和影响",
                    "executor_type": ExecutorType.HUMAN_MACHINE,
                    "attributes": {
                        "complexity": 0.8,
                        "importance": 0.95,
                        "duration": 180.0,
                        "workload": 0.7
                    }
                },
                {
                    "name": "威胁预警",
                    "description": "发出威胁预警信息",
                    "executor_type": ExecutorType.MACHINE,
                    "attributes": {
                        "complexity": 0.3,
                        "importance": 0.9,
                        "duration": 10.0,
                        "workload": 0.2
                    }
                }
            ],
            "辅助决策": [
                {
                    "name": "方案生成",
                    "description": "生成可选决策方案",
                    "executor_type": ExecutorType.HUMAN_MACHINE,
                    "attributes": {
                        "complexity": 0.7,
                        "importance": 0.8,
                        "duration": 300.0,
                        "workload": 0.6
                    }
                },
                {
                    "name": "方案评估",
                    "description": "评估各方案的优劣",
                    "executor_type": ExecutorType.HUMAN_MACHINE,
                    "attributes": {
                        "complexity": 0.8,
                        "importance": 0.9,
                        "duration": 240.0,
                        "workload": 0.7
                    }
                },
                {
                    "name": "决策推荐",
                    "description": "推荐最优决策方案",
                    "executor_type": ExecutorType.MACHINE,
                    "attributes": {
                        "complexity": 0.5,
                        "importance": 0.85,
                        "duration": 60.0,
                        "workload": 0.4
                    }
                }
            ]
        }
    
    def calculate_task_metrics(self, task_id: str) -> Dict[str, float]:
        """计算任务指标"""
        if not self.task_hierarchy:
            return {}
        
        task = self.task_hierarchy.get_task(task_id)
        if not task:
            return {}
        
        # 获取所有子任务
        descendants = self.task_hierarchy.get_all_descendants(task_id)
        
        if not descendants:
            # 叶子任务，直接返回自身属性
            return task.attributes.to_dict()
        
        # 聚合子任务指标
        metrics = {
            "complexity": 0.0,
            "importance": 0.0,
            "urgency": 0.0,
            "frequency": 0.0,
            "duration": 0.0,
            "error_rate": 0.0,
            "workload": 0.0
        }
        
        for descendant in descendants:
            attrs = descendant.attributes.to_dict()
            for key in metrics:
                if key == "duration":
                    # 持续时间累加
                    metrics[key] += attrs[key]
                elif key == "error_rate":
                    # 错误率取最大值
                    metrics[key] = max(metrics[key], attrs[key])
                else:
                    # 其他指标取加权平均
                    metrics[key] += attrs[key] / len(descendants)
        
        return metrics
    
    def validate_hierarchy(self) -> List[str]:
        """验证任务层次结构的完整性"""
        if not self.task_hierarchy:
            return ["任务层次结构未初始化"]
        
        errors = []
        
        for task_id, task in self.task_hierarchy.tasks.items():
            # 检查父子关系一致性
            if task.parent_id:
                parent = self.task_hierarchy.get_task(task.parent_id)
                if not parent:
                    errors.append(f"任务 {task.name} 的父任务 {task.parent_id} 不存在")
                elif task_id not in parent.children_ids:
                    errors.append(f"任务 {task.name} 未在父任务 {parent.name} 的子任务列表中")
            
            # 检查子任务关系
            for child_id in task.children_ids:
                child = self.task_hierarchy.get_task(child_id)
                if not child:
                    errors.append(f"任务 {task.name} 的子任务 {child_id} 不存在")
                elif child.parent_id != task_id:
                    errors.append(f"子任务 {child.name} 的父任务ID不匹配")
            
            # 检查层级深度
            expected_level = 0
            if task.parent_id:
                parent = self.task_hierarchy.get_task(task.parent_id)
                if parent:
                    expected_level = parent.level + 1
            
            if task.level != expected_level:
                errors.append(f"任务 {task.name} 的层级深度不正确，期望 {expected_level}，实际 {task.level}")
        
        return errors
    
    def export_hierarchy(self) -> Dict[str, Any]:
        """导出任务层次结构"""
        if not self.task_hierarchy:
            return {}
        
        return self.task_hierarchy.to_dict()
    
    def import_hierarchy(self, hierarchy_data: Dict[str, Any]) -> TaskHierarchy:
        """导入任务层次结构"""
        # 重建任务层次结构
        self.task_hierarchy = TaskHierarchy(root_task_id=hierarchy_data["root_task_id"])
        
        # 重建任务对象
        for task_id, task_data in hierarchy_data["tasks"].items():
            task = Task(
                id=task_data["id"],
                name=task_data["name"],
                description=task_data["description"],
                task_type=TaskType(task_data["task_type"]),
                parent_id=task_data["parent_id"],
                children_ids=task_data["children_ids"],
                level=task_data["level"],
                executor_type=ExecutorType(task_data["executor_type"]),
                prerequisites=task_data["prerequisites"],
                constraints=task_data["constraints"],
                tags=task_data["tags"],
                metadata=task_data["metadata"]
            )
            
            # 重建任务属性
            attr_data = task_data["attributes"]
            task.attributes = TaskAttribute(**attr_data)
            
            # 重建IO模式
            io_data = task_data["io_pattern"]
            task.io_pattern = IOPattern(**io_data)
            
            # 重建时间戳
            task.created_at = datetime.fromisoformat(task_data["created_at"])
            task.updated_at = datetime.fromisoformat(task_data["updated_at"])
            
            self.task_hierarchy.tasks[task_id] = task
        
        return self.task_hierarchy
