"""
军事扩展功能测试

测试军事决策支持和训练系统等扩展功能
"""

import pytest
from datetime import datetime, timedelta
from src.hmdm.decision.military_decision_support import (
    MilitaryDecisionSupport, MilitaryResource, MilitaryObjective, MilitaryOption,
    DecisionType, OperationalPhase, ResourceType
)
from src.hmdm.training.military_training import (
    MilitaryTrainingSystem, TrainingObjective, TrainingScenario, TrainingResult,
    TrainingType, TrainingLevel, PerformanceMetric
)
from src.hmdm.analysis.situation_awareness import (
    SituationAwarenessEngine, SituationAssessment, ThreatLevel, SituationType
)
from src.hmdm.utils.realtime_processor import RealTimeData, DataType


class TestMilitaryDecisionSupport:
    """军事决策支持测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.decision_support = MilitaryDecisionSupport()
        self.sa_engine = SituationAwarenessEngine()
        
        # 创建测试资源
        self.test_resources = {
            "personnel": MilitaryResource(
                id="personnel",
                name="作战人员",
                resource_type=ResourceType.PERSONNEL,
                quantity=100,
                unit="人",
                quality=0.8,
                availability=0.9
            ),
            "equipment": MilitaryResource(
                id="equipment",
                name="作战装备",
                resource_type=ResourceType.EQUIPMENT,
                quantity=50,
                unit="套",
                quality=0.9,
                availability=0.8
            )
        }
        
        # 创建测试目标
        self.test_objectives = [
            MilitaryObjective(
                id="obj_1",
                name="占领高地",
                description="占领战略高地",
                priority=0.8,
                resources_required={"personnel": 30, "equipment": 10}
            ),
            MilitaryObjective(
                id="obj_2", 
                name="控制道路",
                description="控制主要交通道路",
                priority=0.6,
                resources_required={"personnel": 20, "equipment": 5}
            )
        ]
    
    def test_resource_creation(self):
        """测试资源创建"""
        resource = self.test_resources["personnel"]
        
        assert resource.id == "personnel"
        assert resource.resource_type == ResourceType.PERSONNEL
        assert resource.quantity == 100
        assert resource.effective_quantity() == 100 * 0.8 * 0.9  # quantity * quality * availability
    
    def test_objective_achievability(self):
        """测试目标可达成性"""
        objective = self.test_objectives[0]
        
        # 测试资源充足的情况
        achievable = objective.is_achievable(self.test_resources)
        assert achievable is True
        
        # 测试资源不足的情况
        limited_resources = {
            "personnel": MilitaryResource(
                id="personnel",
                name="作战人员",
                resource_type=ResourceType.PERSONNEL,
                quantity=10,  # 不足的数量
                unit="人"
            )
        }
        
        not_achievable = objective.is_achievable(limited_resources)
        assert not_achievable is False
    
    def test_decision_option_generation(self):
        """测试决策方案生成"""
        # 创建测试态势
        situation = SituationAssessment(
            threat_level=ThreatLevel.MODERATE,
            situation_type=SituationType.NEUTRAL
        )
        
        # 生成决策方案
        options = self.decision_support.generate_decision_options(
            situation=situation,
            objectives=self.test_objectives,
            available_resources=self.test_resources,
            decision_type=DecisionType.TACTICAL
        )
        
        assert len(options) > 0
        assert all(isinstance(option, MilitaryOption) for option in options)
        assert all(option.decision_type == DecisionType.TACTICAL for option in options)
    
    def test_option_evaluation(self):
        """测试方案评估"""
        # 创建测试态势
        situation = SituationAssessment(
            threat_level=ThreatLevel.MODERATE,
            situation_type=SituationType.NEUTRAL
        )
        
        # 生成方案
        options = self.decision_support.generate_decision_options(
            situation, self.test_objectives, self.test_resources, DecisionType.TACTICAL
        )
        
        if options:
            # 评估方案
            evaluated_options = self.decision_support.evaluate_options(
                options, self.test_objectives, situation
            )
            
            assert len(evaluated_options) == len(options)
            assert all(isinstance(score, float) for _, score in evaluated_options)
            assert all(0 <= score <= 1 for _, score in evaluated_options)
            
            # 验证按分数排序
            scores = [score for _, score in evaluated_options]
            assert scores == sorted(scores, reverse=True)
    
    def test_decision_recommendation(self):
        """测试决策推荐"""
        # 创建测试态势
        situation = SituationAssessment(
            threat_level=ThreatLevel.HIGH,
            situation_type=SituationType.ENEMY
        )
        
        # 获取决策推荐
        recommendation = self.decision_support.recommend_decision(
            situation=situation,
            objectives=self.test_objectives,
            available_resources=self.test_resources,
            decision_type=DecisionType.TACTICAL,
            risk_tolerance=0.7
        )
        
        assert recommendation["status"] in ["success", "no_options"]
        
        if recommendation["status"] == "success":
            assert "recommended_option" in recommendation
            assert "alternatives" in recommendation
            assert "decision_rationale" in recommendation
            assert "implementation_guidance" in recommendation
            assert "risk_mitigation" in recommendation
            
            # 验证推荐方案的结构
            recommended = recommendation["recommended_option"]
            assert "id" in recommended
            assert "name" in recommended
            assert "score" in recommended
            assert "effectiveness" in recommended
            assert "success_probability" in recommended
    
    def test_resource_optimization(self):
        """测试资源优化"""
        optimization_result = self.decision_support.optimize_resource_allocation(
            objectives=self.test_objectives,
            available_resources=self.test_resources
        )
        
        assert optimization_result["status"] == "success"
        assert "allocation" in optimization_result
        assert "objective_achievement" in optimization_result
        assert "resource_utilization" in optimization_result
        assert "optimization_score" in optimization_result
        
        # 验证分配结果
        allocation = optimization_result["allocation"]
        assert len(allocation) == len(self.test_objectives)
        
        for obj_id in allocation:
            assert obj_id in [obj.id for obj in self.test_objectives]
            assert isinstance(allocation[obj_id], dict)


class TestMilitaryTrainingSystem:
    """军事训练系统测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.training_system = MilitaryTrainingSystem()
    
    def test_training_objective_creation(self):
        """测试训练目标创建"""
        objective = TrainingObjective(
            id="test_obj",
            name="射击训练",
            description="基础射击技能训练",
            training_type=TrainingType.INDIVIDUAL,
            level=TrainingLevel.BASIC,
            metrics=[PerformanceMetric.ACCURACY, PerformanceMetric.SPEED],
            target_scores={"accuracy": 0.8, "speed": 0.7},
            duration=1.5,
            difficulty=0.4
        )
        
        success = self.training_system.create_training_objective(objective)
        assert success is True
        assert objective.id in self.training_system.training_objectives
        
        # 验证目标属性
        stored_obj = self.training_system.training_objectives[objective.id]
        assert stored_obj.name == "射击训练"
        assert stored_obj.training_type == TrainingType.INDIVIDUAL
        assert stored_obj.level == TrainingLevel.BASIC
        assert len(stored_obj.metrics) == 2
    
    def test_training_scenario_creation(self):
        """测试训练场景创建"""
        scenario = TrainingScenario(
            id="test_scenario",
            name="城市巷战",
            description="城市环境作战训练",
            scenario_type="combat",
            participants=["soldier_1", "soldier_2", "soldier_3"],
            objectives=["basic_combat"],
            duration=2.0,
            complexity=0.6
        )
        
        success = self.training_system.create_training_scenario(scenario)
        assert success is True
        assert scenario.id in self.training_system.training_scenarios
        
        # 验证场景事件生成
        stored_scenario = self.training_system.training_scenarios[scenario.id]
        assert len(stored_scenario.events) > 0
        assert all("id" in event for event in stored_scenario.events)
        assert all("time" in event for event in stored_scenario.events)
    
    def test_training_program_design(self):
        """测试训练计划设计"""
        participants = ["soldier_1", "soldier_2"]
        training_goals = ["combat", "coordination"]
        
        program = self.training_system.design_training_program(
            participants=participants,
            training_goals=training_goals,
            available_time=6.0,
            difficulty_level=TrainingLevel.BASIC
        )
        
        assert "id" in program
        assert program["participants"] == participants
        assert program["goals"] == training_goals
        assert program["total_duration"] == 6.0
        assert "sessions" in program
        assert "expected_outcomes" in program
        
        # 验证训练科目
        if program["sessions"]:
            for session in program["sessions"]:
                assert "objective_id" in session
                assert "scenario_id" in session
                assert "duration" in session
                assert "participants" in session
    
    def test_training_execution(self):
        """测试训练执行"""
        participants = ["soldier_1", "soldier_2"]
        
        # 使用默认的训练目标和场景
        results = self.training_system.execute_training_session(
            objective_id="basic_combat",
            scenario_id="urban_combat",
            participants=participants
        )
        
        assert len(results) == len(participants)
        
        for result in results:
            assert isinstance(result, TrainingResult)
            assert result.participant_id in participants
            assert result.scenario_id == "urban_combat"
            assert result.objective_id == "basic_combat"
            assert result.start_time <= result.end_time
            assert len(result.scores) > 0
            assert 0 <= result.overall_score <= 1
            assert len(result.feedback) > 0
    
    def test_training_effectiveness_evaluation(self):
        """测试训练效果评估"""
        # 先执行一些训练
        participants = ["soldier_1", "soldier_2"]
        results = self.training_system.execute_training_session(
            "basic_combat", "urban_combat", participants
        )
        
        # 获取训练目标
        objectives = [self.training_system.training_objectives["basic_combat"]]
        
        # 评估训练效果
        evaluation = self.training_system.evaluate_training_effectiveness(results, objectives)
        
        assert "overall_effectiveness" in evaluation
        assert "objective_achievement" in evaluation
        assert "participant_performance" in evaluation
        assert "improvement_areas" in evaluation
        assert "recommendations" in evaluation
        
        # 验证评估数据
        assert 0 <= evaluation["overall_effectiveness"] <= 1
        assert len(evaluation["participant_performance"]) == len(participants)
        
        for participant_id, performance in evaluation["participant_performance"].items():
            assert participant_id in participants
            assert "average_score" in performance
            assert "sessions_completed" in performance
            assert "performance_trend" in performance
    
    def test_training_report_generation(self):
        """测试训练报告生成"""
        # 先执行训练
        participant_id = "soldier_1"
        results = self.training_system.execute_training_session(
            "basic_combat", "urban_combat", [participant_id]
        )
        
        # 生成报告
        report = self.training_system.generate_training_report(participant_id)
        
        if "message" not in report:  # 如果有训练记录
            assert "participant_id" in report
            assert "report_period" in report
            assert "summary" in report
            assert "performance_by_metric" in report
            assert "skill_development" in report
            assert "recommendations" in report
            
            # 验证摘要数据
            summary = report["summary"]
            assert "total_sessions" in summary
            assert "total_hours" in summary
            assert "average_score" in summary
            assert summary["total_sessions"] >= 1
    
    def test_participant_skill_tracking(self):
        """测试参训人员技能跟踪"""
        participant_id = "soldier_test"
        
        # 执行多次训练
        for i in range(3):
            self.training_system.execute_training_session(
                "basic_combat", "urban_combat", [participant_id]
            )
        
        # 检查技能档案更新
        assert participant_id in self.training_system.participant_skills
        
        skills = self.training_system.participant_skills[participant_id]
        assert len(skills) > 0
        
        for skill, level in skills.items():
            assert 0 <= level <= 1
            assert isinstance(skill, str)


class TestIntegratedMilitaryExtensions:
    """军事扩展功能集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.decision_support = MilitaryDecisionSupport()
        self.training_system = MilitaryTrainingSystem()
        self.sa_engine = SituationAwarenessEngine()
    
    def test_decision_training_integration(self):
        """测试决策与训练的集成"""
        # 1. 创建态势
        situation = SituationAssessment(
            threat_level=ThreatLevel.MODERATE,
            situation_type=SituationType.NEUTRAL
        )
        
        # 2. 生成决策方案
        resources = {
            "personnel": MilitaryResource(
                id="personnel",
                name="训练人员",
                resource_type=ResourceType.PERSONNEL,
                quantity=20
            )
        }
        
        objectives = [
            MilitaryObjective(
                id="training_obj",
                name="提升作战能力",
                description="通过训练提升作战能力",
                priority=0.8
            )
        ]
        
        recommendation = self.decision_support.recommend_decision(
            situation, objectives, resources, DecisionType.TACTICAL
        )
        
        # 3. 基于决策结果设计训练
        if recommendation["status"] == "success":
            training_program = self.training_system.design_training_program(
                participants=["trainee_1", "trainee_2"],
                training_goals=["combat", "coordination"],  # 使用系统中存在的训练目标
                available_time=4.0,
                difficulty_level=TrainingLevel.BASIC  # 使用基础级别
            )

            # 如果没有找到匹配的训练科目，至少验证程序结构正确
            assert "sessions" in training_program
            if len(training_program["sessions"]) > 0:
            
                # 4. 执行训练并评估
                session = training_program["sessions"][0]
                results = self.training_system.execute_training_session(
                    session["objective_id"],
                    session["scenario_id"],
                    session["participants"]
                )

                assert len(results) == 2
                assert all(result.overall_score >= 0 for result in results)
    
    def test_situation_aware_training(self):
        """测试态势感知训练"""
        # 1. 创建态势感知数据
        sensor_data = RealTimeData(
            id="training_sensor",
            data_type=DataType.SENSOR,
            content={
                "entity_id": "training_target",
                "name": "训练目标",
                "type": "target",
                "position": [116.3974, 39.9093, 0],
                "allegiance": "enemy"
            },
            source="training_system"
        )
        
        # 2. 处理态势数据
        self.sa_engine.ingest_sensor_data(sensor_data)
        assessment = self.sa_engine.perform_situation_assessment()
        
        # 3. 基于态势创建训练场景
        training_scenario = TrainingScenario(
            id="situation_training",
            name="态势感知训练",
            description=f"基于{assessment.threat_level.value}威胁等级的训练",
            scenario_type="situation_awareness",
            participants=["analyst_1"],
            duration=1.5,
            complexity=0.5 if assessment.threat_level == ThreatLevel.LOW else 0.8
        )
        
        success = self.training_system.create_training_scenario(training_scenario)
        assert success is True
        
        # 4. 验证场景适应性
        stored_scenario = self.training_system.training_scenarios[training_scenario.id]
        expected_complexity = 0.5 if assessment.threat_level == ThreatLevel.LOW else 0.8
        assert stored_scenario.complexity == expected_complexity
    
    def test_comprehensive_military_workflow(self):
        """测试综合军事工作流程"""
        # 1. 态势感知
        intel_data = RealTimeData(
            id="comprehensive_intel",
            data_type=DataType.INTELLIGENCE,
            content={
                "target_id": "enemy_unit",
                "threat_level": 0.7,
                "capabilities": ["mobility", "firepower"],
                "allegiance": "enemy"
            },
            source="intelligence"
        )
        
        self.sa_engine.ingest_intelligence_data(intel_data)
        situation = self.sa_engine.perform_situation_assessment()
        
        # 2. 决策支持
        resources = {
            "personnel": MilitaryResource(
                id="personnel",
                name="作战人员",
                resource_type=ResourceType.PERSONNEL,
                quantity=50
            ),
            "equipment": MilitaryResource(
                id="equipment", 
                name="作战装备",
                resource_type=ResourceType.EQUIPMENT,
                quantity=20
            )
        }
        
        objectives = [
            MilitaryObjective(
                id="neutralize_threat",
                name="消除威胁",
                description="消除敌方威胁",
                priority=0.9,
                resources_required={"personnel": 30, "equipment": 10}
            )
        ]
        
        decision = self.decision_support.recommend_decision(
            situation, objectives, resources, DecisionType.OPERATIONAL
        )
        
        # 3. 训练准备
        if decision["status"] == "success":
            # 基于决策结果设计针对性训练
            training_program = self.training_system.design_training_program(
                participants=["commander_1", "operator_1", "operator_2"],
                training_goals=["tactical", "operational"],
                available_time=6.0,
                difficulty_level=TrainingLevel.ADVANCED
            )
            
            assert len(training_program["sessions"]) > 0
            
            # 4. 验证整个流程的一致性
            assert situation.threat_level != ThreatLevel.MINIMAL  # 有威胁
            assert decision["recommended_option"]["effectiveness"] > 0  # 有效方案
            assert training_program["total_duration"] == 6.0  # 训练时间正确
