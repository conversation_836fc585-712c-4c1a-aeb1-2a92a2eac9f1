# HMDM系统部署指南

## 部署概述

本文档提供HMDM（Human-Machine Decision Making）军事综合决策支持系统的完整部署指南，包括环境准备、系统安装、配置设置和启动验证等详细步骤。

### 部署目标
- 在目标环境中成功部署HMDM系统
- 确保系统功能正常运行
- 建立稳定可靠的运行环境
- 提供完整的运维支持

### 部署方式
- **单机部署**：适用于开发、测试和小规模使用
- **分布式部署**：适用于生产环境和大规模使用
- **容器化部署**：使用Docker进行容器化部署
- **云端部署**：在云平台上部署系统

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**：2核心 2.0GHz
- **内存**：4GB RAM
- **存储**：20GB 可用空间
- **网络**：100Mbps 网络连接

#### 推荐配置
- **CPU**：4核心 3.0GHz 或更高
- **内存**：8GB RAM 或更高
- **存储**：100GB SSD存储
- **网络**：1Gbps 网络连接

#### 生产环境配置
- **CPU**：8核心 3.5GHz 或更高
- **内存**：16GB RAM 或更高
- **存储**：500GB SSD存储 + 备份存储
- **网络**：10Gbps 网络连接
- **冗余**：双机热备或集群部署

### 软件要求

#### 操作系统
- **Windows**：Windows 10/11, Windows Server 2019/2022
- **Linux**：Ubuntu 20.04+, CentOS 8+, RHEL 8+, Debian 10+
- **macOS**：macOS 11.0+ （仅用于开发环境）

#### 运行环境
- **Python**：3.8 或更高版本（推荐 3.11）
- **pip**：最新版本
- **Git**：2.0+ （可选，用于源码部署）
- **Web浏览器**：Chrome 90+, Firefox 88+, Edge 90+

#### 依赖软件
- **数据库**：SQLite 3.x（内置）或 PostgreSQL 12+（可选）
- **Web服务器**：Nginx（可选，用于反向代理）
- **进程管理**：systemd（Linux）或 Windows服务

## 部署准备

### 1. 环境检查
在开始部署前，请检查以下环境条件：

**系统检查脚本**（Linux/macOS）：
```bash
#!/bin/bash
echo "=== HMDM系统环境检查 ==="

# 检查操作系统
echo "操作系统: $(uname -s) $(uname -r)"

# 检查Python版本
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: Python 3.8+ 未安装"
    exit 1
fi

# 检查内存
free -h | grep "Mem:"

# 检查磁盘空间
df -h | grep -E "/$|/home"

# 检查网络连接
ping -c 3 ******* > /dev/null
if [ $? -eq 0 ]; then
    echo "网络连接: 正常"
else
    echo "网络连接: 异常"
fi

echo "=== 环境检查完成 ==="
```

**Windows环境检查**：
```powershell
# 检查Python版本
python --version
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: Python 3.8+ 未安装" -ForegroundColor Red
    exit 1
}

# 检查内存
Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory

# 检查磁盘空间
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace

# 检查网络连接
Test-Connection -ComputerName ******* -Count 3 -Quiet
```

### 2. 用户和权限设置

#### Linux系统
```bash
# 创建专用用户
sudo useradd -m -s /bin/bash hmdm
sudo usermod -aG sudo hmdm

# 设置目录权限
sudo mkdir -p /opt/hmdm
sudo chown hmdm:hmdm /opt/hmdm
sudo chmod 755 /opt/hmdm
```

#### Windows系统
```powershell
# 创建专用用户（可选）
net user hmdm /add
net localgroup "Users" hmdm /add

# 创建安装目录
New-Item -ItemType Directory -Path "C:\HMDM" -Force
```

### 3. 防火墙配置

#### Linux (UFW)
```bash
# 开放必要端口
sudo ufw allow 5000/tcp  # HMDM Web服务
sudo ufw allow 22/tcp    # SSH（如需要）
sudo ufw enable
```

#### Windows防火墙
```powershell
# 开放HMDM端口
New-NetFirewallRule -DisplayName "HMDM Web Service" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow
```

## 安装部署

### 方式一：源码部署（推荐）

#### 1. 获取源码
```bash
# 从Git仓库克隆（如果有）
git clone <repository-url> /opt/hmdm
cd /opt/hmdm

# 或者解压源码包
unzip hmdm-system-v2.0.0.zip -d /opt/hmdm
cd /opt/hmdm
```

#### 2. 创建虚拟环境
```bash
# 创建Python虚拟环境
python3 -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

#### 3. 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 验证安装
pip list | grep -E "(flask|numpy|pandas)"
```

#### 4. 初始化系统
```bash
# 创建必要目录
mkdir -p config logs data backups temp

# 初始化配置文件
python scripts/init_config.py

# 设置文件权限（Linux）
chmod 600 config/*.json
chmod 755 logs data backups temp
```

#### 5. 验证安装
```bash
# 运行系统测试
python -m pytest tests/test_system_manager.py -v

# 检查系统状态
python scripts/final_system_check.py
```

### 方式二：Docker部署

#### 1. 准备Docker环境
```bash
# 安装Docker（Ubuntu）
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### 2. 构建Docker镜像
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p config logs data backups temp

# 设置权限
RUN chmod +x scripts/*.py

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "src/hmdm/web/app.py"]
```

#### 3. 使用Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  hmdm:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - hmdm
    restart: unless-stopped
```

#### 4. 启动服务
```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f hmdm
```

### 方式三：包管理器安装

#### 1. 创建安装包
```bash
# 创建Python包
python setup.py sdist bdist_wheel

# 创建系统包（Debian/Ubuntu）
dpkg-buildpackage -us -uc
```

#### 2. 安装系统包
```bash
# 安装deb包（Ubuntu/Debian）
sudo dpkg -i hmdm-system_2.0.0_all.deb
sudo apt-get install -f  # 解决依赖问题

# 安装rpm包（CentOS/RHEL）
sudo rpm -ivh hmdm-system-2.0.0.noarch.rpm
```

## 配置设置

### 1. 系统配置

#### 基础配置文件 (`config/system_config.json`)
```json
{
  "system_name": "HMDM军事综合决策支持系统",
  "version": "2.0.0",
  "environment": "production",
  "debug_mode": false,
  "log_level": "INFO",
  "log_file": "logs/hmdm.log",
  "log_max_size": 10485760,
  "log_backup_count": 5,
  "enable_console_log": false,
  "security_level": "SECRET",
  "enable_encryption": true,
  "enable_audit_log": true,
  "web_host": "0.0.0.0",
  "web_port": 5000,
  "web_debug": false,
  "max_workers": 4,
  "request_timeout": 30
}
```

#### 人机分配配置 (`config/allocation_config.json`)
```json
{
  "allocation_mode": "BALANCED",
  "optimization_objective": "EFFICIENCY",
  "human_capability_weight": 0.6,
  "machine_capability_weight": 0.4,
  "collaboration_factor": 0.8,
  "risk_tolerance": 0.3,
  "performance_threshold": 0.7,
  "max_iterations": 100,
  "convergence_threshold": 0.001
}
```

### 2. 安全配置

#### SSL/TLS配置
```bash
# 生成自签名证书（测试用）
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# 配置HTTPS
# 在system_config.json中添加：
{
  "ssl_enabled": true,
  "ssl_cert_file": "ssl/cert.pem",
  "ssl_key_file": "ssl/key.pem"
}
```

#### 访问控制配置
```json
{
  "authentication": {
    "enabled": true,
    "method": "token",
    "token_expiry": 3600
  },
  "authorization": {
    "enabled": true,
    "default_role": "user",
    "admin_users": ["admin"]
  },
  "rate_limiting": {
    "enabled": true,
    "requests_per_minute": 60
  }
}
```

### 3. 性能配置

#### Web服务器配置
```python
# gunicorn配置文件 (gunicorn.conf.py)
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

#### Nginx反向代理配置
```nginx
# nginx.conf
upstream hmdm_backend {
    server 127.0.0.1:5000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://hmdm_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /opt/hmdm/src/hmdm/web/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 服务启动

### 1. 开发环境启动

#### 直接启动
```bash
# 激活虚拟环境
source venv/bin/activate

# 启动Web服务
python src/hmdm/web/app.py
```

#### 使用Flask命令
```bash
export FLASK_APP=src.hmdm.web.app
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000
```

### 2. 生产环境启动

#### 使用Gunicorn（Linux）
```bash
# 安装Gunicorn
pip install gunicorn

# 启动服务
gunicorn -c gunicorn.conf.py src.hmdm.web.app:app
```

#### 使用Waitress（Windows）
```bash
# 安装Waitress
pip install waitress

# 启动服务
waitress-serve --host=0.0.0.0 --port=5000 src.hmdm.web.app:app
```

### 3. 系统服务配置

#### Linux Systemd服务
创建服务文件 `/etc/systemd/system/hmdm.service`：
```ini
[Unit]
Description=HMDM System
After=network.target

[Service]
Type=simple
User=hmdm
Group=hmdm
WorkingDirectory=/opt/hmdm
Environment=PATH=/opt/hmdm/venv/bin
Environment=PYTHONPATH=/opt/hmdm
ExecStart=/opt/hmdm/venv/bin/gunicorn -c gunicorn.conf.py src.hmdm.web.app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable hmdm
sudo systemctl start hmdm
sudo systemctl status hmdm
```

#### Windows服务
```powershell
# 使用NSSM创建Windows服务
nssm install HMDM "C:\HMDM\venv\Scripts\python.exe"
nssm set HMDM Arguments "src\hmdm\web\app.py"
nssm set HMDM AppDirectory "C:\HMDM"
nssm start HMDM
```

## 部署验证

### 1. 系统健康检查
```bash
# 运行系统检查脚本
python scripts/final_system_check.py

# 检查Web服务
curl -I http://localhost:5000/
curl http://localhost:5000/api/system/status
```

### 2. 功能验证
```bash
# 运行功能测试
python -m pytest tests/test_comprehensive_functionality.py -v

# 运行性能测试
python tests/test_performance_benchmark.py

# 运行安全测试
python tests/test_security.py
```

### 3. 负载测试
```bash
# 使用Apache Bench进行负载测试
ab -n 1000 -c 10 http://localhost:5000/

# 使用wrk进行负载测试
wrk -t12 -c400 -d30s http://localhost:5000/
```

## 监控和维护

### 1. 日志监控
```bash
# 查看实时日志
tail -f logs/hmdm.log

# 查看错误日志
grep ERROR logs/hmdm.log

# 日志轮转配置
logrotate -d /etc/logrotate.d/hmdm
```

### 2. 性能监控
```bash
# 系统资源监控
htop
iostat -x 1
netstat -tuln

# 应用性能监控
python scripts/performance_monitor.py
```

### 3. 定期维护
```bash
# 系统优化
python scripts/system_optimization.py

# 数据备份
tar -czf backup/hmdm-backup-$(date +%Y%m%d).tar.gz config/ data/ logs/

# 日志清理
find logs/ -name "*.log.*" -mtime +30 -delete

# 缓存清理
find . -name "__pycache__" -type d -exec rm -rf {} +
```

## 故障排除

### 1. 常见问题

#### 端口占用
```bash
# 检查端口占用
netstat -tulpn | grep :5000
lsof -i :5000

# 杀死占用进程
kill -9 <PID>
```

#### 权限问题
```bash
# 修复文件权限
chown -R hmdm:hmdm /opt/hmdm
chmod -R 755 /opt/hmdm
chmod 600 /opt/hmdm/config/*.json
```

#### 依赖问题
```bash
# 重新安装依赖
pip install --force-reinstall -r requirements.txt

# 检查Python版本
python --version
which python
```

### 2. 日志分析
```bash
# 分析错误日志
grep -E "(ERROR|CRITICAL)" logs/hmdm.log | tail -20

# 分析访问日志
awk '{print $1}' logs/access.log | sort | uniq -c | sort -nr

# 分析性能日志
grep "slow" logs/hmdm.log
```

### 3. 性能调优
```bash
# 调整系统参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
sysctl -p

# 调整文件描述符限制
echo 'hmdm soft nofile 65536' >> /etc/security/limits.conf
echo 'hmdm hard nofile 65536' >> /etc/security/limits.conf
```

## 安全加固

### 1. 系统安全
```bash
# 更新系统
sudo apt update && sudo apt upgrade

# 配置防火墙
sudo ufw enable
sudo ufw default deny incoming
sudo ufw allow 22/tcp
sudo ufw allow 5000/tcp
```

### 2. 应用安全
```bash
# 设置安全的文件权限
chmod 600 config/*.json
chmod 700 logs/
chmod 755 src/

# 配置SSL/TLS
# 使用Let's Encrypt获取证书
certbot --nginx -d your-domain.com
```

### 3. 数据安全
```bash
# 加密敏感配置
python scripts/encrypt_config.py

# 设置数据备份加密
gpg --cipher-algo AES256 --compress-algo 1 --s2k-cipher-algo AES256 --s2k-digest-algo SHA512 --s2k-mode 3 --s2k-count 65536 --symmetric backup.tar.gz
```

---

**文档版本**：v1.0.0  
**最后更新**：2025年9月8日  
**编写人员**：HMDM部署团队  
**审核状态**：已审核

*如需部署支持，请联系技术支持团队。*
