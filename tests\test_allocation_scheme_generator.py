"""
分配方案生成器测试
"""

import pytest
from unittest.mock import Mock, patch

from src.hmdm.allocation.allocation_scheme_generator import (
    AllocationSchemeGenerator,
    AllocationScheme,
    AllocationStrategy
)
from src.hmdm.task_analysis.hierarchical_task_analyzer import TaskHierarchy, Task
from src.hmdm.models.task_models import TaskType, TaskAttribute, ExecutorType


class TestAllocationSchemeGenerator:
    """分配方案生成器测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.generator = AllocationSchemeGenerator()
        
        # 创建测试任务层次结构
        self.task_hierarchy = self._create_test_task_hierarchy()
    
    def _create_test_task_hierarchy(self) -> TaskHierarchy:
        """创建测试用的任务层次结构"""
        hierarchy = TaskHierarchy(root_task_id="root_task")
        
        # 创建根任务
        root_task = Task(
            id="root_task",
            name="态势分析使命任务",
            description="综合态势分析和威胁评估",
            task_type=TaskType.MISSION_TASK,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        root_task.attributes = TaskAttribute(
            complexity=0.8,
            importance=0.9,
            urgency=0.7,
            real_time_requirement=True
        )
        hierarchy.add_task(root_task)
        
        # 创建子任务1：数据收集
        data_task = Task(
            id="data_collection",
            name="数据收集任务",
            description="收集和预处理传感器数据",
            task_type=TaskType.ZZ_TASK,
            parent_id="root_task",
            level=1,
            executor_type=ExecutorType.MACHINE
        )
        data_task.attributes = TaskAttribute(
            complexity=0.3,
            importance=0.7,
            urgency=0.9,
            real_time_requirement=True
        )
        hierarchy.add_task(data_task)
        
        # 创建子任务2：态势分析
        analysis_task = Task(
            id="situation_analysis",
            name="态势分析任务",
            description="分析态势数据并识别威胁",
            task_type=TaskType.ZZ_TASK,
            parent_id="root_task",
            level=1,
            executor_type=ExecutorType.HUMAN_MACHINE
        )
        analysis_task.attributes = TaskAttribute(
            complexity=0.9,
            importance=0.9,
            urgency=0.6,
            real_time_requirement=False
        )
        hierarchy.add_task(analysis_task)
        
        # 创建子任务3：决策支持
        decision_task = Task(
            id="decision_support",
            name="决策支持任务",
            description="提供决策建议和方案推荐",
            task_type=TaskType.ZZ_TASK,
            parent_id="root_task",
            level=1,
            executor_type=ExecutorType.HUMAN
        )
        decision_task.attributes = TaskAttribute(
            complexity=0.8,
            importance=0.95,
            urgency=0.8,
            real_time_requirement=False
        )
        hierarchy.add_task(decision_task)
        
        return hierarchy
    
    def test_allocation_scheme_creation(self):
        """测试分配方案创建"""
        scheme = AllocationScheme()
        
        # 测试默认值
        assert scheme.scheme_id is not None
        assert len(scheme.scheme_id) > 0
        assert scheme.strategy == AllocationStrategy.CAPABILITY_BASED
        assert isinstance(scheme.task_allocations, dict)
        assert isinstance(scheme.collaboration_details, dict)
        assert isinstance(scheme.expected_performance, dict)
        assert isinstance(scheme.risk_assessment, dict)
        assert isinstance(scheme.implementation_notes, list)
    
    def test_allocation_scheme_serialization(self):
        """测试分配方案序列化"""
        scheme = AllocationScheme(
            name="测试方案",
            description="测试用的分配方案",
            strategy=AllocationStrategy.HUMAN_PRIORITY
        )
        scheme.task_allocations = {"task1": "human", "task2": "machine"}
        scheme.expected_performance = {"efficiency": 0.8}
        
        # 测试转换为字典
        scheme_dict = scheme.to_dict()
        assert isinstance(scheme_dict, dict)
        assert scheme_dict['name'] == "测试方案"
        assert scheme_dict['strategy'] == AllocationStrategy.HUMAN_PRIORITY.value
        assert scheme_dict['task_allocations'] == {"task1": "human", "task2": "machine"}
        
        # 测试从字典创建
        new_scheme = AllocationScheme.from_dict(scheme_dict)
        assert new_scheme.name == scheme.name
        assert new_scheme.strategy == scheme.strategy
        assert new_scheme.task_allocations == scheme.task_allocations
    
    def test_generate_schemes(self):
        """测试生成分配方案"""
        schemes = self.generator.generate_schemes(self.task_hierarchy, num_schemes=3)
        
        # 验证生成的方案数量
        assert len(schemes) == 3
        
        # 验证每个方案的基本属性
        for scheme in schemes:
            assert isinstance(scheme, AllocationScheme)
            assert scheme.scheme_id is not None
            assert scheme.name is not None
            assert len(scheme.name) > 0
            assert isinstance(scheme.task_allocations, dict)
            assert len(scheme.task_allocations) > 0
            
            # 验证任务分配的有效性
            for task_id, allocation_type in scheme.task_allocations.items():
                assert allocation_type in ["human", "machine", "collaboration"]
            
            # 验证性能指标
            assert isinstance(scheme.expected_performance, dict)
            assert 'overall_efficiency' in scheme.expected_performance
            assert 'task_completion_rate' in scheme.expected_performance
            
            # 验证风险评估
            assert isinstance(scheme.risk_assessment, dict)
            assert 'overall_risk' in scheme.risk_assessment
    
    def test_generate_schemes_with_constraints(self):
        """测试带约束条件的方案生成"""
        constraints = {
            'max_human_tasks': 2,
            'max_machine_tasks': 3
        }
        
        schemes = self.generator.generate_schemes(
            self.task_hierarchy, 
            num_schemes=2, 
            constraints=constraints
        )
        
        assert len(schemes) == 2
        
        # 验证约束条件（注意：当前实现中约束检查是简化的）
        for scheme in schemes:
            assert isinstance(scheme.task_allocations, dict)
            assert len(scheme.task_allocations) > 0
    
    def test_generate_schemes_with_preferences(self):
        """测试带用户偏好的方案生成"""
        preferences = {
            'prefer_human_decision': True,
            'efficiency_priority': 0.8
        }
        
        schemes = self.generator.generate_schemes(
            self.task_hierarchy,
            num_schemes=2,
            preferences=preferences
        )
        
        assert len(schemes) == 2
        for scheme in schemes:
            assert isinstance(scheme, AllocationScheme)
    
    def test_strategy_configurations(self):
        """测试策略配置"""
        configs = self.generator.strategy_configs
        
        # 验证所有策略都有配置
        for strategy in AllocationStrategy:
            assert strategy in configs
            
            config = configs[strategy]
            assert isinstance(config, dict)
            assert 'threshold' in config
            assert 'optimization_weight' in config
    
    def test_allocate_task_by_strategy(self):
        """测试按策略分配任务"""
        test_task = Task(
            id="test_task",
            name="测试任务",
            description="用于测试的任务",
            task_type=TaskType.TYPICAL_FUNCTION
        )
        test_task.attributes = TaskAttribute(complexity=0.5, importance=0.7)
        
        # 测试不同策略
        strategies = [
            AllocationStrategy.CAPABILITY_BASED,
            AllocationStrategy.HUMAN_PRIORITY,
            AllocationStrategy.MACHINE_PRIORITY
        ]
        
        for strategy in strategies:
            allocation_type, confidence, details = self.generator._allocate_task_by_strategy(
                test_task, strategy, threshold=0.15
            )
            
            assert allocation_type in ["human", "machine", "collaboration"]
            assert isinstance(confidence, float)
            assert 0 <= confidence <= 1
            assert isinstance(details, dict)
            assert 'reason' in details
    
    def test_calculate_expected_performance(self):
        """测试预期性能计算"""
        scheme = AllocationScheme()
        scheme.task_allocations = {
            "data_collection": "machine",
            "situation_analysis": "collaboration",
            "decision_support": "human"
        }
        
        performance = self.generator._calculate_expected_performance(scheme, self.task_hierarchy)
        
        assert isinstance(performance, dict)
        assert 'overall_efficiency' in performance
        assert 'task_completion_rate' in performance
        assert 'resource_utilization' in performance
        assert 'coordination_overhead' in performance
        
        # 验证性能值在合理范围内
        for key, value in performance.items():
            assert isinstance(value, float)
            assert 0 <= value <= 1
    
    def test_assess_scheme_risks(self):
        """测试方案风险评估"""
        scheme = AllocationScheme()
        scheme.task_allocations = {
            "data_collection": "machine",
            "situation_analysis": "collaboration",
            "decision_support": "human"
        }
        
        risks = self.generator._assess_scheme_risks(scheme, self.task_hierarchy)
        
        assert isinstance(risks, dict)
        assert 'human_overload_risk' in risks
        assert 'machine_failure_risk' in risks
        assert 'coordination_risk' in risks
        assert 'overall_risk' in risks
        
        # 验证风险值在合理范围内
        for key, value in risks.items():
            assert isinstance(value, float)
            assert 0 <= value <= 1
    
    def test_generate_implementation_notes(self):
        """测试实施说明生成"""
        scheme = AllocationScheme()
        scheme.task_allocations = {
            "data_collection": "machine",
            "situation_analysis": "collaboration",
            "decision_support": "human"
        }
        scheme.risk_assessment = {
            'coordination_risk': 0.6,
            'human_overload_risk': 0.3
        }
        
        notes = self.generator._generate_implementation_notes(scheme, self.task_hierarchy)
        
        assert isinstance(notes, list)
        assert len(notes) > 0
        
        # 验证说明内容
        for note in notes:
            assert isinstance(note, str)
            assert len(note) > 0
    
    def test_optimize_scheme(self):
        """测试方案优化"""
        base_scheme = AllocationScheme()
        base_scheme.name = "基础方案"
        base_scheme.task_allocations = {
            "data_collection": "human",
            "situation_analysis": "human",
            "decision_support": "human"
        }
        
        optimization_criteria = {
            'efficiency': 0.8,
            'reliability': 0.6
        }
        
        optimized_scheme = self.generator.optimize_scheme(
            base_scheme, self.task_hierarchy, optimization_criteria
        )
        
        # 验证优化后的方案
        assert isinstance(optimized_scheme, AllocationScheme)
        assert optimized_scheme.scheme_id != base_scheme.scheme_id
        assert "优化版" in optimized_scheme.name
        assert isinstance(optimized_scheme.task_allocations, dict)
        
        # 验证优化效果（某些任务可能被重新分配，可能包含更多任务）
        assert len(optimized_scheme.task_allocations) >= len(base_scheme.task_allocations)
    
    def test_generate_scheme_variant(self):
        """测试方案变体生成"""
        base_scheme = AllocationScheme()
        base_scheme.name = "基础方案"
        base_scheme.strategy = AllocationStrategy.CAPABILITY_BASED
        base_scheme.task_allocations = {
            "data_collection": "machine",
            "situation_analysis": "collaboration",
            "decision_support": "human"
        }
        
        variant_scheme = self.generator._generate_scheme_variant(base_scheme, self.task_hierarchy)
        
        # 验证变体方案
        assert isinstance(variant_scheme, AllocationScheme)
        assert variant_scheme.scheme_id != base_scheme.scheme_id
        assert variant_scheme.strategy == base_scheme.strategy
        assert len(variant_scheme.task_allocations) >= len(base_scheme.task_allocations)
        
        # 验证某些分配可能发生了变化
        assert isinstance(variant_scheme.task_allocations, dict)
    
    def test_get_strategy_name(self):
        """测试策略名称获取"""
        strategy_names = {
            AllocationStrategy.CAPABILITY_BASED: "能力匹配",
            AllocationStrategy.HUMAN_PRIORITY: "人类优先",
            AllocationStrategy.MACHINE_PRIORITY: "机器优先",
            AllocationStrategy.BALANCED: "平衡分配",
            AllocationStrategy.EFFICIENCY_OPTIMIZED: "效率优化",
            AllocationStrategy.RELIABILITY_OPTIMIZED: "可靠性优化"
        }
        
        for strategy, expected_name in strategy_names.items():
            actual_name = self.generator._get_strategy_name(strategy)
            assert actual_name == expected_name
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空任务层次结构
        empty_hierarchy = TaskHierarchy(root_task_id="empty")
        
        schemes = self.generator.generate_schemes(empty_hierarchy, num_schemes=1)
        assert len(schemes) == 1
        assert isinstance(schemes[0], AllocationScheme)
        
        # 测试单个任务
        single_task_hierarchy = TaskHierarchy(root_task_id="single")
        single_task = Task(
            id="single",
            name="单个任务",
            task_type=TaskType.META_OPERATION
        )
        single_task.attributes = TaskAttribute()
        single_task_hierarchy.add_task(single_task)
        
        schemes = self.generator.generate_schemes(single_task_hierarchy, num_schemes=2)
        assert len(schemes) == 2
        
        for scheme in schemes:
            assert len(scheme.task_allocations) == 1
            assert "single" in scheme.task_allocations


if __name__ == '__main__':
    pytest.main([__file__])
