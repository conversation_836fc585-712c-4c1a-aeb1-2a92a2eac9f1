"""
协同效能评估器

评估人机协同分配方案的效能和风险
提供多维度的效能评估指标和改进建议
"""

from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import logging
import numpy as np

from ..models.task_models import TaskHierarchy, Task
from .allocation_scheme_generator import AllocationScheme
from .human_machine_capability_analyzer import HumanMachineCapabilityAnalyzer


@dataclass
class EffectivenessMetrics:
    """效能评估指标"""
    overall_effectiveness: float = 0.0      # 总体效能 (0-1)
    task_completion_rate: float = 0.0       # 任务完成率 (0-1)
    time_efficiency: float = 0.0            # 时间效率 (0-1)
    resource_utilization: float = 0.0       # 资源利用率 (0-1)
    error_rate: float = 0.0                 # 错误率 (0-1, 越低越好)
    coordination_overhead: float = 0.0      # 协调开销 (0-1, 越低越好)
    adaptability: float = 0.0               # 适应性 (0-1)
    cost_effectiveness: float = 0.0         # 成本效益 (0-1)
    
    # 详细分解指标
    human_workload: float = 0.0             # 人类工作负荷 (0-1)
    machine_utilization: float = 0.0        # 机器利用率 (0-1)
    collaboration_quality: float = 0.0      # 协同质量 (0-1)
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'overall_effectiveness': self.overall_effectiveness,
            'task_completion_rate': self.task_completion_rate,
            'time_efficiency': self.time_efficiency,
            'resource_utilization': self.resource_utilization,
            'error_rate': self.error_rate,
            'coordination_overhead': self.coordination_overhead,
            'adaptability': self.adaptability,
            'cost_effectiveness': self.cost_effectiveness,
            'human_workload': self.human_workload,
            'machine_utilization': self.machine_utilization,
            'collaboration_quality': self.collaboration_quality
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, float]) -> 'EffectivenessMetrics':
        """从字典创建效能指标"""
        return cls(
            overall_effectiveness=data.get('overall_effectiveness', 0.0),
            task_completion_rate=data.get('task_completion_rate', 0.0),
            time_efficiency=data.get('time_efficiency', 0.0),
            resource_utilization=data.get('resource_utilization', 0.0),
            error_rate=data.get('error_rate', 0.0),
            coordination_overhead=data.get('coordination_overhead', 0.0),
            adaptability=data.get('adaptability', 0.0),
            cost_effectiveness=data.get('cost_effectiveness', 0.0),
            human_workload=data.get('human_workload', 0.0),
            machine_utilization=data.get('machine_utilization', 0.0),
            collaboration_quality=data.get('collaboration_quality', 0.0)
        )


@dataclass
class EvaluationReport:
    """评估报告"""
    scheme_id: str
    metrics: EffectivenessMetrics
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    improvement_suggestions: List[str] = field(default_factory=list)
    risk_factors: List[str] = field(default_factory=list)
    confidence_level: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'scheme_id': self.scheme_id,
            'metrics': self.metrics.to_dict(),
            'strengths': self.strengths,
            'weaknesses': self.weaknesses,
            'improvement_suggestions': self.improvement_suggestions,
            'risk_factors': self.risk_factors,
            'confidence_level': self.confidence_level
        }


class CollaborationEffectivenessEvaluator:
    """协同效能评估器"""
    
    def __init__(self):
        """初始化评估器"""
        self.logger = logging.getLogger(__name__)
        self.capability_analyzer = HumanMachineCapabilityAnalyzer()
        
        # 评估权重配置
        self.evaluation_weights = {
            "task_completion": 0.25,
            "time_efficiency": 0.20,
            "resource_utilization": 0.15,
            "error_rate": 0.15,
            "coordination_overhead": 0.15,
            "adaptability": 0.10
        }
        
        # 基准性能参数
        self.baseline_performance = {
            'human_only': {'completion_rate': 0.85, 'error_rate': 0.12, 'efficiency': 0.70},
            'machine_only': {'completion_rate': 0.95, 'error_rate': 0.05, 'efficiency': 0.90},
            'collaboration': {'completion_rate': 0.92, 'error_rate': 0.08, 'efficiency': 0.85}
        }
    
    def evaluate_scheme(self, scheme: AllocationScheme, 
                       task_hierarchy: TaskHierarchy) -> EvaluationReport:
        """
        评估分配方案的效能
        
        Args:
            scheme: 待评估的分配方案
            task_hierarchy: 任务层次结构
            
        Returns:
            EvaluationReport: 详细的评估报告
        """
        self.logger.info(f"开始评估分配方案: {scheme.name}")
        
        # 计算各项效能指标
        metrics = self._calculate_effectiveness_metrics(scheme, task_hierarchy)
        
        # 分析优势和劣势
        strengths, weaknesses = self._analyze_strengths_weaknesses(metrics, scheme, task_hierarchy)
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(metrics, scheme, task_hierarchy)
        
        # 识别风险因素
        risk_factors = self._identify_risk_factors(metrics, scheme, task_hierarchy)
        
        # 计算置信度
        confidence_level = self._calculate_confidence_level(metrics, scheme, task_hierarchy)
        
        report = EvaluationReport(
            scheme_id=scheme.scheme_id,
            metrics=metrics,
            strengths=strengths,
            weaknesses=weaknesses,
            improvement_suggestions=improvement_suggestions,
            risk_factors=risk_factors,
            confidence_level=confidence_level
        )
        
        self.logger.info(f"方案评估完成，总体效能: {metrics.overall_effectiveness:.3f}")
        return report
    
    def _calculate_effectiveness_metrics(self, scheme: AllocationScheme, 
                                       task_hierarchy: TaskHierarchy) -> EffectivenessMetrics:
        """计算效能指标"""
        all_tasks = task_hierarchy.get_all_tasks()
        if not all_tasks:
            return EffectivenessMetrics()
        
        # 统计分配情况
        allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
        
        # 计算各项指标
        task_completion_rate = self._calculate_task_completion_rate(scheme, all_tasks)
        time_efficiency = self._calculate_time_efficiency(scheme, all_tasks)
        resource_utilization = self._calculate_resource_utilization(scheme, all_tasks)
        error_rate = self._calculate_error_rate(scheme, all_tasks)
        coordination_overhead = self._calculate_coordination_overhead(scheme, all_tasks)
        adaptability = self._calculate_adaptability(scheme, all_tasks)
        cost_effectiveness = self._calculate_cost_effectiveness(scheme, all_tasks)
        
        # 计算详细分解指标
        human_workload = self._calculate_human_workload(scheme, all_tasks)
        machine_utilization = self._calculate_machine_utilization(scheme, all_tasks)
        collaboration_quality = self._calculate_collaboration_quality(scheme, all_tasks)
        
        # 计算总体效能
        overall_effectiveness = (
            self.evaluation_weights["task_completion"] * task_completion_rate +
            self.evaluation_weights["time_efficiency"] * time_efficiency +
            self.evaluation_weights["resource_utilization"] * resource_utilization +
            self.evaluation_weights["error_rate"] * (1 - error_rate) +  # 错误率越低越好
            self.evaluation_weights["coordination_overhead"] * (1 - coordination_overhead) +  # 开销越低越好
            self.evaluation_weights["adaptability"] * adaptability
        )
        
        return EffectivenessMetrics(
            overall_effectiveness=overall_effectiveness,
            task_completion_rate=task_completion_rate,
            time_efficiency=time_efficiency,
            resource_utilization=resource_utilization,
            error_rate=error_rate,
            coordination_overhead=coordination_overhead,
            adaptability=adaptability,
            cost_effectiveness=cost_effectiveness,
            human_workload=human_workload,
            machine_utilization=machine_utilization,
            collaboration_quality=collaboration_quality
        )
    
    def _get_allocation_statistics(self, scheme: AllocationScheme, 
                                 all_tasks: List[Task]) -> Dict[str, int]:
        """获取分配统计信息"""
        stats = {'human': 0, 'machine': 0, 'collaboration': 0}
        
        for task in all_tasks:
            allocation = scheme.task_allocations.get(task.id, 'human')
            stats[allocation] = stats.get(allocation, 0) + 1
        
        return stats
    
    def _calculate_task_completion_rate(self, scheme: AllocationScheme, 
                                      all_tasks: List[Task]) -> float:
        """计算任务完成率"""
        if not all_tasks:
            return 1.0
        
        total_completion_prob = 0.0
        
        for task in all_tasks:
            allocation = scheme.task_allocations.get(task.id, 'human')
            
            if allocation == 'human':
                human_score = self.capability_analyzer.evaluate_human_suitability(task)
                completion_prob = self.baseline_performance['human_only']['completion_rate'] * human_score
            elif allocation == 'machine':
                machine_score = self.capability_analyzer.evaluate_machine_suitability(task)
                completion_prob = self.baseline_performance['machine_only']['completion_rate'] * machine_score
            else:  # collaboration
                human_score = self.capability_analyzer.evaluate_human_suitability(task)
                machine_score = self.capability_analyzer.evaluate_machine_suitability(task)
                avg_score = (human_score + machine_score) / 2
                completion_prob = self.baseline_performance['collaboration']['completion_rate'] * avg_score
            
            total_completion_prob += completion_prob
        
        return min(1.0, total_completion_prob / len(all_tasks))
    
    def _calculate_time_efficiency(self, scheme: AllocationScheme, 
                                 all_tasks: List[Task]) -> float:
        """计算时间效率"""
        if not all_tasks:
            return 1.0
        
        total_efficiency = 0.0
        
        for task in all_tasks:
            allocation = scheme.task_allocations.get(task.id, 'human')
            
            # 基于分配类型和任务特征计算时间效率
            if allocation == 'machine':
                # 机器通常在处理速度上有优势
                efficiency = 0.9
                if hasattr(task.attributes, 'real_time_requirement') and task.attributes.real_time_requirement:
                    efficiency = 0.95
            elif allocation == 'human':
                # 人类在复杂任务上可能更高效
                efficiency = 0.7
                if hasattr(task.attributes, 'complexity') and task.attributes.complexity > 0.7:
                    efficiency = 0.8
            else:  # collaboration
                # 协同可能有额外的协调时间
                efficiency = 0.75
                coordination_penalty = 0.1 * len(scheme.collaboration_details.get(task.id, {}))
                efficiency = max(0.5, efficiency - coordination_penalty)
            
            total_efficiency += efficiency
        
        return total_efficiency / len(all_tasks)
    
    def _calculate_resource_utilization(self, scheme: AllocationScheme, 
                                      all_tasks: List[Task]) -> float:
        """计算资源利用率"""
        if not all_tasks:
            return 1.0
        
        # 简化的资源利用率计算
        allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
        total_tasks = len(all_tasks)
        
        # 理想情况下，人机任务应该相对平衡
        human_ratio = allocation_stats.get('human', 0) / total_tasks
        machine_ratio = allocation_stats.get('machine', 0) / total_tasks
        collaboration_ratio = allocation_stats.get('collaboration', 0) / total_tasks
        
        # 计算平衡度（越接近均匀分布，资源利用率越高）
        ideal_ratio = 1.0 / 3  # 理想情况下三种分配方式各占1/3
        balance_score = 1.0 - (
            abs(human_ratio - ideal_ratio) + 
            abs(machine_ratio - ideal_ratio) + 
            abs(collaboration_ratio - ideal_ratio)
        ) / 2
        
        return max(0.0, balance_score)
    
    def _calculate_error_rate(self, scheme: AllocationScheme, 
                            all_tasks: List[Task]) -> float:
        """计算错误率"""
        if not all_tasks:
            return 0.0
        
        total_error_rate = 0.0
        
        for task in all_tasks:
            allocation = scheme.task_allocations.get(task.id, 'human')
            
            if allocation == 'human':
                base_error_rate = self.baseline_performance['human_only']['error_rate']
                # 复杂任务人类错误率可能增加
                if hasattr(task.attributes, 'complexity') and task.attributes.complexity > 0.8:
                    base_error_rate *= 1.2
            elif allocation == 'machine':
                base_error_rate = self.baseline_performance['machine_only']['error_rate']
                # 创造性任务机器错误率可能增加
                task_desc = (task.description or "").lower()
                if any(keyword in task_desc for keyword in ['创新', '设计', '策划']):
                    base_error_rate *= 1.5
            else:  # collaboration
                base_error_rate = self.baseline_performance['collaboration']['error_rate']
                # 协同可能降低错误率
                base_error_rate *= 0.8
            
            total_error_rate += base_error_rate
        
        return min(1.0, total_error_rate / len(all_tasks))
    
    def _calculate_coordination_overhead(self, scheme: AllocationScheme, 
                                       all_tasks: List[Task]) -> float:
        """计算协调开销"""
        if not all_tasks:
            return 0.0
        
        collaboration_count = sum(1 for task in all_tasks 
                                if scheme.task_allocations.get(task.id) == 'collaboration')
        
        if collaboration_count == 0:
            return 0.0
        
        # 协调开销与协同任务比例相关
        collaboration_ratio = collaboration_count / len(all_tasks)
        
        # 基础协调开销
        base_overhead = collaboration_ratio * 0.3
        
        # 复杂协同任务的额外开销
        complex_collaboration_penalty = 0.0
        for task in all_tasks:
            if scheme.task_allocations.get(task.id) == 'collaboration':
                if hasattr(task.attributes, 'complexity') and task.attributes.complexity > 0.7:
                    complex_collaboration_penalty += 0.05
        
        total_overhead = base_overhead + complex_collaboration_penalty
        return min(1.0, total_overhead)
    
    def _calculate_adaptability(self, scheme: AllocationScheme, 
                              all_tasks: List[Task]) -> float:
        """计算适应性"""
        if not all_tasks:
            return 1.0
        
        # 适应性主要取决于人类参与度和协同程度
        allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
        total_tasks = len(all_tasks)
        
        human_ratio = allocation_stats.get('human', 0) / total_tasks
        collaboration_ratio = allocation_stats.get('collaboration', 0) / total_tasks
        
        # 人类和协同任务比例越高，适应性越强
        adaptability_score = (human_ratio * 0.8 + collaboration_ratio * 0.9 + 
                            allocation_stats.get('machine', 0) / total_tasks * 0.3)
        
        return min(1.0, adaptability_score)
    
    def _calculate_cost_effectiveness(self, scheme: AllocationScheme, 
                                    all_tasks: List[Task]) -> float:
        """计算成本效益"""
        if not all_tasks:
            return 1.0
        
        # 简化的成本效益计算
        allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
        total_tasks = len(all_tasks)
        
        # 假设机器成本较低，人类成本较高，协同成本中等
        machine_ratio = allocation_stats.get('machine', 0) / total_tasks
        human_ratio = allocation_stats.get('human', 0) / total_tasks
        collaboration_ratio = allocation_stats.get('collaboration', 0) / total_tasks
        
        # 成本效益 = 效能 / 成本
        avg_cost = machine_ratio * 0.3 + human_ratio * 1.0 + collaboration_ratio * 0.7
        avg_effectiveness = self._calculate_task_completion_rate(scheme, all_tasks)
        
        if avg_cost == 0:
            return 1.0
        
        return min(1.0, avg_effectiveness / avg_cost)
    
    def _calculate_human_workload(self, scheme: AllocationScheme, 
                                all_tasks: List[Task]) -> float:
        """计算人类工作负荷"""
        if not all_tasks:
            return 0.0
        
        human_tasks = [task for task in all_tasks 
                      if scheme.task_allocations.get(task.id) in ['human', 'collaboration']]
        
        if not human_tasks:
            return 0.0
        
        total_workload = 0.0
        for task in human_tasks:
            # 基于任务复杂度计算工作负荷
            complexity = getattr(task.attributes, 'complexity', 0.5) if hasattr(task, 'attributes') else 0.5
            importance = getattr(task.attributes, 'importance', 0.5) if hasattr(task, 'attributes') else 0.5
            workload = (complexity + importance) / 2
            
            # 协同任务的人类工作负荷相对较低
            if scheme.task_allocations.get(task.id) == 'collaboration':
                workload *= 0.7
            
            total_workload += workload
        
        # 归一化到0-1范围
        max_possible_workload = len(all_tasks)  # 假设所有任务都是高复杂度人类任务
        return min(1.0, total_workload / max_possible_workload)
    
    def _calculate_machine_utilization(self, scheme: AllocationScheme, 
                                     all_tasks: List[Task]) -> float:
        """计算机器利用率"""
        if not all_tasks:
            return 0.0
        
        machine_tasks = [task for task in all_tasks 
                        if scheme.task_allocations.get(task.id) in ['machine', 'collaboration']]
        
        return len(machine_tasks) / len(all_tasks)
    
    def _calculate_collaboration_quality(self, scheme: AllocationScheme, 
                                       all_tasks: List[Task]) -> float:
        """计算协同质量"""
        collaboration_tasks = [task for task in all_tasks 
                             if scheme.task_allocations.get(task.id) == 'collaboration']
        
        if not collaboration_tasks:
            return 1.0  # 没有协同任务时质量为满分
        
        total_quality = 0.0
        for task in collaboration_tasks:
            # 基于任务特征和协同详情计算质量
            collaboration_details = scheme.collaboration_details.get(task.id, {})
            
            # 有明确角色分工的协同质量更高
            if collaboration_details.get('human_role') and collaboration_details.get('machine_role'):
                quality = 0.8
            else:
                quality = 0.5
            
            # 有协调方法的协同质量更高
            if collaboration_details.get('coordination_method'):
                quality += 0.1
            
            # 置信度高的协同质量更高
            confidence = collaboration_details.get('confidence', 0.5)
            quality = quality * (0.5 + 0.5 * confidence)
            
            total_quality += quality
        
        return total_quality / len(collaboration_tasks)
    
    def compare_schemes(self, schemes: List[AllocationScheme], 
                       task_hierarchy: TaskHierarchy) -> Dict[str, EvaluationReport]:
        """
        比较多个分配方案的效能
        
        Args:
            schemes: 待比较的分配方案列表
            task_hierarchy: 任务层次结构
            
        Returns:
            Dict[str, EvaluationReport]: 各方案的评估报告
        """
        self.logger.info(f"开始比较 {len(schemes)} 个分配方案")
        
        results = {}
        for scheme in schemes:
            report = self.evaluate_scheme(scheme, task_hierarchy)
            results[scheme.scheme_id] = report
        
        # 添加比较分析
        self._add_comparative_analysis(results)
        
        return results
    
    def _add_comparative_analysis(self, results: Dict[str, EvaluationReport]) -> None:
        """添加比较分析"""
        if len(results) < 2:
            return
        
        # 找出各指标的最佳方案
        best_overall = max(results.items(), key=lambda x: x[1].metrics.overall_effectiveness)
        best_completion = max(results.items(), key=lambda x: x[1].metrics.task_completion_rate)
        best_efficiency = max(results.items(), key=lambda x: x[1].metrics.time_efficiency)
        
        # 为每个方案添加比较信息
        for scheme_id, report in results.items():
            if scheme_id == best_overall[0]:
                report.strengths.insert(0, "总体效能最优")
            if scheme_id == best_completion[0]:
                report.strengths.insert(0, "任务完成率最高")
            if scheme_id == best_efficiency[0]:
                report.strengths.insert(0, "时间效率最佳")
    
    def _analyze_strengths_weaknesses(self, metrics: EffectivenessMetrics, 
                                    scheme: AllocationScheme, 
                                    task_hierarchy: TaskHierarchy) -> Tuple[List[str], List[str]]:
        """分析优势和劣势"""
        strengths = []
        weaknesses = []
        
        # 分析各项指标
        if metrics.task_completion_rate > 0.9:
            strengths.append("任务完成率优秀")
        elif metrics.task_completion_rate < 0.7:
            weaknesses.append("任务完成率偏低")
        
        if metrics.time_efficiency > 0.8:
            strengths.append("时间效率很高")
        elif metrics.time_efficiency < 0.6:
            weaknesses.append("时间效率有待提升")
        
        if metrics.error_rate < 0.1:
            strengths.append("错误率控制良好")
        elif metrics.error_rate > 0.2:
            weaknesses.append("错误率较高，需要改进")
        
        if metrics.coordination_overhead < 0.2:
            strengths.append("协调开销较低")
        elif metrics.coordination_overhead > 0.4:
            weaknesses.append("协调开销过高")
        
        if metrics.adaptability > 0.8:
            strengths.append("适应性强")
        elif metrics.adaptability < 0.5:
            weaknesses.append("适应性不足")
        
        return strengths, weaknesses
    
    def _generate_improvement_suggestions(self, metrics: EffectivenessMetrics, 
                                        scheme: AllocationScheme, 
                                        task_hierarchy: TaskHierarchy) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if metrics.task_completion_rate < 0.8:
            suggestions.append("考虑重新分配部分任务以提高完成率")
        
        if metrics.time_efficiency < 0.7:
            suggestions.append("优化任务分配，将时间敏感任务更多分配给机器")
        
        if metrics.error_rate > 0.15:
            suggestions.append("加强质量控制，考虑增加人机协同验证环节")
        
        if metrics.coordination_overhead > 0.3:
            suggestions.append("简化协同流程，减少不必要的协调环节")
        
        if metrics.human_workload > 0.8:
            suggestions.append("人类工作负荷过重，建议将部分任务转移给机器")
        
        if metrics.machine_utilization < 0.4:
            suggestions.append("机器利用率偏低，可以增加机器承担的任务")
        
        return suggestions
    
    def _identify_risk_factors(self, metrics: EffectivenessMetrics, 
                             scheme: AllocationScheme, 
                             task_hierarchy: TaskHierarchy) -> List[str]:
        """识别风险因素"""
        risks = []
        
        if metrics.human_workload > 0.8:
            risks.append("人员过载风险")
        
        if metrics.coordination_overhead > 0.4:
            risks.append("协调复杂度过高风险")
        
        if metrics.error_rate > 0.2:
            risks.append("质量控制风险")
        
        if metrics.adaptability < 0.5:
            risks.append("环境变化适应风险")
        
        # 检查任务分配的集中度风险
        all_tasks = task_hierarchy.get_all_tasks()
        if all_tasks:
            allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
            total_tasks = len(all_tasks)
            
            if allocation_stats.get('human', 0) / total_tasks > 0.8:
                risks.append("过度依赖人力风险")
            elif allocation_stats.get('machine', 0) / total_tasks > 0.8:
                risks.append("过度依赖机器风险")
        
        return risks
    
    def _calculate_confidence_level(self, metrics: EffectivenessMetrics, 
                                  scheme: AllocationScheme, 
                                  task_hierarchy: TaskHierarchy) -> float:
        """计算置信度"""
        # 基于多个因素计算置信度
        factors = []
        
        # 任务数量因素（任务越多，评估越可靠）
        all_tasks = task_hierarchy.get_all_tasks()
        task_count_factor = min(1.0, len(all_tasks) / 10)  # 10个任务以上置信度较高
        factors.append(task_count_factor)
        
        # 分配平衡因素（分配越平衡，评估越可靠）
        if all_tasks:
            allocation_stats = self._get_allocation_statistics(scheme, all_tasks)
            balance_factor = 1.0 - abs(allocation_stats.get('human', 0) - allocation_stats.get('machine', 0)) / len(all_tasks)
            factors.append(balance_factor)
        
        # 指标一致性因素（各指标差异越小，评估越可靠）
        metric_values = [
            metrics.task_completion_rate,
            metrics.time_efficiency,
            metrics.resource_utilization,
            1 - metrics.error_rate,  # 转换为正向指标
            1 - metrics.coordination_overhead,  # 转换为正向指标
            metrics.adaptability
        ]
        consistency_factor = 1.0 - np.std(metric_values)
        factors.append(max(0.0, consistency_factor))
        
        return np.mean(factors)
