"""
GOMS模型分析器

基于GOMS(Goals, Operators, Methods, Selection rules)模型实现任务分析
"""

from typing import List, Dict, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
import uuid

from ..models.task_models import (
    Task, TaskType, MetaOperation, OperationType, TaskHierarchy
)


class GomsMethod(Enum):
    """GOMS方法类型"""
    KEYSTROKE_LEVEL = "按键级模型"
    CARD_MORAN_NEWELL = "CMN-GOMS"
    NATURAL_GOMS = "Natural-GOMS"
    NGOMSL = "NGOMSL"


@dataclass
class GomsOperator:
    """GOMS操作符"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    operation_type: OperationType = OperationType.PERCEPTION
    execution_time: float = 0.0  # 执行时间(秒)
    error_probability: float = 0.0  # 错误概率
    mental_workload: float = 0.0  # 心理负荷
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "operation_type": self.operation_type.value,
            "execution_time": self.execution_time,
            "error_probability": self.error_probability,
            "mental_workload": self.mental_workload
        }


@dataclass
class GomsRule:
    """GOMS选择规则"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    condition: str = ""  # 条件描述
    method_id: str = ""  # 选择的方法ID
    priority: int = 0  # 优先级
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "condition": self.condition,
            "method_id": self.method_id,
            "priority": self.priority
        }


@dataclass
class GomsMethod:
    """GOMS方法"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    goal_id: str = ""  # 目标任务ID
    operators: List[str] = field(default_factory=list)  # 操作符ID列表
    sequence: List[str] = field(default_factory=list)  # 执行序列
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "goal_id": self.goal_id,
            "operators": self.operators,
            "sequence": self.sequence
        }


class GomsAnalyzer:
    """GOMS模型分析器"""
    
    def __init__(self):
        self.operators = {}  # 操作符库
        self.methods = {}    # 方法库
        self.rules = {}      # 规则库
        self._init_standard_operators()
    
    def _init_standard_operators(self):
        """初始化标准操作符库"""
        standard_ops = [
            # 感知操作
            GomsOperator(
                name="视觉搜索",
                description="在界面中搜索目标元素",
                operation_type=OperationType.PERCEPTION,
                execution_time=1.2,
                error_probability=0.02,
                mental_workload=0.3
            ),
            GomsOperator(
                name="读取文本",
                description="读取屏幕上的文本信息",
                operation_type=OperationType.PERCEPTION,
                execution_time=0.8,
                error_probability=0.01,
                mental_workload=0.2
            ),
            
            # 认知操作
            GomsOperator(
                name="记忆检索",
                description="从长期记忆中检索信息",
                operation_type=OperationType.COGNITION,
                execution_time=1.5,
                error_probability=0.05,
                mental_workload=0.6
            ),
            GomsOperator(
                name="决策判断",
                description="基于信息进行决策判断",
                operation_type=OperationType.COGNITION,
                execution_time=2.0,
                error_probability=0.08,
                mental_workload=0.8
            ),
            GomsOperator(
                name="计算处理",
                description="进行数值计算或逻辑处理",
                operation_type=OperationType.COGNITION,
                execution_time=1.8,
                error_probability=0.06,
                mental_workload=0.7
            ),
            
            # 动作操作
            GomsOperator(
                name="鼠标点击",
                description="使用鼠标点击目标",
                operation_type=OperationType.MOTOR,
                execution_time=0.4,
                error_probability=0.01,
                mental_workload=0.1
            ),
            GomsOperator(
                name="键盘输入",
                description="使用键盘输入文本",
                operation_type=OperationType.MOTOR,
                execution_time=0.2,  # 每个字符
                error_probability=0.02,
                mental_workload=0.15
            ),
            GomsOperator(
                name="鼠标移动",
                description="移动鼠标到目标位置",
                operation_type=OperationType.MOTOR,
                execution_time=0.8,
                error_probability=0.005,
                mental_workload=0.05
            ),
            GomsOperator(
                name="手动操作",
                description="执行手动物理操作",
                operation_type=OperationType.MOTOR,
                execution_time=1.0,
                error_probability=0.03,
                mental_workload=0.2
            )
        ]
        
        for op in standard_ops:
            self.operators[op.id] = op
    
    def add_operator(self, operator: GomsOperator) -> str:
        """添加操作符"""
        self.operators[operator.id] = operator
        return operator.id
    
    def create_method(self, name: str, goal_id: str, operator_sequence: List[str]) -> str:
        """创建GOMS方法"""
        method = GomsMethod(
            name=name,
            goal_id=goal_id,
            operators=operator_sequence,
            sequence=operator_sequence
        )
        self.methods[method.id] = method
        return method.id
    
    def add_selection_rule(self, condition: str, method_id: str, priority: int = 0) -> str:
        """添加选择规则"""
        rule = GomsRule(
            condition=condition,
            method_id=method_id,
            priority=priority
        )
        self.rules[rule.id] = rule
        return rule.id
    
    def decompose_to_meta_operations(self, task: Task) -> List[MetaOperation]:
        """将任务分解为元操作"""
        if task.task_type != TaskType.OPERATION_SEQUENCE:
            raise ValueError("只能分解操作序列类型的任务")
        
        # 根据任务描述和IO模式推断需要的操作
        meta_ops = []
        
        # 分析输入操作
        if task.io_pattern.input_description:
            input_ops = self._analyze_input_operations(task.io_pattern.input_description)
            meta_ops.extend(input_ops)
        
        # 分析处理操作
        if task.io_pattern.action_description:
            action_ops = self._analyze_action_operations(task.io_pattern.action_description)
            meta_ops.extend(action_ops)
        
        # 分析输出操作
        if task.io_pattern.output_description:
            output_ops = self._analyze_output_operations(task.io_pattern.output_description)
            meta_ops.extend(output_ops)
        
        # 设置元操作的父任务关系
        for i, meta_op in enumerate(meta_ops):
            meta_op.parent_id = task.id
            meta_op.level = task.level + 1
            meta_op.name = f"{task.name}_元操作_{i+1}"
        
        return meta_ops
    
    def _analyze_input_operations(self, input_desc: str) -> List[MetaOperation]:
        """分析输入操作"""
        meta_ops = []
        
        # 简单的关键词匹配分析
        if "读取" in input_desc or "获取" in input_desc:
            meta_ops.append(self._create_meta_operation(
                "读取输入数据",
                OperationType.PERCEPTION,
                mental_preparation_time=0.2,
                positioning_time=0.1,
                action_time=0.8
            ))
        
        if "搜索" in input_desc or "查找" in input_desc:
            meta_ops.append(self._create_meta_operation(
                "搜索目标信息",
                OperationType.PERCEPTION,
                mental_preparation_time=0.3,
                positioning_time=0.2,
                action_time=1.2
            ))
        
        return meta_ops
    
    def _analyze_action_operations(self, action_desc: str) -> List[MetaOperation]:
        """分析处理操作"""
        meta_ops = []
        
        if "分析" in action_desc or "处理" in action_desc:
            meta_ops.append(self._create_meta_operation(
                "数据分析处理",
                OperationType.COGNITION,
                mental_preparation_time=0.5,
                positioning_time=0.0,
                action_time=2.0
            ))
        
        if "计算" in action_desc:
            meta_ops.append(self._create_meta_operation(
                "数值计算",
                OperationType.COGNITION,
                mental_preparation_time=0.3,
                positioning_time=0.0,
                action_time=1.8
            ))
        
        if "判断" in action_desc or "决策" in action_desc:
            meta_ops.append(self._create_meta_operation(
                "决策判断",
                OperationType.COGNITION,
                mental_preparation_time=0.4,
                positioning_time=0.0,
                action_time=2.0
            ))
        
        return meta_ops
    
    def _analyze_output_operations(self, output_desc: str) -> List[MetaOperation]:
        """分析输出操作"""
        meta_ops = []
        
        if "显示" in output_desc or "展示" in output_desc:
            meta_ops.append(self._create_meta_operation(
                "结果显示",
                OperationType.MOTOR,
                mental_preparation_time=0.2,
                positioning_time=0.3,
                action_time=0.5
            ))
        
        if "输出" in output_desc or "生成" in output_desc:
            meta_ops.append(self._create_meta_operation(
                "生成输出",
                OperationType.MOTOR,
                mental_preparation_time=0.3,
                positioning_time=0.2,
                action_time=0.8
            ))
        
        return meta_ops
    
    def _create_meta_operation(self, name: str, op_type: OperationType, 
                              mental_preparation_time: float,
                              positioning_time: float,
                              action_time: float) -> MetaOperation:
        """创建元操作"""
        return MetaOperation(
            name=name,
            operation_type=op_type,
            mental_preparation_time=mental_preparation_time,
            positioning_time=positioning_time,
            action_time=action_time
        )
    
    def calculate_execution_time(self, method_id: str) -> float:
        """计算方法执行时间"""
        if method_id not in self.methods:
            return 0.0
        
        method = self.methods[method_id]
        total_time = 0.0
        
        for op_id in method.operators:
            if op_id in self.operators:
                total_time += self.operators[op_id].execution_time
        
        return total_time
    
    def calculate_error_probability(self, method_id: str) -> float:
        """计算方法错误概率"""
        if method_id not in self.methods:
            return 0.0
        
        method = self.methods[method_id]
        # 使用独立事件概率公式计算总错误概率
        success_prob = 1.0
        
        for op_id in method.operators:
            if op_id in self.operators:
                op_success_prob = 1.0 - self.operators[op_id].error_probability
                success_prob *= op_success_prob
        
        return 1.0 - success_prob
    
    def calculate_mental_workload(self, method_id: str) -> float:
        """计算方法心理负荷"""
        if method_id not in self.methods:
            return 0.0
        
        method = self.methods[method_id]
        total_workload = 0.0
        
        for op_id in method.operators:
            if op_id in self.operators:
                total_workload += self.operators[op_id].mental_workload
        
        # 返回平均心理负荷
        return total_workload / len(method.operators) if method.operators else 0.0
    
    def select_best_method(self, goal_id: str, context: Dict[str, Any]) -> Optional[str]:
        """根据选择规则选择最佳方法"""
        applicable_methods = []
        
        # 找到适用的方法
        for method_id, method in self.methods.items():
            if method.goal_id == goal_id:
                applicable_methods.append(method_id)
        
        if not applicable_methods:
            return None
        
        if len(applicable_methods) == 1:
            return applicable_methods[0]
        
        # 应用选择规则
        best_method = None
        highest_priority = -1
        
        for rule_id, rule in self.rules.items():
            if rule.method_id in applicable_methods:
                # 简单的条件匹配（实际应用中需要更复杂的条件评估）
                if self._evaluate_condition(rule.condition, context):
                    if rule.priority > highest_priority:
                        highest_priority = rule.priority
                        best_method = rule.method_id
        
        return best_method or applicable_methods[0]
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估选择条件"""
        # 简化的条件评估，实际应用中需要更复杂的逻辑
        for key, value in context.items():
            if key in condition:
                return True
        return False
    
    def export_goms_model(self) -> Dict[str, Any]:
        """导出GOMS模型"""
        return {
            "operators": {op_id: op.to_dict() for op_id, op in self.operators.items()},
            "methods": {method_id: method.to_dict() for method_id, method in self.methods.items()},
            "rules": {rule_id: rule.to_dict() for rule_id, rule in self.rules.items()}
        }
