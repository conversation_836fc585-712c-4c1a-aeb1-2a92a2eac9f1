{% extends "base.html" %}

{% block title %}系统配置 - HMDM{% endblock %}

{% block page_title %}系统配置{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-success" id="save-config-btn">
        <i class="fas fa-save"></i> 保存配置
    </button>
    <button type="button" class="btn btn-secondary" id="reset-config-btn">
        <i class="fas fa-undo"></i> 重置
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> 系统配置参数
                </h5>
            </div>
            <div class="card-body">
                <form id="config-form">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">基础配置</h6>
                            
                            <div class="mb-3">
                                <label for="system_name" class="form-label">系统名称</label>
                                <input type="text" class="form-control" id="system_name" 
                                       value="{{ config.system_name }}" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="version" class="form-label">系统版本</label>
                                <input type="text" class="form-control" id="version" 
                                       value="{{ config.version }}" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="environment" class="form-label">运行环境</label>
                                <select class="form-select" id="environment">
                                    <option value="development" {% if config.environment == 'development' %}selected{% endif %}>开发环境</option>
                                    <option value="testing" {% if config.environment == 'testing' %}selected{% endif %}>测试环境</option>
                                    <option value="production" {% if config.environment == 'production' %}selected{% endif %}>生产环境</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="log_level" class="form-label">日志级别</label>
                                <select class="form-select" id="log_level">
                                    <option value="DEBUG" {% if config.log_level == 'DEBUG' %}selected{% endif %}>DEBUG</option>
                                    <option value="INFO" {% if config.log_level == 'INFO' %}selected{% endif %}>INFO</option>
                                    <option value="WARNING" {% if config.log_level == 'WARNING' %}selected{% endif %}>WARNING</option>
                                    <option value="ERROR" {% if config.log_level == 'ERROR' %}selected{% endif %}>ERROR</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary">性能配置</h6>
                            
                            <div class="mb-3">
                                <label for="max_concurrent_tasks" class="form-label">最大并发任务数</label>
                                <input type="number" class="form-control" id="max_concurrent_tasks" 
                                       value="{{ config.max_concurrent_tasks }}" min="1" max="100">
                            </div>
                            
                            <div class="mb-3">
                                <label for="cache_size" class="form-label">缓存大小</label>
                                <input type="number" class="form-control" id="cache_size" 
                                       value="{{ config.cache_size }}" min="100" max="10000">
                            </div>
                            
                            <div class="mb-3">
                                <label for="database_pool_size" class="form-label">数据库连接池大小</label>
                                <input type="number" class="form-control" id="database_pool_size" 
                                       value="{{ config.database_pool_size }}" min="5" max="100">
                            </div>
                            
                            <div class="mb-3">
                                <label for="session_timeout" class="form-label">会话超时时间（秒）</label>
                                <input type="number" class="form-control" id="session_timeout" 
                                       value="{{ config.session_timeout }}" min="300" max="86400">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary">安全配置</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="default_security_level" class="form-label">默认安全等级</label>
                                        <select class="form-select" id="default_security_level">
                                            <option value="公开" {% if config.default_security_level == '公开' %}selected{% endif %}>公开</option>
                                            <option value="内部" {% if config.default_security_level == '内部' %}selected{% endif %}>内部</option>
                                            <option value="秘密" {% if config.default_security_level == '秘密' %}selected{% endif %}>秘密</option>
                                            <option value="机密" {% if config.default_security_level == '机密' %}selected{% endif %}>机密</option>
                                            <option value="绝密" {% if config.default_security_level == '绝密' %}selected{% endif %}>绝密</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_encryption" 
                                                   {% if config.enable_encryption %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_encryption">
                                                启用数据加密
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary">启用的模块</h6>
                            <div class="row">
                                {% set all_modules = [
                                    ('situation_awareness', '态势感知系统'),
                                    ('communication', '通信协同系统'),
                                    ('decision_support', '决策支持系统'),
                                    ('training', '训练演练系统'),
                                    ('knowledge_base', '知识库系统'),
                                    ('simulation', '仿真建模系统'),
                                    ('scenarios', '场景模板系统')
                                ] %}
                                
                                {% for module_id, module_name in all_modules %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input enabled-module" type="checkbox" 
                                               id="module_{{ module_id }}" value="{{ module_id }}"
                                               {% if module_id in config.enabled_modules %}checked{% endif %}>
                                        <label class="form-check-label" for="module_{{ module_id }}">
                                            {{ module_name }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 配置预览模态框 -->
<div class="modal fade" id="configPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> 配置预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="config-preview-content" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-save-btn">确认保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 收集配置数据
    function collectConfigData() {
        const enabledModules = [];
        document.querySelectorAll('.enabled-module:checked').forEach(checkbox => {
            enabledModules.push(checkbox.value);
        });
        
        return {
            environment: document.getElementById('environment').value,
            log_level: document.getElementById('log_level').value,
            max_concurrent_tasks: parseInt(document.getElementById('max_concurrent_tasks').value),
            cache_size: parseInt(document.getElementById('cache_size').value),
            database_pool_size: parseInt(document.getElementById('database_pool_size').value),
            session_timeout: parseInt(document.getElementById('session_timeout').value),
            default_security_level: document.getElementById('default_security_level').value,
            enable_encryption: document.getElementById('enable_encryption').checked,
            enabled_modules: enabledModules
        };
    }
    
    // 保存配置
    function saveConfig() {
        const configData = collectConfigData();
        
        // 显示配置预览
        document.getElementById('config-preview-content').textContent = 
            JSON.stringify(configData, null, 2);
        
        const modal = new bootstrap.Modal(document.getElementById('configPreviewModal'));
        modal.show();
    }
    
    // 确认保存配置
    function confirmSaveConfig() {
        const configData = collectConfigData();
        
        makeRequest('/api/config/update', 'POST', configData)
            .then(response => {
                if (response.success) {
                    showNotification(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('configPreviewModal')).hide();
                } else {
                    showNotification(response.error || '保存失败', 'danger');
                }
            });
    }
    
    // 重置配置
    function resetConfig() {
        if (confirm('确定要重置所有配置吗？')) {
            location.reload();
        }
    }
    
    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('save-config-btn').addEventListener('click', saveConfig);
        document.getElementById('reset-config-btn').addEventListener('click', resetConfig);
        document.getElementById('confirm-save-btn').addEventListener('click', confirmSaveConfig);
    });
</script>
{% endblock %}
