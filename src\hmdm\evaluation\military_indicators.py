"""
军事专业指标体系

本模块定义了针对联合ZZ指挥系统的军事专业评估指标，
包括作战效能、指挥控制、保障支撑等军事专业指标类别。
"""

from typing import Dict, List, Any
from ..models.evaluation_models import IndicatorDefinition, IndicatorType


class MilitaryIndicatorSystem:
    """军事指标体系管理器"""
    
    @staticmethod
    def get_military_indicators() -> Dict[str, List[IndicatorDefinition]]:
        """获取军事专业指标体系"""
        return {
            "作战效能指标": MilitaryIndicatorSystem._get_combat_effectiveness_indicators(),
            "指挥控制指标": MilitaryIndicatorSystem._get_command_control_indicators()
        }
    
    @staticmethod
    def _get_combat_effectiveness_indicators() -> List[IndicatorDefinition]:
        """作战效能指标"""
        indicators = []

        # 火力打击效能
        indicator1 = IndicatorDefinition(
            id="combat_strike_effectiveness",
            name="火力打击效能",
            description="火力打击的精度、威力和效果",
            indicator_type=IndicatorType.PERFORMANCE,
            min_value=0.0,
            max_value=1.0,
            is_benefit=True,
            unit="效能指数"
        )
        indicator1.military_attributes = {
            "domain": "物理域",
            "measurement_method": "毁伤评估",
            "data_source": "作战系统",
            "update_frequency": "实时",
            "security_level": "机密",
            "weight": 0.25
        }
        indicators.append(indicator1)

        # 机动能力
        indicator2 = IndicatorDefinition(
            id="mobility_capability",
            name="机动能力",
            description="部队机动速度、范围和地形适应性",
            indicator_type=IndicatorType.PERFORMANCE,
            min_value=0.0,
            max_value=1.0,
            is_benefit=True,
            unit="机动指数"
        )
        indicator2.military_attributes = {
            "domain": "物理域",
            "measurement_method": "机动测试",
            "data_source": "导航系统",
            "update_frequency": "定期",
            "security_level": "秘密",
            "weight": 0.20
        }
        indicators.append(indicator2)

        # 防护能力
        indicator3 = IndicatorDefinition(
            id="protection_capability",
            name="防护能力",
            description="生存能力、抗干扰能力和隐蔽性",
            indicator_type=IndicatorType.SAFETY,
            min_value=0.0,
            max_value=1.0,
            is_benefit=True,
            unit="防护指数"
        )
        indicator3.military_attributes = {
            "domain": "物理域",
            "measurement_method": "防护测试",
            "data_source": "防护系统",
            "update_frequency": "实时",
            "security_level": "机密",
            "weight": 0.20
        }
        indicators.append(indicator3)

        return indicators
    
    @staticmethod
    def _get_command_control_indicators() -> List[IndicatorDefinition]:
        """指挥控制指标"""
        indicators = []

        # 指挥效率
        indicator1 = IndicatorDefinition(
            id="command_efficiency",
            name="指挥效率",
            description="决策时间、指令传达速度和执行反馈时间",
            indicator_type=IndicatorType.EFFICIENCY,
            min_value=0.0,
            max_value=1.0,
            is_benefit=True,
            unit="效率指数"
        )
        indicator1.military_attributes = {
            "domain": "认知域",
            "measurement_method": "时间测量",
            "data_source": "指挥系统",
            "update_frequency": "实时",
            "security_level": "机密",
            "weight": 0.30
        }
        indicators.append(indicator1)

        # 协同能力
        indicator2 = IndicatorDefinition(
            id="coordination_capability",
            name="协同能力",
            description="多军种协同、跨域协同和信息共享能力",
            indicator_type=IndicatorType.USABILITY,
            min_value=0.0,
            max_value=1.0,
            is_benefit=True,
            unit="协同指数"
        )
        indicator2.military_attributes = {
            "domain": "信息域",
            "measurement_method": "协同效果评估",
            "data_source": "协同系统",
            "update_frequency": "定期",
            "security_level": "机密",
            "weight": 0.25
        }
        indicators.append(indicator2)

        return indicators
    
    @staticmethod
    def _get_support_indicators() -> List[IndicatorDefinition]:
        """保障支撑指标"""
        return [
            IndicatorDefinition(
                id="logistics_efficiency",
                name="后勤保障效率",
                description="补给、维修和运输保障的效率",
                indicator_type=IndicatorType.EFFICIENCY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="保障指数",
                weight=0.30,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "保障效果评估",
                    "data_source": "后勤系统",
                    "update_frequency": "定期",
                    "security_level": "秘密"
                }
            ),
            IndicatorDefinition(
                id="equipment_reliability",
                name="装备可靠性",
                description="装备的故障率、维修性和可用性",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="可靠性指数",
                weight=0.25,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "可靠性测试",
                    "data_source": "装备系统",
                    "update_frequency": "实时",
                    "security_level": "秘密"
                }
            ),
            IndicatorDefinition(
                id="communication_reliability",
                name="通信可靠性",
                description="通信系统的稳定性和抗干扰能力",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="通信指数",
                weight=0.25,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "通信质量测试",
                    "data_source": "通信系统",
                    "update_frequency": "实时",
                    "security_level": "机密"
                }
            ),
            IndicatorDefinition(
                id="training_effectiveness",
                name="训练效果",
                description="人员训练的质量和效果",
                indicator_type=IndicatorType.USABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="训练指数",
                weight=0.20,
                military_attributes={
                    "domain": "认知域",
                    "measurement_method": "训练考核",
                    "data_source": "训练系统",
                    "update_frequency": "定期",
                    "security_level": "内部"
                }
            )
        ]
    
    @staticmethod
    def _get_information_system_indicators() -> List[IndicatorDefinition]:
        """信息系统指标"""
        return [
            IndicatorDefinition(
                id="system_response_time",
                name="系统响应时间",
                description="信息系统的响应速度",
                indicator_type=IndicatorType.PERFORMANCE,
                min_value=0.0,
                max_value=10.0,
                is_benefit=False,
                unit="秒",
                weight=0.25,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "响应时间测量",
                    "data_source": "监控系统",
                    "update_frequency": "实时",
                    "security_level": "内部"
                }
            ),
            IndicatorDefinition(
                id="data_accuracy",
                name="数据准确性",
                description="系统处理数据的准确程度",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="准确率",
                weight=0.30,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "准确性验证",
                    "data_source": "数据系统",
                    "update_frequency": "定期",
                    "security_level": "机密"
                }
            ),
            IndicatorDefinition(
                id="system_availability",
                name="系统可用性",
                description="系统正常运行的时间比例",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="可用率",
                weight=0.25,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "可用性统计",
                    "data_source": "监控系统",
                    "update_frequency": "实时",
                    "security_level": "内部"
                }
            ),
            IndicatorDefinition(
                id="information_security",
                name="信息安全性",
                description="信息系统的安全防护能力",
                indicator_type=IndicatorType.SAFETY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="安全指数",
                weight=0.20,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "安全评估",
                    "data_source": "安全系统",
                    "update_frequency": "定期",
                    "security_level": "绝密"
                }
            )
        ]

    @staticmethod
    def _get_personnel_indicators() -> List[IndicatorDefinition]:
        """人员素质指标"""
        return [
            IndicatorDefinition(
                id="professional_competency",
                name="专业能力",
                description="人员的专业技能和知识水平",
                indicator_type=IndicatorType.USABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="能力指数",
                weight=0.30,
                military_attributes={
                    "domain": "认知域",
                    "measurement_method": "能力评估",
                    "data_source": "人事系统",
                    "update_frequency": "定期",
                    "security_level": "内部"
                }
            ),
            IndicatorDefinition(
                id="psychological_stability",
                name="心理稳定性",
                description="人员在压力环境下的心理稳定程度",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="稳定指数",
                weight=0.25,
                military_attributes={
                    "domain": "认知域",
                    "measurement_method": "心理测评",
                    "data_source": "医疗系统",
                    "update_frequency": "定期",
                    "security_level": "内部"
                }
            )
        ]

    @staticmethod
    def _get_equipment_indicators() -> List[IndicatorDefinition]:
        """装备技术指标"""
        return [
            IndicatorDefinition(
                id="technical_advancement",
                name="技术先进性",
                description="装备技术的先进程度和创新性",
                indicator_type=IndicatorType.PERFORMANCE,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="先进指数",
                weight=0.30,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "技术评估",
                    "data_source": "装备系统",
                    "update_frequency": "定期",
                    "security_level": "机密"
                }
            ),
            IndicatorDefinition(
                id="interoperability",
                name="互操作性",
                description="装备与其他系统的兼容和协同能力",
                indicator_type=IndicatorType.USABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="互操作指数",
                weight=0.35,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "兼容性测试",
                    "data_source": "集成系统",
                    "update_frequency": "定期",
                    "security_level": "机密"
                }
            ),
            IndicatorDefinition(
                id="cost_effectiveness",
                name="成本效益",
                description="装备的性价比和经济效益",
                indicator_type=IndicatorType.COST,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="效益指数",
                weight=0.35,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "成本分析",
                    "data_source": "财务系统",
                    "update_frequency": "定期",
                    "security_level": "内部"
                }
            )
        ]

    @staticmethod
    def _get_environment_indicators() -> List[IndicatorDefinition]:
        """环境适应指标"""
        return [
            IndicatorDefinition(
                id="weather_adaptability",
                name="气象适应性",
                description="在不同气象条件下的作战能力",
                indicator_type=IndicatorType.RELIABILITY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="适应指数",
                weight=0.25,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "环境测试",
                    "data_source": "气象系统",
                    "update_frequency": "实时",
                    "security_level": "内部"
                }
            ),
            IndicatorDefinition(
                id="electromagnetic_resistance",
                name="电磁环境适应性",
                description="在复杂电磁环境下的工作能力",
                indicator_type=IndicatorType.SAFETY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="抗扰指数",
                weight=0.40,
                military_attributes={
                    "domain": "信息域",
                    "measurement_method": "电磁兼容测试",
                    "data_source": "电磁系统",
                    "update_frequency": "定期",
                    "security_level": "机密"
                }
            ),
            IndicatorDefinition(
                id="threat_environment_adaptability",
                name="威胁环境适应性",
                description="在高威胁环境下的生存和作战能力",
                indicator_type=IndicatorType.SAFETY,
                min_value=0.0,
                max_value=1.0,
                is_benefit=True,
                unit="生存指数",
                weight=0.35,
                military_attributes={
                    "domain": "物理域",
                    "measurement_method": "威胁测试",
                    "data_source": "威胁系统",
                    "update_frequency": "定期",
                    "security_level": "机密"
                }
            )
        ]
