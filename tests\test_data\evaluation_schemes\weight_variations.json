{"metadata": {"name": "权重变化测试数据", "description": "用于敏感性分析的多组权重配置", "version": "1.0", "created_date": "2024-01-01", "purpose": "sensitivity_analysis"}, "base_indicators": ["workload", "efficiency", "reliability", "usability", "safety", "cost", "performance"], "weight_variations": [{"name": "均等权重", "description": "所有指标权重相等", "weights": {"workload": 0.143, "efficiency": 0.143, "reliability": 0.143, "usability": 0.143, "safety": 0.143, "cost": 0.143, "performance": 0.142}, "total": 1.0}, {"name": "效率主导", "description": "效率指标权重最高", "weights": {"workload": 0.1, "efficiency": 0.4, "reliability": 0.15, "usability": 0.1, "safety": 0.1, "cost": 0.1, "performance": 0.05}, "total": 1.0}, {"name": "可靠性主导", "description": "可靠性指标权重最高", "weights": {"workload": 0.1, "efficiency": 0.15, "reliability": 0.4, "usability": 0.1, "safety": 0.15, "cost": 0.05, "performance": 0.05}, "total": 1.0}, {"name": "成本主导", "description": "成本指标权重最高", "weights": {"workload": 0.15, "efficiency": 0.15, "reliability": 0.15, "usability": 0.1, "safety": 0.05, "cost": 0.35, "performance": 0.05}, "total": 1.0}, {"name": "用户体验主导", "description": "可用性和用户体验权重最高", "weights": {"workload": 0.2, "efficiency": 0.15, "reliability": 0.15, "usability": 0.35, "safety": 0.1, "cost": 0.03, "performance": 0.02}, "total": 1.0}, {"name": "安全优先", "description": "安全性指标权重最高", "weights": {"workload": 0.1, "efficiency": 0.1, "reliability": 0.25, "usability": 0.1, "safety": 0.35, "cost": 0.05, "performance": 0.05}, "total": 1.0}, {"name": "性能优先", "description": "性能指标权重最高", "weights": {"workload": 0.1, "efficiency": 0.25, "reliability": 0.15, "usability": 0.1, "safety": 0.1, "cost": 0.05, "performance": 0.25}, "total": 1.0}, {"name": "极端权重1", "description": "单一指标占主导地位", "weights": {"workload": 0.05, "efficiency": 0.7, "reliability": 0.05, "usability": 0.05, "safety": 0.05, "cost": 0.05, "performance": 0.05}, "total": 1.0}, {"name": "极端权重2", "description": "两个指标占主导地位", "weights": {"workload": 0.05, "efficiency": 0.45, "reliability": 0.4, "usability": 0.03, "safety": 0.03, "cost": 0.02, "performance": 0.02}, "total": 1.0}, {"name": "微调权重", "description": "在均等权重基础上的微调", "weights": {"workload": 0.14, "efficiency": 0.15, "reliability": 0.145, "usability": 0.135, "safety": 0.155, "cost": 0.13, "performance": 0.145}, "total": 1.0}], "sensitivity_test_cases": [{"name": "权重变化影响测试", "description": "测试权重变化对决策结果的影响", "test_data": {"alternatives": [{"name": "方案A", "values": {"workload": 0.3, "efficiency": 0.8, "reliability": 0.7, "usability": 0.6, "safety": 0.9, "cost": 0.4, "performance": 0.7}}, {"name": "方案B", "values": {"workload": 0.7, "efficiency": 0.6, "reliability": 0.9, "usability": 0.8, "safety": 0.7, "cost": 0.6, "performance": 0.5}}, {"name": "方案C", "values": {"workload": 0.5, "efficiency": 0.7, "reliability": 0.8, "usability": 0.7, "safety": 0.8, "cost": 0.5, "performance": 0.8}}]}, "expected_behaviors": [{"weight_config": "效率主导", "expected_winner": "方案A", "reason": "方案A在效率指标上表现最好"}, {"weight_config": "可靠性主导", "expected_winner": "方案B", "reason": "方案B在可靠性指标上表现最好"}, {"weight_config": "均等权重", "expected_winner": "方案C", "reason": "方案C在各指标上表现均衡"}]}, {"name": "权重稳定性测试", "description": "测试微小权重变化对结果稳定性的影响", "tolerance": 0.05, "expected_result": "排序基本稳定"}, {"name": "极端权重鲁棒性测试", "description": "测试极端权重配置下的系统鲁棒性", "extreme_configs": ["极端权重1", "极端权重2"], "expected_result": "系统正常运行，无异常错误"}], "validation_rules": [{"rule": "weight_sum_validation", "description": "所有权重配置的权重之和必须为1", "tolerance": 0.001}, {"rule": "weight_range_validation", "description": "所有权重值必须在0-1范围内", "min_value": 0.0, "max_value": 1.0}, {"rule": "weight_precision_validation", "description": "权重值精度不超过3位小数", "max_decimal_places": 3}]}