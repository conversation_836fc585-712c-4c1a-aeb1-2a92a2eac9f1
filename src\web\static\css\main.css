/* HMDM Web界面样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-brand i {
    margin-right: 8px;
}

/* 状态面板 */
.status-item {
    padding: 10px;
    border-radius: 5px;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    margin-bottom: 10px;
}

.status-label {
    font-weight: 600;
    color: #495057;
    display: block;
    font-size: 0.9rem;
}

.status-value {
    color: #007bff;
    font-weight: 500;
    font-size: 1rem;
}

.status-value.success {
    color: #28a745;
}

.status-value.warning {
    color: #ffc107;
}

.status-value.danger {
    color: #dc3545;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #ffffff;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
}

.card-header i {
    margin-right: 8px;
    color: #007bff;
}

/* 任务层次结构显示 */
#task-hierarchy-display {
    background-color: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-y: auto;
    max-height: 400px;
}

.task-tree {
    list-style: none;
    padding-left: 0;
}

.task-tree li {
    margin: 5px 0;
    padding-left: 20px;
    position: relative;
}

.task-tree li:before {
    content: "├─";
    position: absolute;
    left: 0;
    color: #6c757d;
}

.task-tree li:last-child:before {
    content: "└─";
}

.task-item {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    background-color: #e9ecef;
    margin-left: 5px;
}

.task-type {
    font-size: 0.8rem;
    color: #6c757d;
    margin-left: 5px;
}

/* 备选方案列表 */
#alternatives-list {
    background-color: #f8f9fa;
    max-height: 200px;
    overflow-y: auto;
}

.alternative-item-display {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
}

.alternative-name {
    font-weight: 600;
    color: #495057;
}

.alternative-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 2px;
}

.alternative-attributes {
    margin-top: 5px;
}

.attribute-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
    margin-right: 5px;
    margin-top: 2px;
}

/* 结果显示 */
#results-display {
    min-height: 300px;
}

.result-section {
    margin-bottom: 30px;
}

.result-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 5px;
}

.recommended-alternative {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.recommended-alternative .alternative-name {
    color: #155724;
    font-size: 1.1rem;
    font-weight: bold;
}

.score-display {
    font-size: 1.2rem;
    font-weight: bold;
    color: #28a745;
    margin-top: 10px;
}

/* 表格样式 */
.results-table {
    margin-top: 20px;
}

.results-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.rank-1 {
    background-color: #fff3cd;
}

.rank-2 {
    background-color: #f8f9fa;
}

.rank-3 {
    background-color: #f1f3f4;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.alternative-item {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    background-color: #f8f9fa;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.loading-content p {
    margin: 0;
    color: #495057;
    font-weight: 500;
}

/* 按钮样式 */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 8px 16px;
}

.btn i {
    margin-right: 5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .status-item {
        margin-bottom: 15px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 工具提示 */
.tooltip-inner {
    background-color: #495057;
    color: white;
    font-size: 0.875rem;
}

/* 进度条 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* 警告和错误样式 */
.alert {
    border: none;
    border-radius: 5px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
