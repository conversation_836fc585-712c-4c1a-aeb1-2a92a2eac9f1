"""
HMDM Web管理界面

基于Flask的Web管理界面，提供系统监控、配置管理和数据可视化功能。
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_socketio import Socket<PERSON>, emit
import threading
import time

from src.hmdm.core.system_manager import HMDMSystemManager, SystemStatus, ModuleStatus
from src.hmdm.security.military_security import SecurityLevel

# 延迟导入API模块
def import_allocation_api():
    try:
        from src.hmdm.web.api import create_allocation_api
        return create_allocation_api
    except ImportError:
        return None


class HMDMWebApp:
    """HMDM Web应用"""
    
    def __init__(self, system_manager: HMDMSystemManager, host: str = "127.0.0.1", port: int = 5000):
        self.system_manager = system_manager
        self.host = host
        self.port = port
        
        # 创建Flask应用
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.secret_key = 'hmdm_secret_key_2024'
        
        # 创建SocketIO实例用于实时通信
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 实时数据推送线程
        self.data_push_thread = None
        self.stop_data_push = False
        
        # 注册路由
        self._register_routes()
        self._register_socketio_events()

        # 注册API Blueprint
        self._register_api_blueprints()
    
    def _register_routes(self):
        """注册Web路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('index.html')
        
        @self.app.route('/dashboard')
        def dashboard():
            """系统仪表板"""
            system_status = self.system_manager.get_system_status()
            return render_template('dashboard.html', system_status=system_status)
        
        @self.app.route('/modules')
        def modules():
            """模块管理页面"""
            system_status = self.system_manager.get_system_status()
            module_descriptions = {
                'situation_awareness': '态势感知与预测系统',
                'communication': '军事通信与协同系统',
                'decision_support': '军事决策支持系统',
                'training': '军事训练与演练系统',
                'knowledge_base': '军事知识库与专家系统',
                'simulation': '军事仿真与建模系统',
                'scenarios': '辅助决策场景模板系统',
                'human_machine_allocation': '人机功能分配决策系统'
            }
            return render_template('modules.html',
                                 modules=system_status.get('modules', {}),
                                 system_status=system_status.get('system_status'),
                                 module_descriptions=module_descriptions)
        
        @self.app.route('/config')
        def config():
            """配置管理页面"""
            config_data = self.system_manager.config.to_dict()
            return render_template('config.html', config=config_data)
        
        @self.app.route('/logs')
        def logs():
            """日志查看页面"""
            return render_template('logs.html')

        @self.app.route('/allocation')
        def allocation():
            """人机功能分配页面"""
            allocation_system = self.system_manager.get_allocation_system()
            allocation_config = self.system_manager.get_allocation_config()

            return render_template('allocation.html',
                                 allocation_system=allocation_system,
                                 allocation_config=allocation_config.to_dict() if allocation_config else None)

        @self.app.route('/allocation/task-input')
        def allocation_task_input():
            """任务输入页面"""
            return render_template('allocation_task_input.html')

        @self.app.route('/allocation/scheme-comparison')
        def allocation_scheme_comparison():
            """方案比较页面"""
            return render_template('allocation_scheme_comparison.html')

        @self.app.route('/allocation/effectiveness-evaluation')
        def allocation_effectiveness_evaluation():
            """效能评估页面"""
            return render_template('allocation_effectiveness_evaluation.html')

        @self.app.route('/allocation/decision-result')
        def allocation_decision_result():
            """决策结果页面"""
            return render_template('allocation_decision_result.html')

        @self.app.route('/api-docs')
        def api_docs():
            """API文档页面"""
            return render_template('api_docs.html')

        @self.app.route('/config-management')
        def config_management():
            """配置管理页面"""
            return render_template('config_management.html')
        
        @self.app.route('/api/system/status')
        def api_system_status():
            """获取系统状态API"""
            try:
                status = self.system_manager.get_system_status()
                return jsonify({
                    'success': True,
                    'data': status
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/system/start', methods=['POST'])
        def api_system_start():
            """启动系统API"""
            try:
                success = self.system_manager.start_system()
                return jsonify({
                    'success': success,
                    'message': '系统启动成功' if success else '系统启动失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/system/stop', methods=['POST'])
        def api_system_stop():
            """停止系统API"""
            try:
                success = self.system_manager.stop_system()
                return jsonify({
                    'success': success,
                    'message': '系统停止成功' if success else '系统停止失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/module/<module_name>/restart', methods=['POST'])
        def api_module_restart(module_name):
            """重启模块API"""
            try:
                success = self.system_manager.restart_module(module_name)
                return jsonify({
                    'success': success,
                    'message': f'模块 {module_name} 重启成功' if success else f'模块 {module_name} 重启失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/config/update', methods=['POST'])
        def api_config_update():
            """更新配置API"""
            try:
                # 尝试解析JSON数据
                try:
                    config_data = request.get_json()
                except Exception:
                    return jsonify({
                        'success': False,
                        'error': '无效的JSON格式'
                    }), 400

                if not config_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的配置数据'
                    }), 400

                success = self.system_manager.update_config(config_data)
                return jsonify({
                    'success': success,
                    'message': '配置更新成功' if success else '配置更新失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/modules/<module_name>/data')
        def api_module_data(module_name):
            """获取模块数据API"""
            try:
                module_instance = self.system_manager.get_module(module_name)
                if not module_instance:
                    return jsonify({
                        'success': False,
                        'error': f'模块 {module_name} 不存在或未运行'
                    }), 404
                
                # 根据不同模块返回不同的数据
                data = self._get_module_data(module_name, module_instance)
                
                return jsonify({
                    'success': True,
                    'data': data
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/logs')
        def api_logs():
            """获取日志API"""
            try:
                # 读取最近的日志条目
                log_entries = self._read_recent_logs()
                return jsonify({
                    'success': True,
                    'data': log_entries
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        # 人机分配相关API
        @self.app.route('/api/allocation/config')
        def api_allocation_config():
            """获取人机分配配置API"""
            try:
                config = self.system_manager.get_allocation_config()
                return jsonify({
                    'success': True,
                    'data': config.to_dict() if config else None
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/allocation/config', methods=['POST'])
        def api_allocation_config_update():
            """更新人机分配配置API"""
            try:
                config_data = request.get_json()
                if not config_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的配置数据'
                    }), 400

                from src.hmdm.allocation.allocation_config import AllocationConfig
                new_config = AllocationConfig.from_dict(config_data)
                success = self.system_manager.update_allocation_config(new_config)

                return jsonify({
                    'success': success,
                    'message': '配置更新成功' if success else '配置更新失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/allocation/allocate', methods=['POST'])
        def api_allocation_allocate():
            """执行人机功能分配API"""
            try:
                request_data = request.get_json()
                if not request_data:
                    return jsonify({
                        'success': False,
                        'error': '无效的请求数据'
                    }), 400

                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503

                # 解析任务层次结构
                task_hierarchy_data = request_data.get('task_hierarchy')
                if not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少任务层次结构数据'
                    }), 400

                # 这里需要将JSON数据转换为TaskHierarchy对象
                # 简化处理，实际应用中需要完整的序列化/反序列化
                from src.hmdm.models.task_models import TaskHierarchy
                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)

                # 获取约束条件和偏好设置
                constraints = request_data.get('constraints')
                preferences = request_data.get('preferences')
                optimization_criteria = request_data.get('optimization_criteria')

                # 执行分配
                result = allocation_system.allocate_functions(
                    task_hierarchy,
                    constraints=constraints,
                    preferences=preferences,
                    optimization_criteria=optimization_criteria
                )

                return jsonify({
                    'success': True,
                    'data': result.to_dict()
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/allocation/schemes/compare', methods=['POST'])
        def api_allocation_schemes_compare():
            """比较分配方案API"""
            try:
                request_data = request.get_json()
                schemes_data = request_data.get('schemes', [])
                task_hierarchy_data = request_data.get('task_hierarchy')

                if not schemes_data or not task_hierarchy_data:
                    return jsonify({
                        'success': False,
                        'error': '缺少方案或任务数据'
                    }), 400

                allocation_system = self.system_manager.get_allocation_system()
                if not allocation_system:
                    return jsonify({
                        'success': False,
                        'error': '人机分配系统未启动'
                    }), 503

                # 转换数据格式（简化处理）
                from src.hmdm.models.task_models import TaskHierarchy
                from src.hmdm.allocation.allocation_scheme_generator import AllocationScheme

                task_hierarchy = TaskHierarchy.from_dict(task_hierarchy_data)
                schemes = [AllocationScheme.from_dict(scheme_data) for scheme_data in schemes_data]

                # 比较方案
                comparison_results = allocation_system.effectiveness_evaluator.compare_schemes(
                    schemes, task_hierarchy
                )

                # 转换结果为JSON格式
                results_dict = {}
                for scheme_id, report in comparison_results.items():
                    results_dict[scheme_id] = report.to_dict()

                return jsonify({
                    'success': True,
                    'data': results_dict
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        # 配置管理相关API
        @self.app.route('/api/config/profiles', methods=['GET'])
        def api_config_profiles():
            """获取配置档案列表API"""
            try:
                profiles = self.system_manager.list_config_profiles()
                return jsonify({
                    'success': True,
                    'data': profiles
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/config/profiles', methods=['POST'])
        def api_create_config_profile():
            """创建配置档案API"""
            try:
                request_data = request.get_json()
                name = request_data.get('name')
                description = request_data.get('description', '')

                if not name:
                    return jsonify({
                        'success': False,
                        'error': '档案名称不能为空'
                    }), 400

                success = self.system_manager.create_config_profile(name, description)
                return jsonify({
                    'success': success,
                    'message': '配置档案创建成功' if success else '配置档案创建失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/config/profiles/<profile_name>', methods=['POST'])
        def api_load_config_profile(profile_name):
            """加载配置档案API"""
            try:
                success = self.system_manager.load_config_profile(profile_name)
                return jsonify({
                    'success': success,
                    'message': '配置档案加载成功' if success else '配置档案加载失败'
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/config/export', methods=['POST'])
        def api_export_config():
            """导出配置API"""
            try:
                request_data = request.get_json()
                export_file = request_data.get('export_file', 'config_export.json')
                include_profiles = request_data.get('include_profiles', False)

                success = self.system_manager.export_config(export_file, include_profiles)
                return jsonify({
                    'success': success,
                    'message': '配置导出成功' if success else '配置导出失败',
                    'export_file': export_file
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/config/validate', methods=['POST'])
        def api_validate_config():
            """验证配置API"""
            try:
                validation_result = self.system_manager.validate_config()
                return jsonify({
                    'success': True,
                    'data': validation_result
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

    def _register_api_blueprints(self):
        """注册API Blueprint"""
        try:
            # 注册人机分配API
            create_allocation_api_func = import_allocation_api()
            if create_allocation_api_func:
                allocation_api_bp = create_allocation_api_func(self.system_manager)
                self.app.register_blueprint(allocation_api_bp)
                self.logger.info("人机分配API注册成功")
            else:
                self.logger.warning("人机分配API模块导入失败")
        except Exception as e:
            self.logger.error(f"API Blueprint注册失败: {e}")

    def _register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接事件"""
            self.logger.info('客户端已连接')
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接事件"""
            self.logger.info('客户端已断开连接')
        
        @self.socketio.on('subscribe_realtime')
        def handle_subscribe_realtime():
            """订阅实时数据"""
            self.logger.info('客户端订阅实时数据')
            if not self.data_push_thread or not self.data_push_thread.is_alive():
                self._start_data_push()
    
    def _get_module_data(self, module_name: str, module_instance: Any) -> Dict[str, Any]:
        """获取模块特定数据"""
        try:
            if module_name == "situation_awareness":
                # 态势感知模块数据
                return {
                    'entities_count': len(getattr(module_instance, 'entities', {})),
                    'assessments_count': len(getattr(module_instance, 'assessments', [])),
                    'last_update': datetime.now().isoformat()
                }
            elif module_name == "communication":
                # 通信模块数据
                return {
                    'units_count': len(getattr(module_instance, 'units', {})),
                    'messages_sent': getattr(module_instance, 'message_count', 0),
                    'active_channels': len(getattr(module_instance, 'channels', {}))
                }
            elif module_name == "knowledge_base":
                # 知识库模块数据
                stats = module_instance.get_knowledge_statistics()
                return {
                    'knowledge_items': stats.get('total_items', 0),
                    'rules_count': stats.get('total_rules', 0),
                    'average_confidence': stats.get('average_confidence', 0)
                }
            elif module_name == "training":
                # 训练模块数据
                return {
                    'objectives_count': len(getattr(module_instance, 'training_objectives', {})),
                    'scenarios_count': len(getattr(module_instance, 'training_scenarios', {})),
                    'results_count': len(getattr(module_instance, 'training_results', []))
                }
            elif module_name == "simulation":
                # 仿真模块数据
                return {
                    'current_scenario': getattr(module_instance, 'current_scenario', None),
                    'simulation_results': len(getattr(module_instance, 'simulation_results', [])),
                    'is_running': getattr(module_instance, 'is_running', False)
                }
            else:
                # 默认数据
                return {
                    'status': 'running',
                    'last_update': datetime.now().isoformat()
                }
        except Exception as e:
            self.logger.error(f"获取模块 {module_name} 数据失败: {e}")
            return {
                'error': str(e),
                'last_update': datetime.now().isoformat()
            }
    
    def _read_recent_logs(self, lines: int = 100) -> List[Dict[str, Any]]:
        """读取最近的日志条目"""
        try:
            log_file = self.system_manager.config.log_file
            log_entries = []
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    # 读取最后N行
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    
                    for line in recent_lines:
                        line = line.strip()
                        if line:
                            # 简单的日志解析
                            parts = line.split(' - ', 3)
                            if len(parts) >= 4:
                                log_entries.append({
                                    'timestamp': parts[0],
                                    'logger': parts[1],
                                    'level': parts[2],
                                    'message': parts[3]
                                })
                            else:
                                log_entries.append({
                                    'timestamp': datetime.now().isoformat(),
                                    'logger': 'unknown',
                                    'level': 'INFO',
                                    'message': line
                                })
            except FileNotFoundError:
                log_entries.append({
                    'timestamp': datetime.now().isoformat(),
                    'logger': 'web',
                    'level': 'WARNING',
                    'message': f'日志文件不存在: {log_file}'
                })
            
            return log_entries
            
        except Exception as e:
            self.logger.error(f"读取日志失败: {e}")
            return [{
                'timestamp': datetime.now().isoformat(),
                'logger': 'web',
                'level': 'ERROR',
                'message': f'读取日志失败: {str(e)}'
            }]
    
    def _start_data_push(self):
        """启动实时数据推送"""
        if self.data_push_thread and self.data_push_thread.is_alive():
            return
        
        self.stop_data_push = False
        self.data_push_thread = threading.Thread(target=self._data_push_worker)
        self.data_push_thread.daemon = True
        self.data_push_thread.start()
    
    def _data_push_worker(self):
        """实时数据推送工作线程"""
        while not self.stop_data_push:
            try:
                # 获取系统状态
                system_status = self.system_manager.get_system_status()
                
                # 推送系统状态
                self.socketio.emit('system_status_update', {
                    'timestamp': datetime.now().isoformat(),
                    'data': system_status
                })
                
                # 推送模块数据
                for module_name in self.system_manager.config.enabled_modules:
                    module_instance = self.system_manager.get_module(module_name)
                    if module_instance:
                        module_data = self._get_module_data(module_name, module_instance)
                        self.socketio.emit('module_data_update', {
                            'module': module_name,
                            'timestamp': datetime.now().isoformat(),
                            'data': module_data
                        })
                
                # 等待5秒后再次推送
                time.sleep(5)
                
            except Exception as e:
                self.logger.error(f"实时数据推送失败: {e}")
                time.sleep(10)  # 出错时等待更长时间
    
    def run(self, debug: bool = False):
        """运行Web应用"""
        try:
            self.logger.info(f"启动HMDM Web管理界面: http://{self.host}:{self.port}")
            self.socketio.run(self.app, 
                            host=self.host, 
                            port=self.port, 
                            debug=debug,
                            allow_unsafe_werkzeug=True)
        except Exception as e:
            self.logger.error(f"Web应用启动失败: {e}")
            raise
    
    def stop(self):
        """停止Web应用"""
        self.stop_data_push = True
        if self.data_push_thread and self.data_push_thread.is_alive():
            self.data_push_thread.join(timeout=5)
        self.logger.info("HMDM Web管理界面已停止")


def create_app(system_manager: HMDMSystemManager = None, **kwargs) -> HMDMWebApp:
    """创建Web应用实例"""
    if system_manager is None:
        system_manager = HMDMSystemManager()

    return HMDMWebApp(system_manager, **kwargs)


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建系统管理器
    system_manager = HMDMSystemManager()

    # 创建并运行Web应用
    web_app = create_app(system_manager, host='127.0.0.1', port=5000)

    try:
        print("启动HMDM Web管理界面...")
        print("访问地址: http://127.0.0.1:5000")
        web_app.run(debug=True)
    except KeyboardInterrupt:
        print("\n正在停止Web应用...")
        web_app.stop()
        system_manager.stop_system()
        print("Web应用已停止")
