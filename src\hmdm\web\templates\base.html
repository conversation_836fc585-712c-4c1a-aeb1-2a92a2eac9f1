<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HMDM军事综合决策支持系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .status-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-running {
            border-left: 5px solid #28a745;
        }
        
        .status-stopped {
            border-left: 5px solid #dc3545;
        }
        
        .status-error {
            border-left: 5px solid #ffc107;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .log-container {
            background-color: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 0.25rem 0.5rem;
            border-bottom: 1px solid #333;
        }
        
        .log-timestamp {
            color: #6272a4;
        }
        
        .log-level-INFO {
            color: #50fa7b;
        }
        
        .log-level-WARNING {
            color: #ffb86c;
        }
        
        .log-level-ERROR {
            color: #ff5555;
        }
        
        .log-level-DEBUG {
            color: #8be9fd;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #2a5298 !important;
        }
        
        .btn-military {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-military:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        
        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .connected {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .disconnected {
            background-color: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .module-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .module-status-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 连接状态指示器 -->
    <div class="connection-status">
        <div class="alert alert-success alert-sm" id="connection-alert" style="display: none;">
            <span class="connection-indicator connected" id="connection-indicator"></span>
            <span id="connection-text">已连接</span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4><i class="fas fa-shield-alt"></i> HMDM</h4>
                        <small class="text-muted">军事综合决策支持系统</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" 
                               href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i> 系统仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'modules' %}active{% endif %}" 
                               href="{{ url_for('modules') }}">
                                <i class="fas fa-cubes"></i> 模块管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'config' %}active{% endif %}" 
                               href="{{ url_for('config') }}">
                                <i class="fas fa-cog"></i> 系统配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'logs' %}active{% endif %}"
                               href="{{ url_for('logs') }}">
                                <i class="fas fa-file-alt"></i> 系统日志
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'api_docs' %}active{% endif %}"
                               href="{{ url_for('api_docs') }}">
                                <i class="fas fa-code"></i> API文档
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'config_management' %}active{% endif %}"
                               href="{{ url_for('config_management') }}">
                                <i class="fas fa-cogs"></i> 配置管理
                            </a>
                        </li>
                    </ul>

                    <hr class="my-3">

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>人机分配系统</span>
                    </h6>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'allocation' %}active{% endif %}"
                               href="{{ url_for('allocation') }}">
                                <i class="fas fa-users-cog"></i> 分配概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'allocation_task_input' %}active{% endif %}"
                               href="{{ url_for('allocation_task_input') }}">
                                <i class="fas fa-tasks"></i> 任务输入
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'allocation_scheme_comparison' %}active{% endif %}"
                               href="{{ url_for('allocation_scheme_comparison') }}">
                                <i class="fas fa-balance-scale"></i> 方案比较
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'allocation_effectiveness_evaluation' %}active{% endif %}"
                               href="{{ url_for('allocation_effectiveness_evaluation') }}">
                                <i class="fas fa-chart-bar"></i> 效能评估
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'allocation_decision_result' %}active{% endif %}"
                               href="{{ url_for('allocation_decision_result') }}">
                                <i class="fas fa-trophy"></i> 决策结果
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            <span id="current-time"></span>
                        </small>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}系统管理{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Socket.IO连接
        const socket = io();
        
        // 连接状态管理
        socket.on('connect', function() {
            document.getElementById('connection-alert').style.display = 'block';
            document.getElementById('connection-indicator').className = 'connection-indicator connected';
            document.getElementById('connection-text').textContent = '已连接';
            console.log('已连接到服务器');
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connection-indicator').className = 'connection-indicator disconnected';
            document.getElementById('connection-text').textContent = '连接断开';
            console.log('与服务器连接断开');
        });
        
        // 订阅实时数据
        socket.emit('subscribe_realtime');
        
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // 通用AJAX请求函数
        function makeRequest(url, method = 'GET', data = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            return fetch(url, options)
                .then(response => response.json())
                .catch(error => {
                    console.error('请求失败:', error);
                    return { success: false, error: error.message };
                });
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
