{% extends "base.html" %}

{% block title %}HMDM军事综合决策支持系统 - 主页{% endblock %}

{% block page_title %}欢迎使用HMDM系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white rounded p-5 mb-4" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);">
            <div class="container-fluid py-5">
                <h1 class="display-4 fw-bold">
                    <i class="fas fa-shield-alt"></i> HMDM
                </h1>
                <h2 class="display-6">军事综合决策支持系统</h2>
                <p class="lead">
                    Human-Machine Decision Making System - 为军事指挥决策提供智能化支持
                </p>
                <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                <p class="mb-4">
                    集成态势感知、威胁评估、决策支持、训练仿真等多个专业模块，
                    为军事指挥人员提供全方位的决策支持和智能辅助。
                </p>
                <a class="btn btn-light btn-lg" href="{{ url_for('dashboard') }}" role="button">
                    <i class="fas fa-tachometer-alt"></i> 进入系统仪表板
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-radar-chart fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">态势感知</h5>
                <p class="card-text">
                    实时收集和分析战场态势信息，提供全面的态势感知能力，
                    支持态势预测和威胁评估。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-brain fa-3x text-info"></i>
                </div>
                <h5 class="card-title">智能决策</h5>
                <p class="card-text">
                    基于多目标模糊决策模型，提供智能化的决策支持，
                    帮助指挥人员快速制定最优决策方案。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-network-wired fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">通信协同</h5>
                <p class="card-text">
                    提供军事通信和协同作战支持，确保信息的及时传递
                    和各作战单元的有效协同。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-graduation-cap fa-3x text-success"></i>
                </div>
                <h5 class="card-title">训练演练</h5>
                <p class="card-text">
                    提供军事训练和演练支持，包括训练计划制定、
                    演练场景设计和训练效果评估。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-database fa-3x text-danger"></i>
                </div>
                <h5 class="card-title">知识管理</h5>
                <p class="card-text">
                    集成军事知识库和专家系统，提供丰富的军事知识
                    和专业经验支持决策制定。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-cube fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">仿真建模</h5>
                <p class="card-text">
                    提供军事仿真和建模能力，支持作战方案的仿真验证
                    和效果评估，降低决策风险。
                </p>
                <div class="mt-auto">
                    <span class="badge bg-success">已集成</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 系统特性
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>模块化架构</strong> - 灵活的模块化设计，支持功能扩展
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>实时处理</strong> - 支持实时数据处理和态势更新
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>智能决策</strong> - 基于AI的智能决策支持算法
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>安全可靠</strong> - 军用级安全标准和可靠性保障
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>易于部署</strong> - 支持多种部署方式和环境配置
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                <strong>可视化界面</strong> - 直观的Web管理界面和数据可视化
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> 系统状态概览
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="system-overview">
                    <div class="col-md-3 text-center">
                        <div class="metric-card">
                            <div class="metric-value" id="total-modules">-</div>
                            <div class="metric-label">总模块数</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-card">
                            <div class="metric-value" id="running-modules">-</div>
                            <div class="metric-label">运行中模块</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-card">
                            <div class="metric-value" id="system-uptime">-</div>
                            <div class="metric-label">系统运行时间</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-card">
                            <div class="metric-value" id="system-status">-</div>
                            <div class="metric-label">系统状态</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 获取系统状态概览
    function updateSystemOverview() {
        makeRequest('/api/system/status')
            .then(response => {
                if (response.success) {
                    const data = response.data;
                    
                    // 更新模块统计
                    const modules = data.modules || {};
                    const totalModules = Object.keys(modules).length;
                    const runningModules = Object.values(modules).filter(m => m.status === '运行中').length;
                    
                    document.getElementById('total-modules').textContent = totalModules;
                    document.getElementById('running-modules').textContent = runningModules;
                    
                    // 更新系统状态
                    document.getElementById('system-status').textContent = data.system_status || '未知';
                    
                    // 更新运行时间
                    const uptime = data.statistics?.uptime || 0;
                    const uptimeText = formatUptime(uptime);
                    document.getElementById('system-uptime').textContent = uptimeText;
                }
            })
            .catch(error => {
                console.error('获取系统状态失败:', error);
            });
    }
    
    // 格式化运行时间
    function formatUptime(seconds) {
        if (seconds < 60) {
            return Math.floor(seconds) + '秒';
        } else if (seconds < 3600) {
            return Math.floor(seconds / 60) + '分钟';
        } else if (seconds < 86400) {
            return Math.floor(seconds / 3600) + '小时';
        } else {
            return Math.floor(seconds / 86400) + '天';
        }
    }
    
    // 监听实时系统状态更新
    socket.on('system_status_update', function(data) {
        updateSystemOverview();
    });
    
    // 页面加载时获取初始状态
    document.addEventListener('DOMContentLoaded', function() {
        updateSystemOverview();
    });
</script>
{% endblock %}
