"""
压力测试和性能测试套件

测试HMDM系统在高负载和极限条件下的性能表现
"""

import pytest
import time
import threading
import tempfile
import os
import concurrent.futures
from datetime import datetime
import random
import string

from src.hmdm.optimization.cache_manager import CacheManager
from src.hmdm.optimization.database_pool import DatabaseConnectionPool
from src.hmdm.monitoring.performance_monitor import PerformanceMonitor
from src.hmdm.security.enhanced_security import EnhancedSecurityManager
from src.hmdm.knowledge.military_knowledge_base import MilitaryKnowledgeBase
from src.hmdm.analysis.situation_awareness import SituationAwarenessEngine
from src.hmdm.security.military_security import SecurityLevel


class TestStressPerformance:
    """压力测试和性能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 初始化性能监控
        monitor_config = {
            'monitoring_enabled': True,
            'collection_interval': 1
        }
        self.performance_monitor = PerformanceMonitor(monitor_config)
        
        # 初始化缓存系统
        cache_config = {
            'max_memory_size': 1000,
            'enable_disk_cache': True,
            'disk_cache_dir': os.path.join(self.temp_dir, 'cache')
        }
        self.cache_manager = CacheManager(cache_config, self.performance_monitor)
        
        # 初始化数据库连接池
        db_config = {
            'database_url': ':memory:',
            'min_connections': 5,
            'max_connections': 20,
            'connection_timeout': 10
        }
        self.db_pool = DatabaseConnectionPool(db_config, self.performance_monitor)
        
        # 初始化安全系统
        security_config = {
            'audit_file': os.path.join(self.temp_dir, 'audit.log'),
            'encryption_key_file': os.path.join(self.temp_dir, 'encryption.key'),
            'session_timeout': 3600,
            'max_login_attempts': 5,
            'default_admin_username': 'stress_admin',
            'default_admin_password': 'StressAdmin123!'
        }
        self.security_manager = EnhancedSecurityManager(security_config)
        
        # 初始化知识库
        self.knowledge_base = MilitaryKnowledgeBase()
        
        # 初始化态势感知系统
        self.situation_system = SituationAwarenessEngine()
    
    def teardown_method(self):
        """测试后清理"""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring_service()
        if self.db_pool:
            self.db_pool.close_pool()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_concurrent_access(self):
        """测试缓存并发访问"""
        num_threads = 20
        operations_per_thread = 50
        results = []
        
        def cache_worker(thread_id):
            """缓存工作线程"""
            thread_results = {'hits': 0, 'misses': 0, 'puts': 0, 'errors': 0}
            
            for i in range(operations_per_thread):
                try:
                    key = f"thread_{thread_id}_key_{i}"
                    value = f"thread_{thread_id}_value_{i}_{random.randint(1, 1000)}"
                    
                    # 存储操作
                    if self.cache_manager.put(key, value):
                        thread_results['puts'] += 1
                    
                    # 读取操作
                    retrieved = self.cache_manager.get(key)
                    if retrieved is not None:
                        thread_results['hits'] += 1
                    else:
                        thread_results['misses'] += 1
                    
                    # 随机读取其他线程的数据
                    if i > 10:
                        other_key = f"thread_{random.randint(0, num_threads-1)}_key_{random.randint(0, i-1)}"
                        other_value = self.cache_manager.get(other_key)
                        if other_value is not None:
                            thread_results['hits'] += 1
                        else:
                            thread_results['misses'] += 1
                    
                except Exception as e:
                    thread_results['errors'] += 1
            
            return thread_results
        
        # 启动并发线程
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(cache_worker, i) for i in range(num_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_operations = sum(r['hits'] + r['misses'] + r['puts'] for r in results)
        total_errors = sum(r['errors'] for r in results)
        operations_per_second = total_operations / duration
        
        # 验证结果
        assert total_errors == 0, f"缓存并发测试出现 {total_errors} 个错误"
        assert operations_per_second > 100, f"缓存操作性能过低: {operations_per_second:.2f} ops/sec"
        
        # 获取缓存统计
        cache_stats = self.cache_manager.get_stats()
        assert cache_stats['total_requests'] > 0
        
        print(f"缓存并发测试完成: {total_operations} 操作, {operations_per_second:.2f} ops/sec, 耗时 {duration:.2f}s")
    
    def test_database_connection_pool_stress(self):
        """测试数据库连接池压力"""
        num_threads = 15
        queries_per_thread = 30
        results = []
        
        def db_worker(thread_id):
            """数据库工作线程"""
            thread_results = {'successful_queries': 0, 'failed_queries': 0, 'total_time': 0}
            
            for i in range(queries_per_thread):
                query_start = time.time()
                try:
                    with self.db_pool.get_connection() as conn:
                        cursor = conn.cursor()
                        # 执行简单查询
                        cursor.execute(f"SELECT {i} as thread_id, {thread_id} as query_id")
                        result = cursor.fetchone()
                        cursor.close()
                        
                        if result and result[0] == i and result[1] == thread_id:
                            thread_results['successful_queries'] += 1
                        else:
                            thread_results['failed_queries'] += 1
                    
                    query_time = time.time() - query_start
                    thread_results['total_time'] += query_time
                    
                    # 随机延迟模拟真实负载
                    if random.random() < 0.1:
                        time.sleep(0.01)
                        
                except Exception as e:
                    thread_results['failed_queries'] += 1
                    thread_results['total_time'] += time.time() - query_start
            
            return thread_results
        
        # 启动并发线程
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(db_worker, i) for i in range(num_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_queries = sum(r['successful_queries'] + r['failed_queries'] for r in results)
        successful_queries = sum(r['successful_queries'] for r in results)
        failed_queries = sum(r['failed_queries'] for r in results)
        success_rate = successful_queries / total_queries if total_queries > 0 else 0
        queries_per_second = total_queries / duration
        
        # 验证结果
        assert success_rate >= 0.95, f"数据库查询成功率过低: {success_rate:.2%}"
        assert queries_per_second > 50, f"数据库查询性能过低: {queries_per_second:.2f} queries/sec"
        
        # 获取连接池统计
        pool_stats = self.db_pool.get_stats()
        assert pool_stats['current_state']['total_connections'] > 0
        
        print(f"数据库连接池压力测试完成: {total_queries} 查询, 成功率 {success_rate:.2%}, "
              f"{queries_per_second:.2f} queries/sec, 耗时 {duration:.2f}s")
    
    def test_security_authentication_load(self):
        """测试安全认证负载"""
        num_users = 50
        auth_attempts_per_user = 10
        
        # 预创建用户
        users = []
        for i in range(num_users):
            username = f"stress_user_{i}"
            password = f"StressTest{i}123!"
            
            user = self.security_manager.create_user(
                username=username,
                password=password,
                security_level=SecurityLevel.INTERNAL
            )
            users.append((username, password))
        
        def auth_worker(user_data):
            """认证工作线程"""
            username, password = user_data
            results = {'successful_auths': 0, 'failed_auths': 0, 'session_validations': 0}
            
            for _ in range(auth_attempts_per_user):
                try:
                    # 认证用户
                    token = self.security_manager.authenticate_user(
                        username=username,
                        password=password,
                        ip_address='127.0.0.1'
                    )
                    
                    if token:
                        results['successful_auths'] += 1
                        
                        # 验证会话
                        user = self.security_manager.validate_session(token)
                        if user:
                            results['session_validations'] += 1
                        
                        # 登出
                        self.security_manager.logout_user(token)
                    else:
                        results['failed_auths'] += 1
                        
                except Exception as e:
                    results['failed_auths'] += 1
            
            return results
        
        # 启动并发认证
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(auth_worker, user_data) for user_data in users]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_auths = sum(r['successful_auths'] + r['failed_auths'] for r in results)
        successful_auths = sum(r['successful_auths'] for r in results)
        session_validations = sum(r['session_validations'] for r in results)
        
        auth_success_rate = successful_auths / total_auths if total_auths > 0 else 0
        auths_per_second = total_auths / duration
        
        # 验证结果
        assert auth_success_rate >= 0.95, f"认证成功率过低: {auth_success_rate:.2%}"
        assert session_validations >= successful_auths * 0.9, "会话验证失败率过高"
        assert auths_per_second > 10, f"认证性能过低: {auths_per_second:.2f} auths/sec"
        
        # 获取安全统计
        security_stats = self.security_manager.get_security_statistics()
        assert security_stats['users']['total'] >= num_users
        
        print(f"安全认证负载测试完成: {total_auths} 认证, 成功率 {auth_success_rate:.2%}, "
              f"{auths_per_second:.2f} auths/sec, 耗时 {duration:.2f}s")
    
    def test_knowledge_base_concurrent_operations(self):
        """测试知识库并发操作"""
        num_threads = 10
        operations_per_thread = 20
        
        def knowledge_worker(thread_id):
            """知识库工作线程"""
            results = {'adds': 0, 'searches': 0, 'updates': 0, 'errors': 0}
            knowledge_ids = []
            
            for i in range(operations_per_thread):
                try:
                    # 添加知识
                    title = f"压力测试知识_{thread_id}_{i}"
                    content = f"这是线程{thread_id}的第{i}个知识条目，包含随机内容: {random.randint(1, 10000)}"
                    
                    knowledge_id = self.knowledge_base.add_knowledge(
                        title=title,
                        content=content,
                        category="压力测试",
                        security_level=SecurityLevel.INTERNAL
                    )
                    
                    if knowledge_id:
                        results['adds'] += 1
                        knowledge_ids.append(knowledge_id)
                    
                    # 搜索知识
                    if i > 5:
                        search_results = self.knowledge_base.search_knowledge(f"线程{thread_id}")
                        if search_results:
                            results['searches'] += 1
                    
                    # 更新知识
                    if knowledge_ids and random.random() < 0.3:
                        update_id = random.choice(knowledge_ids)
                        success = self.knowledge_base.update_knowledge(
                            update_id,
                            content=f"更新的内容_{datetime.now().timestamp()}"
                        )
                        if success:
                            results['updates'] += 1
                    
                except Exception as e:
                    results['errors'] += 1
            
            return results
        
        # 启动并发操作
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(knowledge_worker, i) for i in range(num_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_operations = sum(r['adds'] + r['searches'] + r['updates'] for r in results)
        total_errors = sum(r['errors'] for r in results)
        operations_per_second = total_operations / duration
        
        # 验证结果
        assert total_errors == 0, f"知识库并发操作出现 {total_errors} 个错误"
        assert operations_per_second > 20, f"知识库操作性能过低: {operations_per_second:.2f} ops/sec"
        
        # 获取知识库统计
        kb_stats = self.knowledge_base.get_knowledge_statistics()
        assert kb_stats['total_knowledge'] > 0
        
        print(f"知识库并发操作测试完成: {total_operations} 操作, {operations_per_second:.2f} ops/sec, "
              f"错误数: {total_errors}, 耗时 {duration:.2f}s")
    
    def test_situation_awareness_high_frequency_updates(self):
        """测试态势感知高频更新"""
        num_threads = 8
        updates_per_thread = 50
        
        def situation_worker(thread_id):
            """态势更新工作线程"""
            results = {'successful_updates': 0, 'failed_updates': 0, 'analyses': 0}
            
            for i in range(updates_per_thread):
                try:
                    # 生成随机态势数据
                    situation_data = {
                        'timestamp': datetime.now().isoformat(),
                        'location': {
                            'lat': 39.9 + random.uniform(-0.1, 0.1),
                            'lon': 116.4 + random.uniform(-0.1, 0.1)
                        },
                        'threat_level': random.randint(1, 5),
                        'unit_status': random.choice(['active', 'standby', 'alert']),
                        'weather': random.choice(['clear', 'cloudy', 'rainy']),
                        'thread_id': thread_id,
                        'update_id': i
                    }
                    
                    # 添加态势数据
                    situation_id = self.situation_system.add_situation_data(situation_data)
                    if situation_id:
                        results['successful_updates'] += 1
                    else:
                        results['failed_updates'] += 1
                    
                    # 定期进行态势分析
                    if i % 10 == 0:
                        analysis = self.situation_system.analyze_current_situation()
                        if analysis:
                            results['analyses'] += 1
                    
                    # 短暂延迟模拟真实更新频率
                    time.sleep(0.01)
                    
                except Exception as e:
                    results['failed_updates'] += 1
            
            return results
        
        # 启动高频更新
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(situation_worker, i) for i in range(num_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_updates = sum(r['successful_updates'] + r['failed_updates'] for r in results)
        successful_updates = sum(r['successful_updates'] for r in results)
        total_analyses = sum(r['analyses'] for r in results)
        
        success_rate = successful_updates / total_updates if total_updates > 0 else 0
        updates_per_second = total_updates / duration
        
        # 验证结果
        assert success_rate >= 0.9, f"态势更新成功率过低: {success_rate:.2%}"
        assert updates_per_second > 30, f"态势更新性能过低: {updates_per_second:.2f} updates/sec"
        assert total_analyses > 0, "态势分析未执行"
        
        print(f"态势感知高频更新测试完成: {total_updates} 更新, 成功率 {success_rate:.2%}, "
              f"{updates_per_second:.2f} updates/sec, {total_analyses} 分析, 耗时 {duration:.2f}s")
    
    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        import psutil
        import gc
        
        # 记录初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量操作
        large_data_sets = []
        
        for i in range(100):
            # 创建大量缓存数据
            for j in range(50):
                key = f"memory_test_{i}_{j}"
                value = {
                    'data': ''.join(random.choices(string.ascii_letters, k=1000)),
                    'timestamp': datetime.now().isoformat(),
                    'index': i * 50 + j
                }
                self.cache_manager.put(key, value)
            
            # 创建大量态势数据
            for j in range(20):
                situation_data = {
                    'timestamp': datetime.now().isoformat(),
                    'location': {'lat': random.uniform(39, 40), 'lon': random.uniform(116, 117)},
                    'threat_level': random.randint(1, 5),
                    'large_data': ''.join(random.choices(string.ascii_letters, k=500))
                }
                self.situation_system.add_situation_data(situation_data)
            
            # 定期检查内存使用
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                # 内存增长不应该过快
                assert memory_increase < 500, f"内存使用增长过快: {memory_increase:.2f}MB"
                
                # 强制垃圾回收
                gc.collect()
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_memory_increase = final_memory - initial_memory
        
        print(f"内存使用测试完成: 初始 {initial_memory:.2f}MB, 最终 {final_memory:.2f}MB, "
              f"增长 {total_memory_increase:.2f}MB")
        
        # 验证内存使用合理
        assert total_memory_increase < 1000, f"内存使用增长过多: {total_memory_increase:.2f}MB"
    
    def test_system_recovery_after_stress(self):
        """测试压力后的系统恢复"""
        # 记录压力测试前的系统状态
        initial_stats = {
            'cache_stats': self.cache_manager.get_stats(),
            'db_stats': self.db_pool.get_stats(),
            'security_stats': self.security_manager.get_security_statistics(),
            'performance_summary': self.performance_monitor.get_performance_summary()
        }
        
        # 短时间高强度操作
        stress_duration = 5  # 5秒
        end_time = time.time() + stress_duration
        
        operations_count = 0
        while time.time() < end_time:
            # 缓存操作
            self.cache_manager.put(f"stress_{operations_count}", f"value_{operations_count}")
            self.cache_manager.get(f"stress_{operations_count}")
            
            # 数据库操作
            try:
                with self.db_pool.get_connection(timeout=1) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
            except:
                pass
            
            # 性能监控
            self.performance_monitor.record_metric("stress_test_metric", random.uniform(0, 100))
            
            operations_count += 1
        
        # 等待系统稳定
        time.sleep(2)
        
        # 检查系统恢复状态
        recovery_stats = {
            'cache_stats': self.cache_manager.get_stats(),
            'db_stats': self.db_pool.get_stats(),
            'security_stats': self.security_manager.get_security_statistics(),
            'performance_summary': self.performance_monitor.get_performance_summary()
        }
        
        # 验证系统正常运行
        assert recovery_stats['cache_stats']['hit_rate'] >= 0, "缓存系统异常"
        assert recovery_stats['db_stats']['current_state']['total_connections'] > 0, "数据库连接池异常"
        assert recovery_stats['security_stats']['users']['total'] > 0, "安全系统异常"
        
        print(f"系统恢复测试完成: 执行了 {operations_count} 次操作，系统状态正常")
        
        return True
