"""
系统管理器测试

测试HMDM系统管理器的核心功能
"""

import pytest
import tempfile
import json
import os
from datetime import datetime
from src.hmdm.core.system_manager import (
    HMDMSystemManager, SystemConfig, SystemStatus, ModuleStatus
)
from src.hmdm.security.military_security import SecurityLevel


class TestSystemConfig:
    """系统配置测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = SystemConfig()
        
        assert config.system_name == "HMDM军事综合决策支持系统"
        assert config.version == "1.0.0"
        assert config.environment == "production"
        assert config.default_security_level == SecurityLevel.SECRET
        assert len(config.enabled_modules) > 0
    
    def test_config_to_dict(self):
        """测试配置转字典"""
        config = SystemConfig()
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert "system_name" in config_dict
        assert "version" in config_dict
        assert "enabled_modules" in config_dict
        assert config_dict["default_security_level"] == SecurityLevel.SECRET.value
    
    def test_config_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            "system_name": "测试系统",
            "version": "2.0.0",
            "environment": "testing",
            "default_security_level": "CONFIDENTIAL",
            "enabled_modules": ["test_module"]
        }
        
        config = SystemConfig.from_dict(config_dict)
        
        assert config.system_name == "测试系统"
        assert config.version == "2.0.0"
        assert config.environment == "testing"
        assert config.default_security_level == SecurityLevel.CONFIDENTIAL
        assert config.enabled_modules == ["test_module"]


class TestHMDMSystemManager:
    """HMDM系统管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        config_data = {
            "system_name": "测试HMDM系统",
            "version": "1.0.0-test",
            "environment": "testing",
            "log_level": "DEBUG",
            "enabled_modules": ["situation_awareness", "knowledge_base", "decision_support"]
        }
        json.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        self.system_manager = HMDMSystemManager(self.temp_config.name)
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'system_manager'):
            self.system_manager.stop_system()
        
        # 清理临时文件
        if os.path.exists(self.temp_config.name):
            os.unlink(self.temp_config.name)
    
    def test_system_initialization(self):
        """测试系统初始化"""
        assert self.system_manager.status == SystemStatus.INITIALIZING
        assert self.system_manager.config.system_name == "测试HMDM系统"
        assert self.system_manager.config.environment == "testing"
        assert len(self.system_manager.modules) > 0
    
    def test_module_registry(self):
        """测试模块注册表"""
        modules = self.system_manager.modules
        
        # 验证核心模块存在
        expected_modules = [
            "situation_awareness", "communication", "decision_support",
            "training", "knowledge_base", "simulation", "scenarios"
        ]
        
        for module_name in expected_modules:
            assert module_name in modules
            assert modules[module_name].status == ModuleStatus.NOT_LOADED
    
    def test_startup_order_calculation(self):
        """测试启动顺序计算"""
        startup_order = self.system_manager._calculate_startup_order()
        
        assert isinstance(startup_order, list)
        assert len(startup_order) > 0
        
        # 验证依赖关系：knowledge_base应该在decision_support之前
        kb_index = startup_order.index("knowledge_base")
        ds_index = startup_order.index("decision_support")
        assert kb_index < ds_index
        
        # 验证scenarios应该在最后（依赖最多）
        scenarios_index = startup_order.index("scenarios")
        assert scenarios_index == len(startup_order) - 1
    
    def test_system_start_stop(self):
        """测试系统启动和停止"""
        # 测试启动
        success = self.system_manager.start_system()
        assert success is True
        assert self.system_manager.status == SystemStatus.RUNNING
        assert self.system_manager.start_time is not None
        
        # 验证启用的模块已启动
        for module_name in self.system_manager.config.enabled_modules:
            if module_name in self.system_manager.modules:
                module_info = self.system_manager.modules[module_name]
                assert module_info.status == ModuleStatus.RUNNING
                assert module_info.instance is not None
        
        # 测试停止
        success = self.system_manager.stop_system()
        assert success is True
        assert self.system_manager.status == SystemStatus.STOPPED
    
    def test_get_system_status(self):
        """测试获取系统状态"""
        # 启动系统
        self.system_manager.start_system()
        
        status = self.system_manager.get_system_status()
        
        assert "system_status" in status
        assert "uptime_seconds" in status
        assert "modules" in status
        assert "statistics" in status
        assert "configuration" in status
        
        # 验证模块状态
        modules_status = status["modules"]
        for module_name in self.system_manager.config.enabled_modules:
            if module_name in modules_status:
                assert "status" in modules_status[module_name]
                assert "start_time" in modules_status[module_name]
    
    def test_get_module(self):
        """测试获取模块实例"""
        # 启动系统
        self.system_manager.start_system()
        
        # 获取已启动的模块
        for module_name in self.system_manager.config.enabled_modules:
            module_instance = self.system_manager.get_module(module_name)
            if module_name in self.system_manager.modules:
                assert module_instance is not None
        
        # 获取不存在的模块
        non_existent = self.system_manager.get_module("non_existent_module")
        assert non_existent is None
    
    def test_module_restart(self):
        """测试模块重启"""
        # 启动系统
        self.system_manager.start_system()
        
        # 选择一个已启动的模块进行重启测试
        module_name = "knowledge_base"
        if module_name in self.system_manager.config.enabled_modules:
            # 获取重启前的实例
            original_instance = self.system_manager.get_module(module_name)
            original_start_time = self.system_manager.modules[module_name].start_time
            
            # 添加小延迟确保时间差异
            import time
            time.sleep(0.01)

            # 重启模块
            success = self.system_manager.restart_module(module_name)
            assert success is True

            # 验证实例已更新
            new_instance = self.system_manager.get_module(module_name)
            new_start_time = self.system_manager.modules[module_name].start_time

            assert new_instance is not None
            assert new_start_time > original_start_time
            assert self.system_manager.stats["module_restarts"] > 0
    
    def test_event_system(self):
        """测试事件系统"""
        events_received = []
        
        def test_handler(data):
            events_received.append(data)
        
        # 注册事件处理器
        self.system_manager.register_event_handler("test_event", test_handler)
        
        # 发送事件
        test_data = {"message": "测试事件"}
        self.system_manager.emit_event("test_event", test_data)
        
        # 验证事件被处理
        assert len(events_received) == 1
        assert events_received[0] == test_data
    
    def test_config_update(self):
        """测试配置更新"""
        original_log_level = self.system_manager.config.log_level
        
        # 更新配置
        new_config = {
            "log_level": "ERROR",
            "max_concurrent_tasks": 20
        }
        
        success = self.system_manager.update_config(new_config)
        assert success is True
        
        # 验证配置已更新
        assert self.system_manager.config.log_level == "ERROR"
        assert self.system_manager.config.max_concurrent_tasks == 20
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = {
            "system_name": "测试系统",
            "version": "1.0.0",
            "environment": "testing",
            "log_level": "INFO"
        }
        
        is_valid = self.system_manager._validate_config(valid_config)
        assert is_valid is True
        
        # 测试无效配置 - 无效的日志级别
        invalid_config = {
            "log_level": "INVALID_LEVEL"
        }

        is_valid = self.system_manager._validate_config(invalid_config)
        assert is_valid is False
        
        # 测试无效配置 - 无效的环境
        invalid_env_config = {
            "system_name": "测试系统",
            "version": "1.0.0",
            "environment": "invalid_env",
            "log_level": "INFO"
        }
        
        is_valid = self.system_manager._validate_config(invalid_env_config)
        assert is_valid is False
    
    def test_data_channels_setup(self):
        """测试数据通道建立"""
        # 启动系统
        self.system_manager.start_system()
        
        # 验证数据通道已建立
        assert len(self.system_manager.data_channels) > 0
        
        # 验证特定的数据通道
        expected_channels = ["kb_to_ds"]  # knowledge_base -> decision_support
        
        for channel_name in expected_channels:
            if channel_name in self.system_manager.data_channels:
                channel = self.system_manager.data_channels[channel_name]
                assert "source" in channel
                assert "target" in channel
                assert "data_type" in channel
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试启动不存在的模块
        self.system_manager.modules["invalid_module"] = type('ModuleInfo', (), {
            'name': 'invalid_module',
            'dependencies': [],
            'status': ModuleStatus.NOT_LOADED,
            'instance': None,
            'last_error': None,
            'start_time': None
        })()
        
        success = self.system_manager._start_module("invalid_module")
        assert success is False
        
        module_info = self.system_manager.modules["invalid_module"]
        assert module_info.status == ModuleStatus.ERROR
        assert module_info.last_error is not None
