"""
评估工具函数

提供评估过程中常用的工具函数
"""

from typing import List, Dict, Optional, Any, Tuple
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

from ..models.evaluation_models import (
    EvaluationResult, EvaluationScheme, IndicatorType, IndicatorDefinition
)


def calculate_indicator_importance(evaluation_results: List[EvaluationResult],
                                 evaluation_scheme: EvaluationScheme) -> Dict[str, float]:
    """计算指标重要性（基于方差分析）"""
    if not evaluation_results or not evaluation_scheme:
        return {}
    
    importance_scores = {}
    
    # 收集所有指标值
    indicator_values = {}
    for indicator_id in evaluation_scheme.indicators.keys():
        values = []
        for result in evaluation_results:
            if indicator_id in result.indicator_values:
                values.append(result.indicator_values[indicator_id].normalized_value)
        
        if values:
            indicator_values[indicator_id] = values
    
    # 计算方差作为重要性指标
    total_variance = 0.0
    variances = {}
    
    for indicator_id, values in indicator_values.items():
        variance = np.var(values)
        variances[indicator_id] = variance
        total_variance += variance
    
    # 归一化重要性分数
    if total_variance > 0:
        for indicator_id, variance in variances.items():
            importance_scores[indicator_id] = variance / total_variance
    
    return importance_scores


def detect_evaluation_outliers(evaluation_results: List[EvaluationResult],
                             threshold: float = 2.0) -> List[Dict[str, Any]]:
    """检测评估异常值"""
    if len(evaluation_results) < 3:
        return []
    
    outliers = []
    
    # 收集总分
    total_scores = [result.total_score for result in evaluation_results]
    mean_score = np.mean(total_scores)
    std_score = np.std(total_scores)
    
    # 检测异常值
    for i, result in enumerate(evaluation_results):
        z_score = abs(result.total_score - mean_score) / std_score if std_score > 0 else 0
        
        if z_score > threshold:
            outliers.append({
                "result_id": result.id,
                "target_name": result.target_name,
                "total_score": result.total_score,
                "z_score": z_score,
                "deviation_type": "high" if result.total_score > mean_score else "low"
            })
    
    return outliers


def generate_evaluation_summary(evaluation_results: List[EvaluationResult],
                              evaluation_scheme: EvaluationScheme) -> Dict[str, Any]:
    """生成评估摘要"""
    if not evaluation_results:
        return {}
    
    summary = {
        "basic_statistics": {},
        "indicator_analysis": {},
        "ranking_analysis": {},
        "distribution_analysis": {}
    }
    
    # 基础统计
    total_scores = [result.total_score for result in evaluation_results]
    summary["basic_statistics"] = {
        "total_alternatives": len(evaluation_results),
        "mean_score": np.mean(total_scores),
        "median_score": np.median(total_scores),
        "std_score": np.std(total_scores),
        "min_score": np.min(total_scores),
        "max_score": np.max(total_scores),
        "score_range": np.max(total_scores) - np.min(total_scores)
    }
    
    # 指标分析
    indicator_stats = {}
    for indicator_id, indicator in evaluation_scheme.indicators.items():
        values = []
        for result in evaluation_results:
            if indicator_id in result.indicator_values:
                values.append(result.indicator_values[indicator_id].normalized_value)
        
        if values:
            indicator_stats[indicator.name] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "min": np.min(values),
                "max": np.max(values),
                "coefficient_of_variation": np.std(values) / np.mean(values) if np.mean(values) > 0 else 0
            }
    
    summary["indicator_analysis"] = indicator_stats
    
    # 排序分析
    sorted_results = sorted(evaluation_results, key=lambda x: x.total_score, reverse=True)
    summary["ranking_analysis"] = {
        "best_alternative": {
            "name": sorted_results[0].target_name,
            "score": sorted_results[0].total_score
        },
        "worst_alternative": {
            "name": sorted_results[-1].target_name,
            "score": sorted_results[-1].total_score
        },
        "score_gap": sorted_results[0].total_score - sorted_results[-1].total_score
    }
    
    # 分布分析
    score_quartiles = np.percentile(total_scores, [25, 50, 75])
    summary["distribution_analysis"] = {
        "q1": score_quartiles[0],
        "q2": score_quartiles[1],
        "q3": score_quartiles[2],
        "iqr": score_quartiles[2] - score_quartiles[0],
        "skewness": calculate_skewness(total_scores),
        "kurtosis": calculate_kurtosis(total_scores)
    }
    
    return summary


def calculate_skewness(data: List[float]) -> float:
    """计算偏度"""
    if len(data) < 3:
        return 0.0
    
    mean = np.mean(data)
    std = np.std(data)
    
    if std == 0:
        return 0.0
    
    skewness = np.mean([((x - mean) / std) ** 3 for x in data])
    return skewness


def calculate_kurtosis(data: List[float]) -> float:
    """计算峰度"""
    if len(data) < 4:
        return 0.0
    
    mean = np.mean(data)
    std = np.std(data)
    
    if std == 0:
        return 0.0
    
    kurtosis = np.mean([((x - mean) / std) ** 4 for x in data]) - 3
    return kurtosis


def visualize_evaluation_results(evaluation_results: List[EvaluationResult],
                               evaluation_scheme: EvaluationScheme,
                               save_path: Optional[str] = None) -> None:
    """可视化评估结果"""
    if not evaluation_results:
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('方案评估结果分析', fontsize=16, fontweight='bold')
    
    # 1. 总分排序图
    ax1 = axes[0, 0]
    sorted_results = sorted(evaluation_results, key=lambda x: x.total_score, reverse=True)
    names = [result.target_name for result in sorted_results]
    scores = [result.total_score for result in sorted_results]
    
    bars = ax1.bar(range(len(names)), scores)
    ax1.set_title('方案总分排序')
    ax1.set_xlabel('方案')
    ax1.set_ylabel('总分')
    ax1.set_xticks(range(len(names)))
    ax1.set_xticklabels(names, rotation=45, ha='right')
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{scores[i]:.3f}', ha='center', va='bottom')
    
    # 2. 指标类型得分雷达图
    ax2 = axes[0, 1]
    
    # 计算各指标类型的平均得分
    type_scores = {}
    for indicator_type in IndicatorType:
        type_name = indicator_type.value
        all_scores = []
        
        for result in evaluation_results:
            if type_name in result.type_scores:
                all_scores.append(result.type_scores[type_name])
        
        if all_scores:
            type_scores[type_name] = np.mean(all_scores)
    
    if type_scores:
        categories = list(type_scores.keys())
        values = list(type_scores.values())
        
        # 创建雷达图
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        ax2.plot(angles, values, 'o-', linewidth=2)
        ax2.fill(angles, values, alpha=0.25)
        ax2.set_xticks(angles[:-1])
        ax2.set_xticklabels(categories)
        ax2.set_title('各指标类型平均得分')
        ax2.grid(True)
    
    # 3. 得分分布直方图
    ax3 = axes[1, 0]
    total_scores = [result.total_score for result in evaluation_results]
    ax3.hist(total_scores, bins=min(10, len(total_scores)), alpha=0.7, edgecolor='black')
    ax3.set_title('总分分布')
    ax3.set_xlabel('总分')
    ax3.set_ylabel('频数')
    ax3.grid(True, alpha=0.3)
    
    # 4. 指标相关性热力图
    ax4 = axes[1, 1]
    
    # 构建指标值矩阵
    indicator_matrix = []
    indicator_names = []
    
    for indicator_id, indicator in evaluation_scheme.indicators.items():
        values = []
        for result in evaluation_results:
            if indicator_id in result.indicator_values:
                values.append(result.indicator_values[indicator_id].normalized_value)
            else:
                values.append(0.0)
        
        if len(values) == len(evaluation_results):
            indicator_matrix.append(values)
            indicator_names.append(indicator.name)
    
    if indicator_matrix and len(indicator_matrix) > 1:
        correlation_matrix = np.corrcoef(indicator_matrix)
        
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   xticklabels=indicator_names, yticklabels=indicator_names,
                   ax=ax4, fmt='.2f')
        ax4.set_title('指标相关性矩阵')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def export_evaluation_report(evaluation_results: List[EvaluationResult],
                           evaluation_scheme: EvaluationScheme,
                           file_path: str,
                           format: str = "excel") -> None:
    """导出评估报告"""
    if format.lower() == "excel":
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 导出总体结果
            summary_data = []
            for result in evaluation_results:
                summary_data.append({
                    "方案ID": result.target_id,
                    "方案名称": result.target_name,
                    "总分": result.total_score,
                    "标准化得分": result.normalized_score,
                    "评估时间": result.evaluation_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "置信度": result.confidence
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values("总分", ascending=False)
            summary_df.insert(0, "排名", range(1, len(summary_df) + 1))
            summary_df.to_excel(writer, sheet_name="总体结果", index=False)
            
            # 导出详细指标值
            detailed_data = []
            for result in evaluation_results:
                row = {
                    "方案ID": result.target_id,
                    "方案名称": result.target_name,
                    "总分": result.total_score
                }
                
                for indicator_id, indicator in evaluation_scheme.indicators.items():
                    if indicator_id in result.indicator_values:
                        indicator_value = result.indicator_values[indicator_id]
                        row[f"{indicator.name}_原始值"] = indicator_value.raw_value
                        row[f"{indicator.name}_标准化值"] = indicator_value.normalized_value
                    else:
                        row[f"{indicator.name}_原始值"] = 0.0
                        row[f"{indicator.name}_标准化值"] = 0.0
                
                detailed_data.append(row)
            
            detailed_df = pd.DataFrame(detailed_data)
            detailed_df.to_excel(writer, sheet_name="详细指标值", index=False)
            
            # 导出指标类型得分
            type_score_data = []
            for result in evaluation_results:
                row = {
                    "方案ID": result.target_id,
                    "方案名称": result.target_name
                }
                
                for type_name, score in result.type_scores.items():
                    row[type_name] = score
                
                type_score_data.append(row)
            
            if type_score_data:
                type_score_df = pd.DataFrame(type_score_data)
                type_score_df.to_excel(writer, sheet_name="指标类型得分", index=False)
    
    elif format.lower() == "json":
        import json
        
        export_data = {
            "evaluation_scheme": evaluation_scheme.to_dict(),
            "results": [result.to_dict() for result in evaluation_results],
            "summary": generate_evaluation_summary(evaluation_results, evaluation_scheme),
            "export_time": datetime.now().isoformat()
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
