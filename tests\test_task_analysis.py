"""
任务分析模块测试
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from hmdm.task_analysis import HierarchicalTaskAnalyzer, GomsAnalyzer
from hmdm.models.task_models import Task, TaskType, TaskHierarchy, MetaOperation, OperationType


class TestHierarchicalTaskAnalyzer:
    """层次任务分析器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = HierarchicalTaskAnalyzer()
    
    def test_create_task_hierarchy(self):
        """测试创建任务层次结构"""
        root_task = Task(
            name="测试任务",
            description="测试用的根任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        assert isinstance(hierarchy, TaskHierarchy)
        assert hierarchy.root_task_id == root_task.id
        assert root_task.id in hierarchy.tasks
        assert hierarchy.get_task(root_task.id) == root_task
    
    def test_decompose_task(self):
        """测试任务分解"""
        # 创建根任务
        root_task = Task(
            name="态势分析任务",
            description="分析当前态势",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 定义子任务规格
        subtask_specs = [
            {
                "name": "数据收集",
                "description": "收集态势数据",
                "attributes": {
                    "complexity": 0.3,
                    "importance": 0.8
                }
            },
            {
                "name": "数据分析",
                "description": "分析态势数据",
                "attributes": {
                    "complexity": 0.7,
                    "importance": 0.9
                }
            }
        ]
        
        # 执行分解
        subtasks = self.analyzer.decompose_task(root_task.id, subtask_specs)
        
        assert len(subtasks) == 2
        assert all(task.parent_id == root_task.id for task in subtasks)
        assert all(task.level == 1 for task in subtasks)
        assert all(task.task_type == TaskType.ZZ_TASK for task in subtasks)
        
        # 验证父任务的子任务列表
        updated_root = hierarchy.get_task(root_task.id)
        assert len(updated_root.children_ids) == 2
        assert all(child_id in [task.id for task in subtasks] for child_id in updated_root.children_ids)
    
    def test_auto_decompose_by_scenario(self):
        """测试按场景自动分解"""
        root_task = Task(
            name="态势分析任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 自动分解
        subtasks = self.analyzer.auto_decompose_by_scenario(root_task.id, "态势分析")
        
        assert len(subtasks) > 0
        assert all(task.parent_id == root_task.id for task in subtasks)
        
        # 验证任务名称包含预期内容
        task_names = [task.name for task in subtasks]
        assert any("数据收集" in name for name in task_names)
        assert any("数据分析" in name for name in task_names)
    
    def test_calculate_task_metrics(self):
        """测试任务指标计算"""
        root_task = Task(
            name="测试任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 添加子任务
        subtask_specs = [
            {
                "name": "子任务1",
                "attributes": {
                    "complexity": 0.5,
                    "duration": 100.0,
                    "workload": 0.6
                }
            },
            {
                "name": "子任务2",
                "attributes": {
                    "complexity": 0.7,
                    "duration": 200.0,
                    "workload": 0.8
                }
            }
        ]
        
        subtasks = self.analyzer.decompose_task(root_task.id, subtask_specs)
        
        # 计算指标
        metrics = self.analyzer.calculate_task_metrics(root_task.id)
        
        assert "complexity" in metrics
        assert "duration" in metrics
        assert "workload" in metrics
        
        # 验证聚合逻辑
        assert metrics["duration"] == 300.0  # 持续时间应该累加
        assert 0.5 < metrics["complexity"] < 0.7  # 复杂度应该是平均值
    
    def test_validate_hierarchy(self):
        """测试层次结构验证"""
        root_task = Task(
            name="根任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        # 正常情况下应该没有错误
        errors = self.analyzer.validate_hierarchy()
        assert len(errors) == 0
        
        # 人为制造错误：添加不存在的子任务ID
        root_task.children_ids.append("non_existent_id")
        
        errors = self.analyzer.validate_hierarchy()
        assert len(errors) > 0
        assert any("不存在" in error for error in errors)
    
    def test_export_import_hierarchy(self):
        """测试导出和导入层次结构"""
        # 创建测试层次结构
        root_task = Task(
            name="测试任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.analyzer.create_task_hierarchy(root_task)
        
        subtask_specs = [{"name": "子任务", "description": "测试子任务"}]
        self.analyzer.decompose_task(root_task.id, subtask_specs)
        
        # 导出
        exported_data = self.analyzer.export_hierarchy()
        
        assert "root_task_id" in exported_data
        assert "tasks" in exported_data
        assert len(exported_data["tasks"]) == 2  # 根任务 + 1个子任务
        
        # 创建新的分析器并导入
        new_analyzer = HierarchicalTaskAnalyzer()
        imported_hierarchy = new_analyzer.import_hierarchy(exported_data)
        
        assert imported_hierarchy.root_task_id == hierarchy.root_task_id
        assert len(imported_hierarchy.tasks) == len(hierarchy.tasks)


class TestGomsAnalyzer:
    """GOMS分析器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = GomsAnalyzer()
    
    def test_standard_operators(self):
        """测试标准操作符库"""
        assert len(self.analyzer.operators) > 0
        
        # 检查是否包含基本操作类型
        operation_types = [op.operation_type for op in self.analyzer.operators.values()]
        assert OperationType.PERCEPTION in operation_types
        assert OperationType.COGNITION in operation_types
        assert OperationType.MOTOR in operation_types
    
    def test_add_operator(self):
        """测试添加操作符"""
        from hmdm.task_analysis.goms_analyzer import GomsOperator
        
        initial_count = len(self.analyzer.operators)
        
        new_operator = GomsOperator(
            name="测试操作",
            description="测试用操作符",
            operation_type=OperationType.COGNITION,
            execution_time=1.5
        )
        
        operator_id = self.analyzer.add_operator(new_operator)
        
        assert len(self.analyzer.operators) == initial_count + 1
        assert operator_id in self.analyzer.operators
        assert self.analyzer.operators[operator_id] == new_operator
    
    def test_create_method(self):
        """测试创建GOMS方法"""
        # 获取一些操作符ID
        operator_ids = list(self.analyzer.operators.keys())[:3]
        
        method_id = self.analyzer.create_method(
            name="测试方法",
            goal_id="test_goal",
            operator_sequence=operator_ids
        )
        
        assert method_id in self.analyzer.methods
        method = self.analyzer.methods[method_id]
        assert method.name == "测试方法"
        assert method.goal_id == "test_goal"
        assert method.operators == operator_ids
    
    def test_decompose_to_meta_operations(self):
        """测试分解为元操作"""
        from hmdm.models.task_models import IOPattern
        
        task = Task(
            name="数据处理任务",
            task_type=TaskType.OPERATION_SEQUENCE,
            io_pattern=IOPattern(
                input_description="读取传感器数据",
                action_description="分析和计算数据",
                output_description="生成分析报告"
            )
        )
        
        meta_ops = self.analyzer.decompose_to_meta_operations(task)
        
        assert len(meta_ops) > 0
        assert all(isinstance(op, MetaOperation) for op in meta_ops)
        assert all(op.parent_id == task.id for op in meta_ops)
        assert all(op.level == task.level + 1 for op in meta_ops)
        
        # 验证操作类型的多样性
        operation_types = [op.operation_type for op in meta_ops]
        assert len(set(operation_types)) > 1  # 应该包含多种操作类型
    
    def test_calculate_execution_time(self):
        """测试执行时间计算"""
        # 创建方法
        operator_ids = list(self.analyzer.operators.keys())[:2]
        method_id = self.analyzer.create_method(
            name="测试方法",
            goal_id="test_goal",
            operator_sequence=operator_ids
        )
        
        # 计算执行时间
        total_time = self.analyzer.calculate_execution_time(method_id)
        
        assert total_time > 0
        
        # 验证时间是操作符时间的总和
        expected_time = sum(
            self.analyzer.operators[op_id].execution_time 
            for op_id in operator_ids
        )
        assert abs(total_time - expected_time) < 0.001
    
    def test_calculate_error_probability(self):
        """测试错误概率计算"""
        operator_ids = list(self.analyzer.operators.keys())[:2]
        method_id = self.analyzer.create_method(
            name="测试方法",
            goal_id="test_goal",
            operator_sequence=operator_ids
        )
        
        error_prob = self.analyzer.calculate_error_probability(method_id)
        
        assert 0 <= error_prob <= 1
        
        # 错误概率应该大于单个操作符的错误概率（因为是复合操作）
        individual_probs = [
            self.analyzer.operators[op_id].error_probability 
            for op_id in operator_ids
        ]
        max_individual = max(individual_probs)
        assert error_prob >= max_individual
    
    def test_export_goms_model(self):
        """测试导出GOMS模型"""
        exported_model = self.analyzer.export_goms_model()
        
        assert "operators" in exported_model
        assert "methods" in exported_model
        assert "rules" in exported_model
        
        assert len(exported_model["operators"]) > 0
        
        # 验证导出的数据结构
        for operator_data in exported_model["operators"].values():
            assert "name" in operator_data
            assert "operation_type" in operator_data
            assert "execution_time" in operator_data


if __name__ == "__main__":
    pytest.main([__file__])
