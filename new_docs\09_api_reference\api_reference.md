# HMDM系统API参考手册

## API概述

HMDM系统提供完整的RESTful API接口，支持所有核心功能的程序化访问。API采用JSON格式进行数据交换，支持标准的HTTP方法，并提供完整的错误处理和状态码。

### 基本信息
- **基础URL**：`http://localhost:5000/api`
- **API版本**：v1.0
- **数据格式**：JSON
- **字符编码**：UTF-8
- **认证方式**：Token认证（可选）

### 通用响应格式
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2025-09-08T16:45:00Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_INPUT",
    "message": "输入参数无效",
    "details": "task_hierarchy字段不能为空"
  },
  "timestamp": "2025-09-08T16:45:00Z"
}
```

## 系统状态API

### 获取系统状态
获取HMDM系统的运行状态和基本信息。

**请求**
```
GET /api/system/status
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "system_name": "HMDM军事综合决策支持系统",
    "version": "2.0.0",
    "status": "运行中",
    "uptime": "2天3小时45分钟",
    "modules": {
      "human_machine_allocation": {
        "status": "正常",
        "last_check": "2025-09-08T16:44:30Z"
      },
      "decision_support": {
        "status": "正常", 
        "last_check": "2025-09-08T16:44:30Z"
      }
    },
    "performance": {
      "cpu_usage": "15%",
      "memory_usage": "45%",
      "response_time": "0.002s"
    }
  }
}
```

### 获取系统配置
获取当前系统配置信息。

**请求**
```
GET /api/system/config
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "system_config": {
      "debug_mode": false,
      "log_level": "INFO",
      "security_level": "SECRET"
    },
    "allocation_config": {
      "allocation_mode": "BALANCED",
      "optimization_objective": "EFFICIENCY",
      "human_capability_weight": 0.6,
      "machine_capability_weight": 0.4
    }
  }
}
```

## 人机分配API

### 执行人机功能分配
执行完整的人机功能分配决策流程。

**请求**
```
POST /api/allocation/allocate
Content-Type: application/json
```

**请求参数**
```json
{
  "task_hierarchy": {
    "id": "task_001",
    "name": "作战任务",
    "description": "执行作战任务的人机分配",
    "subtasks": [
      {
        "id": "subtask_001",
        "name": "态势感知",
        "complexity": "MEDIUM",
        "time_constraint": 300,
        "required_capabilities": ["perception", "analysis"]
      },
      {
        "id": "subtask_002", 
        "name": "决策制定",
        "complexity": "HIGH",
        "time_constraint": 600,
        "required_capabilities": ["reasoning", "judgment"]
      }
    ]
  },
  "constraints": {
    "max_human_tasks": 5,
    "min_machine_tasks": 2,
    "time_limit": 1800
  },
  "preferences": {
    "prefer_human_decision": true,
    "efficiency_priority": 0.8,
    "reliability_priority": 0.6
  },
  "optimization_criteria": {
    "efficiency": 0.8,
    "reliability": 0.6,
    "cost": 0.4
  }
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "allocation_id": "alloc_20250908_001",
    "recommended_scheme": {
      "scheme_id": "scheme_001",
      "name": "平衡分配方案",
      "human_tasks": [
        {
          "task_id": "subtask_002",
          "task_name": "决策制定",
          "allocation_reason": "需要人类的判断能力和经验"
        }
      ],
      "machine_tasks": [
        {
          "task_id": "subtask_001", 
          "task_name": "态势感知",
          "allocation_reason": "机器在数据处理和模式识别方面更有优势"
        }
      ],
      "collaboration_tasks": []
    },
    "effectiveness_metrics": {
      "overall_effectiveness": 0.85,
      "efficiency_score": 0.88,
      "reliability_score": 0.82,
      "coordination_score": 0.85,
      "satisfaction_score": 0.87
    },
    "alternative_schemes": [
      {
        "scheme_id": "scheme_002",
        "name": "人类主导方案",
        "effectiveness_score": 0.78
      },
      {
        "scheme_id": "scheme_003",
        "name": "机器主导方案", 
        "effectiveness_score": 0.72
      }
    ],
    "decision_confidence": 0.92,
    "decision_rationale": {
      "primary_factors": ["任务复杂度匹配", "能力优势发挥"],
      "risk_assessment": "低风险",
      "improvement_suggestions": ["加强人机协作训练"]
    },
    "execution_time": 0.156,
    "timestamp": "2025-09-08T16:45:00Z"
  }
}
```

### 获取分配历史
获取历史分配记录。

**请求**
```
GET /api/allocation/history?limit=10&offset=0
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "total": 25,
    "records": [
      {
        "allocation_id": "alloc_20250908_001",
        "task_name": "作战任务",
        "timestamp": "2025-09-08T16:45:00Z",
        "effectiveness_score": 0.85,
        "status": "completed"
      }
    ]
  }
}
```

## 决策支持API

### 执行多目标决策
执行多目标模糊决策分析。

**请求**
```
POST /api/decision/analyze
Content-Type: application/json
```

**请求参数**
```json
{
  "decision_matrix": {
    "alternatives": [
      {
        "id": "alt_001",
        "name": "方案A",
        "attributes": {
          "cost": 100,
          "efficiency": 0.8,
          "reliability": 0.9
        }
      },
      {
        "id": "alt_002", 
        "name": "方案B",
        "attributes": {
          "cost": 150,
          "efficiency": 0.9,
          "reliability": 0.8
        }
      }
    ],
    "criteria": [
      {
        "id": "cost",
        "name": "成本",
        "weight": 0.3,
        "type": "cost",
        "unit": "万元"
      },
      {
        "id": "efficiency",
        "name": "效率", 
        "weight": 0.4,
        "type": "benefit",
        "unit": "比率"
      },
      {
        "id": "reliability",
        "name": "可靠性",
        "weight": 0.3, 
        "type": "benefit",
        "unit": "比率"
      }
    ]
  },
  "method": "WRDM",
  "options": {
    "normalization": "vector",
    "fuzzy_processing": true
  }
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "decision_id": "dec_20250908_001",
    "method": "WRDM",
    "results": {
      "ranking": [
        {
          "alternative_id": "alt_001",
          "alternative_name": "方案A",
          "score": 0.85,
          "rank": 1
        },
        {
          "alternative_id": "alt_002",
          "alternative_name": "方案B", 
          "score": 0.78,
          "rank": 2
        }
      ],
      "recommended_alternative": {
        "id": "alt_001",
        "name": "方案A",
        "score": 0.85,
        "confidence": 0.92
      }
    },
    "analysis": {
      "sensitivity_analysis": {
        "stable": true,
        "critical_weights": ["efficiency"]
      },
      "consistency_ratio": 0.08,
      "decision_quality": "excellent"
    },
    "execution_time": 0.065
  }
}
```

## 任务分析API

### 执行任务分析
对任务进行层次分析和认知建模。

**请求**
```
POST /api/analysis/task
Content-Type: application/json
```

**请求参数**
```json
{
  "task": {
    "id": "task_001",
    "name": "态势分析任务",
    "description": "分析当前战场态势",
    "type": "cognitive",
    "complexity": "MEDIUM"
  },
  "analysis_type": "HTA",
  "options": {
    "decomposition_depth": 3,
    "include_goms": true,
    "calculate_metrics": true
  }
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "analysis_id": "ana_20250908_001",
    "task_hierarchy": {
      "root_task": {
        "id": "task_001",
        "name": "态势分析任务",
        "subtasks": [
          {
            "id": "subtask_001",
            "name": "数据收集",
            "type": "perceptual",
            "estimated_time": 120,
            "cognitive_load": "LOW"
          },
          {
            "id": "subtask_002",
            "name": "数据分析", 
            "type": "cognitive",
            "estimated_time": 300,
            "cognitive_load": "HIGH"
          }
        ]
      }
    },
    "goms_model": {
      "goals": ["完成态势分析"],
      "operators": ["观察", "分析", "判断"],
      "methods": ["系统分析法", "对比分析法"],
      "selection_rules": ["优先处理紧急信息"]
    },
    "metrics": {
      "total_estimated_time": 420,
      "complexity_score": 0.65,
      "cognitive_load_score": 0.58,
      "error_probability": 0.12
    }
  }
}
```

## 效能评估API

### 执行效能评估
对人机协作方案进行效能评估。

**请求**
```
POST /api/evaluation/assess
Content-Type: application/json
```

**请求参数**
```json
{
  "allocation_scheme": {
    "scheme_id": "scheme_001",
    "human_tasks": ["subtask_002"],
    "machine_tasks": ["subtask_001"],
    "collaboration_tasks": []
  },
  "evaluation_criteria": {
    "efficiency": 0.4,
    "reliability": 0.3,
    "coordination": 0.2,
    "satisfaction": 0.1
  },
  "context": {
    "environment": "combat",
    "urgency": "high",
    "resources": "limited"
  }
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "evaluation_id": "eval_20250908_001",
    "overall_effectiveness": 0.85,
    "detailed_scores": {
      "efficiency": {
        "score": 0.88,
        "weight": 0.4,
        "weighted_score": 0.352
      },
      "reliability": {
        "score": 0.82,
        "weight": 0.3,
        "weighted_score": 0.246
      },
      "coordination": {
        "score": 0.85,
        "weight": 0.2,
        "weighted_score": 0.17
      },
      "satisfaction": {
        "score": 0.87,
        "weight": 0.1,
        "weighted_score": 0.087
      }
    },
    "strengths": [
      "人机能力匹配度高",
      "任务分配合理"
    ],
    "weaknesses": [
      "协作接口需要优化"
    ],
    "improvement_suggestions": [
      "加强人机协作训练",
      "优化信息传递机制"
    ],
    "risk_assessment": {
      "overall_risk": "LOW",
      "risk_factors": [
        {
          "factor": "通信故障",
          "probability": 0.1,
          "impact": "MEDIUM"
        }
      ]
    }
  }
}
```

## 配置管理API

### 获取配置档案列表
获取所有可用的配置档案。

**请求**
```
GET /api/config/profiles
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "profiles": [
      {
        "id": "profile_001",
        "name": "默认配置",
        "description": "系统默认配置档案",
        "created_at": "2025-09-08T10:00:00Z",
        "is_active": true
      },
      {
        "id": "profile_002",
        "name": "高效率配置",
        "description": "优化效率的配置档案", 
        "created_at": "2025-09-08T11:00:00Z",
        "is_active": false
      }
    ]
  }
}
```

### 创建配置档案
创建新的配置档案。

**请求**
```
POST /api/config/profiles
Content-Type: application/json
```

**请求参数**
```json
{
  "name": "自定义配置",
  "description": "用户自定义的配置档案",
  "config": {
    "allocation_mode": "EFFICIENCY_FIRST",
    "optimization_objective": "SPEED",
    "human_capability_weight": 0.7,
    "machine_capability_weight": 0.3
  }
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "profile_id": "profile_003",
    "message": "配置档案创建成功"
  }
}
```

## 错误码说明

### 通用错误码
- `INVALID_INPUT` - 输入参数无效
- `MISSING_PARAMETER` - 缺少必需参数
- `INTERNAL_ERROR` - 内部服务器错误
- `UNAUTHORIZED` - 未授权访问
- `FORBIDDEN` - 禁止访问
- `NOT_FOUND` - 资源未找到

### 业务错误码
- `ALLOCATION_FAILED` - 分配失败
- `DECISION_ERROR` - 决策计算错误
- `ANALYSIS_FAILED` - 任务分析失败
- `EVALUATION_ERROR` - 效能评估错误
- `CONFIG_ERROR` - 配置错误

### HTTP状态码
- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源未找到
- `500` - 内部服务器错误

## 使用示例

### Python示例
```python
import requests
import json

# 基础配置
base_url = "http://localhost:5000/api"
headers = {"Content-Type": "application/json"}

# 执行人机分配
allocation_data = {
    "task_hierarchy": {
        "id": "task_001",
        "name": "测试任务",
        "subtasks": [...]
    },
    "constraints": {"max_human_tasks": 5},
    "preferences": {"efficiency_priority": 0.8}
}

response = requests.post(
    f"{base_url}/allocation/allocate",
    headers=headers,
    data=json.dumps(allocation_data)
)

if response.status_code == 200:
    result = response.json()
    if result["success"]:
        print("分配成功:", result["data"]["recommended_scheme"])
    else:
        print("分配失败:", result["error"]["message"])
```

### JavaScript示例
```javascript
// 执行决策分析
const decisionData = {
  decision_matrix: {
    alternatives: [...],
    criteria: [...]
  },
  method: "WRDM"
};

fetch('/api/decision/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(decisionData)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('决策结果:', data.data.results);
  } else {
    console.error('决策失败:', data.error.message);
  }
});
```

## API测试

### 在线测试
系统提供在线API测试界面，访问：`http://localhost:5000/api-docs`

### 测试工具推荐
- **Postman** - 图形化API测试工具
- **curl** - 命令行HTTP客户端
- **HTTPie** - 现代化的命令行HTTP客户端

### 测试数据
系统提供测试数据集，位于：`tests/test_data/api_test_data.json`

---

**文档版本**：v1.0.0  
**最后更新**：2025年9月8日  
**编写人员**：HMDM API文档团队  
**审核状态**：已审核
