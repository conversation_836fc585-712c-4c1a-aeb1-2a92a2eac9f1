"""
人机功能分配系统主入口

整合所有分配相关功能，提供统一的人机功能分配决策接口
这是HMDM系统的核心模块，专门用于人机功能分配方案的生成和优化
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import logging
import uuid
import numpy as np

from ..models.task_models import TaskHierarchy, Task
from ..models.decision_models import DecisionMatrix
from ..decision.fuzzy_decision_engine import FuzzyDecisionEngine
from .human_machine_capability_analyzer import HumanMachineCapabilityAnalyzer
from .allocation_scheme_generator import AllocationSchemeGenerator, AllocationScheme
from .collaboration_effectiveness_evaluator import (
    CollaborationEffectivenessEvaluator, 
    EffectivenessMetrics,
    EvaluationReport
)


@dataclass
class AllocationDecisionResult:
    """人机分配决策结果"""
    decision_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    recommended_scheme: Optional[AllocationScheme] = None
    alternative_schemes: List[AllocationScheme] = field(default_factory=list)
    decision_confidence: float = 0.0
    decision_rationale: Dict[str, Any] = field(default_factory=dict)
    effectiveness_metrics: Optional[EffectivenessMetrics] = None
    implementation_guidance: Dict[str, Any] = field(default_factory=dict)
    evaluation_report: Optional[EvaluationReport] = None
    
    # 决策过程信息
    task_analysis_summary: Dict[str, Any] = field(default_factory=dict)
    capability_analysis_summary: Dict[str, Any] = field(default_factory=dict)
    scheme_comparison: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'decision_id': self.decision_id,
            'recommended_scheme': self.recommended_scheme.to_dict() if self.recommended_scheme else None,
            'alternative_schemes': [scheme.to_dict() for scheme in self.alternative_schemes],
            'decision_confidence': self.decision_confidence,
            'decision_rationale': self.decision_rationale,
            'effectiveness_metrics': self.effectiveness_metrics.to_dict() if self.effectiveness_metrics else None,
            'implementation_guidance': self.implementation_guidance,
            'evaluation_report': self.evaluation_report.to_dict() if self.evaluation_report else None,
            'task_analysis_summary': self.task_analysis_summary,
            'capability_analysis_summary': self.capability_analysis_summary,
            'scheme_comparison': self.scheme_comparison
        }


class HumanMachineAllocationSystem:
    """
    人机功能分配系统
    
    这是HMDM系统的核心类，专门用于人机功能分配决策
    整合了能力分析、方案生成、效能评估和决策支持等功能
    """
    
    def __init__(self):
        """初始化人机分配系统"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个组件
        self.capability_analyzer = HumanMachineCapabilityAnalyzer()
        self.scheme_generator = AllocationSchemeGenerator()
        self.effectiveness_evaluator = CollaborationEffectivenessEvaluator()
        self.fuzzy_engine = FuzzyDecisionEngine()
        
        # 系统配置
        self.config = {
            'default_scheme_count': 5,
            'decision_threshold': 0.1,
            'confidence_threshold': 0.7,
            'max_iterations': 3
        }
        
        self.logger.info("人机功能分配系统初始化完成")
    
    def allocate_functions(self, task_hierarchy: TaskHierarchy, 
                          constraints: Optional[Dict] = None,
                          preferences: Optional[Dict] = None,
                          optimization_criteria: Optional[Dict] = None) -> AllocationDecisionResult:
        """
        主要的人机功能分配接口
        
        这是系统的核心方法，实现完整的人机功能分配决策流程：
        1. 任务分析和能力需求评估
        2. 生成候选分配方案
        3. 评估各方案的协同效能
        4. 应用模糊决策选择最优方案
        5. 生成实施指导和建议
        
        Args:
            task_hierarchy: 任务层次结构
            constraints: 分配约束条件 {'max_human_tasks': 10, 'min_machine_tasks': 5}
            preferences: 用户偏好设置 {'prefer_human_decision': True, 'efficiency_priority': 0.8}
            optimization_criteria: 优化准则 {'efficiency': 0.8, 'reliability': 0.6}
            
        Returns:
            AllocationDecisionResult: 完整的分配决策结果
        """
        self.logger.info("开始执行人机功能分配决策流程")
        
        try:
            # 第1步：任务分析和能力需求评估
            self.logger.info("步骤1: 分析任务层次结构和能力需求")
            task_analysis_summary = self._analyze_task_requirements(task_hierarchy)
            
            # 第2步：生成候选分配方案
            self.logger.info("步骤2: 生成候选人机分配方案")
            scheme_count = preferences.get('scheme_count', self.config['default_scheme_count']) if preferences else self.config['default_scheme_count']
            candidate_schemes = self.scheme_generator.generate_schemes(
                task_hierarchy, 
                num_schemes=scheme_count,
                constraints=constraints,
                preferences=preferences
            )
            
            if not candidate_schemes:
                raise ValueError("无法生成有效的分配方案")
            
            # 第3步：评估各方案效能
            self.logger.info("步骤3: 评估各方案的协同效能")
            effectiveness_results = {}
            evaluation_reports = {}
            
            for scheme in candidate_schemes:
                report = self.effectiveness_evaluator.evaluate_scheme(scheme, task_hierarchy)
                effectiveness_results[scheme.scheme_id] = report.metrics
                evaluation_reports[scheme.scheme_id] = report
            
            # 第4步：应用模糊决策选择最优方案
            self.logger.info("步骤4: 应用模糊决策算法选择最优方案")
            decision_matrix = self._build_scheme_comparison_matrix(
                candidate_schemes, effectiveness_results, preferences
            )
            
            fuzzy_result = self.fuzzy_engine.weighted_relative_deviation_method(decision_matrix)
            
            # 第5步：生成最终决策结果
            self.logger.info("步骤5: 生成最终决策结果和实施指导")

            # 根据推荐的alternative_id找到对应的方案
            recommended_scheme = None
            for i, alternative_id in enumerate(decision_matrix.alternative_ids):
                if alternative_id == fuzzy_result.recommended_alternative_id or i == 0:  # 默认选择第一个
                    recommended_scheme = candidate_schemes[i]
                    break

            if recommended_scheme is None:
                recommended_scheme = candidate_schemes[0]  # 默认选择第一个方案
            
            # 应用优化准则（如果提供）
            if optimization_criteria:
                self.logger.info("应用优化准则进一步优化推荐方案")
                recommended_scheme = self.scheme_generator.optimize_scheme(
                    recommended_scheme, task_hierarchy, optimization_criteria
                )
                # 重新评估优化后的方案
                optimized_report = self.effectiveness_evaluator.evaluate_scheme(
                    recommended_scheme, task_hierarchy
                )
                evaluation_reports[recommended_scheme.scheme_id] = optimized_report
                effectiveness_results[recommended_scheme.scheme_id] = optimized_report.metrics
            
            # 构建决策结果
            result = AllocationDecisionResult(
                recommended_scheme=recommended_scheme,
                alternative_schemes=[s for s in candidate_schemes if s.scheme_id != recommended_scheme.scheme_id],
                decision_confidence=fuzzy_result.confidence,
                decision_rationale=self._generate_decision_rationale(
                    recommended_scheme, 
                    effectiveness_results[recommended_scheme.scheme_id],
                    fuzzy_result
                ),
                effectiveness_metrics=effectiveness_results[recommended_scheme.scheme_id],
                implementation_guidance=self._generate_implementation_guidance(
                    recommended_scheme, task_hierarchy
                ),
                evaluation_report=evaluation_reports[recommended_scheme.scheme_id],
                task_analysis_summary=task_analysis_summary,
                capability_analysis_summary=self._generate_capability_summary(task_hierarchy),
                scheme_comparison=self._generate_scheme_comparison(
                    candidate_schemes, effectiveness_results
                )
            )
            
            self.logger.info(f"人机功能分配决策完成，推荐方案: {recommended_scheme.name}")
            self.logger.info(f"决策置信度: {result.decision_confidence:.3f}")
            self.logger.info(f"总体效能: {result.effectiveness_metrics.overall_effectiveness:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"人机功能分配决策过程中发生错误: {str(e)}")
            raise
    
    def _analyze_task_requirements(self, task_hierarchy: TaskHierarchy) -> Dict[str, Any]:
        """分析任务层次结构的能力需求"""
        all_tasks = task_hierarchy.get_all_tasks()
        
        if not all_tasks:
            return {
                'total_tasks': 0,
                'complexity_distribution': {},
                'capability_requirements': {},
                'critical_tasks': []
            }
        
        # 统计任务分布
        complexity_distribution = {'low': 0, 'medium': 0, 'high': 0}
        capability_requirements = {}
        critical_tasks = []
        
        for task in all_tasks:
            # 分析复杂度分布
            if hasattr(task, 'attributes') and hasattr(task.attributes, 'complexity'):
                complexity = task.attributes.complexity
                if complexity < 0.4:
                    complexity_distribution['low'] += 1
                elif complexity < 0.7:
                    complexity_distribution['medium'] += 1
                else:
                    complexity_distribution['high'] += 1
            
            # 分析能力需求
            task_requirements = self.capability_analyzer.analyze_task_requirements(task)
            for cap_type, requirement in task_requirements.items():
                if cap_type.value not in capability_requirements:
                    capability_requirements[cap_type.value] = []
                capability_requirements[cap_type.value].append(requirement)
            
            # 识别关键任务
            if (hasattr(task, 'attributes') and 
                hasattr(task.attributes, 'importance') and 
                task.attributes.importance > 0.8):
                critical_tasks.append({
                    'task_id': task.id,
                    'name': task.name,
                    'importance': task.attributes.importance
                })
        
        # 计算平均能力需求
        avg_capability_requirements = {}
        for cap_type, requirements in capability_requirements.items():
            avg_capability_requirements[cap_type] = sum(requirements) / len(requirements)
        
        return {
            'total_tasks': len(all_tasks),
            'complexity_distribution': complexity_distribution,
            'capability_requirements': avg_capability_requirements,
            'critical_tasks': critical_tasks,
            'task_hierarchy_depth': max(task.level for task in all_tasks) if all_tasks else 0
        }
    
    def _build_scheme_comparison_matrix(self, schemes: List[AllocationScheme],
                                      effectiveness_results: Dict[str, EffectivenessMetrics],
                                      preferences: Optional[Dict] = None) -> DecisionMatrix:
        """构建方案比较的决策矩阵"""
        
        # 定义评估准则
        criteria = [
            "overall_effectiveness",
            "task_completion_rate", 
            "time_efficiency",
            "resource_utilization",
            "error_rate_inverse",  # 错误率的倒数（越低越好转换为越高越好）
            "coordination_overhead_inverse",  # 协调开销的倒数
            "adaptability"
        ]
        
        # 构建决策矩阵
        matrix_data = []
        for scheme in schemes:
            metrics = effectiveness_results[scheme.scheme_id]
            row = [
                metrics.overall_effectiveness,
                metrics.task_completion_rate,
                metrics.time_efficiency,
                metrics.resource_utilization,
                1.0 - metrics.error_rate,  # 转换为正向指标
                1.0 - metrics.coordination_overhead,  # 转换为正向指标
                metrics.adaptability
            ]
            matrix_data.append(row)
        
        # 设置权重（可以根据用户偏好调整）
        default_weights = [0.25, 0.20, 0.15, 0.10, 0.10, 0.10, 0.10]
        
        if preferences:
            # 根据用户偏好调整权重
            if preferences.get('efficiency_priority', 0) > 0.7:
                # 提高效率相关指标的权重
                default_weights[2] *= 1.5  # time_efficiency
                default_weights[1] *= 1.2  # task_completion_rate
            
            if preferences.get('reliability_priority', 0) > 0.7:
                # 提高可靠性相关指标的权重
                default_weights[4] *= 1.5  # error_rate_inverse
                default_weights[6] *= 1.2  # adaptability
        
        # 归一化权重
        total_weight = sum(default_weights)
        weights = [w / total_weight for w in default_weights]
        
        # 创建决策矩阵
        decision_matrix = DecisionMatrix(
            name="人机分配方案比较矩阵",
            description="用于比较不同人机分配方案效能的决策矩阵"
        )

        # 设置矩阵数据
        decision_matrix.matrix = np.array(matrix_data)
        decision_matrix.alternative_ids = [f"方案{i+1}" for i in range(len(schemes))]
        decision_matrix.indicator_ids = criteria

        # 创建简单的评估方案
        from ..models.evaluation_models import EvaluationScheme, IndicatorDefinition, IndicatorType
        evaluation_scheme = EvaluationScheme(
            name="人机分配效能评估",
            description="评估人机分配方案的综合效能"
        )

        # 添加指标定义
        for i, criterion in enumerate(criteria):
            indicator = IndicatorDefinition(
                name=criterion,
                description=f"{criterion}指标",
                indicator_type=IndicatorType.PERFORMANCE,
                is_benefit=True  # 所有指标都是越大越好
            )
            evaluation_scheme.add_indicator(indicator)

        decision_matrix.evaluation_scheme = evaluation_scheme

        # 添加备选方案
        from ..models.decision_models import Alternative
        for i, scheme in enumerate(schemes):
            alternative = Alternative(
                name=f"方案{i+1}",
                description=scheme.description or f"人机分配方案{i+1}"
            )
            decision_matrix.add_alternative(alternative)
        
        return decision_matrix
    
    def _generate_decision_rationale(self, scheme: AllocationScheme, 
                                   metrics: EffectivenessMetrics,
                                   fuzzy_result: Any) -> Dict[str, Any]:
        """生成决策理由说明"""
        rationale = {
            'selection_reason': f"该方案在综合评估中得分最高，总体效能达到{metrics.overall_effectiveness:.2%}",
            'key_advantages': [],
            'performance_highlights': {},
            'decision_factors': [],
            'confidence_explanation': f"决策置信度为{fuzzy_result.confidence:.2%}，表明该选择具有较高的可靠性"
        }
        
        # 分析关键优势
        if metrics.task_completion_rate > 0.9:
            rationale['key_advantages'].append("任务完成率优秀，可靠性强")
        if metrics.time_efficiency > 0.8:
            rationale['key_advantages'].append("时间效率出色，响应迅速")
        if metrics.error_rate < 0.1:
            rationale['key_advantages'].append("错误率低，质量控制良好")
        if metrics.coordination_overhead < 0.2:
            rationale['key_advantages'].append("协调开销小，执行顺畅")
        if metrics.adaptability > 0.8:
            rationale['key_advantages'].append("适应性强，环境变化应对能力好")
        
        # 性能亮点
        rationale['performance_highlights'] = {
            '任务完成率': f"{metrics.task_completion_rate:.1%}",
            '时间效率': f"{metrics.time_efficiency:.1%}",
            '资源利用率': f"{metrics.resource_utilization:.1%}",
            '错误率': f"{metrics.error_rate:.1%}",
            '协调开销': f"{metrics.coordination_overhead:.1%}",
            '适应性': f"{metrics.adaptability:.1%}"
        }
        
        # 决策因素
        rationale['decision_factors'] = [
            f"基于{scheme.strategy.value}策略的科学分配",
            "综合考虑人机能力特点和任务需求",
            "平衡效率、可靠性和适应性等多个维度",
            "采用模糊决策算法确保选择的客观性"
        ]
        
        return rationale
    
    def _generate_implementation_guidance(self, scheme: AllocationScheme, 
                                        task_hierarchy: TaskHierarchy) -> Dict[str, Any]:
        """生成实施指导"""
        guidance = {
            'deployment_steps': [],
            'resource_requirements': {},
            'risk_mitigation': [],
            'success_metrics': {},
            'timeline_estimate': {}
        }
        
        all_tasks = task_hierarchy.get_all_tasks()
        if not all_tasks:
            return guidance
        
        # 部署步骤
        guidance['deployment_steps'] = [
            "1. 人员培训：对相关人员进行人机协同工作培训",
            "2. 系统配置：配置机器系统和人机接口",
            "3. 试运行：在受控环境下进行试运行测试",
            "4. 逐步部署：分阶段实施完整的分配方案",
            "5. 监控优化：持续监控效果并进行必要调整"
        ]
        
        # 资源需求
        allocation_stats = {}
        for allocation in scheme.task_allocations.values():
            allocation_stats[allocation] = allocation_stats.get(allocation, 0) + 1
        
        guidance['resource_requirements'] = {
            '人力资源': f"需要 {allocation_stats.get('human', 0)} 个人工任务的执行人员",
            '机器资源': f"需要 {allocation_stats.get('machine', 0)} 个自动化系统或设备",
            '协同资源': f"需要 {allocation_stats.get('collaboration', 0)} 个人机协同工作站",
            '培训资源': "需要进行人机协同工作方法培训"
        }
        
        # 风险缓解
        if scheme.risk_assessment:
            if scheme.risk_assessment.get('human_overload_risk', 0) > 0.6:
                guidance['risk_mitigation'].append("建立人员轮换机制，避免过度疲劳")
            if scheme.risk_assessment.get('coordination_risk', 0) > 0.5:
                guidance['risk_mitigation'].append("制定详细的协同工作流程和沟通协议")
            if scheme.risk_assessment.get('machine_failure_risk', 0) > 0.4:
                guidance['risk_mitigation'].append("建立机器系统备份和故障恢复机制")
        
        # 成功指标
        guidance['success_metrics'] = {
            '任务完成率': "> 90%",
            '平均响应时间': "< 预期时间的110%", 
            '错误率': "< 10%",
            '用户满意度': "> 80%"
        }
        
        # 时间估算
        guidance['timeline_estimate'] = {
            '准备阶段': "1-2周",
            '试运行阶段': "2-3周", 
            '全面部署': "3-4周",
            '优化调整': "持续进行"
        }
        
        return guidance
    
    def _generate_capability_summary(self, task_hierarchy: TaskHierarchy) -> Dict[str, Any]:
        """生成能力分析摘要"""
        all_tasks = task_hierarchy.get_all_tasks()
        
        if not all_tasks:
            return {'total_tasks': 0, 'human_suitable_tasks': 0, 'machine_suitable_tasks': 0}
        
        human_suitable = 0
        machine_suitable = 0
        collaboration_suitable = 0
        
        for task in all_tasks:
            allocation_type, confidence, details = self.capability_analyzer.recommend_allocation(task)
            if allocation_type == 'human':
                human_suitable += 1
            elif allocation_type == 'machine':
                machine_suitable += 1
            else:
                collaboration_suitable += 1
        
        return {
            'total_tasks': len(all_tasks),
            'human_suitable_tasks': human_suitable,
            'machine_suitable_tasks': machine_suitable,
            'collaboration_suitable_tasks': collaboration_suitable,
            'human_suitability_ratio': human_suitable / len(all_tasks),
            'machine_suitability_ratio': machine_suitable / len(all_tasks),
            'collaboration_ratio': collaboration_suitable / len(all_tasks)
        }
    
    def _generate_scheme_comparison(self, schemes: List[AllocationScheme],
                                  effectiveness_results: Dict[str, EffectivenessMetrics]) -> Dict[str, Any]:
        """生成方案比较信息"""
        comparison = {
            'total_schemes': len(schemes),
            'scheme_rankings': [],
            'performance_comparison': {},
            'strategy_distribution': {}
        }
        
        # 按总体效能排序
        sorted_schemes = sorted(schemes, 
                              key=lambda s: effectiveness_results[s.scheme_id].overall_effectiveness, 
                              reverse=True)
        
        for i, scheme in enumerate(sorted_schemes):
            metrics = effectiveness_results[scheme.scheme_id]
            comparison['scheme_rankings'].append({
                'rank': i + 1,
                'scheme_name': scheme.name,
                'strategy': scheme.strategy.value,
                'overall_effectiveness': metrics.overall_effectiveness,
                'key_strength': self._identify_key_strength(metrics)
            })
        
        # 性能对比
        if schemes:
            best_metrics = effectiveness_results[sorted_schemes[0].scheme_id]
            worst_metrics = effectiveness_results[sorted_schemes[-1].scheme_id]
            
            comparison['performance_comparison'] = {
                'best_overall_effectiveness': best_metrics.overall_effectiveness,
                'worst_overall_effectiveness': worst_metrics.overall_effectiveness,
                'performance_gap': best_metrics.overall_effectiveness - worst_metrics.overall_effectiveness,
                'average_effectiveness': sum(effectiveness_results[s.scheme_id].overall_effectiveness 
                                           for s in schemes) / len(schemes)
            }
        
        # 策略分布
        strategy_count = {}
        for scheme in schemes:
            strategy = scheme.strategy.value
            strategy_count[strategy] = strategy_count.get(strategy, 0) + 1
        
        comparison['strategy_distribution'] = strategy_count
        
        return comparison
    
    def _identify_key_strength(self, metrics: EffectivenessMetrics) -> str:
        """识别方案的关键优势"""
        strengths = {
            '任务完成': metrics.task_completion_rate,
            '时间效率': metrics.time_efficiency,
            '资源利用': metrics.resource_utilization,
            '质量控制': 1.0 - metrics.error_rate,
            '协调效率': 1.0 - metrics.coordination_overhead,
            '适应能力': metrics.adaptability
        }
        
        return max(strengths.items(), key=lambda x: x[1])[0]
