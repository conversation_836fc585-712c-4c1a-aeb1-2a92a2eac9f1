"""
人机分配系统配置管理

提供人机分配相关的配置参数管理和默认值设置
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class AllocationMode(Enum):
    """分配模式"""
    AUTOMATIC = "automatic"      # 自动分配
    SEMI_AUTOMATIC = "semi_automatic"  # 半自动分配
    MANUAL = "manual"           # 手动分配


class OptimizationObjective(Enum):
    """优化目标"""
    EFFICIENCY = "efficiency"           # 效率优先
    RELIABILITY = "reliability"         # 可靠性优先
    COST_EFFECTIVENESS = "cost_effectiveness"  # 成本效益优先
    BALANCED = "balanced"               # 平衡优化


@dataclass
class AllocationConfig:
    """人机分配配置"""
    
    # 基本配置
    allocation_mode: AllocationMode = AllocationMode.SEMI_AUTOMATIC
    optimization_objective: OptimizationObjective = OptimizationObjective.BALANCED
    
    # 方案生成配置
    default_scheme_count: int = 5
    max_scheme_count: int = 10
    min_scheme_count: int = 2
    
    # 决策配置
    decision_threshold: float = 0.1
    confidence_threshold: float = 0.7
    max_decision_iterations: int = 3
    
    # 能力分析配置
    capability_weights: Dict[str, float] = field(default_factory=lambda: {
        "cognitive": 0.25,
        "physical": 0.15,
        "perceptual": 0.20,
        "decision_making": 0.25,
        "execution": 0.10,
        "coordination": 0.05
    })
    
    # 效能评估配置
    evaluation_weights: Dict[str, float] = field(default_factory=lambda: {
        "task_completion": 0.25,
        "time_efficiency": 0.20,
        "resource_utilization": 0.15,
        "error_rate": 0.15,
        "coordination_overhead": 0.15,
        "adaptability": 0.10
    })
    
    # 基准性能参数
    baseline_performance: Dict[str, Dict[str, float]] = field(default_factory=lambda: {
        'human_only': {
            'completion_rate': 0.85,
            'error_rate': 0.12,
            'efficiency': 0.70
        },
        'machine_only': {
            'completion_rate': 0.95,
            'error_rate': 0.05,
            'efficiency': 0.90
        },
        'collaboration': {
            'completion_rate': 0.92,
            'error_rate': 0.08,
            'efficiency': 0.85
        }
    })
    
    # 约束条件
    constraints: Dict[str, Any] = field(default_factory=lambda: {
        'max_human_workload': 0.8,
        'min_machine_utilization': 0.3,
        'max_coordination_overhead': 0.4,
        'min_task_completion_rate': 0.8
    })
    
    # 偏好设置
    preferences: Dict[str, Any] = field(default_factory=lambda: {
        'prefer_human_decision': True,
        'prefer_machine_execution': True,
        'allow_full_automation': False,
        'require_human_oversight': True
    })
    
    # 实时配置
    enable_real_time_optimization: bool = True
    optimization_interval: int = 300  # 秒
    performance_monitoring_interval: int = 60  # 秒
    
    # 日志和调试配置
    enable_detailed_logging: bool = True
    log_allocation_decisions: bool = True
    log_performance_metrics: bool = True
    debug_mode: bool = False
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证权重总和
            if abs(sum(self.capability_weights.values()) - 1.0) > 0.01:
                raise ValueError("能力权重总和必须为1.0")
            
            if abs(sum(self.evaluation_weights.values()) - 1.0) > 0.01:
                raise ValueError("评估权重总和必须为1.0")
            
            # 验证阈值范围
            if not (0 <= self.decision_threshold <= 1):
                raise ValueError("决策阈值必须在0-1之间")
            
            if not (0 <= self.confidence_threshold <= 1):
                raise ValueError("置信度阈值必须在0-1之间")
            
            # 验证方案数量
            if self.min_scheme_count > self.max_scheme_count:
                raise ValueError("最小方案数不能大于最大方案数")
            
            if self.default_scheme_count < self.min_scheme_count or \
               self.default_scheme_count > self.max_scheme_count:
                raise ValueError("默认方案数必须在最小和最大方案数之间")
            
            # 验证约束条件
            for key, value in self.constraints.items():
                if isinstance(value, (int, float)) and not (0 <= value <= 1):
                    raise ValueError(f"约束条件 {key} 的值必须在0-1之间")
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'allocation_mode': self.allocation_mode.value,
            'optimization_objective': self.optimization_objective.value,
            'default_scheme_count': self.default_scheme_count,
            'max_scheme_count': self.max_scheme_count,
            'min_scheme_count': self.min_scheme_count,
            'decision_threshold': self.decision_threshold,
            'confidence_threshold': self.confidence_threshold,
            'max_decision_iterations': self.max_decision_iterations,
            'capability_weights': self.capability_weights,
            'evaluation_weights': self.evaluation_weights,
            'baseline_performance': self.baseline_performance,
            'constraints': self.constraints,
            'preferences': self.preferences,
            'enable_real_time_optimization': self.enable_real_time_optimization,
            'optimization_interval': self.optimization_interval,
            'performance_monitoring_interval': self.performance_monitoring_interval,
            'enable_detailed_logging': self.enable_detailed_logging,
            'log_allocation_decisions': self.log_allocation_decisions,
            'log_performance_metrics': self.log_performance_metrics,
            'debug_mode': self.debug_mode
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AllocationConfig':
        """从字典创建配置"""
        config = cls()
        
        if 'allocation_mode' in data:
            config.allocation_mode = AllocationMode(data['allocation_mode'])
        
        if 'optimization_objective' in data:
            config.optimization_objective = OptimizationObjective(data['optimization_objective'])
        
        # 更新其他配置项
        for key, value in data.items():
            if hasattr(config, key) and key not in ['allocation_mode', 'optimization_objective']:
                setattr(config, key, value)
        
        return config
    
    def get_default_config(self) -> 'AllocationConfig':
        """获取默认配置"""
        return AllocationConfig()
    
    def update_weights(self, capability_weights: Optional[Dict[str, float]] = None,
                      evaluation_weights: Optional[Dict[str, float]] = None) -> None:
        """更新权重配置"""
        if capability_weights:
            # 验证权重总和
            if abs(sum(capability_weights.values()) - 1.0) > 0.01:
                raise ValueError("能力权重总和必须为1.0")
            self.capability_weights.update(capability_weights)
        
        if evaluation_weights:
            # 验证权重总和
            if abs(sum(evaluation_weights.values()) - 1.0) > 0.01:
                raise ValueError("评估权重总和必须为1.0")
            self.evaluation_weights.update(evaluation_weights)
    
    def update_constraints(self, constraints: Dict[str, Any]) -> None:
        """更新约束条件"""
        for key, value in constraints.items():
            if isinstance(value, (int, float)) and not (0 <= value <= 1):
                raise ValueError(f"约束条件 {key} 的值必须在0-1之间")
        
        self.constraints.update(constraints)
    
    def update_preferences(self, preferences: Dict[str, Any]) -> None:
        """更新偏好设置"""
        self.preferences.update(preferences)
    
    def get_optimization_config(self) -> Dict[str, Any]:
        """获取优化相关配置"""
        return {
            'optimization_objective': self.optimization_objective.value,
            'decision_threshold': self.decision_threshold,
            'confidence_threshold': self.confidence_threshold,
            'max_decision_iterations': self.max_decision_iterations,
            'constraints': self.constraints,
            'preferences': self.preferences
        }
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """获取评估相关配置"""
        return {
            'evaluation_weights': self.evaluation_weights,
            'baseline_performance': self.baseline_performance,
            'constraints': self.constraints
        }
    
    def get_capability_config(self) -> Dict[str, Any]:
        """获取能力分析相关配置"""
        return {
            'capability_weights': self.capability_weights,
            'baseline_performance': self.baseline_performance
        }


# 默认配置实例
DEFAULT_ALLOCATION_CONFIG = AllocationConfig()


def load_allocation_config(config_file: Optional[str] = None) -> AllocationConfig:
    """加载分配配置"""
    if config_file:
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return AllocationConfig.from_dict(config_data)
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
            return DEFAULT_ALLOCATION_CONFIG
    else:
        return DEFAULT_ALLOCATION_CONFIG


def save_allocation_config(config: AllocationConfig, config_file: str) -> bool:
    """保存分配配置"""
    try:
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False
