#!/usr/bin/env python3
"""
HMDM系统优化脚本

修复测试中发现的问题，优化系统性能和稳定性
"""

import os
import sys
import shutil
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.hmdm.core.system_manager import HMDMSystemManager
from src.hmdm.core.config_manager import ConfigManager


class SystemOptimizer:
    """系统优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.optimization_results = []
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler()
            ]
        )
    
    def optimize_directory_structure(self):
        """优化目录结构"""
        print("优化目录结构...")
        
        # 确保必要的目录存在
        directories = [
            'config',
            'config/profiles',
            'logs',
            'data',
            'backups',
            'temp',
            'tests/reports'
        ]
        
        created_dirs = []
        for directory in directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(directory)
                print(f"  创建目录: {directory}")
        
        if created_dirs:
            self.optimization_results.append(f"创建了 {len(created_dirs)} 个目录")
        else:
            self.optimization_results.append("目录结构已优化")
    
    def optimize_configuration_files(self):
        """优化配置文件"""
        print("优化配置文件...")
        
        try:
            # 创建系统管理器
            system_manager = HMDMSystemManager()
            
            # 验证配置
            validation_result = system_manager.validate_config()
            if validation_result['is_valid']:
                print("  配置验证通过")
                self.optimization_results.append("配置文件验证通过")
            else:
                print("  配置验证失败:")
                for error in validation_result.get('errors', []):
                    print(f"    错误: {error}")
                for warning in validation_result.get('warnings', []):
                    print(f"    警告: {warning}")
                
                self.optimization_results.append(f"配置验证发现 {len(validation_result.get('errors', []))} 个错误")
            
            # 保存优化后的配置
            config_manager = system_manager.get_config_manager()
            success = config_manager.save_system_config(config_manager.current_system_config)
            if success:
                print("  系统配置保存成功")
            
            success = config_manager.save_allocation_config(config_manager.current_allocation_config)
            if success:
                print("  人机分配配置保存成功")
                
        except Exception as e:
            print(f"  配置优化失败: {e}")
            self.optimization_results.append(f"配置优化失败: {e}")
    
    def optimize_log_files(self):
        """优化日志文件"""
        print("优化日志文件...")
        
        log_dir = Path('logs')
        if not log_dir.exists():
            log_dir.mkdir(exist_ok=True)
            print("  创建日志目录")
        
        # 清理旧的日志文件（保留最近7天）
        import time
        current_time = time.time()
        week_ago = current_time - (7 * 24 * 60 * 60)  # 7天前
        
        cleaned_files = 0
        for log_file in log_dir.glob('*.log*'):
            if log_file.stat().st_mtime < week_ago:
                try:
                    log_file.unlink()
                    cleaned_files += 1
                    print(f"  删除旧日志文件: {log_file.name}")
                except Exception as e:
                    print(f"  删除日志文件失败 {log_file.name}: {e}")
        
        if cleaned_files > 0:
            self.optimization_results.append(f"清理了 {cleaned_files} 个旧日志文件")
        else:
            self.optimization_results.append("日志文件已优化")
    
    def optimize_temp_files(self):
        """优化临时文件"""
        print("优化临时文件...")
        
        temp_dir = Path('temp')
        if temp_dir.exists():
            # 清理临时文件
            cleaned_files = 0
            for temp_file in temp_dir.rglob('*'):
                if temp_file.is_file():
                    try:
                        temp_file.unlink()
                        cleaned_files += 1
                    except Exception as e:
                        print(f"  删除临时文件失败 {temp_file.name}: {e}")
            
            if cleaned_files > 0:
                print(f"  清理了 {cleaned_files} 个临时文件")
                self.optimization_results.append(f"清理了 {cleaned_files} 个临时文件")
        else:
            temp_dir.mkdir(exist_ok=True)
            print("  创建临时文件目录")
            self.optimization_results.append("创建临时文件目录")
    
    def optimize_cache_files(self):
        """优化缓存文件"""
        print("优化缓存文件...")
        
        # 清理Python缓存文件
        cache_dirs = []
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                cache_dirs.append(os.path.join(root, '__pycache__'))
        
        cleaned_dirs = 0
        for cache_dir in cache_dirs:
            try:
                shutil.rmtree(cache_dir)
                cleaned_dirs += 1
                print(f"  删除缓存目录: {cache_dir}")
            except Exception as e:
                print(f"  删除缓存目录失败 {cache_dir}: {e}")
        
        if cleaned_dirs > 0:
            self.optimization_results.append(f"清理了 {cleaned_dirs} 个缓存目录")
        else:
            self.optimization_results.append("缓存文件已优化")
    
    def optimize_permissions(self):
        """优化文件权限"""
        print("优化文件权限...")
        
        if os.name == 'posix':  # Unix/Linux系统
            # 设置配置文件权限
            config_files = [
                'config/system_config.json',
                'config/allocation_config.json'
            ]
            
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    try:
                        # 设置为只有所有者可读写 (600)
                        os.chmod(config_path, 0o600)
                        print(f"  设置文件权限: {config_file}")
                    except Exception as e:
                        print(f"  设置权限失败 {config_file}: {e}")
            
            self.optimization_results.append("文件权限已优化")
        else:
            print("  Windows系统跳过权限优化")
            self.optimization_results.append("Windows系统跳过权限优化")
    
    def check_system_health(self):
        """检查系统健康状态"""
        print("检查系统健康状态...")
        
        health_issues = []
        
        try:
            # 检查系统管理器
            system_manager = HMDMSystemManager()
            status = system_manager.get_system_status()
            
            if status.get('system_status') != '运行中':
                health_issues.append("系统状态异常")
            
            # 检查模块状态
            modules = status.get('modules', {})
            for module_name, module_info in modules.items():
                if module_info.get('status') == '错误':
                    health_issues.append(f"模块 {module_name} 状态异常")
            
            # 检查配置完整性
            config_manager = system_manager.get_config_manager()
            validation_result = config_manager.validate_config()
            
            if not validation_result['is_valid']:
                health_issues.extend(validation_result.get('errors', []))
            
        except Exception as e:
            health_issues.append(f"系统健康检查失败: {e}")
        
        if health_issues:
            print("  发现健康问题:")
            for issue in health_issues:
                print(f"    - {issue}")
            self.optimization_results.append(f"发现 {len(health_issues)} 个健康问题")
        else:
            print("  系统健康状态良好")
            self.optimization_results.append("系统健康状态良好")
    
    def generate_optimization_report(self):
        """生成优化报告"""
        report_path = Path('tests/reports/system_optimization_report.md')
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HMDM系统优化报告\n\n")
            f.write(f"**优化时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## 优化结果\n\n")
            
            for i, result in enumerate(self.optimization_results, 1):
                f.write(f"{i}. {result}\n")
            
            f.write("\n## 优化建议\n\n")
            f.write("1. 定期运行系统优化脚本\n")
            f.write("2. 监控系统资源使用情况\n")
            f.write("3. 定期备份重要配置文件\n")
            f.write("4. 保持系统和依赖包的更新\n")
            f.write("5. 定期进行安全审计\n")
        
        print(f"优化报告已生成: {report_path}")
    
    def run_optimization(self):
        """运行完整的系统优化"""
        print("开始HMDM系统优化...")
        print("=" * 50)
        
        self.setup_logging()
        
        # 执行各项优化
        optimization_tasks = [
            ("目录结构优化", self.optimize_directory_structure),
            ("配置文件优化", self.optimize_configuration_files),
            ("日志文件优化", self.optimize_log_files),
            ("临时文件优化", self.optimize_temp_files),
            ("缓存文件优化", self.optimize_cache_files),
            ("文件权限优化", self.optimize_permissions),
            ("系统健康检查", self.check_system_health)
        ]
        
        for task_name, task_func in optimization_tasks:
            try:
                task_func()
            except Exception as e:
                print(f"{task_name}失败: {e}")
                self.optimization_results.append(f"{task_name}失败: {e}")
            print()
        
        # 生成报告
        self.generate_optimization_report()
        
        print("=" * 50)
        print("系统优化完成!")
        print(f"共执行了 {len(optimization_tasks)} 项优化任务")
        print(f"优化结果: {len(self.optimization_results)} 项")


def main():
    """主函数"""
    optimizer = SystemOptimizer()
    optimizer.run_optimization()


if __name__ == "__main__":
    from datetime import datetime
    main()
