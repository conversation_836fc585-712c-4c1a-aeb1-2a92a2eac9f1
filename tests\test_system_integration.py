"""
系统集成测试

测试allocation模块与现有HMDM系统的集成情况
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from src.hmdm.core.system_manager import HMDMSystemManager, SystemStatus, ModuleStatus
from src.hmdm.allocation.human_machine_allocation_system import HumanMachineAllocationSystem
from src.hmdm.models.task_models import TaskHierarchy, Task, TaskType, TaskAttribute


class TestSystemIntegration:
    """系统集成测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建系统管理器
        self.system_manager = HMDMSystemManager()
        
        # 修改配置以使用临时目录
        self.system_manager.config.data_directory = os.path.join(self.temp_dir, "data")
        self.system_manager.config.backup_directory = os.path.join(self.temp_dir, "backups")
        self.system_manager.config.temp_directory = os.path.join(self.temp_dir, "temp")
        self.system_manager.config.log_file = os.path.join(self.temp_dir, "test.log")
        
        # 只启用必要的模块以加快测试速度
        self.system_manager.config.enabled_modules = [
            "knowledge_base",
            "human_machine_allocation"
        ]
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'system_manager'):
            self.system_manager.stop_system()
        
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_system_manager_initialization(self):
        """测试系统管理器初始化"""
        assert self.system_manager is not None
        assert self.system_manager.status == SystemStatus.INITIALIZING
        assert "human_machine_allocation" in self.system_manager.modules
        
        # 验证allocation模块的依赖关系
        hma_module = self.system_manager.modules["human_machine_allocation"]
        assert "knowledge_base" in hma_module.dependencies
    
    def test_allocation_module_in_enabled_modules(self):
        """测试allocation模块在启用模块列表中"""
        assert "human_machine_allocation" in self.system_manager.config.enabled_modules
    
    def test_module_startup_order(self):
        """测试模块启动顺序"""
        startup_order = self.system_manager._calculate_startup_order()
        
        # 验证依赖关系：knowledge_base应该在human_machine_allocation之前启动
        kb_index = startup_order.index("knowledge_base")
        hma_index = startup_order.index("human_machine_allocation")

        assert kb_index < hma_index
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_system_startup_with_allocation(self, mock_knowledge_base):
        """测试包含allocation模块的系统启动"""
        # 模拟依赖模块
        mock_kb_instance = Mock()
        mock_knowledge_base.return_value = mock_kb_instance
        
        # 启动系统
        success = self.system_manager.start_system()
        assert success
        assert self.system_manager.status == SystemStatus.RUNNING
        
        # 验证allocation模块已启动
        hma_module = self.system_manager.modules["human_machine_allocation"]
        assert hma_module.status == ModuleStatus.RUNNING
        assert hma_module.instance is not None
        assert isinstance(hma_module.instance, HumanMachineAllocationSystem)
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_get_allocation_system(self, mock_knowledge_base):
        """测试获取allocation系统实例"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 启动系统
        self.system_manager.start_system()
        
        # 获取allocation系统实例
        allocation_system = self.system_manager.get_allocation_system()
        assert allocation_system is not None
        assert isinstance(allocation_system, HumanMachineAllocationSystem)
    
    def test_get_allocation_system_when_not_running(self):
        """测试系统未运行时获取allocation系统"""
        allocation_system = self.system_manager.get_allocation_system()
        assert allocation_system is None
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_data_channels_setup(self, mock_knowledge_base):
        """测试数据通道设置"""
        # 模拟依赖模块
        mock_kb_instance = Mock()
        mock_knowledge_base.return_value = mock_kb_instance

        # 启动系统
        self.system_manager.start_system()

        # 验证数据通道已建立
        assert "kb_to_hma" in self.system_manager.data_channels

        # 验证数据通道配置
        kb_to_hma = self.system_manager.data_channels["kb_to_hma"]
        assert kb_to_hma["source"] is not None  # 验证source存在
        assert kb_to_hma["data_type"] == "domain_knowledge"
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_system_status_with_allocation(self, mock_knowledge_base):
        """测试包含allocation模块的系统状态"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 启动系统
        self.system_manager.start_system()
        
        # 获取系统状态
        status = self.system_manager.get_system_status()
        
        assert status["system_status"] == SystemStatus.RUNNING.value
        assert "human_machine_allocation" in status["modules"]
        
        hma_status = status["modules"]["human_machine_allocation"]
        assert hma_status["status"] == ModuleStatus.RUNNING.value
        assert hma_status["start_time"] is not None
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_allocation_system_functionality(self, mock_knowledge_base):
        """测试allocation系统的基本功能"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 启动系统
        self.system_manager.start_system()
        
        # 获取allocation系统
        allocation_system = self.system_manager.get_allocation_system()
        assert allocation_system is not None
        
        # 创建测试任务层次结构
        task_hierarchy = TaskHierarchy(root_task_id="test_task")
        test_task = Task(
            id="test_task",
            name="测试任务",
            description="用于测试的任务",
            task_type=TaskType.MISSION_TASK
        )
        test_task.attributes = TaskAttribute(
            complexity=0.7,
            importance=0.8,
            urgency=0.6
        )
        task_hierarchy.add_task(test_task)
        
        # 测试allocation功能
        result = allocation_system.allocate_functions(task_hierarchy)
        
        assert result is not None
        assert result.recommended_scheme is not None
        assert result.decision_confidence > 0
        assert len(result.recommended_scheme.task_allocations) > 0
    
    def test_get_module_instance(self):
        """测试获取模块实例的通用方法"""
        # 测试未启动时
        instance = self.system_manager.get_module_instance("human_machine_allocation")
        assert instance is None
        
        # 测试不存在的模块
        instance = self.system_manager.get_module_instance("nonexistent_module")
        assert instance is None
    
    @patch('src.hmdm.knowledge.military_knowledge_base.MilitaryKnowledgeBase')
    def test_system_shutdown_with_allocation(self, mock_knowledge_base):
        """测试包含allocation模块的系统关闭"""
        # 模拟依赖模块
        mock_knowledge_base.return_value = Mock()
        
        # 启动系统
        self.system_manager.start_system()
        assert self.system_manager.status == SystemStatus.RUNNING
        
        # 关闭系统
        success = self.system_manager.stop_system()
        assert success
        assert self.system_manager.status == SystemStatus.STOPPED
        
        # 验证allocation模块已停止
        hma_module = self.system_manager.modules["human_machine_allocation"]
        assert hma_module.status == ModuleStatus.NOT_LOADED
        assert hma_module.instance is None
    
    def test_module_dependencies_validation(self):
        """测试模块依赖关系验证"""
        # 验证allocation模块的依赖关系是否正确设置
        hma_module = self.system_manager.modules["human_machine_allocation"]

        # 应该依赖knowledge_base
        assert "knowledge_base" in hma_module.dependencies
        
        # 不应该有循环依赖
        startup_order = self.system_manager._calculate_startup_order()
        assert len(startup_order) == len(self.system_manager.modules)


if __name__ == '__main__':
    pytest.main([__file__])
