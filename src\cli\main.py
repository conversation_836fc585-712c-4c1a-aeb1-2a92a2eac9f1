"""
HMDM命令行界面主程序

提供人机功能分配模型系统的命令行操作界面
"""

import click
import json
import sys
from pathlib import Path
from typing import Optional, List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from hmdm.task_analysis import HierarchicalTaskAnalyzer, GomsAnalyzer
from hmdm.decision import FuzzyDecisionEngine
from hmdm.evaluation import SchemeEvaluator
from hmdm.models import (
    Task, TaskType, Alternative, EvaluationScheme, DecisionMatrix
)
from hmdm.utils import DataLoader, get_logger, setup_logging
from rich.console import Console
from rich.table import Table
from rich.progress import Progress
from rich.panel import Panel


# 初始化
console = Console()
logger = get_logger("hmdm.cli")


@click.group()
@click.option('--log-level', default='INFO', help='日志级别')
@click.option('--log-dir', default='logs', help='日志目录')
def cli(log_level, log_dir):
    """人机功能分配模型系统 (HMDM) 命令行工具"""
    # 设置日志
    import logging
    level = getattr(logging, log_level.upper(), logging.INFO)
    setup_logging(log_dir, level)
    
    console.print(Panel.fit(
        "[bold blue]人机功能分配模型系统 (HMDM)[/bold blue]\n"
        "[dim]Human-Machine Function Distribution Model[/dim]",
        border_style="blue"
    ))


@cli.command()
@click.option('--scenario', default='态势分析', help='任务场景类型')
@click.option('--output', '-o', help='输出文件路径')
def create_task(scenario, output):
    """创建任务分解结构"""
    console.print(f"[green]创建任务场景：{scenario}[/green]")
    
    try:
        # 创建任务分析器
        analyzer = HierarchicalTaskAnalyzer()
        
        # 创建根任务
        root_task = Task(
            name=f"{scenario}任务",
            description=f"执行{scenario}相关的任务",
            task_type=TaskType.MISSION_TASK
        )
        
        # 创建任务层次结构
        hierarchy = analyzer.create_task_hierarchy(root_task)
        
        # 自动分解任务
        with Progress() as progress:
            task_progress = progress.add_task("[cyan]分解任务中...", total=100)
            
            subtasks = analyzer.auto_decompose_by_scenario(root_task.id, scenario)
            progress.update(task_progress, advance=50)
            
            # 进一步分解子任务
            for subtask in subtasks:
                if subtask.task_type == TaskType.ZZ_TASK:
                    try:
                        sub_subtasks = analyzer.auto_decompose_by_scenario(subtask.id, "典型功能")
                        progress.update(task_progress, advance=10)
                    except:
                        pass  # 忽略无法进一步分解的任务
            
            progress.update(task_progress, completed=100)
        
        # 验证层次结构
        errors = analyzer.validate_hierarchy()
        if errors:
            console.print("[yellow]警告：任务层次结构存在问题：[/yellow]")
            for error in errors:
                console.print(f"  - {error}")
        
        # 显示结果
        display_task_hierarchy(hierarchy)
        
        # 保存结果
        if output:
            hierarchy_data = analyzer.export_hierarchy()
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(hierarchy_data, f, ensure_ascii=False, indent=2)
            console.print(f"[green]任务结构已保存到：{output}[/green]")
        
    except Exception as e:
        console.print(f"[red]创建任务失败：{e}[/red]")
        logger.error(f"创建任务失败: {e}")


@cli.command()
@click.argument('alternatives_file')
@click.option('--scheme-file', help='评估方案文件')
@click.option('--method', default='WRDM', help='决策方法 (WRDM/TOPSIS/FUZZY_AHP)')
@click.option('--output', '-o', help='输出文件路径')
def evaluate(alternatives_file, scheme_file, method, output):
    """评估备选方案"""
    console.print(f"[green]开始评估方案，使用方法：{method}[/green]")
    
    try:
        # 加载数据
        data_loader = DataLoader()
        
        # 加载备选方案
        if alternatives_file.endswith('.xlsx') or alternatives_file.endswith('.xls'):
            alternatives = data_loader.load_alternatives_from_excel(alternatives_file)
        else:
            console.print("[red]目前只支持Excel格式的备选方案文件[/red]")
            return
        
        if not alternatives:
            console.print("[red]未找到有效的备选方案[/red]")
            return
        
        console.print(f"[cyan]加载了 {len(alternatives)} 个备选方案[/cyan]")
        
        # 加载或创建评估方案
        evaluator = SchemeEvaluator()
        if scheme_file:
            evaluation_scheme = data_loader.load_evaluation_scheme_from_json(scheme_file)
            if not evaluation_scheme:
                console.print("[red]加载评估方案失败，使用默认方案[/red]")
                evaluation_scheme = evaluator.create_default_evaluation_scheme()
        else:
            evaluation_scheme = evaluator.create_default_evaluation_scheme()
        
        console.print(f"[cyan]使用评估方案：{evaluation_scheme.name}[/cyan]")
        
        # 准备数据源（从备选方案属性中提取）
        data_sources = {}
        for alt in alternatives:
            data_sources[alt.id] = alt.attributes
        
        # 执行评估和推荐
        with Progress() as progress:
            eval_progress = progress.add_task("[cyan]评估方案中...", total=100)
            
            recommended_alt, recommended_eval, report = evaluator.recommend_best_scheme(
                alternatives, evaluation_scheme, data_sources, method
            )
            progress.update(eval_progress, completed=100)
        
        # 显示结果
        display_evaluation_results(recommended_alt, recommended_eval, report)
        
        # 保存结果
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            console.print(f"[green]评估结果已保存到：{output}[/green]")
        
    except Exception as e:
        console.print(f"[red]评估失败：{e}[/red]")
        logger.error(f"评估失败: {e}")


@cli.command()
@click.argument('alternatives_file')
@click.option('--scheme-file', help='评估方案文件')
@click.option('--output', '-o', help='输出文件路径')
def compare(alternatives_file, scheme_file, output):
    """比较多个方案"""
    console.print("[green]开始比较方案[/green]")
    
    try:
        # 加载数据
        data_loader = DataLoader()
        
        # 加载备选方案
        if alternatives_file.endswith('.xlsx') or alternatives_file.endswith('.xls'):
            alternatives = data_loader.load_alternatives_from_excel(alternatives_file)
        else:
            console.print("[red]目前只支持Excel格式的备选方案文件[/red]")
            return
        
        if len(alternatives) < 2:
            console.print("[red]至少需要2个备选方案进行比较[/red]")
            return
        
        # 加载或创建评估方案
        evaluator = SchemeEvaluator()
        if scheme_file:
            evaluation_scheme = data_loader.load_evaluation_scheme_from_json(scheme_file)
            if not evaluation_scheme:
                evaluation_scheme = evaluator.create_default_evaluation_scheme()
        else:
            evaluation_scheme = evaluator.create_default_evaluation_scheme()
        
        # 准备数据源
        data_sources = {}
        for alt in alternatives:
            data_sources[alt.id] = alt.attributes
        
        # 执行比较
        with Progress() as progress:
            compare_progress = progress.add_task("[cyan]比较方案中...", total=100)
            
            comparison_report = evaluator.compare_schemes(
                alternatives, evaluation_scheme, data_sources
            )
            progress.update(compare_progress, completed=100)
        
        # 显示比较结果
        display_comparison_results(comparison_report)
        
        # 保存结果
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(comparison_report, f, ensure_ascii=False, indent=2)
            console.print(f"[green]比较结果已保存到：{output}[/green]")
        
    except Exception as e:
        console.print(f"[red]比较失败：{e}[/red]")
        logger.error(f"比较失败: {e}")


@cli.command()
def create_sample():
    """创建示例数据文件"""
    console.print("[green]创建示例数据文件[/green]")
    
    try:
        data_loader = DataLoader()
        sample_data = data_loader.create_sample_data()
        
        # 保存任务层次结构
        task_file = "sample_task_hierarchy.json"
        data_loader.save_json(sample_data["task_hierarchy"], task_file)
        console.print(f"[cyan]任务层次结构示例：{task_file}[/cyan]")
        
        # 保存评估方案
        scheme_file = "sample_evaluation_scheme.json"
        data_loader.save_json(sample_data["evaluation_scheme"], scheme_file)
        console.print(f"[cyan]评估方案示例：{scheme_file}[/cyan]")
        
        # 保存备选方案到Excel
        import pandas as pd
        alternatives_data = []
        for alt in sample_data["alternatives"]:
            row = {"name": alt["name"], "description": alt["description"]}
            row.update(alt["attributes"])
            alternatives_data.append(row)
        
        df = pd.DataFrame(alternatives_data)
        excel_file = "sample_alternatives.xlsx"
        df.to_excel(excel_file, index=False)
        console.print(f"[cyan]备选方案示例：{excel_file}[/cyan]")
        
        console.print("[green]示例数据文件创建完成！[/green]")
        
    except Exception as e:
        console.print(f"[red]创建示例数据失败：{e}[/red]")


def display_task_hierarchy(hierarchy):
    """显示任务层次结构"""
    console.print("\n[bold]任务层次结构：[/bold]")
    
    root_task = hierarchy.get_task(hierarchy.root_task_id)
    if root_task:
        _display_task_tree(hierarchy, root_task, 0)


def _display_task_tree(hierarchy, task, indent_level):
    """递归显示任务树"""
    indent = "  " * indent_level
    console.print(f"{indent}├─ {task.name} ({task.task_type.value})")
    
    children = hierarchy.get_children(task.id)
    for child in children:
        _display_task_tree(hierarchy, child, indent_level + 1)


def display_evaluation_results(alternative, evaluation, report):
    """显示评估结果"""
    console.print(f"\n[bold green]推荐方案：{alternative.name}[/bold green]")
    console.print(f"描述：{alternative.description}")
    console.print(f"总分：{evaluation.total_score:.4f}")
    console.print(f"标准化得分：{evaluation.normalized_score:.4f}")
    
    # 显示指标得分表
    table = Table(title="详细指标得分")
    table.add_column("指标", style="cyan")
    table.add_column("原始值", style="magenta")
    table.add_column("标准化值", style="green")
    
    for indicator_value in evaluation.indicator_values.values():
        table.add_row(
            indicator_value.indicator_id,
            f"{indicator_value.raw_value:.4f}",
            f"{indicator_value.normalized_value:.4f}"
        )
    
    console.print(table)


def display_comparison_results(report):
    """显示比较结果"""
    console.print("\n[bold]方案比较结果：[/bold]")
    
    # 显示排序表
    table = Table(title="方案排序")
    table.add_column("排名", style="cyan")
    table.add_column("方案", style="magenta")
    table.add_column("总分", style="green")
    table.add_column("标准化得分", style="yellow")
    
    for ranking in report["ranking_by_total_score"]:
        table.add_row(
            str(ranking["rank"]),
            ranking["alternative_name"],
            f"{ranking['total_score']:.4f}",
            f"{ranking['normalized_score']:.4f}"
        )
    
    console.print(table)
    
    # 显示统计信息
    stats = report["statistical_analysis"]
    console.print(f"\n[bold]统计信息：[/bold]")
    console.print(f"平均得分：{stats['mean_score']:.4f}")
    console.print(f"标准差：{stats['std_score']:.4f}")
    console.print(f"得分范围：{stats['score_range']:.4f}")


if __name__ == '__main__':
    cli()
