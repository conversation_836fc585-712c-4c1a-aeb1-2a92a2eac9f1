# 人机功能分配模型系统 (HMDM)

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/tests-32%20passed-brightgreen.svg)](tests/)
[![Coverage](https://img.shields.io/badge/coverage-62%25-yellow.svg)](tests/)

Human-Machine Function Distribution Model - 基于多目标模糊决策的人机功能分配决策支持系统

## 🎯 项目简介

HMDM是一个专门用于支持联合指挥系统人机功能分配方案优选的决策支持系统。系统采用层次任务分析法和GOMS模型进行任务分解，结合加权相对偏差距离最小法的多目标模糊决策模型，为复杂的人机功能分配问题提供科学的决策支持。

## ✨ 主要功能

### 🔄 任务分解
- **层次任务分析法(HTA)**：支持使命任务→ZZ任务→典型功能→人机交互流程→操作序列→元操作的六层分解
- **GOMS模型**：基于目标-操作-方法-选择规则的认知建模
- **场景化分解**：支持态势分析、威胁计算、辅助决策等典型场景

### 🎯 多目标决策
- **加权相对偏差距离最小法(WRDM)**：主要决策算法
- **TOPSIS法**：理想解排序法
- **模糊层次分析法(Fuzzy AHP)**：处理不确定性决策
- **敏感性分析**：评估决策结果的稳定性

### 📊 方案评估
- **多指标评估**：支持负荷、效率、可靠性、可用性、安全性、成本、性能等7大类指标
- **模糊评价**：处理评估中的不确定性和主观性
- **综合排序**：基于量化评估的方案排序和推荐

### 💻 用户界面
- **Web界面**：直观的图形化操作界面
- **命令行工具**：支持批处理和自动化
- **API接口**：RESTful API和Python API

## 🛠️ 技术栈

- **核心语言**: Python 3.8+
- **Web框架**: Flask + Bootstrap
- **科学计算**: NumPy, SciPy, scikit-learn
- **数据处理**: Pandas, OpenPyXL
- **可视化**: Matplotlib, Seaborn, Plotly
- **模糊逻辑**: scikit-fuzzy
- **测试框架**: pytest
- **代码质量**: Black, Flake8, MyPy

## 项目结构

```
typhon/
├── src/                    # 源代码目录
│   ├── hmdm/              # 核心模块
│   │   ├── __init__.py
│   │   ├── models/        # 数据模型
│   │   ├── task_analysis/ # 任务分解模块
│   │   ├── decision/      # 多目标决策模块
│   │   ├── evaluation/    # 方案评估模块
│   │   └── utils/         # 工具函数
│   ├── web/               # Web界面
│   └── cli/               # 命令行界面
├── tests/                 # 测试代码
├── docs/                  # 文档
├── examples/              # 示例代码
├── data/                  # 示例数据
├── requirements.txt       # 依赖包
├── setup.py              # 安装配置
└── README.md             # 项目说明
```

## 快速开始

### 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 运行示例

```bash
# 命令行模式
python -m src.cli.main --help

# Web界面模式
python -m src.web.app
```

## 开发指南

详细的开发文档请参考 `docs/` 目录。

## 许可证

本项目采用 MIT 许可证。
