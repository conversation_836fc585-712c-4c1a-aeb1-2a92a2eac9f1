# 核心依赖
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 数学计算和优化
scikit-learn>=1.0.0
scikit-fuzzy>=0.4.2

# 数据可视化
matplotlib>=3.4.0
plotly>=5.0.0
seaborn>=0.11.0

# Web框架
flask>=2.0.0
flask-cors>=3.0.0

# 命令行界面
click>=8.0.0
rich>=10.0.0

# 数据序列化
pydantic>=1.8.0
pyyaml>=5.4.0
openpyxl>=3.0.0

# HTTP客户端
requests>=2.25.0

# 测试框架
pytest>=6.2.0
pytest-cov>=2.12.0
pytest-mock>=3.6.0

# 代码质量
black>=21.0.0
flake8>=3.9.0
mypy>=0.910

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 开发工具
jupyter>=1.0.0
ipython>=7.25.0
