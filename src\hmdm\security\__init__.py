"""
HMDM军事安全模块

提供军用级安全机制，包括：
- 用户认证和授权
- 访问控制
- 数据加密
- 审计日志
- 会话管理
- 增强安全管理
- Web安全中间件
- 安全配置管理
"""

from .military_security import (
    MilitarySecurityManager,
    SecurityLevel,
    UserRole,
    OperationType,
    SecurityCredential,
    AuditLog
)

from .enhanced_security import (
    EnhancedSecurityManager,
    User,
    AuditEvent,
    AuthenticationMethod,
    PermissionType,
    AuditEventType
)

from .web_security_middleware import WebSecurityMiddleware

from .security_config import (
    SecurityConfig,
    SecurityConfigManager,
    security_config_manager
)

__all__ = [
    # 基础军事安全
    'MilitarySecurityManager',
    'SecurityLevel',
    'UserRole',
    'OperationType',
    'SecurityCredential',
    'AuditLog',

    # 增强安全管理
    'EnhancedSecurityManager',
    'User',
    'AuditEvent',
    'AuthenticationMethod',
    'PermissionType',
    'AuditEventType',

    # Web安全中间件
    'WebSecurityMiddleware',

    # 安全配置管理
    'SecurityConfig',
    'SecurityConfigManager',
    'security_config_manager'
]
