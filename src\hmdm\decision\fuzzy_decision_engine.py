"""
模糊决策引擎

实现加权相对偏差距离最小法的多目标模糊决策模型
"""

from typing import List, Dict, Optional, Any, Tuple
import numpy as np
from scipy.spatial.distance import euclidean
from scipy.optimize import minimize
import logging

from ..models.decision_models import (
    DecisionMatrix, DecisionResult, DecisionMethod, Alternative, FuzzyNumber
)
from ..models.evaluation_models import EvaluationScheme


class FuzzyDecisionEngine:
    """模糊决策引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def weighted_relative_deviation_method(self, matrix: DecisionMatrix) -> DecisionResult:
        """
        加权相对偏差距离最小法
        
        步骤：
        1. 建立决策论域
        2. 确定因素指标集合
        3. 确定因素指标值矩阵
        4. 计算模糊关系矩阵
        5. 确定各因素指标的权值向量
        6. 根据目标函数决策结果
        """
        if not matrix.alternatives or not matrix.evaluation_scheme:
            raise ValueError("决策矩阵或评估方案为空")
        
        # 1. 建立决策论域和因素指标集合（已在DecisionMatrix中完成）
        
        # 2. 构建因素指标值矩阵
        decision_matrix = matrix.build_matrix()
        if decision_matrix.size == 0:
            raise ValueError("无法构建决策矩阵")
        
        # 3. 标准化矩阵
        normalized_matrix = matrix.normalize_matrix(method="vector")
        
        # 4. 计算模糊关系矩阵
        fuzzy_relation_matrix = self._calculate_fuzzy_relation_matrix(normalized_matrix)
        
        # 5. 应用权重
        weighted_matrix = matrix.apply_weights()
        
        # 6. 计算理想解
        positive_ideal, negative_ideal = matrix.calculate_ideal_solutions()
        
        # 7. 计算相对偏差距离
        scores = self._calculate_relative_deviation_distances(
            weighted_matrix, positive_ideal, negative_ideal, matrix.evaluation_scheme
        )
        
        # 8. 生成决策结果
        result = DecisionResult(
            matrix_id=matrix.id,
            method=DecisionMethod.WEIGHTED_RELATIVE_DEVIATION
        )
        
        # 设置排序结果
        rankings = [(alt_id, scores[i]) for i, alt_id in enumerate(matrix.alternative_ids)]
        result.set_rankings(rankings)
        
        # 计算距离信息
        distances = {}
        for i, alt_id in enumerate(matrix.alternative_ids):
            pos_dist = euclidean(weighted_matrix[i], positive_ideal)
            neg_dist = euclidean(weighted_matrix[i], negative_ideal)
            distances[alt_id] = {"positive": pos_dist, "negative": neg_dist}
        
        result.distances = distances
        result.parameters = {
            "normalization_method": "vector",
            "fuzzy_relation_calculated": True,
            "ideal_solutions_calculated": True
        }
        
        return result
    
    def _calculate_fuzzy_relation_matrix(self, matrix: np.ndarray) -> np.ndarray:
        """计算模糊关系矩阵"""
        m, n = matrix.shape
        fuzzy_matrix = np.zeros((m, m))  # 方案间的模糊关系矩阵
        
        for i in range(m):
            for j in range(m):
                if i == j:
                    fuzzy_matrix[i, j] = 1.0
                else:
                    # 计算方案i和方案j的相似度
                    similarity = self._calculate_similarity(matrix[i], matrix[j])
                    fuzzy_matrix[i, j] = similarity
        
        return fuzzy_matrix
    
    def _calculate_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """计算两个向量的相似度"""
        # 使用余弦相似度
        dot_product = np.dot(vector1, vector2)
        norm1 = np.linalg.norm(vector1)
        norm2 = np.linalg.norm(vector2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        similarity = dot_product / (norm1 * norm2)
        return max(0.0, similarity)  # 确保非负
    
    def _calculate_relative_deviation_distances(self, 
                                              weighted_matrix: np.ndarray,
                                              positive_ideal: np.ndarray,
                                              negative_ideal: np.ndarray,
                                              scheme: EvaluationScheme) -> np.ndarray:
        """计算加权相对偏差距离"""
        m, n = weighted_matrix.shape
        scores = np.zeros(m)
        
        for i in range(m):
            # 计算到正理想解的相对偏差
            positive_deviations = []
            negative_deviations = []
            
            for j in range(n):
                indicator_id = list(scheme.indicators.keys())[j]
                weight = scheme.weights[indicator_id].weight
                
                # 相对偏差计算
                if positive_ideal[j] != 0:
                    pos_deviation = abs(weighted_matrix[i, j] - positive_ideal[j]) / positive_ideal[j]
                else:
                    pos_deviation = abs(weighted_matrix[i, j] - positive_ideal[j])
                
                if negative_ideal[j] != 0:
                    neg_deviation = abs(weighted_matrix[i, j] - negative_ideal[j]) / abs(negative_ideal[j])
                else:
                    neg_deviation = abs(weighted_matrix[i, j] - negative_ideal[j])
                
                positive_deviations.append(weight * pos_deviation)
                negative_deviations.append(weight * neg_deviation)
            
            # 计算综合偏差距离
            pos_distance = np.sqrt(np.sum(np.array(positive_deviations) ** 2))
            neg_distance = np.sqrt(np.sum(np.array(negative_deviations) ** 2))
            
            # 相对接近度（越大越好）
            if pos_distance + neg_distance != 0:
                scores[i] = neg_distance / (pos_distance + neg_distance)
            else:
                scores[i] = 0.5
        
        return scores
    
    def topsis_method(self, matrix: DecisionMatrix) -> DecisionResult:
        """TOPSIS方法"""
        if not matrix.alternatives or not matrix.evaluation_scheme:
            raise ValueError("决策矩阵或评估方案为空")
        
        # 构建和标准化矩阵
        decision_matrix = matrix.build_matrix()
        normalized_matrix = matrix.normalize_matrix(method="vector")
        weighted_matrix = matrix.apply_weights()
        
        # 计算理想解
        positive_ideal, negative_ideal = matrix.calculate_ideal_solutions()
        
        # 计算距离
        m = weighted_matrix.shape[0]
        pos_distances = np.zeros(m)
        neg_distances = np.zeros(m)
        
        for i in range(m):
            pos_distances[i] = euclidean(weighted_matrix[i], positive_ideal)
            neg_distances[i] = euclidean(weighted_matrix[i], negative_ideal)
        
        # 计算相对接近度
        scores = neg_distances / (pos_distances + neg_distances)
        
        # 生成结果
        result = DecisionResult(
            matrix_id=matrix.id,
            method=DecisionMethod.TOPSIS
        )
        
        rankings = [(alt_id, scores[i]) for i, alt_id in enumerate(matrix.alternative_ids)]
        result.set_rankings(rankings)
        
        # 设置距离信息
        distances = {}
        for i, alt_id in enumerate(matrix.alternative_ids):
            distances[alt_id] = {
                "positive": pos_distances[i],
                "negative": neg_distances[i]
            }
        result.distances = distances
        
        return result
    
    def fuzzy_ahp_method(self, matrix: DecisionMatrix) -> DecisionResult:
        """模糊层次分析法"""
        # 简化实现，实际应用中需要更复杂的模糊AHP算法
        if not matrix.alternatives or not matrix.evaluation_scheme:
            raise ValueError("决策矩阵或评估方案为空")

        # 先构建决策矩阵以初始化indicator_ids
        decision_matrix = matrix.build_matrix()

        # 构建模糊判断矩阵
        fuzzy_matrix = self._build_fuzzy_judgment_matrix(matrix)

        # 计算模糊权重
        fuzzy_weights = self._calculate_fuzzy_weights(fuzzy_matrix)

        # 如果没有计算出权重，使用均等权重
        if not fuzzy_weights:
            n = len(matrix.indicator_ids)
            equal_weight = 1.0 / n if n > 0 else 0.0
            crisp_weights = [equal_weight] * n
            fuzzy_weights = [FuzzyNumber(parameters=[equal_weight, equal_weight, equal_weight]) for _ in range(n)]
        else:
            # 去模糊化得到精确权重
            crisp_weights = [fw.defuzzify() for fw in fuzzy_weights]

        # 标准化矩阵并计算方案得分
        normalized_matrix = matrix.normalize_matrix(method="linear")

        scores = np.dot(normalized_matrix, crisp_weights)

        # 生成结果
        result = DecisionResult(
            matrix_id=matrix.id,
            method=DecisionMethod.FUZZY_AHP
        )

        rankings = [(alt_id, scores[i]) for i, alt_id in enumerate(matrix.alternative_ids)]
        result.set_rankings(rankings)

        result.parameters = {
            "fuzzy_weights": [fw.to_dict() for fw in fuzzy_weights],
            "crisp_weights": crisp_weights
        }

        return result
    
    def _build_fuzzy_judgment_matrix(self, matrix: DecisionMatrix) -> List[List[FuzzyNumber]]:
        """构建模糊判断矩阵"""
        n = len(matrix.indicator_ids)
        if n == 0:
            return []

        fuzzy_matrix = []

        for i in range(n):
            row = []
            for j in range(n):
                if i == j:
                    # 对角线元素为(1,1,1)
                    row.append(FuzzyNumber(parameters=[1.0, 1.0, 1.0]))
                else:
                    # 简化处理：基于指标重要性生成模糊判断值
                    indicator_i = list(matrix.evaluation_scheme.indicators.values())[i]
                    indicator_j = list(matrix.evaluation_scheme.indicators.values())[j]

                    # 基于指标类型的简单判断逻辑
                    if indicator_i.indicator_type == indicator_j.indicator_type:
                        # 同类型指标，重要性相近
                        row.append(FuzzyNumber(parameters=[0.8, 1.0, 1.2]))
                    else:
                        # 不同类型指标，根据预设重要性
                        row.append(FuzzyNumber(parameters=[1.5, 2.0, 2.5]))

            fuzzy_matrix.append(row)

        return fuzzy_matrix
    
    def _calculate_fuzzy_weights(self, fuzzy_matrix: List[List[FuzzyNumber]]) -> List[FuzzyNumber]:
        """计算模糊权重"""
        n = len(fuzzy_matrix)
        if n == 0:
            return []

        fuzzy_weights = []

        for i in range(n):
            # 计算第i行的几何平均
            product_params = [1.0, 1.0, 1.0]

            for j in range(n):
                fuzzy_val = fuzzy_matrix[i][j]
                for k in range(3):
                    product_params[k] *= fuzzy_val.parameters[k]

            # 开n次方
            geometric_mean = FuzzyNumber(parameters=[
                product_params[0] ** (1/n),
                product_params[1] ** (1/n),
                product_params[2] ** (1/n)
            ])

            fuzzy_weights.append(geometric_mean)

        # 归一化（简化处理）
        total_center = sum(fw.center for fw in fuzzy_weights)
        if total_center > 0:
            for fw in fuzzy_weights:
                for k in range(3):
                    fw.parameters[k] /= total_center

        return fuzzy_weights
    
    def sensitivity_analysis(self, matrix: DecisionMatrix, result: DecisionResult) -> Dict[str, Any]:
        """敏感性分析"""
        if not matrix.evaluation_scheme:
            return {}
        
        sensitivity_results = {}
        original_weights = {}
        
        # 保存原始权重
        for indicator_id, weight_obj in matrix.evaluation_scheme.weights.items():
            original_weights[indicator_id] = weight_obj.weight
        
        # 对每个指标进行权重扰动分析
        for indicator_id in matrix.evaluation_scheme.weights.keys():
            weight_variations = []
            score_variations = []
            
            # 权重变化范围：±20%
            for delta in [-0.2, -0.1, 0.0, 0.1, 0.2]:
                # 调整权重
                new_weight = max(0.0, min(1.0, original_weights[indicator_id] * (1 + delta)))
                matrix.evaluation_scheme.weights[indicator_id].weight = new_weight
                
                # 重新计算
                temp_result = self.weighted_relative_deviation_method(matrix)
                
                # 记录结果
                weight_variations.append(new_weight)
                if temp_result.recommended_alternative_id in temp_result.scores:
                    score_variations.append(temp_result.scores[temp_result.recommended_alternative_id])
                else:
                    score_variations.append(0.0)
            
            # 恢复原始权重
            matrix.evaluation_scheme.weights[indicator_id].weight = original_weights[indicator_id]
            
            # 计算敏感性指标
            score_range = max(score_variations) - min(score_variations)
            weight_range = max(weight_variations) - min(weight_variations)
            
            sensitivity = score_range / weight_range if weight_range > 0 else 0.0
            
            sensitivity_results[indicator_id] = {
                "sensitivity_coefficient": sensitivity,
                "weight_variations": weight_variations,
                "score_variations": score_variations,
                "score_range": score_range
            }
        
        return sensitivity_results
    
    def compare_methods(self, matrix: DecisionMatrix) -> Dict[str, DecisionResult]:
        """比较不同决策方法的结果"""
        results = {}
        
        try:
            results["WRDM"] = self.weighted_relative_deviation_method(matrix)
        except Exception as e:
            self.logger.error(f"WRDM方法执行失败: {e}")
        
        try:
            results["TOPSIS"] = self.topsis_method(matrix)
        except Exception as e:
            self.logger.error(f"TOPSIS方法执行失败: {e}")
        
        try:
            results["Fuzzy_AHP"] = self.fuzzy_ahp_method(matrix)
        except Exception as e:
            self.logger.error(f"模糊AHP方法执行失败: {e}")
        
        return results
