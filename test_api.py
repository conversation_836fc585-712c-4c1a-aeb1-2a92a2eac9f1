#!/usr/bin/env python3
"""
HMDM API测试工具

用于测试HMDM系统的各种API接口
"""

import requests
import json
import sys
from datetime import datetime


class HMDMAPITester:
    """HMDM API测试器"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'HMDM-API-Tester/1.0'
        })
    
    def test_system_status(self):
        """测试系统状态API"""
        print("测试系统状态API...")
        try:
            response = self.session.get(f"{self.base_url}/api/system/status")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_allocation_config(self):
        """测试人机分配配置API"""
        print("\n测试人机分配配置API...")
        try:
            # 获取配置
            response = self.session.get(f"{self.base_url}/api/allocation/config")
            print(f"获取配置 - 状态码: {response.status_code}")
            if response.status_code == 200:
                config = response.json()
                print(f"当前配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
                
                # 更新配置
                new_config = {
                    "allocation_mode": "automatic",
                    "optimization_objective": "efficiency",
                    "default_scheme_count": 8,
                    "decision_threshold": 0.15
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/allocation/config",
                    json=new_config
                )
                print(f"更新配置 - 状态码: {response.status_code}")
                print(f"更新响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
                
                return response.status_code == 200
            return False
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_allocation_system_status(self):
        """测试人机分配系统状态API"""
        print("\n测试人机分配系统状态API...")
        try:
            response = self.session.get(f"{self.base_url}/api/allocation/system/status")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_task_validation(self):
        """测试任务验证API"""
        print("\n测试任务验证API...")
        try:
            # 创建测试任务数据
            task_data = {
                "tasks": {
                    "task_1": {
                        "name": "主任务",
                        "description": "测试主任务",
                        "task_type": "MISSION_TASK",
                        "executor_type": "HUMAN_MACHINE",
                        "parent_id": None,
                        "level": 0,
                        "attributes": {
                            "complexity": 0.7,
                            "importance": 0.9,
                            "urgency": 0.6,
                            "real_time_requirement": True
                        }
                    },
                    "task_2": {
                        "name": "子任务1",
                        "description": "测试子任务",
                        "task_type": "ZZ_TASK",
                        "executor_type": "HUMAN",
                        "parent_id": "task_1",
                        "level": 1,
                        "attributes": {
                            "complexity": 0.5,
                            "importance": 0.7,
                            "urgency": 0.8,
                            "real_time_requirement": False
                        }
                    }
                },
                "rootTaskId": "task_1"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/allocation/tasks/validate",
                json=task_data
            )
            print(f"状态码: {response.status_code}")
            print(f"验证结果: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_config_validation(self):
        """测试配置验证API"""
        print("\n测试配置验证API...")
        try:
            # 测试有效配置
            valid_config = {
                "allocation_mode": "semi_automatic",
                "optimization_objective": "balanced",
                "default_scheme_count": 5,
                "decision_threshold": 0.1,
                "confidence_threshold": 0.7,
                "capability_weights": {
                    "cognitive": 0.25,
                    "physical": 0.15,
                    "perceptual": 0.20,
                    "decision_making": 0.25,
                    "execution": 0.10,
                    "coordination": 0.05
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/allocation/config/validate",
                json=valid_config
            )
            print(f"有效配置验证 - 状态码: {response.status_code}")
            print(f"验证结果: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试无效配置
            invalid_config = {
                "allocation_mode": "invalid_mode",
                "decision_threshold": 1.5,  # 超出范围
                "capability_weights": {
                    "cognitive": 0.5,
                    "physical": 0.3
                    # 权重总和不为1
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/allocation/config/validate",
                json=invalid_config
            )
            print(f"无效配置验证 - 状态码: {response.status_code}")
            print(f"验证结果: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            return True
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_capability_analysis(self):
        """测试能力分析API"""
        print("\n测试能力分析API...")
        try:
            analysis_data = {
                "task": {
                    "name": "目标识别",
                    "complexity": 0.7,
                    "real_time_requirement": True,
                    "cognitive_load": 0.8,
                    "physical_demand": 0.3
                },
                "context": {
                    "environment": "combat",
                    "time_pressure": "high",
                    "available_resources": ["radar", "camera", "human_operator"]
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/allocation/capabilities/analyze",
                json=analysis_data
            )
            print(f"状态码: {response.status_code}")
            print(f"分析结果: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def test_logs_api(self):
        """测试日志API"""
        print("\n测试日志API...")
        try:
            response = self.session.get(f"{self.base_url}/api/logs")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                logs = response.json()
                print(f"日志条目数: {len(logs.get('data', []))}")
                # 只显示前3条日志
                for i, log_entry in enumerate(logs.get('data', [])[:3]):
                    print(f"日志 {i+1}: {log_entry}")
            return response.status_code == 200
        except Exception as e:
            print(f"错误: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print(f"开始HMDM API测试 - {datetime.now()}")
        print(f"目标服务器: {self.base_url}")
        print("=" * 60)
        
        tests = [
            ("系统状态", self.test_system_status),
            ("人机分配配置", self.test_allocation_config),
            ("人机分配系统状态", self.test_allocation_system_status),
            ("任务验证", self.test_task_validation),
            ("配置验证", self.test_config_validation),
            ("能力分析", self.test_capability_analysis),
            ("日志接口", self.test_logs_api)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                print(f"✓ {test_name}: {'通过' if result else '失败'}")
            except Exception as e:
                results.append((test_name, False))
                print(f"✗ {test_name}: 异常 - {e}")
        
        print("\n" + "=" * 60)
        print("测试总结:")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        print(f"通过: {passed}/{total}")
        
        for test_name, result in results:
            status = "✓" if result else "✗"
            print(f"  {status} {test_name}")
        
        return passed == total


def main():
    """主函数"""
    base_url = "http://127.0.0.1:5000"
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    tester = HMDMAPITester(base_url)
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
