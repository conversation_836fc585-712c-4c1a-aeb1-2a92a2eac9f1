"""
性能监控系统

实时监控系统性能指标，包括：
- CPU和内存使用率
- 响应时间和吞吐量
- 数据库性能
- 缓存命中率
- 错误率和异常统计
"""

import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
from enum import Enum
import json
import os
from pathlib import Path

from ..security.military_security import SecurityLevel


class MetricType(Enum):
    """指标类型"""
    COUNTER = "计数器"
    GAUGE = "仪表盘"
    HISTOGRAM = "直方图"
    TIMER = "计时器"


class AlertLevel(Enum):
    """告警级别"""
    INFO = "信息"
    WARNING = "警告"
    ERROR = "错误"
    CRITICAL = "严重"


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'value': self.value,
            'type': self.metric_type.value,
            'timestamp': self.timestamp.isoformat(),
            'tags': self.tags,
            'description': self.description
        }


@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    metric_name: str
    level: AlertLevel
    message: str
    threshold: float
    current_value: float
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'alert_id': self.alert_id,
            'metric_name': self.metric_name,
            'level': self.level.value,
            'message': self.message,
            'threshold': self.threshold,
            'current_value': self.current_value,
            'timestamp': self.timestamp.isoformat(),
            'resolved': self.resolved
        }


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # 监控配置
        self.monitoring_enabled = self.config.get('monitoring_enabled', True)
        self.collection_interval = self.config.get('collection_interval', 10)  # 秒
        self.retention_period = self.config.get('retention_period', 86400)  # 24小时
        self.max_metrics_count = self.config.get('max_metrics_count', 10000)
        
        # 数据存储
        self.metrics_history: deque = deque(maxlen=self.max_metrics_count)
        self.current_metrics: Dict[str, PerformanceMetric] = {}
        self.alerts: List[Alert] = []
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        
        # 性能统计
        self.request_times: deque = deque(maxlen=1000)
        self.error_counts: defaultdict = defaultdict(int)
        self.cache_stats: Dict[str, int] = {'hits': 0, 'misses': 0}
        
        # 监控线程
        self.monitoring_thread: Optional[threading.Thread] = None
        self.stop_monitoring = threading.Event()
        
        # 初始化告警规则
        self._init_alert_rules()
        
        # 启动监控
        if self.monitoring_enabled:
            self.start_monitoring()
        
        self.logger.info("性能监控器初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            return
        
        self.stop_monitoring.clear()
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring_service(self):
        """停止监控"""
        self.stop_monitoring.set()
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.logger.info("性能监控已停止")
    
    def record_metric(self, name: str, value: float, metric_type: MetricType = MetricType.GAUGE,
                     tags: Dict[str, str] = None, description: str = ""):
        """记录性能指标"""
        try:
            metric = PerformanceMetric(
                name=name,
                value=value,
                metric_type=metric_type,
                tags=tags or {},
                description=description
            )
            
            # 更新当前指标
            self.current_metrics[name] = metric
            
            # 添加到历史记录
            self.metrics_history.append(metric)
            
            # 检查告警规则
            self._check_alert_rules(metric)
            
        except Exception as e:
            self.logger.error(f"记录性能指标失败: {e}")
    
    def record_request_time(self, duration: float, endpoint: str = ""):
        """记录请求时间"""
        self.request_times.append({
            'duration': duration,
            'endpoint': endpoint,
            'timestamp': datetime.now()
        })
        
        # 记录响应时间指标
        self.record_metric(
            name="response_time",
            value=duration,
            metric_type=MetricType.TIMER,
            tags={'endpoint': endpoint},
            description="请求响应时间"
        )
    
    def record_error(self, error_type: str, endpoint: str = ""):
        """记录错误"""
        self.error_counts[f"{error_type}:{endpoint}"] += 1
        
        # 记录错误计数指标
        self.record_metric(
            name="error_count",
            value=self.error_counts[f"{error_type}:{endpoint}"],
            metric_type=MetricType.COUNTER,
            tags={'error_type': error_type, 'endpoint': endpoint},
            description="错误计数"
        )
    
    def record_cache_hit(self, cache_name: str = "default"):
        """记录缓存命中"""
        self.cache_stats['hits'] += 1
        hit_rate = self.cache_stats['hits'] / (self.cache_stats['hits'] + self.cache_stats['misses'])
        
        self.record_metric(
            name="cache_hit_rate",
            value=hit_rate,
            metric_type=MetricType.GAUGE,
            tags={'cache': cache_name},
            description="缓存命中率"
        )
    
    def record_cache_miss(self, cache_name: str = "default"):
        """记录缓存未命中"""
        self.cache_stats['misses'] += 1
        hit_rate = self.cache_stats['hits'] / (self.cache_stats['hits'] + self.cache_stats['misses'])
        
        self.record_metric(
            name="cache_hit_rate",
            value=hit_rate,
            metric_type=MetricType.GAUGE,
            tags={'cache': cache_name},
            description="缓存命中率"
        )
    
    def get_current_metrics(self) -> Dict[str, PerformanceMetric]:
        """获取当前指标"""
        return self.current_metrics.copy()
    
    def get_metrics_history(self, metric_name: Optional[str] = None, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> List[PerformanceMetric]:
        """获取指标历史"""
        try:
            metrics = list(self.metrics_history)
            
            # 按指标名过滤
            if metric_name:
                metrics = [m for m in metrics if m.name == metric_name]
            
            # 按时间范围过滤
            if start_time:
                metrics = [m for m in metrics if m.timestamp >= start_time]
            if end_time:
                metrics = [m for m in metrics if m.timestamp <= end_time]
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"获取指标历史失败: {e}")
            return []
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            now = datetime.now()
            last_hour = now - timedelta(hours=1)
            
            # 系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 请求统计
            recent_requests = [r for r in self.request_times 
                             if r['timestamp'] >= last_hour]
            
            avg_response_time = 0
            if recent_requests:
                avg_response_time = sum(r['duration'] for r in recent_requests) / len(recent_requests)
            
            # 错误统计
            total_errors = sum(self.error_counts.values())
            
            # 缓存统计
            cache_hit_rate = 0
            if self.cache_stats['hits'] + self.cache_stats['misses'] > 0:
                cache_hit_rate = self.cache_stats['hits'] / (self.cache_stats['hits'] + self.cache_stats['misses'])
            
            return {
                'timestamp': now.isoformat(),
                'system_resources': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'memory_total_gb': memory.total / (1024**3),
                    'disk_percent': disk.percent,
                    'disk_used_gb': disk.used / (1024**3),
                    'disk_total_gb': disk.total / (1024**3)
                },
                'performance_metrics': {
                    'requests_last_hour': len(recent_requests),
                    'avg_response_time_ms': avg_response_time * 1000,
                    'total_errors': total_errors,
                    'cache_hit_rate': cache_hit_rate,
                    'active_alerts': len([a for a in self.alerts if not a.resolved])
                },
                'top_metrics': [
                    metric.to_dict() for metric in 
                    sorted(self.current_metrics.values(), 
                          key=lambda x: x.timestamp, reverse=True)[:10]
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取性能摘要失败: {e}")
            return {}
    
    def add_alert_rule(self, metric_name: str, threshold: float, 
                      level: AlertLevel = AlertLevel.WARNING,
                      operator: str = "gt", message: str = ""):
        """添加告警规则"""
        self.alert_rules[metric_name] = {
            'threshold': threshold,
            'level': level,
            'operator': operator,  # gt, lt, eq, gte, lte
            'message': message or f"{metric_name} 超过阈值"
        }
    
    def get_alerts(self, resolved: Optional[bool] = None) -> List[Alert]:
        """获取告警"""
        alerts = self.alerts.copy()
        
        if resolved is not None:
            alerts = [a for a in alerts if a.resolved == resolved]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        try:
            for alert in self.alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    self.logger.info(f"告警已解决: {alert_id}")
                    return True
            return False
            
        except Exception as e:
            self.logger.error(f"解决告警失败: {e}")
            return False

    def _monitoring_loop(self):
        """监控循环"""
        while not self.stop_monitoring.is_set():
            try:
                # 收集系统指标
                self._collect_system_metrics()

                # 清理过期数据
                self._cleanup_old_data()

                # 等待下次收集
                self.stop_monitoring.wait(self.collection_interval)

            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(self.collection_interval)

    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.record_metric("cpu_usage", cpu_percent, MetricType.GAUGE,
                             description="CPU使用率")

            # 内存使用情况
            memory = psutil.virtual_memory()
            self.record_metric("memory_usage", memory.percent, MetricType.GAUGE,
                             description="内存使用率")
            self.record_metric("memory_used_bytes", memory.used, MetricType.GAUGE,
                             description="已使用内存字节数")

            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            self.record_metric("disk_usage", disk.percent, MetricType.GAUGE,
                             description="磁盘使用率")

            # 网络IO
            net_io = psutil.net_io_counters()
            self.record_metric("network_bytes_sent", net_io.bytes_sent, MetricType.COUNTER,
                             description="网络发送字节数")
            self.record_metric("network_bytes_recv", net_io.bytes_recv, MetricType.COUNTER,
                             description="网络接收字节数")

            # 进程数
            process_count = len(psutil.pids())
            self.record_metric("process_count", process_count, MetricType.GAUGE,
                             description="进程数量")

        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")

    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(seconds=self.retention_period)

            # 清理过期的告警
            self.alerts = [alert for alert in self.alerts
                          if alert.timestamp >= cutoff_time or not alert.resolved]

            # 清理过期的请求时间记录
            self.request_times = deque([
                req for req in self.request_times
                if req['timestamp'] >= cutoff_time
            ], maxlen=1000)

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")

    def _check_alert_rules(self, metric: PerformanceMetric):
        """检查告警规则"""
        try:
            if metric.name not in self.alert_rules:
                return

            rule = self.alert_rules[metric.name]
            threshold = rule['threshold']
            operator = rule['operator']
            level = rule['level']
            message = rule['message']

            # 检查阈值条件
            triggered = False
            if operator == 'gt' and metric.value > threshold:
                triggered = True
            elif operator == 'lt' and metric.value < threshold:
                triggered = True
            elif operator == 'gte' and metric.value >= threshold:
                triggered = True
            elif operator == 'lte' and metric.value <= threshold:
                triggered = True
            elif operator == 'eq' and metric.value == threshold:
                triggered = True

            if triggered:
                # 生成告警
                alert_id = f"{metric.name}_{int(time.time())}"
                alert = Alert(
                    alert_id=alert_id,
                    metric_name=metric.name,
                    level=level,
                    message=message,
                    threshold=threshold,
                    current_value=metric.value
                )

                self.alerts.append(alert)
                self.logger.warning(f"触发告警: {message}, 当前值: {metric.value}, 阈值: {threshold}")

        except Exception as e:
            self.logger.error(f"检查告警规则失败: {e}")

    def _init_alert_rules(self):
        """初始化默认告警规则"""
        # CPU使用率告警
        self.add_alert_rule("cpu_usage", 80.0, AlertLevel.WARNING, "gt", "CPU使用率过高")
        self.add_alert_rule("cpu_usage", 95.0, AlertLevel.CRITICAL, "gt", "CPU使用率严重过高")

        # 内存使用率告警
        self.add_alert_rule("memory_usage", 85.0, AlertLevel.WARNING, "gt", "内存使用率过高")
        self.add_alert_rule("memory_usage", 95.0, AlertLevel.CRITICAL, "gt", "内存使用率严重过高")

        # 磁盘使用率告警
        self.add_alert_rule("disk_usage", 90.0, AlertLevel.WARNING, "gt", "磁盘使用率过高")
        self.add_alert_rule("disk_usage", 98.0, AlertLevel.CRITICAL, "gt", "磁盘使用率严重过高")

        # 响应时间告警
        self.add_alert_rule("response_time", 2.0, AlertLevel.WARNING, "gt", "响应时间过长")
        self.add_alert_rule("response_time", 5.0, AlertLevel.ERROR, "gt", "响应时间严重过长")

        # 缓存命中率告警
        self.add_alert_rule("cache_hit_rate", 0.5, AlertLevel.WARNING, "lt", "缓存命中率过低")

    def export_metrics(self, file_path: str, format: str = "json") -> bool:
        """导出指标数据"""
        try:
            metrics_data = {
                'export_time': datetime.now().isoformat(),
                'current_metrics': {name: metric.to_dict()
                                  for name, metric in self.current_metrics.items()},
                'alerts': [alert.to_dict() for alert in self.alerts],
                'performance_summary': self.get_performance_summary()
            }

            if format.lower() == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(metrics_data, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的导出格式: {format}")

            self.logger.info(f"指标数据已导出到: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出指标数据失败: {e}")
            return False

    def get_metric_statistics(self, metric_name: str,
                            time_range: timedelta = timedelta(hours=1)) -> Dict[str, float]:
        """获取指标统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - time_range

            metrics = self.get_metrics_history(metric_name, start_time, end_time)

            if not metrics:
                return {}

            values = [m.value for m in metrics]

            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else 0
            }

        except Exception as e:
            self.logger.error(f"获取指标统计失败: {e}")
            return {}


def performance_timer(monitor: PerformanceMonitor, metric_name: str = "function_duration"):
    """性能计时装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                monitor.record_metric(
                    name=metric_name,
                    value=duration,
                    metric_type=MetricType.TIMER,
                    tags={'function': func.__name__},
                    description=f"函数 {func.__name__} 执行时间"
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                monitor.record_error("function_error", func.__name__)
                monitor.record_metric(
                    name=metric_name,
                    value=duration,
                    metric_type=MetricType.TIMER,
                    tags={'function': func.__name__, 'error': str(e)},
                    description=f"函数 {func.__name__} 执行时间（异常）"
                )
                raise
        return wrapper
    return decorator
