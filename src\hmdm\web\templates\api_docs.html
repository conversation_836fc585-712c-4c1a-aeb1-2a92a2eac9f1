{% extends "base.html" %}

{% block title %}API文档 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-code"></i>
                    API接口文档
                </h1>
                <p class="page-subtitle">HMDM系统RESTful API接口说明</p>
            </div>
        </div>
    </div>

    <!-- API概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        API概览
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary mb-1">15+</h4>
                                <small class="text-muted">API接口</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info mb-1">4</h4>
                                <small class="text-muted">功能模块</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning mb-1">JSON</h4>
                                <small class="text-muted">数据格式</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success mb-1">REST</h4>
                                <small class="text-muted">架构风格</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API分类 -->
    <div class="row">
        <div class="col-lg-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">API分类</h6>
                </div>
                <div class="card-body">
                    <div class="nav flex-column nav-pills" id="api-tabs" role="tablist">
                        <a class="nav-link active" id="system-tab" data-bs-toggle="pill" href="#system" role="tab">
                            <i class="fas fa-cogs"></i> 系统管理
                        </a>
                        <a class="nav-link" id="allocation-tab" data-bs-toggle="pill" href="#allocation" role="tab">
                            <i class="fas fa-users-cog"></i> 人机分配
                        </a>
                        <a class="nav-link" id="config-tab" data-bs-toggle="pill" href="#config" role="tab">
                            <i class="fas fa-sliders-h"></i> 配置管理
                        </a>
                        <a class="nav-link" id="data-tab" data-bs-toggle="pill" href="#data" role="tab">
                            <i class="fas fa-database"></i> 数据接口
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-9">
            <div class="tab-content" id="api-tabContent">
                <!-- 系统管理API -->
                <div class="tab-pane fade show active" id="system" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">系统管理API</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-success">GET</span> /api/system/status</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/system/status', 'GET')">测试</button>
                                </div>
                                <p class="text-muted">获取系统状态信息</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "system_status": "running",
    "modules": {...},
    "uptime": "2h 30m"
  }
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/system/start</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/system/start', 'POST')">测试</button>
                                </div>
                                <p class="text-muted">启动系统</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "message": "系统启动成功"
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/system/stop</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/system/stop', 'POST')">测试</button>
                                </div>
                                <p class="text-muted">停止系统</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "message": "系统停止成功"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 人机分配API -->
                <div class="tab-pane fade" id="allocation" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">人机分配API</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-success">GET</span> /api/allocation/system/status</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/allocation/system/status', 'GET')">测试</button>
                                </div>
                                <p class="text-muted">获取人机分配系统状态</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "status": "running",
    "config": {...},
    "total_allocations": 42
  }
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/allocate</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('allocate')">示例</button>
                                </div>
                                <p class="text-muted">执行人机功能分配</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "task_hierarchy": {...},
  "constraints": {...},
  "preferences": {...}
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/schemes/generate</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('generate')">示例</button>
                                </div>
                                <p class="text-muted">生成分配方案</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "task_hierarchy": {...},
  "scheme_count": 5,
  "strategy": "balanced"
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/schemes/compare</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('compare')">示例</button>
                                </div>
                                <p class="text-muted">比较分配方案</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "schemes": [...],
  "task_hierarchy": {...}
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/schemes/evaluate</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('evaluate')">示例</button>
                                </div>
                                <p class="text-muted">评估分配方案效能</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "schemes": [...],
  "task_hierarchy": {...},
  "evaluation_criteria": {...}
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 配置管理API -->
                <div class="tab-pane fade" id="config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">配置管理API</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-success">GET</span> /api/allocation/config</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/allocation/config', 'GET')">测试</button>
                                </div>
                                <p class="text-muted">获取人机分配配置</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "allocation_mode": "semi_automatic",
    "optimization_objective": "balanced",
    "default_scheme_count": 5
  }
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/config</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('config')">示例</button>
                                </div>
                                <p class="text-muted">更新人机分配配置</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "allocation_mode": "automatic",
  "optimization_objective": "efficiency",
  "default_scheme_count": 8
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/config/validate</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('validate')">示例</button>
                                </div>
                                <p class="text-muted">验证人机分配配置</p>
                                <div class="code-block">
                                    <strong>响应:</strong>
                                    <pre><code>{
  "success": true,
  "data": {
    "is_valid": true,
    "config": {...}
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据接口API -->
                <div class="tab-pane fade" id="data" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">数据接口API</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/tasks/validate</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('tasks')">示例</button>
                                </div>
                                <p class="text-muted">验证任务层次结构</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "tasks": {...},
  "rootTaskId": "task_1"
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-warning">POST</span> /api/allocation/capabilities/analyze</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showRequestExample('capabilities')">示例</button>
                                </div>
                                <p class="text-muted">分析人机能力</p>
                                <div class="code-block">
                                    <strong>请求体:</strong>
                                    <pre><code>{
  "task": {...},
  "context": {...}
}</code></pre>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="api-endpoint">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><span class="badge bg-success">GET</span> /api/logs</h6>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testAPI('/api/logs', 'GET')">测试</button>
                                </div>
                                <p class="text-muted">获取系统日志</p>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "timestamp": "2024-01-01T12:00:00",
      "level": "INFO",
      "message": "系统启动成功"
    }
  ]
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API测试模态框 -->
<div class="modal fade" id="apiTestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API测试</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="apiTestContent">
                    <!-- API测试内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="executeAPITest()">执行测试</button>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.api-endpoint {
    margin-bottom: 1.5rem;
}

.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.code-block pre {
    margin: 0;
    font-size: 0.875rem;
}

.nav-pills .nav-link {
    color: #495057;
    margin-bottom: 0.25rem;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
}

.badge {
    font-size: 0.75rem;
    margin-right: 0.5rem;
}
</style>

<script>
let currentAPITest = null;

// 测试API
function testAPI(endpoint, method) {
    currentAPITest = { endpoint, method };
    
    const content = document.getElementById('apiTestContent');
    content.innerHTML = `
        <div class="mb-3">
            <label class="form-label">API端点</label>
            <input type="text" class="form-control" value="${endpoint}" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">请求方法</label>
            <input type="text" class="form-control" value="${method}" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">响应结果</label>
            <div id="apiResponse" class="code-block">
                <pre>点击"执行测试"查看结果</pre>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('apiTestModal')).show();
}

// 执行API测试
function executeAPITest() {
    if (!currentAPITest) return;
    
    const responseDiv = document.getElementById('apiResponse');
    responseDiv.innerHTML = '<pre>正在请求...</pre>';
    
    fetch(currentAPITest.endpoint, {
        method: currentAPITest.method,
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    })
    .catch(error => {
        responseDiv.innerHTML = `<pre style="color: red;">错误: ${error.message}</pre>`;
    });
}

// 显示请求示例
function showRequestExample(type) {
    let example = '';
    
    switch(type) {
        case 'allocate':
            example = `{
  "task_hierarchy": {
    "tasks": {
      "task_1": {
        "name": "主任务",
        "description": "示例主任务",
        "task_type": "MISSION_TASK",
        "executor_type": "HUMAN_MACHINE",
        "attributes": {
          "complexity": 0.7,
          "importance": 0.9,
          "urgency": 0.6
        }
      }
    },
    "rootTaskId": "task_1"
  },
  "constraints": {
    "max_human_workload": 0.8,
    "min_machine_utilization": 0.3
  },
  "preferences": {
    "prefer_human_decision": true,
    "prefer_machine_execution": true
  }
}`;
            break;
        case 'generate':
            example = `{
  "task_hierarchy": {...},
  "scheme_count": 5,
  "strategy": "balanced",
  "constraints": {
    "human_workload_limit": true,
    "machine_utilization_min": true
  }
}`;
            break;
        case 'compare':
            example = `{
  "schemes": [
    {
      "scheme_id": "scheme_1",
      "name": "方案1",
      "allocations": {...}
    }
  ],
  "task_hierarchy": {...}
}`;
            break;
        case 'evaluate':
            example = `{
  "schemes": [...],
  "task_hierarchy": {...},
  "evaluation_criteria": {
    "weights": {
      "task_completion": 0.25,
      "time_efficiency": 0.20,
      "resource_utilization": 0.15
    }
  }
}`;
            break;
        case 'config':
            example = `{
  "allocation_mode": "automatic",
  "optimization_objective": "efficiency",
  "default_scheme_count": 8,
  "decision_threshold": 0.15,
  "confidence_threshold": 0.8
}`;
            break;
        case 'validate':
            example = `{
  "allocation_mode": "semi_automatic",
  "optimization_objective": "balanced",
  "capability_weights": {
    "cognitive": 0.25,
    "physical": 0.15,
    "perceptual": 0.20,
    "decision_making": 0.25,
    "execution": 0.10,
    "coordination": 0.05
  }
}`;
            break;
        case 'tasks':
            example = `{
  "tasks": {
    "task_1": {
      "name": "主任务",
      "description": "示例任务",
      "task_type": "MISSION_TASK",
      "attributes": {
        "complexity": 0.5,
        "importance": 0.8,
        "urgency": 0.6
      }
    }
  },
  "rootTaskId": "task_1"
}`;
            break;
        case 'capabilities':
            example = `{
  "task": {
    "name": "目标识别",
    "complexity": 0.7,
    "real_time_requirement": true
  },
  "context": {
    "environment": "combat",
    "time_pressure": "high"
  }
}`;
            break;
    }
    
    alert('请求示例:\n\n' + example);
}
</script>
{% endblock %}
