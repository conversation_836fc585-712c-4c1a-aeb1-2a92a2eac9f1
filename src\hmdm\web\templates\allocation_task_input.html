{% extends "base.html" %}

{% block title %}任务输入与分解 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tasks"></i>
                    任务输入与分解
                </h1>
                <p class="page-subtitle">构建任务层次结构，为人机分配提供基础数据</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 任务输入区域 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle"></i>
                        任务信息输入
                    </h5>
                </div>
                <div class="card-body">
                    <form id="taskForm">
                        <div class="mb-3">
                            <label class="form-label">任务名称 *</label>
                            <input type="text" class="form-control" id="taskName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">任务类型</label>
                                    <select class="form-select" id="taskType">
                                        <option value="MISSION_TASK">任务级</option>
                                        <option value="ZZ_TASK">作战任务</option>
                                        <option value="META_OPERATION">元操作</option>
                                        <option value="BASIC_OPERATION">基本操作</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">执行者类型</label>
                                    <select class="form-select" id="executorType">
                                        <option value="HUMAN_MACHINE">人机协同</option>
                                        <option value="HUMAN">人类</option>
                                        <option value="MACHINE">机器</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">复杂度 (0-1)</label>
                                    <input type="range" class="form-range" id="complexity" min="0" max="1" step="0.1" value="0.5">
                                    <div class="text-center">
                                        <small class="text-muted">当前值: <span id="complexityValue">0.5</span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">重要性 (0-1)</label>
                                    <input type="range" class="form-range" id="importance" min="0" max="1" step="0.1" value="0.5">
                                    <div class="text-center">
                                        <small class="text-muted">当前值: <span id="importanceValue">0.5</span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">紧急性 (0-1)</label>
                                    <input type="range" class="form-range" id="urgency" min="0" max="1" step="0.1" value="0.5">
                                    <div class="text-center">
                                        <small class="text-muted">当前值: <span id="urgencyValue">0.5</span></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="realTimeRequirement">
                                <label class="form-check-label" for="realTimeRequirement">
                                    实时性要求
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">父任务</label>
                            <select class="form-select" id="parentTask">
                                <option value="">无（根任务）</option>
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加任务
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                <i class="fas fa-eraser"></i> 清空表单
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 任务层次结构显示区域 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sitemap"></i>
                        任务层次结构
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="expandAll()">
                            <i class="fas fa-expand-arrows-alt"></i> 展开全部
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="collapseAll()">
                            <i class="fas fa-compress-arrows-alt"></i> 收起全部
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="taskHierarchy" class="task-tree">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-info-circle fa-2x mb-2"></i>
                            <p>请先添加任务以构建层次结构</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">任务层次结构操作</h6>
                            <small class="text-muted">管理和导出任务数据</small>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-info" onclick="importTasks()">
                                <i class="fas fa-file-import"></i> 导入任务
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="exportTasks()">
                                <i class="fas fa-file-export"></i> 导出任务
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="validateTasks()">
                                <i class="fas fa-check-circle"></i> 验证结构
                            </button>
                            <button type="button" class="btn btn-primary" onclick="proceedToAllocation()" disabled id="proceedBtn">
                                <i class="fas fa-arrow-right"></i> 进行分配
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务编辑模态框 -->
<div class="modal fade" id="editTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTaskForm">
                    <input type="hidden" id="editTaskId">
                    <div class="mb-3">
                        <label class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="editTaskName">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">任务描述</label>
                        <textarea class="form-control" id="editTaskDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">复杂度</label>
                                <input type="range" class="form-range" id="editComplexity" min="0" max="1" step="0.1">
                                <div class="text-center">
                                    <small class="text-muted">当前值: <span id="editComplexityValue">0.5</span></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">重要性</label>
                                <input type="range" class="form-range" id="editImportance" min="0" max="1" step="0.1">
                                <div class="text-center">
                                    <small class="text-muted">当前值: <span id="editImportanceValue">0.5</span></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">紧急性</label>
                                <input type="range" class="form-range" id="editUrgency" min="0" max="1" step="0.1">
                                <div class="text-center">
                                    <small class="text-muted">当前值: <span id="editUrgencyValue">0.5</span></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTaskEdit()">保存修改</button>
                <button type="button" class="btn btn-danger" onclick="deleteTask()">删除任务</button>
            </div>
        </div>
    </div>
</div>

<style>
.task-tree {
    max-height: 500px;
    overflow-y: auto;
}

.task-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s;
}

.task-item:hover {
    background-color: #e9ecef;
}

.task-item.selected {
    background-color: #cfe2ff;
    border-color: #0d6efd;
}

.task-level-0 { margin-left: 0; }
.task-level-1 { margin-left: 1rem; }
.task-level-2 { margin-left: 2rem; }
.task-level-3 { margin-left: 3rem; }

.task-attributes {
    font-size: 0.875rem;
    color: #6c757d;
}

.attribute-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<script>
let taskHierarchy = {
    tasks: {},
    rootTaskId: null
};

let taskIdCounter = 1;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 绑定滑块值显示
    bindRangeInputs();
    
    // 绑定表单提交
    document.getElementById('taskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addTask();
    });
});

// 绑定滑块输入
function bindRangeInputs() {
    const ranges = ['complexity', 'importance', 'urgency'];
    ranges.forEach(range => {
        const input = document.getElementById(range);
        const display = document.getElementById(range + 'Value');
        
        input.addEventListener('input', function() {
            display.textContent = this.value;
        });
        
        // 编辑模态框的滑块
        const editInput = document.getElementById('edit' + range.charAt(0).toUpperCase() + range.slice(1));
        const editDisplay = document.getElementById('edit' + range.charAt(0).toUpperCase() + range.slice(1) + 'Value');
        
        if (editInput && editDisplay) {
            editInput.addEventListener('input', function() {
                editDisplay.textContent = this.value;
            });
        }
    });
}

// 添加任务
function addTask() {
    const taskData = {
        id: 'task_' + taskIdCounter++,
        name: document.getElementById('taskName').value,
        description: document.getElementById('taskDescription').value,
        task_type: document.getElementById('taskType').value,
        executor_type: document.getElementById('executorType').value,
        parent_id: document.getElementById('parentTask').value || null,
        level: 0,
        attributes: {
            complexity: parseFloat(document.getElementById('complexity').value),
            importance: parseFloat(document.getElementById('importance').value),
            urgency: parseFloat(document.getElementById('urgency').value),
            real_time_requirement: document.getElementById('realTimeRequirement').checked
        }
    };
    
    // 计算层级
    if (taskData.parent_id) {
        const parentTask = taskHierarchy.tasks[taskData.parent_id];
        taskData.level = parentTask ? parentTask.level + 1 : 0;
    } else {
        taskData.level = 0;
        if (!taskHierarchy.rootTaskId) {
            taskHierarchy.rootTaskId = taskData.id;
        }
    }
    
    // 添加到层次结构
    taskHierarchy.tasks[taskData.id] = taskData;
    
    // 更新父任务选择器
    updateParentTaskSelector();
    
    // 重新渲染任务树
    renderTaskTree();
    
    // 清空表单
    clearForm();
    
    // 启用进行分配按钮
    document.getElementById('proceedBtn').disabled = false;
    
    showToast('任务添加成功', 'success');
}

// 更新父任务选择器
function updateParentTaskSelector() {
    const selector = document.getElementById('parentTask');
    selector.innerHTML = '<option value="">无（根任务）</option>';
    
    Object.values(taskHierarchy.tasks).forEach(task => {
        const option = document.createElement('option');
        option.value = task.id;
        option.textContent = '  '.repeat(task.level) + task.name;
        selector.appendChild(option);
    });
}

// 渲染任务树
function renderTaskTree() {
    const container = document.getElementById('taskHierarchy');
    
    if (Object.keys(taskHierarchy.tasks).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>请先添加任务以构建层次结构</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = '';
    
    // 按层级排序任务
    const sortedTasks = Object.values(taskHierarchy.tasks).sort((a, b) => {
        if (a.level !== b.level) return a.level - b.level;
        return a.name.localeCompare(b.name);
    });
    
    sortedTasks.forEach(task => {
        const taskElement = createTaskElement(task);
        container.appendChild(taskElement);
    });
}

// 创建任务元素
function createTaskElement(task) {
    const div = document.createElement('div');
    div.className = `task-item task-level-${task.level}`;
    div.onclick = () => selectTask(task.id);
    
    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h6 class="mb-1">${task.name}</h6>
                <p class="mb-2 text-muted small">${task.description || '无描述'}</p>
                <div class="task-attributes">
                    <span class="attribute-badge">复杂度: ${task.attributes.complexity}</span>
                    <span class="attribute-badge">重要性: ${task.attributes.importance}</span>
                    <span class="attribute-badge">紧急性: ${task.attributes.urgency}</span>
                    ${task.attributes.real_time_requirement ? '<span class="attribute-badge bg-warning text-dark">实时</span>' : ''}
                </div>
            </div>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary" onclick="editTask('${task.id}'); event.stopPropagation();">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
    `;
    
    return div;
}

// 选择任务
function selectTask(taskId) {
    // 移除之前的选择
    document.querySelectorAll('.task-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    // 选择当前任务
    event.currentTarget.classList.add('selected');
}

// 编辑任务
function editTask(taskId) {
    const task = taskHierarchy.tasks[taskId];
    if (!task) return;
    
    // 填充编辑表单
    document.getElementById('editTaskId').value = taskId;
    document.getElementById('editTaskName').value = task.name;
    document.getElementById('editTaskDescription').value = task.description || '';
    document.getElementById('editComplexity').value = task.attributes.complexity;
    document.getElementById('editImportance').value = task.attributes.importance;
    document.getElementById('editUrgency').value = task.attributes.urgency;
    
    // 更新显示值
    document.getElementById('editComplexityValue').textContent = task.attributes.complexity;
    document.getElementById('editImportanceValue').textContent = task.attributes.importance;
    document.getElementById('editUrgencyValue').textContent = task.attributes.urgency;
    
    // 显示模态框
    new bootstrap.Modal(document.getElementById('editTaskModal')).show();
}

// 保存任务编辑
function saveTaskEdit() {
    const taskId = document.getElementById('editTaskId').value;
    const task = taskHierarchy.tasks[taskId];
    
    if (!task) return;
    
    // 更新任务数据
    task.name = document.getElementById('editTaskName').value;
    task.description = document.getElementById('editTaskDescription').value;
    task.attributes.complexity = parseFloat(document.getElementById('editComplexity').value);
    task.attributes.importance = parseFloat(document.getElementById('editImportance').value);
    task.attributes.urgency = parseFloat(document.getElementById('editUrgency').value);
    
    // 重新渲染
    renderTaskTree();
    updateParentTaskSelector();
    
    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('editTaskModal')).hide();
    
    showToast('任务更新成功', 'success');
}

// 删除任务
function deleteTask() {
    const taskId = document.getElementById('editTaskId').value;
    
    if (confirm('确定要删除这个任务吗？这将同时删除所有子任务。')) {
        deleteTaskRecursive(taskId);
        renderTaskTree();
        updateParentTaskSelector();
        
        // 关闭模态框
        bootstrap.Modal.getInstance(document.getElementById('editTaskModal')).hide();
        
        showToast('任务删除成功', 'success');
        
        // 如果没有任务了，禁用进行分配按钮
        if (Object.keys(taskHierarchy.tasks).length === 0) {
            document.getElementById('proceedBtn').disabled = true;
        }
    }
}

// 递归删除任务
function deleteTaskRecursive(taskId) {
    // 删除所有子任务
    Object.values(taskHierarchy.tasks).forEach(task => {
        if (task.parent_id === taskId) {
            deleteTaskRecursive(task.id);
        }
    });
    
    // 删除任务本身
    delete taskHierarchy.tasks[taskId];
    
    // 如果删除的是根任务，重置根任务ID
    if (taskHierarchy.rootTaskId === taskId) {
        taskHierarchy.rootTaskId = null;
    }
}

// 清空表单
function clearForm() {
    document.getElementById('taskForm').reset();
    document.getElementById('complexityValue').textContent = '0.5';
    document.getElementById('importanceValue').textContent = '0.5';
    document.getElementById('urgencyValue').textContent = '0.5';
}

// 展开全部
function expandAll() {
    // 这里可以实现展开逻辑
    showToast('全部展开', 'info');
}

// 收起全部
function collapseAll() {
    // 这里可以实现收起逻辑
    showToast('全部收起', 'info');
}

// 导入任务
function importTasks() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    taskHierarchy = data;
                    renderTaskTree();
                    updateParentTaskSelector();
                    document.getElementById('proceedBtn').disabled = false;
                    showToast('任务导入成功', 'success');
                } catch (error) {
                    showToast('导入失败：文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

// 导出任务
function exportTasks() {
    if (Object.keys(taskHierarchy.tasks).length === 0) {
        showToast('没有任务可以导出', 'warning');
        return;
    }
    
    const blob = new Blob([JSON.stringify(taskHierarchy, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'task_hierarchy.json';
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('任务导出成功', 'success');
}

// 验证任务结构
function validateTasks() {
    if (Object.keys(taskHierarchy.tasks).length === 0) {
        showToast('没有任务需要验证', 'warning');
        return;
    }
    
    // 简单验证逻辑
    let isValid = true;
    let errors = [];
    
    // 检查是否有根任务
    if (!taskHierarchy.rootTaskId) {
        errors.push('缺少根任务');
        isValid = false;
    }
    
    // 检查任务完整性
    Object.values(taskHierarchy.tasks).forEach(task => {
        if (!task.name || task.name.trim() === '') {
            errors.push(`任务 ${task.id} 缺少名称`);
            isValid = false;
        }
    });
    
    if (isValid) {
        showToast('任务结构验证通过', 'success');
    } else {
        showToast('验证失败：' + errors.join(', '), 'error');
    }
}

// 进行分配
function proceedToAllocation() {
    if (Object.keys(taskHierarchy.tasks).length === 0) {
        showToast('请先添加任务', 'warning');
        return;
    }
    
    // 将任务数据存储到sessionStorage
    sessionStorage.setItem('taskHierarchy', JSON.stringify(taskHierarchy));
    
    // 跳转到方案比较页面
    window.location.href = '/allocation/scheme-comparison';
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 简单的提示实现
    alert(message);
}
</script>
{% endblock %}
