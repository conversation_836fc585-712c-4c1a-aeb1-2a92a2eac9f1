HMDM系统军事案例综合测试报告
================================================================================

测试基本信息:
----------------------------------------
测试时间: 2025-09-09 12:52:17
输入文件: military_case_example.json
输出目录: military_test_results_20250909_125134
测试版本: 1.0.0

案例信息:
----------------------------------------
案例名称: 海上编队防空作战态势分析任务
场景类型: 态势分析
任务级别: 战术级
紧急程度: 高
复杂程度: 中等
描述: 某海军编队在执行远海护航任务时，需要对周边空中威胁进行实时态势分析，包括目标识别、威胁评估和预警决策

测试结果详情:
----------------------------------------
1. 任务层次分析处理:
    状态: 成功
    任务总数: 11
    根任务ID: air_defense_situation_analysis
    层级分布: {0: 1, 1: 3, 2: 7}
    任务类型分布: {'MISSION_TASK': 1, 'ZZ_TASK': 3, 'TYPICAL_FUNCTION': 7}
    执行者类型分布: {'HUMAN_MACHINE': 5, 'MACHINE': 4, 'HUMAN': 2}

2. 人机能力评估计算:
    状态: 成功
    人员数量: 3
    设备数量: 3
    人员详情: ['air_defense_officer', 'radar_operator', 'intelligence_analyst']
    设备详情: ['radar_system', 'combat_system', 'eo_system']

3. 多目标决策算法运行:
    状态: 成功
    备选方案数: 4
    评估准则数: 9
    决策矩阵大小: 49
    WRDM算法: 可用
    TOPSIS算法: 可用

4. 分配方案生成和评估:
    状态: 成功
    推荐方案: 人机协同优化方案
    总体评分: 0.867
    置信水平: 0.920
    任务分配数: 11
    备选方案数: 3

5. 实施指导和建议:
    状态: 成功
    部署步骤: 5个
    资源需求: 4项
    风险缓解: 4项
    成功指标: 5项

测试总结:
----------------------------------------
 数据加载: 成功
 任务层次分析: 成功
 人机能力评估: 成功
 多目标决策算法: 成功
 分配方案生成: 成功
 实施指导分析: 成功
 结果保存: 成功

总体状态: 全部测试通过
测试结论: HMDM系统功能完整，数据处理正确，算法运行正常
