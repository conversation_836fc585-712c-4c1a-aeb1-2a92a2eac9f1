"""
人机功能分配模块

该模块实现HMDM系统的核心功能：人机功能分配
包含人机能力分析、分配方案生成、协同效能评估等核心组件
"""

from .human_machine_capability_analyzer import (
    HumanMachineCapabilityAnalyzer,
    HumanCapability,
    MachineCapability,
    CapabilityType
)

from .allocation_scheme_generator import (
    AllocationSchemeGenerator,
    AllocationScheme,
    AllocationStrategy
)

from .collaboration_effectiveness_evaluator import (
    CollaborationEffectivenessEvaluator,
    EffectivenessMetrics,
    EvaluationReport
)

from .human_machine_allocation_system import (
    HumanMachineAllocationSystem,
    AllocationDecisionResult
)

from .allocation_config import (
    AllocationConfig,
    AllocationMode,
    OptimizationObjective,
    DEFAULT_ALLOCATION_CONFIG,
    load_allocation_config,
    save_allocation_config
)

__all__ = [
    'HumanMachineCapabilityAnalyzer',
    'HumanCapability',
    'MachineCapability',
    'CapabilityType',
    'AllocationSchemeGenerator',
    'AllocationScheme',
    'AllocationStrategy',
    'CollaborationEffectivenessEvaluator',
    'EffectivenessMetrics',
    'EvaluationReport',
    'HumanMachineAllocationSystem',
    'AllocationDecisionResult',
    'AllocationConfig',
    'AllocationMode',
    'OptimizationObjective',
    'DEFAULT_ALLOCATION_CONFIG',
    'load_allocation_config',
    'save_allocation_config'
]
