{"metadata": {"name": "完整工作流程测试数据", "description": "端到端业务流程测试数据", "version": "1.0", "created_date": "2024-01-01"}, "workflow_scenarios": [{"name": "标准工作流程", "description": "从任务创建到方案推荐的完整流程", "steps": [{"step": 1, "name": "创建任务层次结构", "action": "create_task_hierarchy", "input": {"scenario": "态势分析", "root_task_name": "态势分析任务"}, "expected_output": {"task_count": 4, "hierarchy_levels": 3, "root_task_type": "MISSION_TASK"}}, {"step": 2, "name": "创建评估方案", "action": "create_evaluation_scheme", "input": {"scheme_type": "default"}, "expected_output": {"indicator_count": 10, "weight_sum": 1.0, "indicator_types": 7}}, {"step": 3, "name": "添加备选方案", "action": "add_alternatives", "input": {"alternatives": [{"name": "人工主导方案", "attributes": {"workload": 0.8, "efficiency": 0.6, "reliability": 0.7, "usability": 0.9, "safety": 0.8, "cost": 0.3, "performance": 0.5}}, {"name": "机器主导方案", "attributes": {"workload": 0.3, "efficiency": 0.9, "reliability": 0.8, "usability": 0.5, "safety": 0.7, "cost": 0.7, "performance": 0.9}}, {"name": "人机协作方案", "attributes": {"workload": 0.5, "efficiency": 0.8, "reliability": 0.9, "usability": 0.8, "safety": 0.9, "cost": 0.5, "performance": 0.7}}]}, "expected_output": {"alternatives_count": 3, "all_attributes_present": true}}, {"step": 4, "name": "执行方案评估", "action": "evaluate_alternatives", "input": {"method": "WRDM"}, "expected_output": {"has_recommended_alternative": true, "has_rankings": true, "has_scores": true}}, {"step": 5, "name": "比较多种决策方法", "action": "compare_methods", "input": {"methods": ["WRDM", "TOPSIS", "Fuzzy AHP"]}, "expected_output": {"method_count": 3, "has_comparison_results": true, "has_consistency_analysis": true}}, {"step": 6, "name": "生成决策报告", "action": "generate_report", "input": {"include_charts": true, "include_sensitivity": true}, "expected_output": {"has_executive_summary": true, "has_detailed_analysis": true, "has_recommendations": true}}], "success_criteria": ["所有步骤成功执行", "数据在步骤间正确传递", "最终生成完整的决策报告", "推荐方案合理可信"]}, {"name": "快速决策流程", "description": "紧急情况下的快速决策流程", "steps": [{"step": 1, "name": "快速任务分解", "action": "quick_task_decompose", "input": {"scenario": "威胁计算", "depth_limit": 2}, "time_limit": 30}, {"step": 2, "name": "使用预设评估方案", "action": "use_preset_scheme", "input": {"scheme_name": "efficiency_focused"}, "time_limit": 5}, {"step": 3, "name": "快速方案评估", "action": "quick_evaluate", "input": {"method": "TOPSIS", "alternatives_limit": 5}, "time_limit": 10}], "total_time_limit": 60, "success_criteria": ["总时间不超过60秒", "生成可用的推荐方案", "决策质量可接受"]}, {"name": "复杂场景流程", "description": "多场景、多方案的复杂决策流程", "steps": [{"step": 1, "name": "多场景任务分解", "action": "multi_scenario_decompose", "input": {"scenarios": ["态势分析", "威胁计算", "辅助决策"]}}, {"step": 2, "name": "自定义评估方案", "action": "create_custom_scheme", "input": {"indicators": [{"name": "效率", "weight": 0.3}, {"name": "可靠性", "weight": 0.25}, {"name": "成本", "weight": 0.2}, {"name": "安全性", "weight": 0.15}, {"name": "可用性", "weight": 0.1}]}}, {"step": 3, "name": "大规模方案评估", "action": "evaluate_large_set", "input": {"alternatives_count": 20, "methods": ["WRDM", "TOPSIS", "Fuzzy AHP"]}}, {"step": 4, "name": "敏感性分析", "action": "sensitivity_analysis", "input": {"weight_variations": 5, "parameter_ranges": {"efficiency": [0.2, 0.4], "reliability": [0.15, 0.35]}}}], "success_criteria": ["处理多场景任务", "评估大量备选方案", "提供敏感性分析", "结果稳定可靠"]}], "data_flow_validation": [{"from_step": "create_task_hierarchy", "to_step": "create_evaluation_scheme", "data_elements": ["task_structure", "task_attributes"], "validation_rules": ["任务结构完整", "任务属性有效"]}, {"from_step": "create_evaluation_scheme", "to_step": "add_alternatives", "data_elements": ["evaluation_scheme", "indicator_definitions"], "validation_rules": ["评估方案有效", "指标定义完整"]}, {"from_step": "add_alternatives", "to_step": "evaluate_alternatives", "data_elements": ["alternatives_list", "attribute_values"], "validation_rules": ["备选方案完整", "属性值有效"]}], "error_scenarios": [{"name": "中断恢复测试", "description": "测试流程中断后的恢复能力", "interrupt_points": ["step_2_completion", "step_4_middle", "step_5_start"], "recovery_actions": ["保存当前状态", "从中断点恢复", "验证数据完整性"]}, {"name": "数据异常处理", "description": "测试异常数据的处理", "error_injections": [{"step": 3, "error_type": "missing_attributes", "expected_behavior": "提示缺失属性并提供默认值"}, {"step": 4, "error_type": "invalid_method", "expected_behavior": "返回错误信息并建议有效方法"}]}], "performance_expectations": {"standard_workflow": {"total_time": 30, "memory_usage": 200, "success_rate": 0.99}, "quick_workflow": {"total_time": 60, "memory_usage": 100, "success_rate": 0.95}, "complex_workflow": {"total_time": 300, "memory_usage": 500, "success_rate": 0.98}}}