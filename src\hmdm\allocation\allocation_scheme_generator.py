"""
分配方案生成器

基于任务分解和能力分析生成多个人机功能分配方案
支持不同的分配策略和优化目标
"""

from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid
import logging

from ..task_analysis.hierarchical_task_analyzer import TaskHierarchy, Task
from .human_machine_capability_analyzer import HumanMachineCapabilityAnalyzer


class AllocationStrategy(Enum):
    """分配策略枚举"""
    CAPABILITY_BASED = "capability_based"      # 基于能力匹配
    HUMAN_PRIORITY = "human_priority"          # 人类优先
    MACHINE_PRIORITY = "machine_priority"      # 机器优先
    BALANCED = "balanced"                      # 平衡分配
    EFFICIENCY_OPTIMIZED = "efficiency_optimized"  # 效率优化
    RELIABILITY_OPTIMIZED = "reliability_optimized"  # 可靠性优化


@dataclass
class AllocationScheme:
    """人机分配方案"""
    scheme_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    strategy: AllocationStrategy = AllocationStrategy.CAPABILITY_BASED
    task_allocations: Dict[str, str] = field(default_factory=dict)  # task_id -> allocation_type
    collaboration_details: Dict[str, Dict] = field(default_factory=dict)  # 协同任务的详细分工
    expected_performance: Dict[str, float] = field(default_factory=dict)  # 预期性能指标
    risk_assessment: Dict[str, float] = field(default_factory=dict)  # 风险评估
    implementation_notes: List[str] = field(default_factory=list)  # 实施说明
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'scheme_id': self.scheme_id,
            'name': self.name,
            'description': self.description,
            'strategy': self.strategy.value,
            'task_allocations': self.task_allocations,
            'collaboration_details': self.collaboration_details,
            'expected_performance': self.expected_performance,
            'risk_assessment': self.risk_assessment,
            'implementation_notes': self.implementation_notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AllocationScheme':
        """从字典创建分配方案"""
        scheme = cls()
        scheme.scheme_id = data.get('scheme_id', scheme.scheme_id)
        scheme.name = data.get('name', '')
        scheme.description = data.get('description', '')
        scheme.strategy = AllocationStrategy(data.get('strategy', AllocationStrategy.CAPABILITY_BASED.value))
        scheme.task_allocations = data.get('task_allocations', {})
        scheme.collaboration_details = data.get('collaboration_details', {})
        scheme.expected_performance = data.get('expected_performance', {})
        scheme.risk_assessment = data.get('risk_assessment', {})
        scheme.implementation_notes = data.get('implementation_notes', [])
        return scheme


class AllocationSchemeGenerator:
    """分配方案生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.logger = logging.getLogger(__name__)
        self.capability_analyzer = HumanMachineCapabilityAnalyzer()
        
        # 策略配置
        self.strategy_configs = self._init_strategy_configs()
    
    def _init_strategy_configs(self) -> Dict[AllocationStrategy, Dict[str, Any]]:
        """初始化策略配置"""
        return {
            AllocationStrategy.CAPABILITY_BASED: {
                'threshold': 0.15,
                'prefer_collaboration': True,
                'optimization_weight': {'efficiency': 0.4, 'reliability': 0.3, 'cost': 0.3}
            },
            AllocationStrategy.HUMAN_PRIORITY: {
                'threshold': 0.25,
                'human_bias': 0.2,
                'optimization_weight': {'flexibility': 0.5, 'creativity': 0.3, 'reliability': 0.2}
            },
            AllocationStrategy.MACHINE_PRIORITY: {
                'threshold': 0.25,
                'machine_bias': 0.2,
                'optimization_weight': {'efficiency': 0.5, 'accuracy': 0.3, 'cost': 0.2}
            },
            AllocationStrategy.BALANCED: {
                'threshold': 0.1,
                'balance_factor': 0.5,
                'optimization_weight': {'efficiency': 0.25, 'reliability': 0.25, 'flexibility': 0.25, 'cost': 0.25}
            },
            AllocationStrategy.EFFICIENCY_OPTIMIZED: {
                'threshold': 0.2,
                'efficiency_priority': 0.8,
                'optimization_weight': {'efficiency': 0.7, 'cost': 0.2, 'reliability': 0.1}
            },
            AllocationStrategy.RELIABILITY_OPTIMIZED: {
                'threshold': 0.1,
                'reliability_priority': 0.8,
                'optimization_weight': {'reliability': 0.6, 'accuracy': 0.3, 'efficiency': 0.1}
            }
        }
    
    def generate_schemes(self, task_hierarchy: TaskHierarchy, 
                        num_schemes: int = 3,
                        constraints: Optional[Dict] = None,
                        preferences: Optional[Dict] = None) -> List[AllocationScheme]:
        """
        生成多个人机分配方案
        
        Args:
            task_hierarchy: 任务层次结构
            num_schemes: 生成方案数量
            constraints: 分配约束条件
            preferences: 用户偏好设置
            
        Returns:
            List[AllocationScheme]: 生成的分配方案列表
        """
        self.logger.info(f"开始生成 {num_schemes} 个人机分配方案")
        
        schemes = []
        
        # 生成基于不同策略的方案
        strategies = list(AllocationStrategy)[:num_schemes]
        
        for i, strategy in enumerate(strategies):
            scheme = self._generate_scheme_by_strategy(
                task_hierarchy, strategy, constraints, preferences
            )
            scheme.name = f"方案{i+1}: {self._get_strategy_name(strategy)}"
            schemes.append(scheme)
        
        # 如果需要更多方案，生成变体
        while len(schemes) < num_schemes:
            base_scheme = schemes[len(schemes) % len(strategies)]
            variant_scheme = self._generate_scheme_variant(base_scheme, task_hierarchy)
            variant_scheme.name = f"方案{len(schemes)+1}: {base_scheme.name}变体"
            schemes.append(variant_scheme)
        
        self.logger.info(f"成功生成 {len(schemes)} 个分配方案")
        return schemes[:num_schemes]
    
    def _generate_scheme_by_strategy(self, task_hierarchy: TaskHierarchy,
                                   strategy: AllocationStrategy,
                                   constraints: Optional[Dict] = None,
                                   preferences: Optional[Dict] = None) -> AllocationScheme:
        """根据策略生成分配方案"""
        scheme = AllocationScheme(strategy=strategy)
        scheme.description = f"基于{self._get_strategy_name(strategy)}策略的人机分配方案"
        
        config = self.strategy_configs[strategy]
        threshold = config.get('threshold', 0.15)
        
        # 获取所有任务
        all_tasks = task_hierarchy.get_all_tasks()
        
        for task in all_tasks:
            allocation_type, confidence, details = self._allocate_task_by_strategy(
                task, strategy, threshold, constraints, preferences
            )
            
            scheme.task_allocations[task.id] = allocation_type
            
            if allocation_type == "collaboration":
                scheme.collaboration_details[task.id] = {
                    'human_role': details.get('human_role', ''),
                    'machine_role': details.get('machine_role', ''),
                    'coordination_method': self._suggest_coordination_method(task),
                    'confidence': confidence
                }
        
        # 计算预期性能和风险评估
        scheme.expected_performance = self._calculate_expected_performance(scheme, task_hierarchy)
        scheme.risk_assessment = self._assess_scheme_risks(scheme, task_hierarchy)
        scheme.implementation_notes = self._generate_implementation_notes(scheme, task_hierarchy)
        
        return scheme
    
    def _allocate_task_by_strategy(self, task: Task, strategy: AllocationStrategy,
                                 threshold: float, constraints: Optional[Dict] = None,
                                 preferences: Optional[Dict] = None) -> tuple:
        """根据策略分配单个任务"""
        # 获取基础的人机适合度评估
        human_score = self.capability_analyzer.evaluate_human_suitability(task)
        machine_score = self.capability_analyzer.evaluate_machine_suitability(task)
        
        # 根据策略调整评分
        if strategy == AllocationStrategy.HUMAN_PRIORITY:
            human_score += self.strategy_configs[strategy].get('human_bias', 0.2)
        elif strategy == AllocationStrategy.MACHINE_PRIORITY:
            machine_score += self.strategy_configs[strategy].get('machine_bias', 0.2)
        elif strategy == AllocationStrategy.BALANCED:
            balance_factor = self.strategy_configs[strategy].get('balance_factor', 0.5)
            avg_score = (human_score + machine_score) / 2
            human_score = human_score * (1 - balance_factor) + avg_score * balance_factor
            machine_score = machine_score * (1 - balance_factor) + avg_score * balance_factor
        elif strategy == AllocationStrategy.EFFICIENCY_OPTIMIZED:
            # 效率优化倾向于机器
            if hasattr(task.attributes, 'real_time_requirement') and task.attributes.real_time_requirement:
                machine_score += 0.3
        elif strategy == AllocationStrategy.RELIABILITY_OPTIMIZED:
            # 可靠性优化考虑任务重要性
            if hasattr(task.attributes, 'importance') and task.attributes.importance > 0.8:
                # 重要任务倾向于人机协同
                collaboration_bonus = 0.2
                return "collaboration", (human_score + machine_score) / 2 + collaboration_bonus, {
                    'reason': '重要任务采用人机协同以提高可靠性',
                    'human_role': '监督和决策',
                    'machine_role': '执行和计算',
                    'human_score': human_score,
                    'machine_score': machine_score
                }
        
        # 应用约束条件
        if constraints:
            if constraints.get('max_human_tasks') and self._count_human_tasks() >= constraints['max_human_tasks']:
                machine_score += 0.3
            if constraints.get('max_machine_tasks') and self._count_machine_tasks() >= constraints['max_machine_tasks']:
                human_score += 0.3
        
        # 做出分配决策
        if human_score > machine_score + threshold:
            return "human", human_score, {
                'reason': f'人类在该任务中优势明显 (评分: {human_score:.3f} vs {machine_score:.3f})',
                'human_score': human_score,
                'machine_score': machine_score
            }
        elif machine_score > human_score + threshold:
            return "machine", machine_score, {
                'reason': f'机器在该任务中优势明显 (评分: {machine_score:.3f} vs {human_score:.3f})',
                'human_score': human_score,
                'machine_score': machine_score
            }
        else:
            collaboration_score = (human_score + machine_score) / 2 + 0.1
            return "collaboration", collaboration_score, {
                'reason': '人机协同执行效果最佳',
                'human_role': self.capability_analyzer._suggest_human_role(task),
                'machine_role': self.capability_analyzer._suggest_machine_role(task),
                'human_score': human_score,
                'machine_score': machine_score
            }
    
    def _count_human_tasks(self) -> int:
        """计算当前人类分配的任务数量（简化实现）"""
        return 0  # 实际实现中需要维护状态
    
    def _count_machine_tasks(self) -> int:
        """计算当前机器分配的任务数量（简化实现）"""
        return 0  # 实际实现中需要维护状态
    
    def _suggest_coordination_method(self, task: Task) -> str:
        """建议协调方法"""
        if hasattr(task.attributes, 'real_time_requirement') and task.attributes.real_time_requirement:
            return "实时协同"
        elif hasattr(task.attributes, 'complexity') and task.attributes.complexity > 0.7:
            return "分阶段协同"
        else:
            return "并行协同"
    
    def _calculate_expected_performance(self, scheme: AllocationScheme, 
                                      task_hierarchy: TaskHierarchy) -> Dict[str, float]:
        """计算预期性能指标"""
        performance = {
            'overall_efficiency': 0.0,
            'task_completion_rate': 0.0,
            'resource_utilization': 0.0,
            'coordination_overhead': 0.0
        }
        
        all_tasks = task_hierarchy.get_all_tasks()
        if not all_tasks:
            return performance
        
        total_efficiency = 0.0
        total_completion_rate = 0.0
        collaboration_count = 0
        
        for task in all_tasks:
            allocation_type = scheme.task_allocations.get(task.id, "human")
            
            if allocation_type == "human":
                efficiency = self.capability_analyzer.evaluate_human_suitability(task)
                completion_rate = 0.85  # 人类基础完成率
            elif allocation_type == "machine":
                efficiency = self.capability_analyzer.evaluate_machine_suitability(task)
                completion_rate = 0.95  # 机器基础完成率
            else:  # collaboration
                human_eff = self.capability_analyzer.evaluate_human_suitability(task)
                machine_eff = self.capability_analyzer.evaluate_machine_suitability(task)
                efficiency = (human_eff + machine_eff) / 2 + 0.1  # 协同加成
                completion_rate = 0.9
                collaboration_count += 1
            
            total_efficiency += efficiency
            total_completion_rate += completion_rate
        
        performance['overall_efficiency'] = total_efficiency / len(all_tasks)
        performance['task_completion_rate'] = total_completion_rate / len(all_tasks)
        performance['resource_utilization'] = 0.8  # 简化计算
        performance['coordination_overhead'] = collaboration_count / len(all_tasks) * 0.2
        
        return performance
    
    def _assess_scheme_risks(self, scheme: AllocationScheme, 
                           task_hierarchy: TaskHierarchy) -> Dict[str, float]:
        """评估方案风险"""
        risks = {
            'human_overload_risk': 0.0,
            'machine_failure_risk': 0.0,
            'coordination_risk': 0.0,
            'overall_risk': 0.0
        }
        
        all_tasks = task_hierarchy.get_all_tasks()
        if not all_tasks:
            return risks
        
        human_task_count = sum(1 for task_id, allocation in scheme.task_allocations.items() 
                              if allocation == "human")
        machine_task_count = sum(1 for task_id, allocation in scheme.task_allocations.items() 
                                if allocation == "machine")
        collaboration_count = sum(1 for task_id, allocation in scheme.task_allocations.items() 
                                 if allocation == "collaboration")
        
        total_tasks = len(all_tasks)
        
        # 人类过载风险
        risks['human_overload_risk'] = min(1.0, human_task_count / total_tasks * 1.5)
        
        # 机器故障风险
        risks['machine_failure_risk'] = min(1.0, machine_task_count / total_tasks * 0.8)
        
        # 协调风险
        risks['coordination_risk'] = min(1.0, collaboration_count / total_tasks * 1.2)
        
        # 总体风险
        risks['overall_risk'] = (risks['human_overload_risk'] + 
                               risks['machine_failure_risk'] + 
                               risks['coordination_risk']) / 3
        
        return risks
    
    def _generate_implementation_notes(self, scheme: AllocationScheme, 
                                     task_hierarchy: TaskHierarchy) -> List[str]:
        """生成实施说明"""
        notes = []
        
        # 分析分配分布
        allocation_counts = {}
        for allocation in scheme.task_allocations.values():
            allocation_counts[allocation] = allocation_counts.get(allocation, 0) + 1
        
        if allocation_counts.get('collaboration', 0) > 0:
            notes.append(f"包含 {allocation_counts['collaboration']} 个协同任务，需要建立有效的人机协调机制")
        
        if allocation_counts.get('human', 0) > allocation_counts.get('machine', 0):
            notes.append("人类任务较多，需要关注人员培训和工作负荷管理")
        elif allocation_counts.get('machine', 0) > allocation_counts.get('human', 0):
            notes.append("机器任务较多，需要确保系统稳定性和故障恢复能力")
        
        # 基于风险评估添加说明
        risks = scheme.risk_assessment
        if risks.get('coordination_risk', 0) > 0.5:
            notes.append("协调风险较高，建议制定详细的协同工作流程")
        
        if risks.get('human_overload_risk', 0) > 0.6:
            notes.append("人员工作负荷较重，建议合理安排工作时间和休息")
        
        return notes
    
    def _generate_scheme_variant(self, base_scheme: AllocationScheme, 
                               task_hierarchy: TaskHierarchy) -> AllocationScheme:
        """生成方案变体"""
        variant = AllocationScheme()
        variant.strategy = base_scheme.strategy
        variant.description = f"{base_scheme.description}的优化变体"
        
        # 复制基础分配
        variant.task_allocations = base_scheme.task_allocations.copy()
        
        # 随机调整部分任务分配
        all_tasks = task_hierarchy.get_all_tasks()
        adjustment_count = max(1, len(all_tasks) // 4)  # 调整25%的任务
        
        for i, task in enumerate(all_tasks[:adjustment_count]):
            current_allocation = variant.task_allocations.get(task.id, "human")
            
            # 简单的变体逻辑：在人类和机器之间切换
            if current_allocation == "human":
                variant.task_allocations[task.id] = "machine"
            elif current_allocation == "machine":
                variant.task_allocations[task.id] = "human"
            # 协同任务保持不变
        
        # 重新计算性能和风险
        variant.expected_performance = self._calculate_expected_performance(variant, task_hierarchy)
        variant.risk_assessment = self._assess_scheme_risks(variant, task_hierarchy)
        variant.implementation_notes = self._generate_implementation_notes(variant, task_hierarchy)
        
        return variant
    
    def _get_strategy_name(self, strategy: AllocationStrategy) -> str:
        """获取策略中文名称"""
        strategy_names = {
            AllocationStrategy.CAPABILITY_BASED: "能力匹配",
            AllocationStrategy.HUMAN_PRIORITY: "人类优先",
            AllocationStrategy.MACHINE_PRIORITY: "机器优先",
            AllocationStrategy.BALANCED: "平衡分配",
            AllocationStrategy.EFFICIENCY_OPTIMIZED: "效率优化",
            AllocationStrategy.RELIABILITY_OPTIMIZED: "可靠性优化"
        }
        return strategy_names.get(strategy, strategy.value)
    
    def optimize_scheme(self, scheme: AllocationScheme, 
                       task_hierarchy: TaskHierarchy,
                       optimization_criteria: Dict[str, float]) -> AllocationScheme:
        """
        优化分配方案
        
        Args:
            scheme: 待优化的方案
            task_hierarchy: 任务层次结构
            optimization_criteria: 优化准则 {'efficiency': 0.8, 'reliability': 0.6}
            
        Returns:
            AllocationScheme: 优化后的方案
        """
        optimized_scheme = AllocationScheme()
        optimized_scheme.scheme_id = str(uuid.uuid4())
        optimized_scheme.name = f"{scheme.name}（优化版）"
        optimized_scheme.description = f"{scheme.description}，基于{list(optimization_criteria.keys())}优化"
        optimized_scheme.strategy = scheme.strategy
        
        # 复制原始分配
        optimized_scheme.task_allocations = scheme.task_allocations.copy()
        optimized_scheme.collaboration_details = scheme.collaboration_details.copy()
        
        # 根据优化准则调整分配
        all_tasks = task_hierarchy.get_all_tasks()
        
        for task in all_tasks:
            current_allocation = optimized_scheme.task_allocations.get(task.id, "human")
            
            # 根据优化准则重新评估
            if "efficiency" in optimization_criteria and optimization_criteria["efficiency"] > 0.7:
                machine_score = self.capability_analyzer.evaluate_machine_suitability(task)
                if machine_score > 0.7 and current_allocation != "machine":
                    optimized_scheme.task_allocations[task.id] = "machine"
            
            if "reliability" in optimization_criteria and optimization_criteria["reliability"] > 0.7:
                if hasattr(task.attributes, 'importance') and task.attributes.importance > 0.8:
                    if current_allocation != "collaboration":
                        optimized_scheme.task_allocations[task.id] = "collaboration"
                        optimized_scheme.collaboration_details[task.id] = {
                            'human_role': '监督和决策',
                            'machine_role': '执行和计算',
                            'coordination_method': '实时协同',
                            'confidence': 0.9
                        }
        
        # 重新计算性能和风险
        optimized_scheme.expected_performance = self._calculate_expected_performance(optimized_scheme, task_hierarchy)
        optimized_scheme.risk_assessment = self._assess_scheme_risks(optimized_scheme, task_hierarchy)
        optimized_scheme.implementation_notes = self._generate_implementation_notes(optimized_scheme, task_hierarchy)
        
        return optimized_scheme
