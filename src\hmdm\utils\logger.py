"""
日志工具

提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
import sys
from datetime import datetime


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._default_config = {
            "level": logging.INFO,
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S"
        }
    
    def get_logger(self, 
                   name: str,
                   level: Optional[int] = None,
                   log_file: Optional[str] = None,
                   console_output: bool = True) -> logging.Logger:
        """获取或创建日志器"""
        
        if name in self._loggers:
            return self._loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(level or self._default_config["level"])
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            self._default_config["format"],
            self._default_config["date_format"]
        )
        
        # 控制台输出
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件输出
        if log_file:
            # 确保日志目录存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建文件处理器（支持日志轮转）
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # 防止日志重复
        logger.propagate = False
        
        self._loggers[name] = logger
        return logger
    
    def setup_default_logging(self, 
                            log_dir: str = "logs",
                            level: int = logging.INFO) -> None:
        """设置默认日志配置"""
        
        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 生成日志文件名
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = log_path / f"hmdm_{timestamp}.log"
        
        # 配置根日志器
        root_logger = self.get_logger(
            "hmdm",
            level=level,
            log_file=str(log_file),
            console_output=True
        )
        
        # 配置各模块日志器
        modules = [
            "hmdm.task_analysis",
            "hmdm.decision",
            "hmdm.evaluation",
            "hmdm.models",
            "hmdm.utils"
        ]
        
        for module in modules:
            self.get_logger(
                module,
                level=level,
                log_file=str(log_file),
                console_output=False  # 模块日志只写文件，避免重复输出
            )
    
    def set_level(self, name: str, level: int) -> None:
        """设置日志级别"""
        if name in self._loggers:
            self._loggers[name].setLevel(level)
    
    def add_file_handler(self, 
                        name: str, 
                        log_file: str,
                        level: Optional[int] = None) -> None:
        """为指定日志器添加文件处理器"""
        if name not in self._loggers:
            return
        
        logger = self._loggers[name]
        
        # 创建文件处理器
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        
        # 设置格式器
        formatter = logging.Formatter(
            self._default_config["format"],
            self._default_config["date_format"]
        )
        file_handler.setFormatter(formatter)
        
        if level:
            file_handler.setLevel(level)
        
        logger.addHandler(file_handler)
    
    def create_performance_logger(self, log_file: str = "logs/performance.log") -> logging.Logger:
        """创建性能日志器"""
        return self.get_logger(
            "hmdm.performance",
            level=logging.DEBUG,
            log_file=log_file,
            console_output=False
        )
    
    def create_error_logger(self, log_file: str = "logs/error.log") -> logging.Logger:
        """创建错误日志器"""
        logger = self.get_logger(
            "hmdm.error",
            level=logging.ERROR,
            log_file=log_file,
            console_output=True
        )
        
        # 为错误日志器添加特殊格式
        error_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
            self._default_config["date_format"]
        )
        
        for handler in logger.handlers:
            handler.setFormatter(error_formatter)
        
        return logger


# 全局日志管理器实例
logger_manager = LoggerManager()


def get_logger(name: str, **kwargs) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name, **kwargs)


def setup_logging(log_dir: str = "logs", level: int = logging.INFO) -> None:
    """设置日志的便捷函数"""
    logger_manager.setup_default_logging(log_dir, level)


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        self.name = name
        self.logger = logger or get_logger("hmdm.performance")
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.debug(f"开始执行: {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.debug(f"完成执行: {self.name}, 耗时: {duration:.3f}秒")
        else:
            self.logger.error(f"执行失败: {self.name}, 耗时: {duration:.3f}秒, 错误: {exc_val}")
    
    @property
    def duration(self) -> Optional[float]:
        """获取执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger(f"hmdm.{func.__module__}")
        
        # 记录函数调用
        logger.debug(f"调用函数: {func.__name__}")
        
        try:
            with PerformanceTimer(func.__name__, logger):
                result = func(*args, **kwargs)
            
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_method_call(cls):
    """类方法调用日志装饰器"""
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if callable(attr) and not attr_name.startswith('_'):
            setattr(cls, attr_name, log_function_call(attr))
    return cls


# 配置默认日志
setup_logging()
