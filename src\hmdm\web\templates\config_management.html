{% extends "base.html" %}

{% block title %}配置管理 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-cogs"></i>
                    配置管理
                </h1>
                <p class="page-subtitle">统一管理系统配置、模块配置和用户配置</p>
            </div>
        </div>
    </div>

    <!-- 配置概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        配置概览
                    </h5>
                </div>
                <div class="card-body">
                    <div id="configSummary" class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary mb-1" id="systemVersion">-</h4>
                                <small class="text-muted">系统版本</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info mb-1" id="enabledModules">0</h4>
                                <small class="text-muted">启用模块</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning mb-1" id="configProfiles">0</h4>
                                <small class="text-muted">配置档案</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success mb-1" id="configStatus">-</h4>
                                <small class="text-muted">配置状态</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置管理选项卡 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="configTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-server"></i> 系统配置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="allocation-tab" data-bs-toggle="tab" data-bs-target="#allocation" type="button" role="tab">
                                <i class="fas fa-users-cog"></i> 人机分配
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="profiles-tab" data-bs-toggle="tab" data-bs-target="#profiles" type="button" role="tab">
                                <i class="fas fa-bookmark"></i> 配置档案
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-export-tab" data-bs-toggle="tab" data-bs-target="#import-export" type="button" role="tab">
                                <i class="fas fa-exchange-alt"></i> 导入导出
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="configTabContent">
                        <!-- 系统配置 -->
                        <div class="tab-pane fade show active" id="system" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>基础配置</h6>
                                    <form id="systemConfigForm">
                                        <div class="mb-3">
                                            <label class="form-label">系统名称</label>
                                            <input type="text" class="form-control" id="systemName">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">版本</label>
                                            <input type="text" class="form-control" id="systemVersion">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">环境</label>
                                            <select class="form-select" id="environment">
                                                <option value="development">开发环境</option>
                                                <option value="testing">测试环境</option>
                                                <option value="production">生产环境</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">日志级别</label>
                                            <select class="form-select" id="logLevel">
                                                <option value="DEBUG">DEBUG</option>
                                                <option value="INFO">INFO</option>
                                                <option value="WARNING">WARNING</option>
                                                <option value="ERROR">ERROR</option>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-lg-6">
                                    <h6>模块配置</h6>
                                    <div id="modulesList">
                                        <!-- 模块列表将在这里动态生成 -->
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-primary" onclick="saveSystemConfig()">
                                            <i class="fas fa-save"></i> 保存系统配置
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="resetSystemConfig()">
                                            <i class="fas fa-undo"></i> 重置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 人机分配配置 -->
                        <div class="tab-pane fade" id="allocation" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <form id="allocationConfigForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">分配模式</label>
                                                    <select class="form-select" id="allocationMode">
                                                        <option value="automatic">自动分配</option>
                                                        <option value="semi_automatic">半自动分配</option>
                                                        <option value="manual">手动分配</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">优化目标</label>
                                                    <select class="form-select" id="optimizationObjective">
                                                        <option value="efficiency">效率优先</option>
                                                        <option value="reliability">可靠性优先</option>
                                                        <option value="cost_effectiveness">成本效益优先</option>
                                                        <option value="balanced">平衡优化</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">默认方案数</label>
                                                    <input type="number" class="form-control" id="defaultSchemeCount" min="2" max="10">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">决策阈值</label>
                                                    <input type="number" class="form-control" id="decisionThreshold" min="0" max="1" step="0.01">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">置信度阈值</label>
                                                    <input type="number" class="form-control" id="confidenceThreshold" min="0" max="1" step="0.01">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <h6>能力权重配置</h6>
                                        <div class="row" id="capabilityWeights">
                                            <!-- 能力权重将在这里动态生成 -->
                                        </div>
                                        
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-primary" onclick="saveAllocationConfig()">
                                                <i class="fas fa-save"></i> 保存分配配置
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="resetAllocationConfig()">
                                                <i class="fas fa-undo"></i> 重置
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="validateAllocationConfig()">
                                                <i class="fas fa-check"></i> 验证配置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-lg-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">配置预览</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="allocationConfigPreview">
                                                <!-- 配置预览将在这里显示 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 配置档案 -->
                        <div class="tab-pane fade" id="profiles" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>配置档案列表</h6>
                                        <button type="button" class="btn btn-primary" onclick="showCreateProfileModal()">
                                            <i class="fas fa-plus"></i> 创建档案
                                        </button>
                                    </div>
                                    <div id="profilesList">
                                        <!-- 配置档案列表将在这里显示 -->
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">档案操作</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-primary" onclick="refreshProfiles()">
                                                    <i class="fas fa-sync"></i> 刷新列表
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="loadSelectedProfile()">
                                                    <i class="fas fa-download"></i> 加载选中档案
                                                </button>
                                                <button type="button" class="btn btn-outline-warning" onclick="duplicateSelectedProfile()">
                                                    <i class="fas fa-copy"></i> 复制选中档案
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteSelectedProfile()">
                                                    <i class="fas fa-trash"></i> 删除选中档案
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 导入导出 -->
                        <div class="tab-pane fade" id="import-export" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">
                                                <i class="fas fa-file-export"></i>
                                                导出配置
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="exportProfiles" checked>
                                                    <label class="form-check-label" for="exportProfiles">
                                                        包含配置档案
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">导出格式</label>
                                                <select class="form-select" id="exportFormat">
                                                    <option value="json">JSON格式</option>
                                                    <option value="yaml">YAML格式</option>
                                                </select>
                                            </div>
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-success" onclick="exportConfig()">
                                                    <i class="fas fa-download"></i> 导出配置
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">
                                                <i class="fas fa-file-import"></i>
                                                导入配置
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">选择配置文件</label>
                                                <input type="file" class="form-control" id="importFile" accept=".json,.yaml,.yml">
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="importProfiles">
                                                    <label class="form-check-label" for="importProfiles">
                                                        导入配置档案
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-warning" onclick="importConfig()">
                                                    <i class="fas fa-upload"></i> 导入配置
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">
                                                <i class="fas fa-check-circle"></i>
                                                配置验证
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <span>验证当前配置的完整性和正确性</span>
                                                <button type="button" class="btn btn-info" onclick="validateCurrentConfig()">
                                                    <i class="fas fa-search"></i> 验证配置
                                                </button>
                                            </div>
                                            <div id="validationResults">
                                                <!-- 验证结果将在这里显示 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建配置档案模态框 -->
<div class="modal fade" id="createProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建配置档案</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createProfileForm">
                    <div class="mb-3">
                        <label class="form-label">档案名称 *</label>
                        <input type="text" class="form-control" id="profileName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" id="profileDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createProfile()">创建档案</button>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.profile-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.profile-item:hover {
    background-color: #f8f9fa;
}

.profile-item.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.weight-input {
    width: 80px;
}

.validation-result {
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.validation-success {
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
    color: #0f5132;
}

.validation-error {
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
    color: #721c24;
}

.validation-warning {
    background-color: #fff3cd;
    border: 1px solid #ffecb5;
    color: #664d03;
}
</style>

<script>
let currentSystemConfig = null;
let currentAllocationConfig = null;
let selectedProfile = null;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    loadConfigSummary();
    loadSystemConfig();
    loadAllocationConfig();
    loadProfiles();
});

// 加载配置概览
function loadConfigSummary() {
    fetch('/api/system/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.data.configuration;
                document.getElementById('systemVersion').textContent = config.version || '1.0.0';
                document.getElementById('enabledModules').textContent = config.enabled_modules ? config.enabled_modules.length : 0;
                document.getElementById('configStatus').textContent = '正常';
            }
        })
        .catch(error => {
            console.error('加载配置概览失败:', error);
        });
    
    // 加载配置档案数量
    loadProfiles();
}

// 加载系统配置
function loadSystemConfig() {
    // 这里应该调用获取系统配置的API
    // 暂时使用模拟数据
    currentSystemConfig = {
        system_name: 'HMDM军事综合决策支持系统',
        version: '1.0.0',
        environment: 'production',
        log_level: 'INFO',
        enabled_modules: [
            'situation_awareness',
            'communication',
            'decision_support',
            'training',
            'knowledge_base',
            'simulation',
            'scenarios',
            'human_machine_allocation'
        ]
    };
    
    // 填充表单
    document.getElementById('systemName').value = currentSystemConfig.system_name;
    document.getElementById('systemVersion').value = currentSystemConfig.version;
    document.getElementById('environment').value = currentSystemConfig.environment;
    document.getElementById('logLevel').value = currentSystemConfig.log_level;
    
    // 生成模块列表
    generateModulesList();
}

// 生成模块列表
function generateModulesList() {
    const modulesList = document.getElementById('modulesList');
    const allModules = [
        { id: 'situation_awareness', name: '态势感知' },
        { id: 'communication', name: '通信协同' },
        { id: 'decision_support', name: '决策支持' },
        { id: 'training', name: '训练演练' },
        { id: 'knowledge_base', name: '知识库' },
        { id: 'simulation', name: '仿真建模' },
        { id: 'scenarios', name: '场景模板' },
        { id: 'human_machine_allocation', name: '人机分配' }
    ];
    
    modulesList.innerHTML = '';
    allModules.forEach(module => {
        const isEnabled = currentSystemConfig.enabled_modules.includes(module.id);
        const moduleDiv = document.createElement('div');
        moduleDiv.className = 'form-check mb-2';
        moduleDiv.innerHTML = `
            <input class="form-check-input" type="checkbox" id="module_${module.id}" ${isEnabled ? 'checked' : ''}>
            <label class="form-check-label" for="module_${module.id}">
                ${module.name}
            </label>
        `;
        modulesList.appendChild(moduleDiv);
    });
}

// 加载人机分配配置
function loadAllocationConfig() {
    fetch('/api/allocation/config')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                currentAllocationConfig = data.data;
                fillAllocationConfigForm();
                updateAllocationConfigPreview();
            }
        })
        .catch(error => {
            console.error('加载人机分配配置失败:', error);
        });
}

// 填充人机分配配置表单
function fillAllocationConfigForm() {
    if (!currentAllocationConfig) return;
    
    document.getElementById('allocationMode').value = currentAllocationConfig.allocation_mode;
    document.getElementById('optimizationObjective').value = currentAllocationConfig.optimization_objective;
    document.getElementById('defaultSchemeCount').value = currentAllocationConfig.default_scheme_count;
    document.getElementById('decisionThreshold').value = currentAllocationConfig.decision_threshold;
    document.getElementById('confidenceThreshold').value = currentAllocationConfig.confidence_threshold;
    
    // 生成能力权重配置
    generateCapabilityWeights();
}

// 生成能力权重配置
function generateCapabilityWeights() {
    const container = document.getElementById('capabilityWeights');
    const weights = currentAllocationConfig.capability_weights;
    
    container.innerHTML = '';
    Object.entries(weights).forEach(([key, value]) => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-2';
        col.innerHTML = `
            <label class="form-label small">${getCapabilityName(key)}</label>
            <input type="number" class="form-control weight-input" id="weight_${key}" 
                   value="${value}" min="0" max="1" step="0.01" onchange="updateWeightPreview()">
        `;
        container.appendChild(col);
    });
}

// 获取能力名称
function getCapabilityName(key) {
    const names = {
        'cognitive': '认知能力',
        'physical': '物理能力',
        'perceptual': '感知能力',
        'decision_making': '决策能力',
        'execution': '执行能力',
        'coordination': '协调能力'
    };
    return names[key] || key;
}

// 更新权重预览
function updateWeightPreview() {
    updateAllocationConfigPreview();
}

// 更新人机分配配置预览
function updateAllocationConfigPreview() {
    const preview = document.getElementById('allocationConfigPreview');
    if (!currentAllocationConfig) {
        preview.innerHTML = '<p class="text-muted">配置加载中...</p>';
        return;
    }
    
    preview.innerHTML = `
        <div class="mb-2">
            <strong>分配模式:</strong> ${document.getElementById('allocationMode')?.value || currentAllocationConfig.allocation_mode}
        </div>
        <div class="mb-2">
            <strong>优化目标:</strong> ${document.getElementById('optimizationObjective')?.value || currentAllocationConfig.optimization_objective}
        </div>
        <div class="mb-2">
            <strong>方案数量:</strong> ${document.getElementById('defaultSchemeCount')?.value || currentAllocationConfig.default_scheme_count}
        </div>
        <div class="mb-2">
            <strong>决策阈值:</strong> ${document.getElementById('decisionThreshold')?.value || currentAllocationConfig.decision_threshold}
        </div>
    `;
}

// 保存系统配置
function saveSystemConfig() {
    // 收集表单数据
    const config = {
        system_name: document.getElementById('systemName').value,
        version: document.getElementById('systemVersion').value,
        environment: document.getElementById('environment').value,
        log_level: document.getElementById('logLevel').value,
        enabled_modules: []
    };
    
    // 收集启用的模块
    document.querySelectorAll('#modulesList input[type="checkbox"]:checked').forEach(checkbox => {
        const moduleId = checkbox.id.replace('module_', '');
        config.enabled_modules.push(moduleId);
    });
    
    // 这里应该调用保存系统配置的API
    console.log('保存系统配置:', config);
    showToast('系统配置保存成功', 'success');
}

// 保存人机分配配置
function saveAllocationConfig() {
    const config = {
        allocation_mode: document.getElementById('allocationMode').value,
        optimization_objective: document.getElementById('optimizationObjective').value,
        default_scheme_count: parseInt(document.getElementById('defaultSchemeCount').value),
        decision_threshold: parseFloat(document.getElementById('decisionThreshold').value),
        confidence_threshold: parseFloat(document.getElementById('confidenceThreshold').value),
        capability_weights: {}
    };
    
    // 收集能力权重
    document.querySelectorAll('#capabilityWeights input').forEach(input => {
        const key = input.id.replace('weight_', '');
        config.capability_weights[key] = parseFloat(input.value);
    });
    
    fetch('/api/allocation/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('人机分配配置保存成功', 'success');
            currentAllocationConfig = config;
            updateAllocationConfigPreview();
        } else {
            showToast('保存失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showToast('保存失败: ' + error.message, 'error');
    });
}

// 验证人机分配配置
function validateAllocationConfig() {
    const config = {
        allocation_mode: document.getElementById('allocationMode').value,
        optimization_objective: document.getElementById('optimizationObjective').value,
        default_scheme_count: parseInt(document.getElementById('defaultSchemeCount').value),
        decision_threshold: parseFloat(document.getElementById('decisionThreshold').value),
        confidence_threshold: parseFloat(document.getElementById('confidenceThreshold').value),
        capability_weights: {}
    };
    
    // 收集能力权重
    document.querySelectorAll('#capabilityWeights input').forEach(input => {
        const key = input.id.replace('weight_', '');
        config.capability_weights[key] = parseFloat(input.value);
    });
    
    fetch('/api/allocation/config/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.data.is_valid) {
                showToast('配置验证通过', 'success');
            } else {
                showToast('配置验证失败', 'error');
            }
        } else {
            showToast('验证失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showToast('验证失败: ' + error.message, 'error');
    });
}

// 加载配置档案
function loadProfiles() {
    // 这里应该调用获取配置档案列表的API
    // 暂时使用模拟数据
    const profiles = [
        {
            name: '默认配置',
            description: '系统默认配置档案',
            created_time: '2024-01-01T00:00:00',
            is_current: true
        },
        {
            name: '高性能配置',
            description: '针对高性能场景优化的配置',
            created_time: '2024-01-02T00:00:00',
            is_current: false
        }
    ];
    
    const container = document.getElementById('profilesList');
    container.innerHTML = '';
    
    profiles.forEach(profile => {
        const profileDiv = document.createElement('div');
        profileDiv.className = `profile-item ${profile.is_current ? 'selected' : ''}`;
        profileDiv.onclick = () => selectProfile(profile.name);
        profileDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="mb-1">${profile.name} ${profile.is_current ? '<span class="badge bg-primary">当前</span>' : ''}</h6>
                    <p class="mb-1 text-muted small">${profile.description}</p>
                    <small class="text-muted">创建时间: ${new Date(profile.created_time).toLocaleString()}</small>
                </div>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="loadProfile('${profile.name}'); event.stopPropagation();">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteProfile('${profile.name}'); event.stopPropagation();">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(profileDiv);
    });
    
    // 更新配置档案数量
    document.getElementById('configProfiles').textContent = profiles.length;
}

// 选择配置档案
function selectProfile(name) {
    selectedProfile = name;
    document.querySelectorAll('.profile-item').forEach(item => {
        item.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
}

// 显示创建配置档案模态框
function showCreateProfileModal() {
    new bootstrap.Modal(document.getElementById('createProfileModal')).show();
}

// 创建配置档案
function createProfile() {
    const name = document.getElementById('profileName').value;
    const description = document.getElementById('profileDescription').value;
    
    if (!name) {
        showToast('请输入档案名称', 'warning');
        return;
    }
    
    // 这里应该调用创建配置档案的API
    console.log('创建配置档案:', { name, description });
    
    bootstrap.Modal.getInstance(document.getElementById('createProfileModal')).hide();
    showToast('配置档案创建成功', 'success');
    loadProfiles();
}

// 导出配置
function exportConfig() {
    const includeProfiles = document.getElementById('exportProfiles').checked;
    const format = document.getElementById('exportFormat').value;
    
    // 这里应该调用导出配置的API
    console.log('导出配置:', { includeProfiles, format });
    showToast('配置导出成功', 'success');
}

// 导入配置
function importConfig() {
    const fileInput = document.getElementById('importFile');
    const includeProfiles = document.getElementById('importProfiles').checked;
    
    if (!fileInput.files.length) {
        showToast('请选择配置文件', 'warning');
        return;
    }
    
    // 这里应该处理文件上传和导入
    console.log('导入配置:', { file: fileInput.files[0], includeProfiles });
    showToast('配置导入成功', 'success');
}

// 验证当前配置
function validateCurrentConfig() {
    const resultsDiv = document.getElementById('validationResults');
    
    // 这里应该调用验证配置的API
    // 暂时使用模拟结果
    const results = {
        is_valid: true,
        errors: [],
        warnings: ['某些模块未启用']
    };
    
    resultsDiv.innerHTML = '';
    
    if (results.is_valid) {
        const successDiv = document.createElement('div');
        successDiv.className = 'validation-result validation-success';
        successDiv.innerHTML = '<i class="fas fa-check-circle"></i> 配置验证通过';
        resultsDiv.appendChild(successDiv);
    }
    
    results.warnings.forEach(warning => {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'validation-result validation-warning';
        warningDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${warning}`;
        resultsDiv.appendChild(warningDiv);
    });
    
    results.errors.forEach(error => {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-result validation-error';
        errorDiv.innerHTML = `<i class="fas fa-times-circle"></i> ${error}`;
        resultsDiv.appendChild(errorDiv);
    });
}

// 重置配置
function resetSystemConfig() {
    if (confirm('确定要重置系统配置吗？')) {
        loadSystemConfig();
        showToast('系统配置已重置', 'info');
    }
}

function resetAllocationConfig() {
    if (confirm('确定要重置人机分配配置吗？')) {
        loadAllocationConfig();
        showToast('人机分配配置已重置', 'info');
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 简单的提示实现
    alert(message);
}
</script>
{% endblock %}
