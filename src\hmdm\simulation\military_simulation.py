"""
军事仿真与建模系统

提供军事作战仿真、态势推演、效果评估等功能。
"""

import numpy as np
import logging
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import math

from ..models.task_models import Task
from ..security.military_security import SecurityLevel


class SimulationType(Enum):
    """仿真类型"""
    COMBAT = "作战仿真"
    LOGISTICS = "后勤仿真"
    COMMUNICATION = "通信仿真"
    TRAINING = "训练仿真"
    STRATEGIC = "战略仿真"
    TACTICAL = "战术仿真"


class EntityType(Enum):
    """实体类型"""
    UNIT = "作战单位"
    VEHICLE = "载具"
    AIRCRAFT = "飞行器"
    SHIP = "舰船"
    FACILITY = "设施"
    SENSOR = "传感器"
    WEAPON = "武器系统"


class EntityState(Enum):
    """实体状态"""
    ACTIVE = "活跃"
    INACTIVE = "非活跃"
    DAMAGED = "受损"
    DESTROYED = "摧毁"
    MAINTENANCE = "维护"


@dataclass
class Position:
    """位置信息"""
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    
    def distance_to(self, other: 'Position') -> float:
        """计算到另一位置的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)
    
    def move_towards(self, target: 'Position', speed: float, time_delta: float) -> 'Position':
        """向目标位置移动"""
        distance = self.distance_to(target)
        if distance <= speed * time_delta:
            return Position(target.x, target.y, target.z)
        
        # 计算移动方向
        dx = (target.x - self.x) / distance
        dy = (target.y - self.y) / distance
        dz = (target.z - self.z) / distance
        
        # 计算新位置
        move_distance = speed * time_delta
        new_x = self.x + dx * move_distance
        new_y = self.y + dy * move_distance
        new_z = self.z + dz * move_distance
        
        return Position(new_x, new_y, new_z)


@dataclass
class SimulationEntity:
    """仿真实体"""
    id: str
    name: str
    entity_type: EntityType
    position: Position = field(default_factory=Position)
    state: EntityState = EntityState.ACTIVE
    allegiance: str = "friendly"  # friendly, enemy, neutral, unknown
    
    # 能力参数
    max_speed: float = 10.0  # 最大速度 (m/s)
    detection_range: float = 1000.0  # 探测距离 (m)
    weapon_range: float = 500.0  # 武器射程 (m)
    armor: float = 100.0  # 装甲值
    health: float = 100.0  # 生命值
    fuel: float = 100.0  # 燃料
    ammunition: int = 100  # 弹药
    
    # 任务参数
    current_task: Optional[str] = None
    target_position: Optional[Position] = None
    target_entity: Optional[str] = None
    
    # 历史记录
    position_history: List[Tuple[datetime, Position]] = field(default_factory=list)
    action_history: List[Tuple[datetime, str]] = field(default_factory=list)
    
    def update_position(self, new_position: Position, timestamp: datetime = None) -> None:
        """更新位置"""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.position_history.append((timestamp, Position(self.position.x, self.position.y, self.position.z)))
        self.position = new_position
    
    def take_damage(self, damage: float) -> bool:
        """受到伤害"""
        effective_damage = max(0, damage - self.armor * 0.1)
        self.health -= effective_damage
        
        if self.health <= 0:
            self.state = EntityState.DESTROYED
            return True
        elif self.health <= 30:
            self.state = EntityState.DAMAGED
        
        return False
    
    def can_detect(self, target: 'SimulationEntity') -> bool:
        """是否能探测到目标"""
        if self.state != EntityState.ACTIVE:
            return False
        
        distance = self.position.distance_to(target.position)
        return distance <= self.detection_range
    
    def can_engage(self, target: 'SimulationEntity') -> bool:
        """是否能攻击目标"""
        if not self.can_detect(target) or self.ammunition <= 0:
            return False
        
        distance = self.position.distance_to(target.position)
        return distance <= self.weapon_range
    
    def engage_target(self, target: 'SimulationEntity') -> Tuple[bool, float]:
        """攻击目标"""
        if not self.can_engage(target):
            return False, 0.0
        
        # 计算命中概率
        distance = self.position.distance_to(target.position)
        hit_probability = max(0.1, 1.0 - distance / self.weapon_range)
        
        # 判断是否命中
        if random.random() < hit_probability:
            damage = random.uniform(20, 50)  # 随机伤害
            target.take_damage(damage)
            self.ammunition -= 1
            
            # 记录行动
            self.action_history.append((datetime.now(), f"攻击 {target.id}，造成 {damage:.1f} 伤害"))
            
            return True, damage
        else:
            self.ammunition -= 1
            self.action_history.append((datetime.now(), f"攻击 {target.id} 未命中"))
            return False, 0.0


@dataclass
class SimulationScenario:
    """仿真场景"""
    id: str
    name: str
    description: str
    simulation_type: SimulationType
    duration: float = 3600.0  # 仿真时长（秒）
    time_step: float = 1.0  # 时间步长（秒）
    
    # 环境参数
    terrain_type: str = "平原"
    weather_condition: str = "晴朗"
    visibility: float = 1.0  # 可见度系数
    
    # 场景实体
    entities: Dict[str, SimulationEntity] = field(default_factory=dict)
    
    # 事件列表
    events: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_entity(self, entity: SimulationEntity) -> None:
        """添加实体"""
        self.entities[entity.id] = entity
    
    def remove_entity(self, entity_id: str) -> None:
        """移除实体"""
        if entity_id in self.entities:
            del self.entities[entity_id]
    
    def get_entities_by_allegiance(self, allegiance: str) -> List[SimulationEntity]:
        """按阵营获取实体"""
        return [entity for entity in self.entities.values() if entity.allegiance == allegiance]
    
    def get_active_entities(self) -> List[SimulationEntity]:
        """获取活跃实体"""
        return [entity for entity in self.entities.values() if entity.state == EntityState.ACTIVE]


class MilitarySimulationEngine:
    """军事仿真引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 仿真状态
        self.current_scenario: Optional[SimulationScenario] = None
        self.current_time: float = 0.0
        self.is_running: bool = False
        
        # 仿真结果
        self.simulation_results: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {}
        
        # 事件处理器
        self.event_handlers = {
            "move": self._handle_move_event,
            "attack": self._handle_attack_event,
            "supply": self._handle_supply_event,
            "communication": self._handle_communication_event
        }
    
    def load_scenario(self, scenario: SimulationScenario) -> bool:
        """加载仿真场景"""
        try:
            self.current_scenario = scenario
            self.current_time = 0.0
            self.is_running = False
            
            self.logger.info(f"加载仿真场景: {scenario.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载场景失败: {e}")
            return False
    
    def run_simulation(self, real_time: bool = False) -> Dict[str, Any]:
        """运行仿真"""
        if not self.current_scenario:
            return {"error": "未加载仿真场景"}
        
        try:
            self.is_running = True
            self.current_time = 0.0
            
            scenario = self.current_scenario
            results = {
                "scenario_id": scenario.id,
                "start_time": datetime.now(),
                "duration": scenario.duration,
                "time_steps": [],
                "final_state": {},
                "statistics": {}
            }
            
            self.logger.info(f"开始仿真: {scenario.name}")
            
            # 仿真主循环
            while self.current_time < scenario.duration and self.is_running:
                step_result = self._simulate_time_step()
                results["time_steps"].append(step_result)
                
                self.current_time += scenario.time_step
                
                # 实时模式下的延迟
                if real_time:
                    import time
                    time.sleep(scenario.time_step)
            
            # 记录最终状态
            results["final_state"] = self._get_scenario_state()
            results["statistics"] = self._calculate_statistics()
            results["end_time"] = datetime.now()
            
            self.simulation_results.append(results)
            self.logger.info(f"仿真完成: {scenario.name}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"仿真运行失败: {e}")
            return {"error": str(e)}
    
    def pause_simulation(self) -> None:
        """暂停仿真"""
        self.is_running = False
        self.logger.info("仿真已暂停")
    
    def resume_simulation(self) -> None:
        """恢复仿真"""
        self.is_running = True
        self.logger.info("仿真已恢复")
    
    def stop_simulation(self) -> None:
        """停止仿真"""
        self.is_running = False
        self.current_time = 0.0
        self.logger.info("仿真已停止")
    
    def get_simulation_state(self) -> Dict[str, Any]:
        """获取仿真状态"""
        if not self.current_scenario:
            return {}
        
        return {
            "scenario_id": self.current_scenario.id,
            "current_time": self.current_time,
            "is_running": self.is_running,
            "entities": {
                entity_id: {
                    "position": [entity.position.x, entity.position.y, entity.position.z],
                    "state": entity.state.value,
                    "health": entity.health,
                    "fuel": entity.fuel,
                    "ammunition": entity.ammunition
                }
                for entity_id, entity in self.current_scenario.entities.items()
            }
        }
    
    def analyze_simulation_results(self, result_id: int = -1) -> Dict[str, Any]:
        """分析仿真结果"""
        if not self.simulation_results:
            return {"error": "无仿真结果"}
        
        try:
            result = self.simulation_results[result_id]
            
            analysis = {
                "scenario_info": {
                    "id": result["scenario_id"],
                    "duration": result["duration"],
                    "total_steps": len(result["time_steps"])
                },
                "entity_analysis": self._analyze_entities(result),
                "engagement_analysis": self._analyze_engagements(result),
                "movement_analysis": self._analyze_movements(result),
                "performance_metrics": result.get("statistics", {}),
                "recommendations": self._generate_recommendations(result)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"结果分析失败: {e}")
            return {"error": str(e)}
    
    def _simulate_time_step(self) -> Dict[str, Any]:
        """模拟一个时间步"""
        if not self.current_scenario:
            return {}
        
        step_result = {
            "time": self.current_time,
            "events": [],
            "entity_states": {}
        }
        
        # 处理实体行为
        for entity in self.current_scenario.get_active_entities():
            # 移动逻辑
            if entity.target_position:
                new_position = entity.position.move_towards(
                    entity.target_position, entity.max_speed, self.current_scenario.time_step
                )
                entity.update_position(new_position)
            
            # 探测和攻击逻辑
            if entity.allegiance == "friendly":
                enemies = self.current_scenario.get_entities_by_allegiance("enemy")
                for enemy in enemies:
                    if entity.can_engage(enemy):
                        hit, damage = entity.engage_target(enemy)
                        step_result["events"].append({
                            "type": "attack",
                            "attacker": entity.id,
                            "target": enemy.id,
                            "hit": hit,
                            "damage": damage
                        })
            
            # 记录实体状态
            step_result["entity_states"][entity.id] = {
                "position": [entity.position.x, entity.position.y, entity.position.z],
                "state": entity.state.value,
                "health": entity.health,
                "fuel": entity.fuel,
                "ammunition": entity.ammunition
            }
        
        # 处理场景事件
        for event in self.current_scenario.events:
            if event.get("time", 0) <= self.current_time:
                self._process_scenario_event(event)
                step_result["events"].append(event)
        
        return step_result
    
    def _get_scenario_state(self) -> Dict[str, Any]:
        """获取场景最终状态"""
        if not self.current_scenario:
            return {}
        
        state = {
            "entities": {},
            "casualties": {"friendly": 0, "enemy": 0, "neutral": 0},
            "survivors": {"friendly": 0, "enemy": 0, "neutral": 0}
        }
        
        for entity in self.current_scenario.entities.values():
            state["entities"][entity.id] = {
                "final_position": [entity.position.x, entity.position.y, entity.position.z],
                "final_state": entity.state.value,
                "final_health": entity.health,
                "ammunition_used": 100 - entity.ammunition
            }
            
            if entity.state == EntityState.DESTROYED:
                state["casualties"][entity.allegiance] += 1
            else:
                state["survivors"][entity.allegiance] += 1
        
        return state
    
    def _calculate_statistics(self) -> Dict[str, float]:
        """计算统计数据"""
        if not self.current_scenario:
            return {}
        
        stats = {
            "total_entities": len(self.current_scenario.entities),
            "active_entities": len(self.current_scenario.get_active_entities()),
            "casualty_rate": 0.0,
            "ammunition_consumption": 0.0,
            "average_movement": 0.0
        }
        
        destroyed_count = 0
        total_ammo_used = 0
        total_movement = 0.0
        
        for entity in self.current_scenario.entities.values():
            if entity.state == EntityState.DESTROYED:
                destroyed_count += 1
            
            total_ammo_used += (100 - entity.ammunition)
            
            if entity.position_history:
                for i in range(1, len(entity.position_history)):
                    prev_pos = entity.position_history[i-1][1]
                    curr_pos = entity.position_history[i][1]
                    total_movement += prev_pos.distance_to(curr_pos)
        
        if stats["total_entities"] > 0:
            stats["casualty_rate"] = destroyed_count / stats["total_entities"]
            stats["ammunition_consumption"] = total_ammo_used / stats["total_entities"]
            stats["average_movement"] = total_movement / stats["total_entities"]
        
        return stats
    
    def _process_scenario_event(self, event: Dict[str, Any]) -> None:
        """处理场景事件"""
        event_type = event.get("type", "")
        if event_type in self.event_handlers:
            self.event_handlers[event_type](event)
    
    def _handle_move_event(self, event: Dict[str, Any]) -> None:
        """处理移动事件"""
        entity_id = event.get("entity_id")
        target_pos = event.get("target_position", [0, 0, 0])
        
        if entity_id in self.current_scenario.entities:
            entity = self.current_scenario.entities[entity_id]
            entity.target_position = Position(target_pos[0], target_pos[1], target_pos[2])
    
    def _handle_attack_event(self, event: Dict[str, Any]) -> None:
        """处理攻击事件"""
        attacker_id = event.get("attacker_id")
        target_id = event.get("target_id")
        
        if (attacker_id in self.current_scenario.entities and 
            target_id in self.current_scenario.entities):
            attacker = self.current_scenario.entities[attacker_id]
            target = self.current_scenario.entities[target_id]
            attacker.engage_target(target)
    
    def _handle_supply_event(self, event: Dict[str, Any]) -> None:
        """处理补给事件"""
        entity_id = event.get("entity_id")
        supply_type = event.get("supply_type", "fuel")
        amount = event.get("amount", 50)
        
        if entity_id in self.current_scenario.entities:
            entity = self.current_scenario.entities[entity_id]
            if supply_type == "fuel":
                entity.fuel = min(100, entity.fuel + amount)
            elif supply_type == "ammunition":
                entity.ammunition = min(100, entity.ammunition + amount)
    
    def _handle_communication_event(self, event: Dict[str, Any]) -> None:
        """处理通信事件"""
        # 通信事件处理逻辑
        pass
    
    def _analyze_entities(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """分析实体表现"""
        return {
            "total_entities": len(result.get("final_state", {}).get("entities", {})),
            "survival_rate": 1.0 - result.get("statistics", {}).get("casualty_rate", 0.0),
            "effectiveness": result.get("statistics", {}).get("ammunition_consumption", 0.0)
        }
    
    def _analyze_engagements(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """分析交战情况"""
        total_attacks = 0
        successful_attacks = 0
        
        for step in result.get("time_steps", []):
            for event in step.get("events", []):
                if event.get("type") == "attack":
                    total_attacks += 1
                    if event.get("hit", False):
                        successful_attacks += 1
        
        hit_rate = successful_attacks / max(1, total_attacks)
        
        return {
            "total_attacks": total_attacks,
            "successful_attacks": successful_attacks,
            "hit_rate": hit_rate
        }
    
    def _analyze_movements(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """分析移动情况"""
        return {
            "average_movement": result.get("statistics", {}).get("average_movement", 0.0),
            "mobility_effectiveness": 0.8  # 简化的机动性评估
        }
    
    def _generate_recommendations(self, result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        casualty_rate = result.get("statistics", {}).get("casualty_rate", 0.0)
        if casualty_rate > 0.3:
            recommendations.append("伤亡率较高，建议改进战术或增强防护")
        
        hit_rate = self._analyze_engagements(result).get("hit_rate", 0.0)
        if hit_rate < 0.5:
            recommendations.append("命中率较低，建议加强训练或改进武器系统")
        
        ammo_consumption = result.get("statistics", {}).get("ammunition_consumption", 0.0)
        if ammo_consumption > 80:
            recommendations.append("弹药消耗过大，建议优化射击策略")
        
        return recommendations
