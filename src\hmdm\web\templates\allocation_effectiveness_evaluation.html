{% extends "base.html" %}

{% block title %}效能评估 - HMDM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar"></i>
                    效能评估
                </h1>
                <p class="page-subtitle">多维度评估分配方案的协同效能和风险</p>
            </div>
        </div>
    </div>

    <!-- 评估概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        评估概览
                    </h5>
                </div>
                <div class="card-body">
                    <div id="evaluationSummary" class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary mb-1" id="totalSchemes">0</h4>
                                <small class="text-muted">待评估方案</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info mb-1" id="bestScore">0.0</h4>
                                <small class="text-muted">最高评分</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning mb-1" id="avgScore">0.0</h4>
                                <small class="text-muted">平均评分</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success mb-1" id="recommendedScheme">-</h4>
                                <small class="text-muted">推荐方案</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细评估结果 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i>
                        效能分析图表
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-4">
                            <canvas id="effectivenessChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-12">
                            <canvas id="riskAnalysisChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-award"></i>
                        推荐方案
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recommendedSchemeDetail">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>正在分析方案...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        风险提示
                    </h5>
                </div>
                <div class="card-body">
                    <div id="riskWarnings">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <p>暂无风险提示</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">评估操作</h6>
                            <small class="text-muted">导出评估报告或查看决策结果</small>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-info" onclick="exportEvaluation()">
                                <i class="fas fa-file-export"></i> 导出报告
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="backToComparison()">
                                <i class="fas fa-arrow-left"></i> 返回比较
                            </button>
                            <button type="button" class="btn btn-primary" onclick="proceedToDecision()">
                                <i class="fas fa-arrow-right"></i> 查看决策
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.risk-item {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    border-left: 4px solid;
}

.risk-high {
    background-color: #f8d7da;
    border-left-color: #dc3545;
}

.risk-medium {
    background-color: #fff3cd;
    border-left-color: #ffc107;
}

.risk-low {
    background-color: #d1e7dd;
    border-left-color: #198754;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let comparisonResults = null;
let evaluationResults = null;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 从sessionStorage加载比较结果
    const comparisonData = sessionStorage.getItem('comparisonResults');
    if (comparisonData) {
        comparisonResults = JSON.parse(comparisonData);
        performEvaluation();
    } else {
        showToast('未找到比较结果，请先进行方案比较', 'warning');
        setTimeout(() => {
            window.location.href = '/allocation/scheme-comparison';
        }, 2000);
        return;
    }
});

// 执行效能评估
function performEvaluation() {
    if (!comparisonResults) return;
    
    // 模拟评估过程
    showToast('正在进行效能评估...', 'info');
    
    setTimeout(() => {
        evaluationResults = generateEvaluationResults();
        displayEvaluationSummary();
        displayRecommendedScheme();
        displayRiskWarnings();
        drawCharts();
        
        showToast('效能评估完成', 'success');
    }, 2000);
}

// 生成评估结果
function generateEvaluationResults() {
    return comparisonResults.map(scheme => {
        const effectiveness = {
            task_completion_effectiveness: scheme.metrics.task_completion * (0.9 + Math.random() * 0.2),
            time_effectiveness: scheme.metrics.time_efficiency * (0.9 + Math.random() * 0.2),
            resource_effectiveness: scheme.metrics.resource_utilization * (0.9 + Math.random() * 0.2),
            collaboration_effectiveness: (1 - scheme.metrics.coordination_overhead) * (0.8 + Math.random() * 0.4),
            adaptability_effectiveness: scheme.metrics.adaptability * (0.9 + Math.random() * 0.2)
        };
        
        const risks = {
            execution_risk: scheme.metrics.error_rate + Math.random() * 0.1,
            coordination_risk: scheme.metrics.coordination_overhead + Math.random() * 0.1,
            adaptation_risk: (1 - scheme.metrics.adaptability) * (0.8 + Math.random() * 0.4),
            resource_risk: (1 - scheme.metrics.resource_utilization) * (0.7 + Math.random() * 0.6)
        };
        
        // 计算综合效能评分
        const effectiveness_score = Object.values(effectiveness).reduce((sum, val) => sum + val, 0) / Object.keys(effectiveness).length;
        const risk_score = Object.values(risks).reduce((sum, val) => sum + val, 0) / Object.keys(risks).length;
        const overall_effectiveness = effectiveness_score * (1 - risk_score * 0.5);
        
        return {
            ...scheme,
            effectiveness,
            risks,
            effectiveness_score,
            risk_score,
            overall_effectiveness
        };
    }).sort((a, b) => b.overall_effectiveness - a.overall_effectiveness);
}

// 显示评估概览
function displayEvaluationSummary() {
    if (!evaluationResults) return;
    
    const totalSchemes = evaluationResults.length;
    const bestScore = Math.max(...evaluationResults.map(s => s.overall_effectiveness));
    const avgScore = evaluationResults.reduce((sum, s) => sum + s.overall_effectiveness, 0) / totalSchemes;
    const recommendedScheme = evaluationResults[0].name;
    
    document.getElementById('totalSchemes').textContent = totalSchemes;
    document.getElementById('bestScore').textContent = (bestScore * 100).toFixed(1);
    document.getElementById('avgScore').textContent = (avgScore * 100).toFixed(1);
    document.getElementById('recommendedScheme').textContent = recommendedScheme;
}

// 显示推荐方案
function displayRecommendedScheme() {
    if (!evaluationResults || evaluationResults.length === 0) return;
    
    const recommended = evaluationResults[0];
    const container = document.getElementById('recommendedSchemeDetail');
    
    container.innerHTML = `
        <div class="text-center mb-3">
            <h5 class="text-primary">${recommended.name}</h5>
            <div class="badge bg-success fs-6">推荐度: ${(recommended.overall_effectiveness * 100).toFixed(1)}%</div>
        </div>
        
        <h6>效能指标</h6>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <small>任务完成效能</small>
                <small>${(recommended.effectiveness.task_completion_effectiveness * 100).toFixed(1)}%</small>
            </div>
            <div class="progress mb-1" style="height: 6px;">
                <div class="progress-bar bg-success" style="width: ${recommended.effectiveness.task_completion_effectiveness * 100}%"></div>
            </div>
            
            <div class="d-flex justify-content-between">
                <small>时间效能</small>
                <small>${(recommended.effectiveness.time_effectiveness * 100).toFixed(1)}%</small>
            </div>
            <div class="progress mb-1" style="height: 6px;">
                <div class="progress-bar bg-info" style="width: ${recommended.effectiveness.time_effectiveness * 100}%"></div>
            </div>
            
            <div class="d-flex justify-content-between">
                <small>协同效能</small>
                <small>${(recommended.effectiveness.collaboration_effectiveness * 100).toFixed(1)}%</small>
            </div>
            <div class="progress mb-1" style="height: 6px;">
                <div class="progress-bar bg-warning" style="width: ${recommended.effectiveness.collaboration_effectiveness * 100}%"></div>
            </div>
        </div>
        
        <h6>风险评估</h6>
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <small>执行风险</small>
                <small>${(recommended.risks.execution_risk * 100).toFixed(1)}%</small>
            </div>
            <div class="progress mb-1" style="height: 6px;">
                <div class="progress-bar bg-danger" style="width: ${recommended.risks.execution_risk * 100}%"></div>
            </div>
            
            <div class="d-flex justify-content-between">
                <small>协调风险</small>
                <small>${(recommended.risks.coordination_risk * 100).toFixed(1)}%</small>
            </div>
            <div class="progress mb-1" style="height: 6px;">
                <div class="progress-bar bg-warning" style="width: ${recommended.risks.coordination_risk * 100}%"></div>
            </div>
        </div>
        
        <div class="d-grid">
            <button type="button" class="btn btn-primary btn-sm" onclick="selectRecommended()">
                <i class="fas fa-check"></i> 选择此方案
            </button>
        </div>
    `;
}

// 显示风险提示
function displayRiskWarnings() {
    if (!evaluationResults) return;
    
    const container = document.getElementById('riskWarnings');
    const warnings = [];
    
    // 分析风险
    evaluationResults.forEach(scheme => {
        if (scheme.risks.execution_risk > 0.3) {
            warnings.push({
                level: 'high',
                message: `${scheme.name}: 执行风险较高 (${(scheme.risks.execution_risk * 100).toFixed(1)}%)`
            });
        }
        
        if (scheme.risks.coordination_risk > 0.4) {
            warnings.push({
                level: 'medium',
                message: `${scheme.name}: 协调风险需要关注 (${(scheme.risks.coordination_risk * 100).toFixed(1)}%)`
            });
        }
    });
    
    if (warnings.length === 0) {
        container.innerHTML = `
            <div class="text-center text-success py-4">
                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                <p>所有方案风险可控</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = warnings.map(warning => `
        <div class="risk-item risk-${warning.level}">
            <small>${warning.message}</small>
        </div>
    `).join('');
}

// 绘制图表
function drawCharts() {
    drawEffectivenessChart();
    drawRiskAnalysisChart();
}

// 绘制效能图表
function drawEffectivenessChart() {
    const ctx = document.getElementById('effectivenessChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: evaluationResults.map(s => s.name),
            datasets: [{
                label: '综合效能',
                data: evaluationResults.map(s => s.overall_effectiveness * 100),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: '效能评分',
                data: evaluationResults.map(s => s.effectiveness_score * 100),
                backgroundColor: 'rgba(75, 192, 192, 0.8)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '方案效能对比分析'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 绘制风险分析图表
function drawRiskAnalysisChart() {
    const ctx = document.getElementById('riskAnalysisChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: evaluationResults.map(s => s.name),
            datasets: [{
                label: '执行风险',
                data: evaluationResults.map(s => s.risks.execution_risk * 100),
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }, {
                label: '协调风险',
                data: evaluationResults.map(s => s.risks.coordination_risk * 100),
                borderColor: 'rgba(255, 159, 64, 1)',
                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                tension: 0.1
            }, {
                label: '适应风险',
                data: evaluationResults.map(s => s.risks.adaptation_risk * 100),
                borderColor: 'rgba(153, 102, 255, 1)',
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '方案风险分析'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 选择推荐方案
function selectRecommended() {
    if (!evaluationResults || evaluationResults.length === 0) return;
    
    const recommended = evaluationResults[0];
    sessionStorage.setItem('selectedScheme', JSON.stringify(recommended));
    
    showToast('已选择推荐方案', 'success');
    setTimeout(() => {
        proceedToDecision();
    }, 1000);
}

// 导出评估报告
function exportEvaluation() {
    if (!evaluationResults) {
        showToast('没有评估结果可以导出', 'warning');
        return;
    }
    
    const report = {
        evaluation_date: new Date().toISOString(),
        summary: {
            total_schemes: evaluationResults.length,
            best_scheme: evaluationResults[0],
            average_effectiveness: evaluationResults.reduce((sum, s) => sum + s.overall_effectiveness, 0) / evaluationResults.length
        },
        detailed_results: evaluationResults,
        recommendations: {
            primary: evaluationResults[0],
            alternatives: evaluationResults.slice(1, 3)
        }
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'effectiveness_evaluation_report.json';
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('评估报告导出成功', 'success');
}

// 返回比较
function backToComparison() {
    window.location.href = '/allocation/scheme-comparison';
}

// 进行决策
function proceedToDecision() {
    if (!evaluationResults) {
        showToast('请先完成效能评估', 'warning');
        return;
    }
    
    // 保存评估结果到sessionStorage
    sessionStorage.setItem('evaluationResults', JSON.stringify(evaluationResults));
    
    // 跳转到决策结果页面
    window.location.href = '/allocation/decision-result';
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 简单的提示实现
    alert(message);
}
</script>
{% endblock %}
