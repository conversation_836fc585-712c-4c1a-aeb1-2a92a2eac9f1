"""
动态任务管理器

支持军事环境下的动态任务重分配、实时任务调整和自适应任务优化。
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
from queue import Queue, PriorityQueue

from ..models.task_models import Task, TaskType, ExecutorType, TaskAttribute
from ..security.military_security import SecurityLevel, MilitarySecurityManager


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "待执行"
    ASSIGNED = "已分配"
    EXECUTING = "执行中"
    COMPLETED = "已完成"
    FAILED = "执行失败"
    CANCELLED = "已取消"
    REASSIGNED = "重新分配"


class ResourceType(Enum):
    """资源类型"""
    HUMAN = "人员"
    MACHINE = "机器"
    HYBRID = "人机协同"
    SYSTEM = "系统"


@dataclass
class TaskResource:
    """任务资源"""
    id: str
    name: str
    type: ResourceType
    capacity: float = 1.0  # 资源容量
    current_load: float = 0.0  # 当前负载
    availability: float = 1.0  # 可用性 (0-1)
    capabilities: List[str] = field(default_factory=list)  # 能力列表
    location: str = ""  # 位置信息
    security_clearance: SecurityLevel = SecurityLevel.SECRET
    
    def is_available(self) -> bool:
        """检查资源是否可用"""
        return self.availability > 0.5 and self.current_load < self.capacity * 0.9
    
    def can_handle_task(self, task: Task) -> bool:
        """检查是否能处理指定任务"""
        # 检查执行者类型匹配
        if task.executor_type == ExecutorType.HUMAN and self.type not in [ResourceType.HUMAN, ResourceType.HYBRID]:
            return False
        if task.executor_type == ExecutorType.MACHINE and self.type not in [ResourceType.MACHINE, ResourceType.HYBRID]:
            return False
        
        # 检查能力匹配
        if hasattr(task, 'required_capabilities'):
            for capability in task.required_capabilities:
                if capability not in self.capabilities:
                    return False
        
        return True


@dataclass
class DynamicTask:
    """动态任务"""
    task: Task
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    assigned_resources: List[str] = field(default_factory=list)  # 分配的资源ID列表
    deadline: Optional[datetime] = None
    estimated_duration: float = 0.0  # 预估执行时间（分钟）
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    progress: float = 0.0  # 执行进度 (0-1)
    dependencies: List[str] = field(default_factory=list)  # 依赖任务ID列表
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    reassignment_count: int = 0  # 重分配次数
    
    def is_ready_to_execute(self, completed_tasks: set) -> bool:
        """检查任务是否准备好执行"""
        if self.status != TaskStatus.ASSIGNED:
            return False
        
        # 检查依赖任务是否完成
        for dep_id in self.dependencies:
            if dep_id not in completed_tasks:
                return False
        
        return True
    
    def is_overdue(self) -> bool:
        """检查任务是否超期"""
        if not self.deadline:
            return False
        return datetime.now() > self.deadline
    
    def get_urgency_score(self) -> float:
        """计算任务紧急度分数"""
        base_score = self.priority.value
        
        # 考虑截止时间
        if self.deadline:
            time_left = (self.deadline - datetime.now()).total_seconds() / 3600  # 小时
            if time_left < 1:
                base_score += 3
            elif time_left < 4:
                base_score += 2
            elif time_left < 12:
                base_score += 1
        
        # 考虑重分配次数（重分配次数多的任务优先级提高）
        base_score += self.reassignment_count * 0.5
        
        return base_score


class DynamicTaskManager:
    """动态任务管理器"""
    
    def __init__(self, security_manager: Optional[MilitarySecurityManager] = None):
        self.logger = logging.getLogger(__name__)
        self.security_manager = security_manager
        
        # 任务和资源管理
        self.tasks: Dict[str, DynamicTask] = {}
        self.resources: Dict[str, TaskResource] = {}
        self.task_queue = PriorityQueue()
        self.completed_tasks: set = set()
        
        # 监控和统计
        self.performance_metrics = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_completion_time": 0.0,
            "resource_utilization": 0.0,
            "reassignment_rate": 0.0
        }
        
        # 动态调整参数
        self.rebalance_interval = 30  # 重平衡间隔（秒）
        self.max_reassignments = 3  # 最大重分配次数
        self.load_threshold = 0.8  # 负载阈值
        
        # 启动后台监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_tasks, daemon=True)
        self.monitor_thread.start()
    
    def add_resource(self, resource: TaskResource) -> bool:
        """添加任务资源"""
        if resource.id in self.resources:
            self.logger.warning(f"资源 {resource.id} 已存在")
            return False
        
        self.resources[resource.id] = resource
        self.logger.info(f"添加资源: {resource.name} ({resource.type.value})")
        return True
    
    def submit_task(self, task: Task, priority: TaskPriority = TaskPriority.NORMAL,
                   deadline: Optional[datetime] = None,
                   estimated_duration: float = 60.0,
                   dependencies: List[str] = None) -> str:
        """提交任务"""
        dynamic_task = DynamicTask(
            task=task,
            priority=priority,
            deadline=deadline,
            estimated_duration=estimated_duration,
            dependencies=dependencies or []
        )
        
        self.tasks[task.id] = dynamic_task
        self.performance_metrics["total_tasks"] += 1
        
        # 尝试立即分配资源
        if self._assign_resources(dynamic_task):
            dynamic_task.status = TaskStatus.ASSIGNED
            # 添加到执行队列
            urgency_score = dynamic_task.get_urgency_score()
            self.task_queue.put((-urgency_score, time.time(), task.id))  # 负数用于优先级排序
        
        self.logger.info(f"提交任务: {task.name} (优先级: {priority.value})")
        return task.id
    
    def reassign_task(self, task_id: str, reason: str = "性能优化") -> bool:
        """重新分配任务"""
        if task_id not in self.tasks:
            return False
        
        dynamic_task = self.tasks[task_id]
        
        # 检查重分配次数限制
        if dynamic_task.reassignment_count >= self.max_reassignments:
            self.logger.warning(f"任务 {task_id} 已达到最大重分配次数")
            return False
        
        # 释放当前资源
        self._release_resources(dynamic_task)
        
        # 重新分配资源
        if self._assign_resources(dynamic_task):
            dynamic_task.status = TaskStatus.REASSIGNED
            dynamic_task.reassignment_count += 1
            dynamic_task.updated_at = datetime.now()
            
            # 重新加入执行队列
            urgency_score = dynamic_task.get_urgency_score()
            self.task_queue.put((-urgency_score, time.time(), task_id))
            
            self.logger.info(f"重新分配任务 {task_id}: {reason}")
            return True
        
        return False
    
    def update_task_progress(self, task_id: str, progress: float) -> bool:
        """更新任务进度"""
        if task_id not in self.tasks:
            return False
        
        dynamic_task = self.tasks[task_id]
        dynamic_task.progress = max(0.0, min(1.0, progress))
        dynamic_task.updated_at = datetime.now()
        
        # 如果任务完成
        if progress >= 1.0:
            self._complete_task(dynamic_task)
        
        return True
    
    def cancel_task(self, task_id: str, reason: str = "用户取消") -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
        
        dynamic_task = self.tasks[task_id]
        
        # 释放资源
        self._release_resources(dynamic_task)
        
        dynamic_task.status = TaskStatus.CANCELLED
        dynamic_task.updated_at = datetime.now()
        
        self.logger.info(f"取消任务 {task_id}: {reason}")
        return True
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
        
        dynamic_task = self.tasks[task_id]
        return {
            "task_id": task_id,
            "name": dynamic_task.task.name,
            "status": dynamic_task.status.value,
            "priority": dynamic_task.priority.value,
            "progress": dynamic_task.progress,
            "assigned_resources": dynamic_task.assigned_resources,
            "deadline": dynamic_task.deadline.isoformat() if dynamic_task.deadline else None,
            "estimated_duration": dynamic_task.estimated_duration,
            "actual_start_time": dynamic_task.actual_start_time.isoformat() if dynamic_task.actual_start_time else None,
            "reassignment_count": dynamic_task.reassignment_count,
            "is_overdue": dynamic_task.is_overdue()
        }
    
    def get_resource_status(self) -> List[Dict[str, Any]]:
        """获取资源状态"""
        status_list = []
        for resource in self.resources.values():
            status_list.append({
                "id": resource.id,
                "name": resource.name,
                "type": resource.type.value,
                "capacity": resource.capacity,
                "current_load": resource.current_load,
                "utilization": resource.current_load / resource.capacity if resource.capacity > 0 else 0,
                "availability": resource.availability,
                "is_available": resource.is_available()
            })
        return status_list
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        # 更新资源利用率
        if self.resources:
            total_utilization = sum(r.current_load / r.capacity for r in self.resources.values() if r.capacity > 0)
            self.performance_metrics["resource_utilization"] = total_utilization / len(self.resources)
        
        # 更新重分配率
        total_reassignments = sum(t.reassignment_count for t in self.tasks.values())
        self.performance_metrics["reassignment_rate"] = total_reassignments / max(1, len(self.tasks))
        
        return self.performance_metrics.copy()
    
    def optimize_task_allocation(self) -> Dict[str, Any]:
        """优化任务分配"""
        optimization_results = {
            "reassigned_tasks": 0,
            "load_balanced": 0,
            "overdue_handled": 0,
            "recommendations": []
        }
        
        # 处理超期任务
        for task_id, dynamic_task in self.tasks.items():
            if dynamic_task.is_overdue() and dynamic_task.status in [TaskStatus.ASSIGNED, TaskStatus.EXECUTING]:
                if self.reassign_task(task_id, "任务超期"):
                    optimization_results["overdue_handled"] += 1
        
        # 负载均衡
        overloaded_resources = [r for r in self.resources.values() 
                              if r.current_load / r.capacity > self.load_threshold]
        
        for resource in overloaded_resources:
            # 找到该资源上的低优先级任务进行重分配
            tasks_on_resource = [t for t in self.tasks.values() 
                               if resource.id in t.assigned_resources and 
                               t.status in [TaskStatus.ASSIGNED, TaskStatus.EXECUTING] and
                               t.priority in [TaskPriority.LOW, TaskPriority.NORMAL]]
            
            if tasks_on_resource:
                # 选择优先级最低的任务重分配
                task_to_reassign = min(tasks_on_resource, key=lambda t: t.priority.value)
                if self.reassign_task(task_to_reassign.task.id, "负载均衡"):
                    optimization_results["load_balanced"] += 1
        
        # 生成优化建议
        if overloaded_resources:
            optimization_results["recommendations"].append(
                f"建议增加 {len(overloaded_resources)} 个过载资源的容量"
            )
        
        pending_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.PENDING]
        if pending_tasks:
            optimization_results["recommendations"].append(
                f"有 {len(pending_tasks)} 个任务等待资源分配"
            )
        
        return optimization_results
    
    def _assign_resources(self, dynamic_task: DynamicTask) -> bool:
        """分配资源给任务"""
        suitable_resources = []
        
        for resource in self.resources.values():
            if resource.is_available() and resource.can_handle_task(dynamic_task.task):
                suitable_resources.append(resource)
        
        if not suitable_resources:
            self.logger.warning(f"没有可用资源执行任务: {dynamic_task.task.name}")
            return False
        
        # 选择负载最低的资源
        best_resource = min(suitable_resources, key=lambda r: r.current_load / r.capacity)
        
        # 分配资源
        dynamic_task.assigned_resources = [best_resource.id]
        best_resource.current_load += dynamic_task.estimated_duration / 60.0  # 转换为小时
        
        self.logger.info(f"分配资源 {best_resource.name} 给任务 {dynamic_task.task.name}")
        return True
    
    def _release_resources(self, dynamic_task: DynamicTask) -> None:
        """释放任务资源"""
        for resource_id in dynamic_task.assigned_resources:
            if resource_id in self.resources:
                resource = self.resources[resource_id]
                resource.current_load = max(0, resource.current_load - dynamic_task.estimated_duration / 60.0)
        
        dynamic_task.assigned_resources.clear()
    
    def _complete_task(self, dynamic_task: DynamicTask) -> None:
        """完成任务"""
        dynamic_task.status = TaskStatus.COMPLETED
        dynamic_task.actual_end_time = datetime.now()
        dynamic_task.progress = 1.0
        
        # 释放资源
        self._release_resources(dynamic_task)
        
        # 添加到已完成任务集合
        self.completed_tasks.add(dynamic_task.task.id)
        
        # 更新性能指标
        self.performance_metrics["completed_tasks"] += 1
        
        if dynamic_task.actual_start_time:
            actual_duration = (dynamic_task.actual_end_time - dynamic_task.actual_start_time).total_seconds() / 60
            current_avg = self.performance_metrics["average_completion_time"]
            completed_count = self.performance_metrics["completed_tasks"]
            self.performance_metrics["average_completion_time"] = (
                (current_avg * (completed_count - 1) + actual_duration) / completed_count
            )
        
        self.logger.info(f"任务完成: {dynamic_task.task.name}")
    
    def _monitor_tasks(self) -> None:
        """后台任务监控"""
        while self.monitoring_active:
            try:
                # 定期优化任务分配
                self.optimize_task_allocation()
                
                # 检查任务状态
                current_time = datetime.now()
                for dynamic_task in self.tasks.values():
                    # 检查执行中的任务是否超时
                    if (dynamic_task.status == TaskStatus.EXECUTING and 
                        dynamic_task.actual_start_time and
                        (current_time - dynamic_task.actual_start_time).total_seconds() > 
                        dynamic_task.estimated_duration * 60 * 1.5):  # 超过预估时间1.5倍
                        
                        self.logger.warning(f"任务 {dynamic_task.task.name} 执行超时")
                        # 可以选择重新分配或标记为失败
                
                time.sleep(self.rebalance_interval)
                
            except Exception as e:
                self.logger.error(f"任务监控异常: {e}")
                time.sleep(5)
    
    def shutdown(self) -> None:
        """关闭任务管理器"""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        self.logger.info("动态任务管理器已关闭")
