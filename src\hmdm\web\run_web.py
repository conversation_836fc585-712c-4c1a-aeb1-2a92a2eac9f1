#!/usr/bin/env python3
"""
HMDM Web管理界面启动脚本

用于启动HMDM系统的Web管理界面
"""

import argparse
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.hmdm.core.system_manager import HMDMSystemManager
from src.hmdm.web.app import create_app


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HMDM Web管理界面')
    parser.add_argument('--host', default='127.0.0.1', help='绑定的主机地址')
    parser.add_argument('--port', type=int, default=5000, help='绑定的端口号')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config', help='系统配置文件路径')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    try:
        # 创建系统管理器
        logger.info("初始化HMDM系统管理器...")
        system_manager = HMDMSystemManager(args.config)
        
        # 启动系统（如果尚未启动）
        if system_manager.status.value != "运行中":
            logger.info("启动HMDM系统...")
            success = system_manager.start_system()
            if not success:
                logger.error("HMDM系统启动失败")
                return 1
        
        # 创建Web应用
        logger.info("创建Web管理界面...")
        web_app = create_app(system_manager, host=args.host, port=args.port)
        
        # 启动Web应用
        logger.info(f"启动Web管理界面: http://{args.host}:{args.port}")
        web_app.run(debug=args.debug)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
        return 0
    except Exception as e:
        logger.error(f"启动失败: {e}")
        return 1
    finally:
        # 清理资源
        try:
            if 'system_manager' in locals():
                system_manager.stop_system()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


if __name__ == '__main__':
    sys.exit(main())
