{"case_info": {"case_name": "海上编队防空作战态势分析任务", "scenario_type": "态势分析", "mission_level": "战术级", "urgency": "高", "complexity": "中等", "description": "某海军编队在执行远海护航任务时，需要对周边空中威胁进行实时态势分析，包括目标识别、威胁评估和预警决策"}, "input_data": {"task_hierarchy": {"root_task_id": "air_defense_situation_analysis", "tasks": {"air_defense_situation_analysis": {"id": "air_defense_situation_analysis", "name": "海上编队防空态势分析", "description": "对海上编队周边空域进行全面态势分析", "task_type": "MISSION_TASK", "parent_id": null, "children_ids": ["data_collection", "threat_assessment", "decision_support"], "level": 0, "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.8, "importance": 0.95, "urgency": 0.9, "time_constraint": 300, "accuracy_requirement": 0.92, "security_level": "机密", "cognitive_load": 0.75, "physical_demand": 0.2, "decision_criticality": 0.9}}, "data_collection": {"id": "data_collection", "name": "多源数据收集", "description": "收集雷达、光电、电子侦察等多源探测数据", "task_type": "ZZ_TASK", "parent_id": "air_defense_situation_analysis", "children_ids": ["radar_data", "eo_data", "esm_data"], "level": 1, "executor_type": "MACHINE", "attributes": {"complexity": 0.6, "importance": 0.85, "urgency": 0.95, "time_constraint": 60, "accuracy_requirement": 0.88, "security_level": "机密", "cognitive_load": 0.3, "physical_demand": 0.1, "decision_criticality": 0.7}}, "radar_data": {"id": "radar_data", "name": "雷达数据处理", "description": "处理舰载雷达探测的空中目标数据", "task_type": "TYPICAL_FUNCTION", "parent_id": "data_collection", "children_ids": [], "level": 2, "executor_type": "MACHINE", "attributes": {"complexity": 0.5, "importance": 0.8, "urgency": 0.9, "time_constraint": 20, "accuracy_requirement": 0.9, "security_level": "机密", "cognitive_load": 0.2, "physical_demand": 0.1, "decision_criticality": 0.6}}, "eo_data": {"id": "eo_data", "name": "光电数据处理", "description": "处理光电探测系统获取的目标图像数据", "task_type": "TYPICAL_FUNCTION", "parent_id": "data_collection", "children_ids": [], "level": 2, "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.7, "importance": 0.75, "urgency": 0.8, "time_constraint": 45, "accuracy_requirement": 0.85, "security_level": "机密", "cognitive_load": 0.6, "physical_demand": 0.2, "decision_criticality": 0.7}}, "esm_data": {"id": "esm_data", "name": "电子侦察数据处理", "description": "处理电子侦察系统截获的电磁信号数据", "task_type": "TYPICAL_FUNCTION", "parent_id": "data_collection", "children_ids": [], "level": 2, "executor_type": "MACHINE", "attributes": {"complexity": 0.8, "importance": 0.85, "urgency": 0.85, "time_constraint": 30, "accuracy_requirement": 0.88, "security_level": "机密", "cognitive_load": 0.4, "physical_demand": 0.1, "decision_criticality": 0.75}}, "threat_assessment": {"id": "threat_assessment", "name": "威胁评估分析", "description": "对识别的空中目标进行威胁等级评估", "task_type": "ZZ_TASK", "parent_id": "air_defense_situation_analysis", "children_ids": ["target_classification", "threat_calculation"], "level": 1, "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.85, "importance": 0.9, "urgency": 0.85, "time_constraint": 120, "accuracy_requirement": 0.9, "security_level": "机密", "cognitive_load": 0.8, "physical_demand": 0.3, "decision_criticality": 0.95}}, "target_classification": {"id": "target_classification", "name": "目标分类识别", "description": "对探测到的空中目标进行分类识别", "task_type": "TYPICAL_FUNCTION", "parent_id": "threat_assessment", "children_ids": [], "level": 2, "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.75, "importance": 0.85, "urgency": 0.8, "time_constraint": 60, "accuracy_requirement": 0.88, "security_level": "机密", "cognitive_load": 0.7, "physical_demand": 0.2, "decision_criticality": 0.8}}, "threat_calculation": {"id": "threat_calculation", "name": "威胁度计算", "description": "计算各目标对编队的威胁程度", "task_type": "TYPICAL_FUNCTION", "parent_id": "threat_assessment", "children_ids": [], "level": 2, "executor_type": "MACHINE", "attributes": {"complexity": 0.6, "importance": 0.9, "urgency": 0.9, "time_constraint": 30, "accuracy_requirement": 0.92, "security_level": "机密", "cognitive_load": 0.4, "physical_demand": 0.1, "decision_criticality": 0.9}}, "decision_support": {"id": "decision_support", "name": "决策支持建议", "description": "基于态势分析结果提供决策支持建议", "task_type": "ZZ_TASK", "parent_id": "air_defense_situation_analysis", "children_ids": ["response_planning", "resource_allocation"], "level": 1, "executor_type": "HUMAN", "attributes": {"complexity": 0.9, "importance": 0.95, "urgency": 0.8, "time_constraint": 180, "accuracy_requirement": 0.85, "security_level": "机密", "cognitive_load": 0.9, "physical_demand": 0.2, "decision_criticality": 0.95}}, "response_planning": {"id": "response_planning", "name": "应对方案规划", "description": "制定针对威胁的应对方案", "task_type": "TYPICAL_FUNCTION", "parent_id": "decision_support", "children_ids": [], "level": 2, "executor_type": "HUMAN", "attributes": {"complexity": 0.85, "importance": 0.9, "urgency": 0.75, "time_constraint": 120, "accuracy_requirement": 0.8, "security_level": "机密", "cognitive_load": 0.85, "physical_demand": 0.2, "decision_criticality": 0.9}}, "resource_allocation": {"id": "resource_allocation", "name": "资源配置建议", "description": "提供防空资源的最优配置建议", "task_type": "TYPICAL_FUNCTION", "parent_id": "decision_support", "children_ids": [], "level": 2, "executor_type": "HUMAN_MACHINE", "attributes": {"complexity": 0.8, "importance": 0.85, "urgency": 0.7, "time_constraint": 90, "accuracy_requirement": 0.82, "security_level": "机密", "cognitive_load": 0.75, "physical_demand": 0.3, "decision_criticality": 0.85}}}}, "human_capabilities": {"air_defense_officer": {"name": "防空作战军官", "rank": "少校", "experience_years": 8, "capabilities": {"cognitive": 0.85, "physical": 0.7, "perceptual": 0.9, "decision_making": 0.88, "execution": 0.75, "coordination": 0.82, "creative": 0.8, "adaptive": 0.85, "learning": 0.78, "stress_resistance": 0.9}, "specialties": ["空中威胁识别", "防空作战指挥", "多目标跟踪"], "workload_capacity": 0.8, "fatigue_level": 0.2}, "radar_operator": {"name": "雷达操作员", "rank": "中士", "experience_years": 5, "capabilities": {"cognitive": 0.75, "physical": 0.8, "perceptual": 0.95, "decision_making": 0.7, "execution": 0.9, "coordination": 0.75, "creative": 0.6, "adaptive": 0.7, "learning": 0.8, "stress_resistance": 0.85}, "specialties": ["雷达操作", "目标跟踪", "数据处理"], "workload_capacity": 0.85, "fatigue_level": 0.15}, "intelligence_analyst": {"name": "情报分析员", "rank": "上尉", "experience_years": 6, "capabilities": {"cognitive": 0.9, "physical": 0.6, "perceptual": 0.85, "decision_making": 0.82, "execution": 0.7, "coordination": 0.8, "creative": 0.85, "adaptive": 0.8, "learning": 0.88, "stress_resistance": 0.75}, "specialties": ["情报分析", "威胁评估", "态势研判"], "workload_capacity": 0.75, "fatigue_level": 0.25}}, "machine_capabilities": {"radar_system": {"name": "舰载相控阵雷达系统", "type": "探测设备", "capabilities": {"processing_speed": 0.95, "accuracy": 0.92, "reliability": 0.88, "response_time": 0.98, "data_throughput": 0.9, "multi_target_tracking": 0.95, "interference_resistance": 0.85, "continuous_operation": 0.95, "precision": 0.9, "automation_level": 0.9}, "performance_metrics": {"detection_range": "300km", "tracking_capacity": "200个目标", "update_rate": "1秒", "availability": 0.95}}, "combat_system": {"name": "作战指挥系统", "type": "指挥控制设备", "capabilities": {"processing_speed": 0.88, "accuracy": 0.85, "reliability": 0.9, "response_time": 0.85, "data_throughput": 0.92, "multi_target_tracking": 0.8, "interference_resistance": 0.8, "continuous_operation": 0.92, "precision": 0.88, "automation_level": 0.85}, "performance_metrics": {"processing_capacity": "1000次/秒", "data_fusion_capability": "多源融合", "decision_support_level": "高级", "availability": 0.92}}, "eo_system": {"name": "光电探测系统", "type": "探测设备", "capabilities": {"processing_speed": 0.8, "accuracy": 0.88, "reliability": 0.85, "response_time": 0.75, "data_throughput": 0.7, "multi_target_tracking": 0.7, "interference_resistance": 0.6, "continuous_operation": 0.8, "precision": 0.92, "automation_level": 0.75}, "performance_metrics": {"resolution": "高清", "detection_range": "50km", "weather_dependency": "中等", "availability": 0.88}}}, "evaluation_criteria": {"efficiency": {"name": "作战效率", "weight": 0.2, "description": "任务完成的时间效率和资源利用率", "measurement": "任务完成时间/标准时间"}, "accuracy": {"name": "准确性", "weight": 0.18, "description": "态势分析和威胁评估的准确程度", "measurement": "正确识别率"}, "reliability": {"name": "可靠性", "weight": 0.15, "description": "系统和人员执行任务的可靠程度", "measurement": "任务成功率"}, "response_time": {"name": "响应时间", "weight": 0.12, "description": "从威胁出现到完成分析的响应时间", "measurement": "平均响应时间"}, "resource_utilization": {"name": "资源利用率", "weight": 0.1, "description": "人力和装备资源的利用效率", "measurement": "资源使用率"}, "coordination": {"name": "协调性", "weight": 0.08, "description": "人机协作的协调程度", "measurement": "协作效率指数"}, "adaptability": {"name": "适应性", "weight": 0.07, "description": "对环境变化和突发情况的适应能力", "measurement": "适应性评分"}, "cost_effectiveness": {"name": "成本效益", "weight": 0.06, "description": "完成任务的成本效益比", "measurement": "效益/成本比"}, "security": {"name": "安全性", "weight": 0.04, "description": "信息安全和作战安全保障程度", "measurement": "安全等级评分"}}, "constraints": {"time_limit": 300, "max_human_workload": 0.8, "min_machine_utilization": 0.6, "security_clearance_required": "机密", "budget_limit": 1000000, "personnel_availability": {"air_defense_officer": 1, "radar_operator": 2, "intelligence_analyst": 1}}, "preferences": {"prefer_human_decision": true, "prefer_machine_execution": true, "efficiency_priority": 0.8, "reliability_priority": 0.9, "scheme_count": 4}}, "processing_data": {"capability_analysis": {"task_requirements": {"air_defense_situation_analysis": {"cognitive": 0.8, "physical": 0.2, "perceptual": 0.9, "decision_making": 0.9, "execution": 0.7, "coordination": 0.8}, "data_collection": {"cognitive": 0.3, "physical": 0.1, "perceptual": 0.8, "decision_making": 0.2, "execution": 0.9, "coordination": 0.4}, "threat_assessment": {"cognitive": 0.85, "physical": 0.3, "perceptual": 0.8, "decision_making": 0.9, "execution": 0.6, "coordination": 0.7}}, "human_machine_matching": {"air_defense_officer": {"suitable_tasks": ["decision_support", "threat_assessment", "response_planning"], "match_scores": [0.92, 0.88, 0.9]}, "radar_operator": {"suitable_tasks": ["radar_data", "data_collection"], "match_scores": [0.95, 0.85]}, "radar_system": {"suitable_tasks": ["radar_data", "threat_calculation", "data_collection"], "match_scores": [0.98, 0.92, 0.88]}}}, "decision_matrix": {"alternatives": ["方案A", "方案B", "方案C", "方案D"], "criteria": ["efficiency", "accuracy", "reliability", "response_time", "resource_utilization", "coordination", "adaptability", "cost_effectiveness", "security"], "matrix": [[0.85, 0.9, 0.88, 0.82, 0.75, 0.8, 0.85, 0.78, 0.92], [0.78, 0.85, 0.92, 0.88, 0.82, 0.85, 0.8, 0.85, 0.88], [0.82, 0.88, 0.85, 0.85, 0.88, 0.78, 0.88, 0.8, 0.9], [0.88, 0.82, 0.8, 0.9, 0.85, 0.88, 0.82, 0.88, 0.85]], "weights": [0.2, 0.18, 0.15, 0.12, 0.1, 0.08, 0.07, 0.06, 0.04]}}, "output_results": {"recommended_allocation_scheme": {"scheme_id": "optimal_scheme_A", "scheme_name": "人机协同优化方案", "overall_score": 0.867, "confidence_level": 0.92, "task_allocations": {"air_defense_situation_analysis": {"executor": "HUMAN_MACHINE", "primary_executor": "air_defense_officer", "supporting_systems": ["combat_system"], "allocation_confidence": 0.95}, "data_collection": {"executor": "MACHINE", "primary_executor": "radar_system", "supporting_personnel": ["radar_operator"], "allocation_confidence": 0.98}, "radar_data": {"executor": "MACHINE", "primary_executor": "radar_system", "supporting_personnel": [], "allocation_confidence": 0.99}, "eo_data": {"executor": "HUMAN_MACHINE", "primary_executor": "eo_system", "supporting_personnel": ["radar_operator"], "allocation_confidence": 0.85}, "esm_data": {"executor": "MACHINE", "primary_executor": "combat_system", "supporting_personnel": [], "allocation_confidence": 0.92}, "threat_assessment": {"executor": "HUMAN_MACHINE", "primary_executor": "intelligence_analyst", "supporting_systems": ["combat_system"], "allocation_confidence": 0.9}, "target_classification": {"executor": "HUMAN_MACHINE", "primary_executor": "intelligence_analyst", "supporting_systems": ["combat_system", "eo_system"], "allocation_confidence": 0.88}, "threat_calculation": {"executor": "MACHINE", "primary_executor": "combat_system", "supporting_personnel": [], "allocation_confidence": 0.95}, "decision_support": {"executor": "HUMAN", "primary_executor": "air_defense_officer", "supporting_systems": ["combat_system"], "allocation_confidence": 0.93}, "response_planning": {"executor": "HUMAN", "primary_executor": "air_defense_officer", "supporting_systems": [], "allocation_confidence": 0.95}, "resource_allocation": {"executor": "HUMAN_MACHINE", "primary_executor": "air_defense_officer", "supporting_systems": ["combat_system"], "allocation_confidence": 0.87}}, "performance_prediction": {"estimated_completion_time": 285, "expected_accuracy": 0.89, "resource_utilization": 0.82, "human_workload": 0.75, "machine_utilization": 0.88}}, "alternative_schemes": [{"scheme_id": "scheme_B", "scheme_name": "机器主导方案", "overall_score": 0.834, "key_features": ["高度自动化", "快速响应", "人员负荷低"], "trade_offs": ["决策灵活性降低", "适应性不足"]}, {"scheme_id": "scheme_C", "scheme_name": "人员主导方案", "overall_score": 0.798, "key_features": ["决策灵活性高", "适应性强", "经验丰富"], "trade_offs": ["响应时间较长", "人员负荷高"]}, {"scheme_id": "scheme_D", "scheme_name": "平衡协作方案", "overall_score": 0.845, "key_features": ["人机平衡", "协调性好", "稳定性高"], "trade_offs": ["效率中等", "复杂度较高"]}], "decision_analysis": {"wrdm_results": {"scheme_rankings": [{"scheme": "方案A", "score": 0.867, "rank": 1}, {"scheme": "方案D", "score": 0.845, "rank": 2}, {"scheme": "方案B", "score": 0.834, "rank": 3}, {"scheme": "方案C", "score": 0.798, "rank": 4}], "relative_deviations": [0.133, 0.155, 0.166, 0.202]}, "topsis_results": {"positive_ideal_distances": [0.125, 0.148, 0.162, 0.195], "negative_ideal_distances": [0.195, 0.162, 0.148, 0.125], "relative_closeness": [0.609, 0.523, 0.477, 0.391]}, "sensitivity_analysis": {"weight_sensitivity": "低", "robust_ranking": true, "critical_criteria": ["efficiency", "accuracy", "reliability"]}}, "implementation_guidance": {"deployment_steps": ["配置雷达系统自动数据处理模式", "设置情报分析员威胁评估工作站", "建立防空军官决策支持界面", "配置人机协作通信协议", "进行系统集成测试"], "resource_requirements": {"人力资源": "需要3名专业人员：1名防空军官、1名情报分析员、1名雷达操作员", "装备资源": "需要雷达系统、作战指挥系统、光电系统协同工作", "时间资源": "预计部署时间2-3天，培训时间1周", "成本估算": "约85万元（包括系统配置和人员培训）"}, "risk_mitigation": ["建立备份操作员轮换机制", "设置系统故障应急预案", "制定人机协作标准作业程序", "建立质量控制检查点"], "success_metrics": {"任务完成率": "> 95%", "威胁识别准确率": "> 90%", "平均响应时间": "< 300秒", "系统可用性": "> 92%", "人员满意度": "> 85%"}, "optimization_suggestions": ["定期更新威胁数据库提升识别准确性", "加强人员训练提高人机协作效率", "优化算法参数提升系统响应速度", "建立经验知识库支持决策优化"]}}}