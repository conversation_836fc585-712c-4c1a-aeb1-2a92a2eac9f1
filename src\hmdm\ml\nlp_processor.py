"""
自然语言处理模块

提供军事文本处理、指令解析、情报分析等NLP功能。
支持中文军事术语和指令的理解与处理。
"""

import re
import jieba
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from collections import Counter

# NLP库
try:
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_NLP_AVAILABLE = True
except ImportError:
    SKLEARN_NLP_AVAILABLE = False

from ..security.military_security import SecurityLevel


class TextType(Enum):
    """文本类型"""
    COMMAND = "指令"
    INTELLIGENCE = "情报"
    REPORT = "报告"
    COMMUNICATION = "通信"
    ANALYSIS = "分析"


class SentimentType(Enum):
    """情感类型"""
    POSITIVE = "积极"
    NEGATIVE = "消极"
    NEUTRAL = "中性"
    URGENT = "紧急"
    CRITICAL = "严重"


@dataclass
class TextAnalysisResult:
    """文本分析结果"""
    text: str
    text_type: TextType
    sentiment: SentimentType
    confidence: float
    keywords: List[str] = field(default_factory=list)
    entities: List[Dict[str, Any]] = field(default_factory=list)
    summary: str = ""
    urgency_level: int = 0  # 0-10
    security_classification: SecurityLevel = SecurityLevel.INTERNAL
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'text': self.text,
            'text_type': self.text_type.value,
            'sentiment': self.sentiment.value,
            'confidence': self.confidence,
            'keywords': self.keywords,
            'entities': self.entities,
            'summary': self.summary,
            'urgency_level': self.urgency_level,
            'security_classification': self.security_classification.value,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class MilitaryNLPProcessor:
    """军事自然语言处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化jieba分词
        jieba.initialize()
        
        # 军事词典
        self.military_terms = self._load_military_terms()
        self.command_patterns = self._load_command_patterns()
        self.urgency_keywords = self._load_urgency_keywords()
        
        # 停用词
        self.stop_words = self._load_stop_words()
        
        # TF-IDF向量化器
        if SKLEARN_NLP_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words=list(self.stop_words)
            )
        
        # 情感分析器
        self.sentiment_analyzer = None
        if NLTK_AVAILABLE:
            try:
                self.sentiment_analyzer = SentimentIntensityAnalyzer()
            except Exception as e:
                self.logger.warning(f"NLTK情感分析器初始化失败: {e}")
        
        # 分析历史
        self.analysis_history: List[TextAnalysisResult] = []
        
        self.logger.info("军事NLP处理器初始化完成")
    
    def analyze_text(self, text: str, text_type: Optional[TextType] = None) -> TextAnalysisResult:
        """分析文本"""
        try:
            # 预处理文本
            cleaned_text = self._preprocess_text(text)
            
            # 自动检测文本类型
            if text_type is None:
                text_type = self._detect_text_type(cleaned_text)
            
            # 情感分析
            sentiment, sentiment_confidence = self._analyze_sentiment(cleaned_text)
            
            # 关键词提取
            keywords = self._extract_keywords(cleaned_text)
            
            # 实体识别
            entities = self._extract_entities(cleaned_text)
            
            # 生成摘要
            summary = self._generate_summary(cleaned_text)
            
            # 紧急程度评估
            urgency_level = self._assess_urgency(cleaned_text)
            
            # 安全等级分类
            security_level = self._classify_security_level(cleaned_text)
            
            # 创建分析结果
            result = TextAnalysisResult(
                text=text,
                text_type=text_type,
                sentiment=sentiment,
                confidence=sentiment_confidence,
                keywords=keywords,
                entities=entities,
                summary=summary,
                urgency_level=urgency_level,
                security_classification=security_level,
                metadata={
                    'text_length': len(text),
                    'cleaned_length': len(cleaned_text),
                    'word_count': len(cleaned_text.split()),
                    'sentence_count': len(self._split_sentences(cleaned_text))
                }
            )
            
            # 记录分析历史
            self.analysis_history.append(result)
            
            self.logger.info(f"文本分析完成: 类型={text_type.value}, 情感={sentiment.value}, 紧急度={urgency_level}")
            return result
            
        except Exception as e:
            self.logger.error(f"文本分析失败: {e}")
            # 返回基本结果
            return TextAnalysisResult(
                text=text,
                text_type=text_type or TextType.ANALYSIS,
                sentiment=SentimentType.NEUTRAL,
                confidence=0.0
            )
    
    def extract_commands(self, text: str) -> List[Dict[str, Any]]:
        """提取指令"""
        try:
            commands = []
            cleaned_text = self._preprocess_text(text)
            
            for pattern_name, pattern in self.command_patterns.items():
                matches = re.finditer(pattern, cleaned_text, re.IGNORECASE)
                for match in matches:
                    command = {
                        'type': pattern_name,
                        'text': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'confidence': 0.8
                    }
                    commands.append(command)
            
            return commands
            
        except Exception as e:
            self.logger.error(f"指令提取失败: {e}")
            return []
    
    def similarity_search(self, query: str, documents: List[str], top_k: int = 5) -> List[Tuple[str, float]]:
        """相似度搜索"""
        try:
            if not SKLEARN_NLP_AVAILABLE:
                self.logger.warning("sklearn不可用，无法进行相似度搜索")
                return []
            
            # 构建文档集合
            all_docs = [query] + documents
            
            # TF-IDF向量化
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(all_docs)
            
            # 计算相似度
            query_vector = tfidf_matrix[0]
            doc_vectors = tfidf_matrix[1:]
            
            similarities = cosine_similarity(query_vector, doc_vectors).flatten()
            
            # 排序并返回top_k结果
            doc_similarities = list(zip(documents, similarities))
            doc_similarities.sort(key=lambda x: x[1], reverse=True)
            
            return doc_similarities[:top_k]
            
        except Exception as e:
            self.logger.error(f"相似度搜索失败: {e}")
            return []
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计"""
        try:
            if not self.analysis_history:
                return {'message': '暂无分析历史'}
            
            # 统计文本类型分布
            type_counts = Counter(result.text_type.value for result in self.analysis_history)
            
            # 统计情感分布
            sentiment_counts = Counter(result.sentiment.value for result in self.analysis_history)
            
            # 统计紧急程度分布
            urgency_levels = [result.urgency_level for result in self.analysis_history]
            avg_urgency = sum(urgency_levels) / len(urgency_levels)
            
            # 统计安全等级分布
            security_counts = Counter(result.security_classification.value for result in self.analysis_history)
            
            # 最常见关键词
            all_keywords = []
            for result in self.analysis_history:
                all_keywords.extend(result.keywords)
            top_keywords = Counter(all_keywords).most_common(10)
            
            return {
                'total_analyses': len(self.analysis_history),
                'text_type_distribution': dict(type_counts),
                'sentiment_distribution': dict(sentiment_counts),
                'average_urgency_level': round(avg_urgency, 2),
                'security_level_distribution': dict(security_counts),
                'top_keywords': top_keywords,
                'last_analysis_time': self.analysis_history[-1].timestamp.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取分析统计失败: {e}")
            return {}
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 去除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）。，；：！？]', '', text)
        
        return text
    
    def _detect_text_type(self, text: str) -> TextType:
        """检测文本类型"""
        # 指令关键词
        command_keywords = ['命令', '指示', '执行', '行动', '部署', '撤退', '攻击', '防守']
        # 情报关键词
        intel_keywords = ['情报', '侦察', '监视', '发现', '观察', '报告', '敌情']
        # 通信关键词
        comm_keywords = ['通信', '联络', '呼叫', '频道', '信号', '传输']
        
        text_lower = text.lower()
        
        command_score = sum(1 for kw in command_keywords if kw in text_lower)
        intel_score = sum(1 for kw in intel_keywords if kw in text_lower)
        comm_score = sum(1 for kw in comm_keywords if kw in text_lower)
        
        if command_score >= intel_score and command_score >= comm_score:
            return TextType.COMMAND
        elif intel_score >= comm_score:
            return TextType.INTELLIGENCE
        elif comm_score > 0:
            return TextType.COMMUNICATION
        else:
            return TextType.ANALYSIS
    
    def _analyze_sentiment(self, text: str) -> Tuple[SentimentType, float]:
        """分析情感"""
        try:
            # 紧急关键词检测
            urgent_keywords = ['紧急', '立即', '马上', '危险', '威胁', '攻击', '敌人']
            critical_keywords = ['严重', '重大', '关键', '致命', '毁灭', '损失']
            
            urgent_count = sum(1 for kw in urgent_keywords if kw in text)
            critical_count = sum(1 for kw in critical_keywords if kw in text)
            
            if urgent_count > 0:
                return SentimentType.URGENT, 0.8 + min(urgent_count * 0.1, 0.2)
            elif critical_count > 0:
                return SentimentType.CRITICAL, 0.7 + min(critical_count * 0.1, 0.3)
            
            # 使用NLTK情感分析（如果可用）
            if self.sentiment_analyzer and NLTK_AVAILABLE:
                try:
                    # 对英文部分进行情感分析
                    english_text = re.sub(r'[\u4e00-\u9fa5]', '', text)
                    if english_text.strip():
                        scores = self.sentiment_analyzer.polarity_scores(english_text)
                        compound = scores['compound']
                        
                        if compound >= 0.05:
                            return SentimentType.POSITIVE, abs(compound)
                        elif compound <= -0.05:
                            return SentimentType.NEGATIVE, abs(compound)
                except Exception:
                    pass
            
            return SentimentType.NEUTRAL, 0.5
            
        except Exception as e:
            self.logger.error(f"情感分析失败: {e}")
            return SentimentType.NEUTRAL, 0.0
    
    def _extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        try:
            # 使用jieba分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            filtered_words = [
                word for word in words 
                if len(word) > 1 and word not in self.stop_words
            ]
            
            # 统计词频
            word_counts = Counter(filtered_words)
            
            # 返回最常见的关键词
            return [word for word, count in word_counts.most_common(max_keywords)]
            
        except Exception as e:
            self.logger.error(f"关键词提取失败: {e}")
            return []
    
    def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """提取实体"""
        try:
            entities = []

            # 军事单位实体
            unit_pattern = r'(第\d+军|第\d+师|第\d+团|第\d+营|第\d+连|第\d+排|第\d+班|装甲部队|步兵|炮兵)'
            for match in re.finditer(unit_pattern, text):
                entities.append({
                    'type': '军事单位',
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end()
                })

            # 地点实体
            location_pattern = r'([A-Z]\d*区域|[A-Z]区域|\d+高地|[东西南北]\d+|阵地[A-Z]?)'
            for match in re.finditer(location_pattern, text):
                entities.append({
                    'type': '地点',
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end()
                })

            # 时间实体
            time_pattern = r'(\d{1,2}:\d{2}|\d+分钟|立即|马上|明日|凌晨)'
            for match in re.finditer(time_pattern, text):
                entities.append({
                    'type': '时间',
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end()
                })

            # 数量实体
            quantity_pattern = r'(\d+辆|约\d+|数量\d+)'
            for match in re.finditer(quantity_pattern, text):
                entities.append({
                    'type': '数量',
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end()
                })

            return entities

        except Exception as e:
            self.logger.error(f"实体提取失败: {e}")
            return []

    def _generate_summary(self, text: str, max_length: int = 100) -> str:
        """生成摘要"""
        try:
            sentences = self._split_sentences(text)
            if not sentences:
                return ""

            if len(sentences) <= 2:
                return text[:max_length] + "..." if len(text) > max_length else text

            # 简单的摘要生成：选择包含关键词最多的句子
            keywords = self._extract_keywords(text, 5)

            sentence_scores = []
            for sentence in sentences:
                score = sum(1 for kw in keywords if kw in sentence)
                sentence_scores.append((sentence, score))

            # 选择得分最高的句子
            sentence_scores.sort(key=lambda x: x[1], reverse=True)

            summary = sentence_scores[0][0]
            if len(summary) > max_length:
                summary = summary[:max_length] + "..."

            return summary

        except Exception as e:
            self.logger.error(f"摘要生成失败: {e}")
            return text[:max_length] + "..." if len(text) > max_length else text

    def _assess_urgency(self, text: str) -> int:
        """评估紧急程度 (0-10)"""
        try:
            urgency_score = 0

            # 紧急关键词权重
            high_urgency = ['紧急', '立即', '马上', '危险', '威胁', '攻击']
            medium_urgency = ['重要', '尽快', '及时', '注意', '警告']
            low_urgency = ['建议', '考虑', '可能', '或许']

            for keyword in high_urgency:
                urgency_score += text.count(keyword) * 3

            for keyword in medium_urgency:
                urgency_score += text.count(keyword) * 2

            for keyword in low_urgency:
                urgency_score -= text.count(keyword) * 1

            # 标点符号影响
            urgency_score += text.count('!') * 1
            urgency_score += text.count('！') * 1

            # 限制在0-10范围内
            return max(0, min(10, urgency_score))

        except Exception as e:
            self.logger.error(f"紧急程度评估失败: {e}")
            return 0

    def _classify_security_level(self, text: str) -> SecurityLevel:
        """分类安全等级"""
        try:
            # 绝密关键词
            top_secret_keywords = ['绝密', '核武器', '战略核心', '特殊任务']
            # 机密关键词
            confidential_keywords = ['机密情报', '机密', '武器装备']
            # 秘密关键词
            secret_keywords = ['秘密', '作战计划', '军事行动', '敌情', '部队调动']
            # 一般情报关键词
            general_intel_keywords = ['情报']

            for keyword in top_secret_keywords:
                if keyword in text:
                    return SecurityLevel.TOP_SECRET

            for keyword in confidential_keywords:
                if keyword in text:
                    return SecurityLevel.CONFIDENTIAL

            for keyword in secret_keywords:
                if keyword in text:
                    return SecurityLevel.SECRET

            for keyword in general_intel_keywords:
                if keyword in text:
                    return SecurityLevel.SECRET

            return SecurityLevel.INTERNAL

        except Exception as e:
            self.logger.error(f"安全等级分类失败: {e}")
            return SecurityLevel.INTERNAL

    def _split_sentences(self, text: str) -> List[str]:
        """分割句子"""
        # 中文句子分割
        sentences = re.split(r'[。！？；]', text)
        return [s.strip() for s in sentences if s.strip()]

    def _load_military_terms(self) -> Dict[str, str]:
        """加载军事术语词典"""
        return {
            '步兵': '陆军基本兵种',
            '装甲兵': '坦克和装甲车辆部队',
            '炮兵': '火炮部队',
            '工兵': '工程兵种',
            '通信兵': '通信保障兵种',
            '侦察兵': '侦察部队',
            '特种兵': '特殊作战部队',
            '空降兵': '伞兵部队',
            '海军陆战队': '海军陆战部队',
            '导弹兵': '导弹部队'
        }

    def _load_command_patterns(self) -> Dict[str, str]:
        """加载指令模式"""
        return {
            'movement': r'(前进|后退|左转|右转|停止|集合|散开)到?([A-Z]\d*区域|[A-Z]区域|\d+高地|指定位置|阵地)?',
            'attack': r'(攻击|打击|消灭|摧毁)(目标|敌人|敌方|[A-Z]\d*)?',
            'defense': r'(防守|守卫|保护|掩护)([A-Z]\d*区域|[A-Z]区域|\d+高地|阵地)?',
            'communication': r'(呼叫|联系|报告)(指挥部|上级|[A-Z]\d+)?',
            'reconnaissance': r'(侦察|监视|观察)(目标|敌情|[A-Z]\d+)?'
        }

    def _load_urgency_keywords(self) -> Dict[str, int]:
        """加载紧急程度关键词"""
        return {
            '紧急': 5,
            '立即': 4,
            '马上': 4,
            '危险': 3,
            '威胁': 3,
            '重要': 2,
            '尽快': 2,
            '注意': 1,
            '建议': -1
        }

    def _load_stop_words(self) -> set:
        """加载停用词"""
        chinese_stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还', '把', '被', '让', '如果', '但是',
            '因为', '所以', '虽然', '然而', '而且', '或者', '以及', '以后', '以前',
            '可以', '应该', '必须', '需要', '能够', '可能', '也许', '大概', '或许'
        }

        english_stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

        return chinese_stop_words.union(english_stop_words)
