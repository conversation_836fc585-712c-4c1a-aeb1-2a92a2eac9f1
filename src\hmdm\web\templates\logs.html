{% extends "base.html" %}

{% block title %}系统日志 - HMDM{% endblock %}

{% block page_title %}系统日志{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-primary" id="refresh-logs-btn">
        <i class="fas fa-sync-alt"></i> 刷新日志
    </button>
    <button type="button" class="btn btn-secondary" id="clear-display-btn">
        <i class="fas fa-eraser"></i> 清空显示
    </button>
    <button type="button" class="btn btn-info" id="auto-scroll-btn">
        <i class="fas fa-arrow-down"></i> 自动滚动
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 日志过滤器 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="log-level-filter" class="form-label">日志级别</label>
                        <select class="form-select" id="log-level-filter">
                            <option value="">全部</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                            <option value="CRITICAL">CRITICAL</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="logger-filter" class="form-label">日志来源</label>
                        <select class="form-select" id="logger-filter">
                            <option value="">全部</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="search-filter" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search-filter" placeholder="输入搜索关键词...">
                    </div>
                    
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-primary w-100" id="apply-filter-btn">
                            <i class="fas fa-filter"></i> 应用过滤
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志统计 -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总日志数</h6>
                        <h4 class="mb-0" id="total-logs-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">错误日志</h6>
                        <h4 class="mb-0" id="error-logs-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">警告日志</h6>
                        <h4 class="mb-0" id="warning-logs-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">信息日志</h6>
                        <h4 class="mb-0" id="info-logs-count">0</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志显示区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-terminal"></i> 实时日志
                </h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="real-time-toggle" checked>
                    <label class="form-check-label" for="real-time-toggle">
                        实时更新
                    </label>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="logs-display" style="height: 500px; overflow-y: auto;">
                    <div class="p-3 text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载日志...
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small class="text-muted">
                            显示最近 <span id="displayed-logs-count">0</span> 条日志
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            最后更新: <span id="last-update-time">-</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let allLogs = [];
    let filteredLogs = [];
    let autoScroll = true;
    let realTimeUpdate = true;
    let logUpdateInterval;
    
    // 加载日志
    function loadLogs() {
        makeRequest('/api/logs')
            .then(response => {
                if (response.success) {
                    allLogs = response.data;
                    updateLoggerFilter();
                    applyFilters();
                    updateLogStatistics();
                    document.getElementById('last-update-time').textContent = new Date().toLocaleString();
                }
            })
            .catch(error => {
                console.error('加载日志失败:', error);
                showNotification('加载日志失败', 'danger');
            });
    }
    
    // 更新日志来源过滤器
    function updateLoggerFilter() {
        const loggerSet = new Set();
        allLogs.forEach(log => {
            if (log.logger) {
                loggerSet.add(log.logger);
            }
        });
        
        const loggerFilter = document.getElementById('logger-filter');
        const currentValue = loggerFilter.value;
        
        // 清空现有选项（保留"全部"）
        loggerFilter.innerHTML = '<option value="">全部</option>';
        
        // 添加新选项
        Array.from(loggerSet).sort().forEach(logger => {
            const option = document.createElement('option');
            option.value = logger;
            option.textContent = logger;
            if (logger === currentValue) {
                option.selected = true;
            }
            loggerFilter.appendChild(option);
        });
    }
    
    // 应用过滤器
    function applyFilters() {
        const levelFilter = document.getElementById('log-level-filter').value;
        const loggerFilter = document.getElementById('logger-filter').value;
        const searchFilter = document.getElementById('search-filter').value.toLowerCase();
        
        filteredLogs = allLogs.filter(log => {
            // 级别过滤
            if (levelFilter && log.level !== levelFilter) {
                return false;
            }
            
            // 来源过滤
            if (loggerFilter && log.logger !== loggerFilter) {
                return false;
            }
            
            // 搜索过滤
            if (searchFilter && !log.message.toLowerCase().includes(searchFilter)) {
                return false;
            }
            
            return true;
        });
        
        displayLogs();
    }
    
    // 显示日志
    function displayLogs() {
        const container = document.getElementById('logs-display');
        const shouldScrollToBottom = autoScroll && 
            (container.scrollTop + container.clientHeight >= container.scrollHeight - 10);
        
        container.innerHTML = '';
        
        if (filteredLogs.length === 0) {
            container.innerHTML = '<div class="p-3 text-center text-muted">暂无日志数据</div>';
            return;
        }
        
        filteredLogs.forEach(log => {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const levelClass = `log-level-${log.level}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">${log.timestamp}</span>
                <span class="${levelClass}">[${log.level}]</span>
                <span class="log-logger">${log.logger}</span>
                <span class="log-message">${escapeHtml(log.message)}</span>
            `;
            
            container.appendChild(logEntry);
        });
        
        // 更新显示计数
        document.getElementById('displayed-logs-count').textContent = filteredLogs.length;
        
        // 自动滚动到底部
        if (shouldScrollToBottom) {
            container.scrollTop = container.scrollHeight;
        }
    }
    
    // 更新日志统计
    function updateLogStatistics() {
        const stats = {
            total: allLogs.length,
            error: 0,
            warning: 0,
            info: 0
        };
        
        allLogs.forEach(log => {
            switch (log.level) {
                case 'ERROR':
                case 'CRITICAL':
                    stats.error++;
                    break;
                case 'WARNING':
                    stats.warning++;
                    break;
                case 'INFO':
                case 'DEBUG':
                    stats.info++;
                    break;
            }
        });
        
        document.getElementById('total-logs-count').textContent = stats.total;
        document.getElementById('error-logs-count').textContent = stats.error;
        document.getElementById('warning-logs-count').textContent = stats.warning;
        document.getElementById('info-logs-count').textContent = stats.info;
    }
    
    // HTML转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // 清空显示
    function clearDisplay() {
        document.getElementById('logs-display').innerHTML = 
            '<div class="p-3 text-center text-muted">显示已清空</div>';
        document.getElementById('displayed-logs-count').textContent = '0';
    }
    
    // 切换自动滚动
    function toggleAutoScroll() {
        autoScroll = !autoScroll;
        const btn = document.getElementById('auto-scroll-btn');
        if (autoScroll) {
            btn.innerHTML = '<i class="fas fa-arrow-down"></i> 自动滚动';
            btn.classList.remove('btn-outline-info');
            btn.classList.add('btn-info');
        } else {
            btn.innerHTML = '<i class="fas fa-pause"></i> 手动滚动';
            btn.classList.remove('btn-info');
            btn.classList.add('btn-outline-info');
        }
    }
    
    // 切换实时更新
    function toggleRealTimeUpdate() {
        realTimeUpdate = document.getElementById('real-time-toggle').checked;
        
        if (realTimeUpdate) {
            startRealTimeUpdate();
        } else {
            stopRealTimeUpdate();
        }
    }
    
    // 开始实时更新
    function startRealTimeUpdate() {
        if (logUpdateInterval) {
            clearInterval(logUpdateInterval);
        }
        
        logUpdateInterval = setInterval(loadLogs, 5000); // 每5秒更新一次
    }
    
    // 停止实时更新
    function stopRealTimeUpdate() {
        if (logUpdateInterval) {
            clearInterval(logUpdateInterval);
            logUpdateInterval = null;
        }
    }
    
    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 初始加载日志
        loadLogs();
        
        // 开始实时更新
        startRealTimeUpdate();
        
        // 按钮事件
        document.getElementById('refresh-logs-btn').addEventListener('click', loadLogs);
        document.getElementById('clear-display-btn').addEventListener('click', clearDisplay);
        document.getElementById('auto-scroll-btn').addEventListener('click', toggleAutoScroll);
        document.getElementById('apply-filter-btn').addEventListener('click', applyFilters);
        document.getElementById('real-time-toggle').addEventListener('change', toggleRealTimeUpdate);
        
        // 过滤器事件
        document.getElementById('log-level-filter').addEventListener('change', applyFilters);
        document.getElementById('logger-filter').addEventListener('change', applyFilters);
        document.getElementById('search-filter').addEventListener('input', applyFilters);
        
        // 滚动事件
        document.getElementById('logs-display').addEventListener('scroll', function() {
            const container = this;
            const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
            
            if (!isAtBottom && autoScroll) {
                // 用户手动滚动时暂时禁用自动滚动
                autoScroll = false;
                setTimeout(() => {
                    if (container.scrollTop + container.clientHeight >= container.scrollHeight - 10) {
                        autoScroll = true;
                    }
                }, 3000);
            }
        });
    });
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        stopRealTimeUpdate();
    });
</script>
{% endblock %}
