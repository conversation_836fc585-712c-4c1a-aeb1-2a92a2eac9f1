"""
系统集成测试

测试整个HMDM系统的端到端功能
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from hmdm.task_analysis import HierarchicalTaskAnalyzer
from hmdm.evaluation import SchemeEvaluator
from hmdm.decision import FuzzyDecisionEngine
from hmdm.models import (
    Task, TaskType, Alternative, EvaluationScheme, DecisionMatrix
)
from hmdm.utils import DataLoader


class TestHMDMIntegration:
    """HMDM系统集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.task_analyzer = HierarchicalTaskAnalyzer()
        self.evaluator = SchemeEvaluator()
        self.decision_engine = FuzzyDecisionEngine()
        self.data_loader = DataLoader()
    
    def test_complete_workflow(self):
        """测试完整的工作流程"""
        # 1. 创建任务分解
        root_task = Task(
            name="态势分析任务",
            description="完整的态势分析任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.task_analyzer.create_task_hierarchy(root_task)
        subtasks = self.task_analyzer.auto_decompose_by_scenario(root_task.id, "态势分析")
        
        assert len(subtasks) > 0
        assert hierarchy.root_task_id == root_task.id
        
        # 2. 创建评估方案
        evaluation_scheme = self.evaluator.create_default_evaluation_scheme()
        
        assert len(evaluation_scheme.indicators) > 0
        assert len(evaluation_scheme.weights) > 0
        
        # 3. 创建备选方案
        alternatives = [
            Alternative(
                name="人工主导方案",
                description="以人工操作为主的方案",
                attributes={
                    "workload": 0.8,
                    "efficiency": 0.6,
                    "reliability": 0.7,
                    "cost": 50000
                }
            ),
            Alternative(
                name="机器主导方案", 
                description="以机器自动化为主的方案",
                attributes={
                    "workload": 0.3,
                    "efficiency": 0.9,
                    "reliability": 0.8,
                    "cost": 150000
                }
            ),
            Alternative(
                name="人机协作方案",
                description="人机协作的混合方案",
                attributes={
                    "workload": 0.5,
                    "efficiency": 0.8,
                    "reliability": 0.9,
                    "cost": 100000
                }
            )
        ]
        
        # 4. 执行评估
        data_sources = {}
        for alt in alternatives:
            data_sources[alt.id] = alt.attributes
        
        recommended_alt, recommended_eval, report = self.evaluator.recommend_best_scheme(
            alternatives, evaluation_scheme, data_sources, "WRDM"
        )
        
        assert recommended_alt is not None
        assert recommended_eval is not None
        assert report is not None
        assert "decision_result" in report
        assert "all_evaluations" in report
        
        # 5. 验证推荐结果
        assert recommended_alt.name in ["人工主导方案", "机器主导方案", "人机协作方案"]
        assert 0 <= recommended_eval.total_score <= 1
        assert len(report["all_evaluations"]) == 3
        
        # 6. 比较不同决策方法
        decision_matrix = DecisionMatrix(
            name="集成测试决策矩阵",
            evaluation_scheme=evaluation_scheme
        )
        
        # 添加模糊值
        for alternative in alternatives:
            for indicator_id in evaluation_scheme.indicators.keys():
                from hmdm.models.decision_models import FuzzyNumber
                
                # 从属性中获取值或使用默认值
                value = 0.5  # 默认值
                if "workload" in indicator_id.lower() and "workload" in alternative.attributes:
                    value = alternative.attributes["workload"]
                elif "efficiency" in indicator_id.lower() and "efficiency" in alternative.attributes:
                    value = alternative.attributes["efficiency"]
                elif "reliability" in indicator_id.lower() and "reliability" in alternative.attributes:
                    value = alternative.attributes["reliability"]
                
                fuzzy_value = FuzzyNumber(parameters=[
                    max(0.0, value - 0.1),
                    value,
                    min(1.0, value + 0.1)
                ])
                alternative.add_fuzzy_value(indicator_id, fuzzy_value)
            
            decision_matrix.add_alternative(alternative)
        
        # 比较多种决策方法
        comparison_results = self.decision_engine.compare_methods(decision_matrix)
        
        assert isinstance(comparison_results, dict)
        assert len(comparison_results) > 0
        
        # 验证每个方法都有有效结果
        for method, result in comparison_results.items():
            assert len(result.rankings) == 3
            assert result.recommended_alternative_id in decision_matrix.alternatives
    
    def test_data_export_import(self):
        """测试数据导出和导入"""
        # 创建示例数据
        sample_data = self.data_loader.create_sample_data()
        
        assert "task_hierarchy" in sample_data
        assert "evaluation_scheme" in sample_data
        assert "alternatives" in sample_data
        
        # 测试JSON导出导入
        import tempfile
        import os
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 导出任务层次结构
            task_file = os.path.join(temp_dir, "test_tasks.json")
            success = self.data_loader.save_json(sample_data["task_hierarchy"], task_file)
            assert success
            
            # 导入任务层次结构
            loaded_hierarchy = self.data_loader.load_task_hierarchy_from_json(task_file)
            assert loaded_hierarchy is not None
            assert loaded_hierarchy.root_task_id == sample_data["task_hierarchy"]["root_task_id"]
            
            # 导出评估方案
            scheme_file = os.path.join(temp_dir, "test_scheme.json")
            success = self.data_loader.save_json(sample_data["evaluation_scheme"], scheme_file)
            assert success
            
            # 导入评估方案
            loaded_scheme = self.data_loader.load_evaluation_scheme_from_json(scheme_file)
            assert loaded_scheme is not None
            assert loaded_scheme.name == sample_data["evaluation_scheme"]["name"]
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空输入的处理
        with pytest.raises(ValueError):
            self.decision_engine.weighted_relative_deviation_method(
                DecisionMatrix(name="空矩阵")
            )
        
        # 测试无效场景的处理
        root_task = Task(name="测试任务", task_type=TaskType.MISSION_TASK)
        hierarchy = self.task_analyzer.create_task_hierarchy(root_task)
        
        with pytest.raises(ValueError):
            self.task_analyzer.auto_decompose_by_scenario(root_task.id, "无效场景")
        
        # 测试无效任务分解
        with pytest.raises(ValueError):
            self.task_analyzer.decompose_task("不存在的任务ID", [])
    
    def test_performance_metrics(self):
        """测试性能指标计算"""
        from hmdm.utils.logger import PerformanceTimer
        
        # 测试任务分解性能
        with PerformanceTimer("任务分解性能测试") as timer:
            root_task = Task(
                name="性能测试任务",
                task_type=TaskType.MISSION_TASK
            )
            hierarchy = self.task_analyzer.create_task_hierarchy(root_task)
            subtasks = self.task_analyzer.auto_decompose_by_scenario(root_task.id, "态势分析")
        
        assert timer.duration is not None
        assert timer.duration >= 0  # 允许执行时间为0（非常快的操作）
        
        # 测试决策计算性能
        evaluation_scheme = self.evaluator.create_default_evaluation_scheme()
        
        alternatives = []
        for i in range(5):  # 创建5个备选方案
            alt = Alternative(
                name=f"方案{i+1}",
                description=f"测试方案{i+1}"
            )
            
            # 添加随机属性
            import random
            alt.attributes = {
                "workload": random.uniform(0.1, 0.9),
                "efficiency": random.uniform(0.1, 0.9),
                "reliability": random.uniform(0.1, 0.9)
            }
            alternatives.append(alt)
        
        data_sources = {alt.id: alt.attributes for alt in alternatives}
        
        with PerformanceTimer("决策计算性能测试") as timer:
            recommended_alt, recommended_eval, report = self.evaluator.recommend_best_scheme(
                alternatives, evaluation_scheme, data_sources, "WRDM"
            )
        
        assert timer.duration is not None
        assert timer.duration >= 0  # 允许执行时间为0（非常快的操作）
        assert recommended_alt is not None
    
    def test_validation_and_consistency(self):
        """测试验证和一致性检查"""
        # 创建任务层次结构
        root_task = Task(
            name="验证测试任务",
            task_type=TaskType.MISSION_TASK
        )
        
        hierarchy = self.task_analyzer.create_task_hierarchy(root_task)
        subtasks = self.task_analyzer.auto_decompose_by_scenario(root_task.id, "态势分析")
        
        # 验证层次结构
        errors = self.task_analyzer.validate_hierarchy()
        assert len(errors) == 0  # 应该没有错误
        
        # 测试任务指标计算
        metrics = self.task_analyzer.calculate_task_metrics(root_task.id)
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
        
        # 验证指标值的合理性
        for key, value in metrics.items():
            assert isinstance(value, (int, float))
            assert value >= 0  # 所有指标值应该非负
    
    def test_scenario_coverage(self):
        """测试场景覆盖度"""
        scenarios = ["态势分析", "威胁计算", "辅助决策"]
        
        for scenario in scenarios:
            root_task = Task(
                name=f"{scenario}任务",
                task_type=TaskType.MISSION_TASK
            )
            
            hierarchy = self.task_analyzer.create_task_hierarchy(root_task)
            subtasks = self.task_analyzer.auto_decompose_by_scenario(root_task.id, scenario)
            
            assert len(subtasks) > 0, f"场景 {scenario} 应该能够分解出子任务"
            
            # 验证子任务的合理性
            for subtask in subtasks:
                assert subtask.parent_id == root_task.id
                assert subtask.level == 1
                assert subtask.task_type == TaskType.ZZ_TASK
                assert len(subtask.name) > 0
                assert len(subtask.description) > 0


if __name__ == "__main__":
    pytest.main([__file__])
